{"name": "react-bootstrap", "dist-tags": {"bs3": "0.33.1", "bs4": "1.6.7", "next": "2.9.0-beta.1", "latest": "2.10.10"}, "versions": {"0.2.0": {"name": "react-bootstrap", "version": "0.2.0", "devDependencies": {"grunt": "~0.4.2", "karma": "~0.10.9", "mocha": "~1.16.2", "react": "git://github.com/stevoland/react-with-test-utils", "envify": "~0.2.0", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.6.0", "karma-mocha": "~0.1.1", "karma-jasmine": "~0.1.5", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "grunt-es6-module-transpiler": "~0.6.0", "grunt-es6-module-wrap-default": "~0.1.0"}, "peerDependencies": {"react": "~0.8"}, "dist": {"shasum": "c4947f61e402cbf9930ac679ad1c45681692621c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.2.0.tgz", "integrity": "sha512-8OADYzY0nfh/dU4V6RTtqNnMLymzwrTjPkb1xyXJ3mQBdbNDzGdyx426fyK/f9bnljcC0spiXxVWXW3lea/5cg==", "signatures": [{"sig": "MEUCIEP+h6inOWCFa/gABJC74r1XfXIXJ7wizpwGhWsQunJYAiEA7VsMl6H1T/HsVu4G758roW5sF8kTyQf6BZoqSICqWRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.0": {"name": "react-bootstrap", "version": "0.3.0", "devDependencies": {"grunt": "~0.4.2", "karma": "~0.10.9", "mocha": "~1.16.2", "react": "git://github.com/stevoland/react-with-test-utils", "envify": "~0.2.0", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.6.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-jasmine": "~0.1.5", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "grunt-es6-module-transpiler": "~0.6.0", "grunt-es6-module-wrap-default": "~0.1.0"}, "peerDependencies": {"react": "~0.8"}, "dist": {"shasum": "91cb98f45e1d67a21f963e20354763b1b2d047ad", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.3.0.tgz", "integrity": "sha512-Plrdmy08CD7s1sDOoGlzMLeLr4615LzroxHv7f0PKC0BSbVRsPuaDWuy4Q8IieNaP1diwoKh1grp3roC3rPXyQ==", "signatures": [{"sig": "MEQCIA2GF8d+u4sdGeK+W6ek4vm9FRXLERr0UcCLZPVcvq9vAiAPTEjLqNNQ5FKTkMEZTM6o9EyLV3clEJem6yitU8wlsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.0": {"name": "react-bootstrap", "version": "0.4.0", "devDependencies": {"grunt": "~0.4.2", "karma": "~0.10.9", "mocha": "~1.16.2", "react": "git://github.com/stevoland/react-with-test-utils", "envify": "~0.2.0", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.6.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-jasmine": "~0.1.5", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "grunt-es6-module-transpiler": "~0.6.0", "grunt-es6-module-wrap-default": "~0.1.0"}, "peerDependencies": {"react": "~0.8"}, "dist": {"shasum": "5fbeb455ed68595e7ef41cfa2556bd5ed29da80d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.4.0.tgz", "integrity": "sha512-S9Vf++HMybWPhDvndQ0XUnwk91ydrZ1hbQmRcjpT1liKwZ3U56PZIPijE4POtLBa51Ataw9NmMQ6VeeL3HTHIQ==", "signatures": [{"sig": "MEQCICzmHlKmilYyJ1ULaUNyndcvX6ByuvQmtr5q1WQxXPG/AiBHowHBcAWwugKKqc324hVayOKQ1ivsqZJ8t3j53lsY8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.2": {"name": "react-bootstrap", "version": "0.4.2", "devDependencies": {"grunt": "~0.4.2", "karma": "~0.10.9", "mocha": "~1.16.2", "react": "git://github.com/stevoland/react-with-test-utils", "envify": "~0.2.0", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.6.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-jasmine": "~0.1.5", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "grunt-es6-module-transpiler": "~0.6.0", "grunt-es6-module-wrap-default": "~0.1.0"}, "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "669e81efa1a9cc05803121fb66522290f7068760", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.4.2.tgz", "integrity": "sha512-xWVqNZqIt1MQBRT8GxhAs8P+t7wVEn38OHFW14C7pK4Uil8Aapd90mEnWkU6xoTnMZcvr7dwKbom17GoM2V5lg==", "signatures": [{"sig": "MEUCIQDUMHz/WXl78mg8KvQsJQZCsoAoiuEZARgS6ehkw4WUaAIgKSAVVeySglHZGgwDKy4xTZa0SNaphNItF6anMOSrImw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.0": {"name": "react-bootstrap", "version": "0.5.0", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "d67d9bec6cdfe7e52e8fd675bda362c1227c47fd", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.5.0.tgz", "integrity": "sha512-QIT/SlrAN/KH/LzoUGuABxSJQHu7eUrPAiVhEWxeuaX5mgd7+LcfAVF7PlVCxhgh+gsyZu4bhiFinSG0jfuhFw==", "signatures": [{"sig": "MEQCIHIiE8rAnMw7UP8FzgrTEQmeGdCm9g+2r50zvYl69DjoAiAZwaudJVfEdVGG5hUqr4LrePNJChqIsfeliT4DSnRE2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.1": {"name": "react-bootstrap", "version": "0.5.1", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "e0a1637a8cdbcbe5977668f217d56cacf9d82c4f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.5.1.tgz", "integrity": "sha512-pwFGkm12K6l1HdNB3aY0bmT7obIT4s6AzMba1S3nNaSsl1QNoMUq4+7Ioyz54NA6Pc+bcyAFB+GvYqJf2fQr/Q==", "signatures": [{"sig": "MEUCIQD8IXTofkJSHfCJNeHNDEO2JoRUgrhPFGsaKPmM7eWRrwIgO2vHYAo3uLKN5GWIGCqeI3aS3ZsXUlkbk0XwqKZSv0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.2": {"name": "react-bootstrap", "version": "0.5.2", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "333ae1824325dd58ef00776497901bda79c27bd4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.5.2.tgz", "integrity": "sha512-CD6vT6G26Re1ai2IgULBqL8mMa1vYfGn3AXlYc5xGXg8PD7nn9IHd2/Jt+LJ2q5Qoxso2bKwRyyDPYIqPBp6eQ==", "signatures": [{"sig": "MEUCIFNWV09RxTw+90YVfVMswD5NPdYP+mnly3xLuFLN7TMkAiEA7wEaJqeoRVz144NkqBhp3wLCHqdktlbPsOesGs2qq14=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.2": {"name": "react-bootstrap", "version": "0.6.2", "devDependencies": {"grunt": "~0.4.2", "karma": "~0.10.9", "mocha": "~1.16.2", "react": "0.9.0", "envify": "~1.2.1", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.6.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "react-async": "~0.6.0", "karma-jasmine": "~0.1.5", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "~0.10.0", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0", "grunt-es6-module-transpiler": "~0.6.0", "grunt-es6-module-wrap-default": "~0.1.0"}, "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "3441aeb422dbbfe261040e6d629cd21852246752", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.6.2.tgz", "integrity": "sha512-chrsZ5T9LTRhd9qk8384bmbXavKeEwq2LeLuSF+fBmjDKBe4LHCRU9LjUdrGiKoKb9/YnGE3iikUMMlhgFLHVA==", "signatures": [{"sig": "MEUCIE8RpbgF7lhl8tM0knoAcEYAytAeQB8cDV+S2Bfhg4WqAiEAwOGaP6Dfvh9h3q/GkEArJCbhAEimpU61SMYQo7hqkJc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.0": {"name": "react-bootstrap", "version": "0.7.0", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "17fd343210ab97fa6c2392ac1b6aa8effc02c311", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.7.0.tgz", "integrity": "sha512-Yi9bLJBvRwXfYvJ+98ZYxqdQSU4q0cNliu44gv+Ubh7UGhRK1tMpoPftc4dlkAG1IFvjyWZ7tbNqMqTSzHBGHg==", "signatures": [{"sig": "MEQCICHTYY8zg/pThgcKZDJwPCl8FOlEbDyj9kqfZQOhpSexAiAlqjo/PJbOExoQk216Om8/A3G6RmstbhShUoKi1WOSNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.0": {"name": "react-bootstrap", "version": "0.6.0", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "a5f942c06db6389d816ec0e680fc074bddfac316", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.6.0.tgz", "integrity": "sha512-y1UFnHX8jnbqgfWCi94y1uIM/WULM7+KfckXgxnAgenoeewPwsnwMGMesyCOVVwyOGgMeeO9Jf4eu/8h2N0S0A==", "signatures": [{"sig": "MEUCIFXVQ8ZkTu36LHVwkO3stL1l7eZtVQWAjAu1rFK06UbiAiEA4BOeUXAUqNv4DRc9Xg1obEg34F6HC1PNNDI17c40IKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.1": {"name": "react-bootstrap", "version": "0.6.1", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "edd92ef2523aa8cb44bdb9b38389600f8344e73f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.6.1.tgz", "integrity": "sha512-6UvM0Hun66INfvJ8YS2kuJiSz7z+OeLSqczmVvqMBNQ5KQZt5iiw7eHLKsM0pOyYjcceFiTZOCf95hmuuCTTWw==", "signatures": [{"sig": "MEYCIQDmm+ZhIU3hn1c4KmjGpm4uvtrW8Y1t2ggK/BL8Aeis1AIhAMmeNPXpX2Lwczcj8Re/PQ3U+PWPZ+58WScXVN/nzFfv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.1": {"name": "react-bootstrap", "version": "0.7.1", "peerDependencies": {"react": "~0.9"}, "dist": {"shasum": "4b68d6bdd1504ca026ed91430324b1e539df972f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.7.1.tgz", "integrity": "sha512-M+QTD3Fun5VeNQERt8B2DdWzgc7OaQP1Bda3bmkUEwSnaIirnx1GtatOKqIiZZ5yYCcy1VfDbV/iGNhfn9WFOg==", "signatures": [{"sig": "MEQCIBsvFmRdwG2zT3Alqq6y11VxdVtLHa3Q9geZ/vyahnIeAiAtgHqUeOqV/LVOVD1cC8D+MYF6U2JOxs2HIbohrQT5kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.2": {"name": "react-bootstrap", "version": "0.7.2", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "c7cc90fe07c95bbc89f3cae780dc64880eda7352", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.7.2.tgz", "integrity": "sha512-Zh/aWyrh5faaKTrOcZCD5rbnxRGnzaXjbm0ZkzJ58n4uOIuZES23Qwfc9sXg+R6yoHq8wRYStmxAQn9+qUxhjg==", "signatures": [{"sig": "MEQCIEq+5LzjaqeosSEJB9cUclDzaMqCRzfKg8lBxytniSxnAiAC9tTnpwiCLZTgSmOBi2DXIoRd+DArFVlHNX5nZsCehA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.0": {"name": "react-bootstrap", "version": "0.8.0", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "fef32e774952bf258ce16daf8a6f1958ce925d0f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.8.0.tgz", "integrity": "sha512-NCx7NE5zJ2G+hwwwgHIyW7uX5YbxYSxlAeZu67VIbwzgmRXFL3fKdMULqDtmEe8Zy2mDyl5li3oVpbUhGrUBxA==", "signatures": [{"sig": "MEUCIBRXqjZK/ucIAidaALtFvk0KokkWmI7xpDiyS+TkoyLNAiEAlu4L2P2OTP8Ci6oZ5IN1PfMJfJev3KPs/98CzSPVg1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.1": {"name": "react-bootstrap", "version": "0.8.1", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "a52b8bd856ddd840a8aa157fa1f5cf0a53ed818d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.8.1.tgz", "integrity": "sha512-erEhxvuhX5Kb19yFDcYVXB9tGM10f2MUBfBdlZQIBdQxurUrx/9/QxtdazRDJC5yd4cxe+pndRlq/aMmiwlMgg==", "signatures": [{"sig": "MEYCIQCtyZyVU1z5ETu3ifUnfO67KGhHcI8voEYV6isj/IhxGAIhAIExm0R+plSnhHBXH1ch50A5KPhwmdqpX58dMNqTnUjA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.2": {"name": "react-bootstrap", "version": "0.8.2", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "532e796c9740144dd8f387a3d16574e0b12fc4db", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.8.2.tgz", "integrity": "sha512-rSzCI6yQRq+8HgB3ws5sY2FhRnFtc8SaTt++oB97tqkf3phiWHYRMHaUR2ccEQjFx39gsFbOM7OBUzuexKZuvA==", "signatures": [{"sig": "MEYCIQCKt3CdvtDMeh7cy6N0skJH2s9v+hg3KLmr/F58VnCVeQIhAPyMthLfV5DXpBbz18USE/mWc1YjBhKX4hx2lGNVA80n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.9.0": {"name": "react-bootstrap", "version": "0.9.0", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "2b713a7563dba8fc72b9321996f099e6216bede0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.9.0.tgz", "integrity": "sha512-6nicsyEAc9WJE6HX8lJgdC2Rs75k2qAF4Kh9mTzNHZbJlauHWOLeA8QXWsB21J4KiXlvJh/OYbFOAER4VK47TQ==", "signatures": [{"sig": "MEQCIDn82jzy7DvBSkbLJA67NQYeCnVCN9oQoP2HqUOAGlurAiBrIPyfmHFY+g+7HJJ4DhjZ5cvBPi26/2Q4bCWP4LJT9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.0": {"name": "react-bootstrap", "version": "0.10.0", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "dd4557d32fa4e990029cf979ec7243804aeafa5d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.10.0.tgz", "integrity": "sha512-hLfRqckvv4/lBulQMYY6nA7q0JveT0HQQJd4947ulUj59di+j/ETGou1gnpipOoICZ2RuR0ordgbOucNo+I1SQ==", "signatures": [{"sig": "MEUCIQDzBOzyBLmlnXWn+Efzu6uuYUzCNl3fSL/o6L7ycVWGOwIgUATBEMUWfe6MjR5wK3d8pT0/J37W0vQqhMwrkamtv1I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.1": {"name": "react-bootstrap", "version": "0.10.1", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "bba14f392e1ffa263e92ad44de7244587a64cac4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.10.1.tgz", "integrity": "sha512-HUbW99o7bXJj+Grmk4YUE9qEBXD1hBvK6Mr2BIB/Lvyb+x898k9N9m9pJkXXRHi+B0Jf2moHvxZhjgczzwvOXQ==", "signatures": [{"sig": "MEYCIQDdgzSEONQvmJIsmoVN16bM1vMKzXlaZYn7SLVvzzVakgIhAJSWYkAFKb5LD9Y+BW+A9QLM17FH6TkYkJNuw8l+2MOz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.10.2": {"name": "react-bootstrap", "version": "0.10.2", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "008aee7d7d7181ecb312e9d6fb3432b9400d28ed", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.10.2.tgz", "integrity": "sha512-hViN/Pvc1DqcpLgsNaMwBoq/+qlf/zZz+bY2U4qXy09SFgANUPRfwNrPoKlImRoTLYEvsQZfrYhYyIivGVVzIg==", "signatures": [{"sig": "MEYCIQD/v5aLp7VJ603DgZrZ0uGisf2jyeHwqzQsFIhVJaUUiAIhAPy+yMs6Dli3e4Noy1jOlMAs5j0NUkbjpJBLjRZu6DH3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.0": {"name": "react-bootstrap", "version": "0.11.0", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "7c890cd2a3de2dc1429384e89862e4b890157eb8", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.11.0.tgz", "integrity": "sha512-y4WIVmMtkdcNfGbaInSgPydAVF8xb1ewAMSzofzgJS9SmPOT9R6XYYB5wgsChT0vtFIf7rABp+3e6HouqyvwCw==", "signatures": [{"sig": "MEYCIQCyoSqZBWu8muBIsn3TiIS8Sqth+3DAlen8dnTMsXpBAwIhAPD+nHOTZdJKxE8MoWxEVQcByQF9HNhhGFy2WDp7CIAV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.11.1": {"name": "react-bootstrap", "version": "0.11.1", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "7cf072ebf5f345680c539c011fe1b40844754828", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.11.1.tgz", "integrity": "sha512-ikaaY1VI/3cTVnamiqAgZDid1WwvFEl4n54WwpiyeDXUBXU89OO69PsprAQMBQM0xJSs6qjWHuzn3QsMrNuxZA==", "signatures": [{"sig": "MEYCIQD3iUR+gr9gRs3P4XSaJW5k5xL5PlueroOUDodlEKyATQIhAM3co9IQoQUZw286AQh9d2ay3xmH9QurDMLN/SQb+G1p", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.12.0": {"name": "react-bootstrap", "version": "0.12.0", "peerDependencies": {"react": ">=0.9"}, "dist": {"shasum": "a23e250b57ba63c36e2021170ffe3489bf704300", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.12.0.tgz", "integrity": "sha512-G+srCxWR6r7Pq/cXpyq4CgO5cEiehwCTP2yQ5oaM9Y/bw+msjGWuFhBSp4fIIKn94SdaYdbBOI7xhkv/ByP3Ug==", "signatures": [{"sig": "MEUCICeNkuxGILjwBSwommnR/6pS6jteHcSSX5g0+rHbW3mUAiEA4zw4yjpONoBTlUERlT4eDzP7wNkFJdQQZPZx6xgjhxo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.0": {"name": "react-bootstrap", "version": "0.13.0", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "b024b5706ef9413e8d8ac66fb3d1487f103d6ca6", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.13.0.tgz", "integrity": "sha512-XJyAoGlxIgrekKEd30FIT9tpxaP72yy3X5OK4npbbzrQaxGTjzQB++cYiqY4GuMRdyFa9XaRGIpy6PO+OPvOEg==", "signatures": [{"sig": "MEQCIENd4FAGYkw1G+z+ypviQNtEJErMjn0tGnt4Ue46oDkxAiB+9oZtWWpsBkMaCW4xVLSdtLDCWunrQ/vfvyzhIwrsLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "deprecated": "Accidentally pushed from the react-bootsrap src repo which does not line up with documentation on CommonJs usage. Please use 0.13.1"}, "0.13.1": {"name": "react-bootstrap", "version": "0.13.1", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "41e7f30fc529ee33ba004ca45861e19de26edf18", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.13.1.tgz", "integrity": "sha512-Vj+bTMmCytk6eq2QljgODbiH0cVXSkj2tHffTApcEl8GRwJfU97uQJievmotzi/Llr2x47oUp2SPSz57e0uCjQ==", "signatures": [{"sig": "MEUCIQDnmg+COfNu8Z4Hemy1fMFaz0mopIlBPA+DsQP9JHR3ggIgYX2wCgltnuc4gHE2hICDYqk7bnQpyFExkhDpc8rOr1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.2": {"name": "react-bootstrap", "version": "0.13.2", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "fb2a6b00d1ad2898cff9c7a7c19b99e383512e6f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.13.2.tgz", "integrity": "sha512-XH+IG+R/oCu0Xn9hCNv5rImM86xccV2NdybU/nLIqjB4gWFXnqqL0pwmVT58ew+EqYlQRh+uhtBN5Tm5fD1/Qg==", "signatures": [{"sig": "MEYCIQD5mbQ8nnBlDEqS9fEHuTcAZYYVslTS4BVdT/Ig9HiJigIhAPY/w+hwLhzTWP///glTwcBfYQaiOAFMePgxFRyKiIL3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.13.3": {"name": "react-bootstrap", "version": "0.13.3", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "ba0db9533da84bace654671eb37502211ea196b1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.13.3.tgz", "integrity": "sha512-i1NtoPYKjhgEKH0uFUHiU2/YLCz2Fb37wSj9dSqY7aX2lGsiM4wZD2uQ5GjP2E6RqRxuhbIFsPIw1JG79PPwyg==", "signatures": [{"sig": "MEQCIH9T9N0Cd8uTEoJQcahcGPtEBBFRABqFrFliJ73KrroLAiAKpkgS0F9Y0BcEZenRPQhmLejA2iB3oJjq5Fb9VUVyMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.0": {"name": "react-bootstrap", "version": "0.14.0", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "19fa4ba6f01c3e133720913aa5e7c65e36e9904b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.14.0.tgz", "integrity": "sha512-hKt7z+jPlNy9yX5ypSyqBGVKHL7K2AqBIMm3oNJMwy611oyH5MdzjPgCsiDrBzjuFnJiTM5LGh5k930PJoc7mg==", "signatures": [{"sig": "MEUCIQD0MVp7/ENsz1K8EOpiXsvsv25oI9VUXTMzL+iT5x7LIAIgAcYn4XnMuQx63Dv1JFiUBlULvInirsXgzVgZLYcLM/0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.14.1": {"name": "react-bootstrap", "version": "0.14.1", "peerDependencies": {"react": ">=0.12"}, "dist": {"shasum": "85748f1a15943ef2008a78461dfea776745b09a0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.14.1.tgz", "integrity": "sha512-gBNROd23KnIkZDvab/TIMuf3nmCts7WF7xD60TxHime8fu1QBuIvIOXVqAvx9sW7N/ByV9sS2VhKSB/i8RAUTA==", "signatures": [{"sig": "MEUCIQD+PMiMqsWZXga9ZSnkAnXwrCg8X6+ftI0vr1jJo2q7IAIgHrk/ae1JDb8dEHWIrZay7uBCYqqJL7LPkp0CXtGB15g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.0": {"name": "react-bootstrap", "version": "0.15.0", "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "~0.12.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-async": "~2.0.0", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "git://github.com/STRML/react-router-component#react-0.12", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.12"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "3bb307f4f7065d9a9e49755f1b1c6c7f8ea2b608", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.15.0.tgz", "integrity": "sha512-5yu2DNqz1ifbgV63oPuKfcMfN8iFG4Wl/67AuMkttupqYJEavULtuEEVGP066lKqfuaAqYjg2KFCUsB5dvSk+Q==", "signatures": [{"sig": "MEQCIB8B/FRorPom1W1fyRu+os/X9AzaP8RqHdaD9pPj+bcpAiAtGpiN5hpKX1E7XV3R//TQr3itSmM4FVt941m7XoNfuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.15.1": {"name": "react-bootstrap", "version": "0.15.1", "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "~0.12.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-async": "~2.0.0", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "git://github.com/STRML/react-router-component#react-0.12", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.12"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "057a897a144e140e14fb58f470e3c3e205e6ef6d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.15.1.tgz", "integrity": "sha512-mw43G0PEM02NN1A5qpDE44ThDKWf5zkjMzhMMzd73S7Zc31dkPfJKFyKvgOMWSU7eOPkH3u4OIXg88bqqjhzAQ==", "signatures": [{"sig": "MEUCICyqZLmcMh15/NYk3CajCu0HlYNdcads5mY/TSRa7TDdAiEAxcVyx3HEWHE/+SQ0rBoZEZSnd9Pi9YJfq0khCqDswls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.16.0": {"name": "react-bootstrap", "version": "0.16.0", "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "~0.12.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-karma": "^0.10.1", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-async": "~2.0.0", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "git://github.com/STRML/react-router-component#react-0.12", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.12"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "92b60db149e6ba211428c546505ffb38088cc1bb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.16.0.tgz", "integrity": "sha512-qCv+d4LwWdOUx+qa37MLU6ULSSyXxaRQQKkghF8SBZzPtVWIwYPQ1xPMkBD07kXEYYMu92dLyDoajghjMtPbJw==", "signatures": [{"sig": "MEYCIQCG3iNO8d5KdMhjgidw4dhhOI71dNWeIdB5hTIVXYYqMQIhAItVgnD4JWAhHKHp2iX7jEcNCeuoejKzjNfi28oqBMkQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.16.1": {"name": "react-bootstrap", "version": "0.16.1", "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "~0.12.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-karma": "^0.10.1", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-async": "~2.0.0", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "https://github.com/STRML/react-router-component/archive/v0.23.0.tar.gz", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.12"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "1cc9f1570eed41db343d5ff8aef0e911d57cea31", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.16.1.tgz", "integrity": "sha512-Ov4GqASzilEhel18RcrNkvyWVH+YZMjoXN8QQ+sIFdkiYi80WR5CtUT+PWfWWRPrIZyDBYSZpj3YGtVss1e0fg==", "signatures": [{"sig": "MEUCIQDHfvlZ5a2t6/3nQXR5ol2Ks+nVxF9XiYTrdQU6VbwNnQIgX5i+Ms99gmcjji9Lt23B4SxtjYHPnGD2IanqjzOaWs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.17.0": {"name": "react-bootstrap", "version": "0.17.0", "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "~0.12.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-karma": "^0.10.1", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-async": "~2.0.0", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "react-router-component": "https://github.com/STRML/react-router-component/archive/v0.23.0.tar.gz", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.12"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "8b42279b3343ade28f1a7ebed82f98ff1866b067", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.17.0.tgz", "integrity": "sha512-JREUoGQcmdg2l06ZayvSb4xDxXXBeq014zUcviqOe0ffat14oZR1iKcpSe5vfs+oAhWf4Nbc57cg7pAMmiv91A==", "signatures": [{"sig": "MEUCIQDvjsoT7VOmNhJbMaMCqN39fUfE1L9dCRdTH/83TRZ3NAIgS8ob16tQJHgLqyHudDeQ8pbs8+q2u+lJndfx4AUv3u4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.18.0": {"name": "react-bootstrap", "version": "0.18.0", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"async": "~0.2.9", "grunt": "~0.4.2", "karma": "~0.12.8", "mocha": "~1.16.2", "react": "0.13.0", "sinon": "^1.10.3", "envify": "~1.2.1", "semver": "~2.0.7", "grunt-cli": "~0.1.13", "requirejs": "~2.1.9", "karma-chai": "0.0.2", "grunt-karma": "^0.10.1", "grunt-react": "~0.10.0", "grunt-shell": "~0.6.4", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "karma-jasmine": "~0.1.5", "grunt-amd-wrap": "^1.0.1", "karma-requirejs": "~0.2.1", "grunt-browserify": "~1.3.0", "grunt-contrib-copy": "~0.5.0", "grunt-contrib-clean": "~0.5.0", "grunt-contrib-watch": "~0.5.3", "grunt-contrib-uglify": "~0.3.2", "karma-chrome-launcher": "~0.1.2", "karma-script-launcher": "~0.1.0", "karma-firefox-launcher": "~0.1.3", "grunt-contrib-requirejs": "~0.4.1", "karma-phantomjs-launcher": "~0.1.1", "karma-coffee-preprocessor": "~0.1.2", "karma-html2js-preprocessor": "~0.1.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "d0e56ae6d6b5d992880993dd9b7db1e70181522b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.18.0.tgz", "integrity": "sha512-5HHJh7nZHy8L57kHYF5+KJ+RxHoj4g5CwaW+lekHgSRPZyz5WZAoitZe220RVMp8PRtB18M24acjp3FLbTVW9Q==", "signatures": [{"sig": "MEUCIQDrNc9as3Ch5HoDBeatDAcylO84PtGiFPgAhPNVVhAx3QIgOhLX8+x37EV1j6nmqpZszBfAD4KIfIC12y8IJarjuJ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.19.0": {"name": "react-bootstrap", "version": "0.19.0", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "~1.16.2", "react": "0.13.0", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.17.1", "lodash": "^3.5.0", "semver": "^4.3.1", "express": "^3.4.8", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "bootstrap": "3.2.0", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "0.0.2", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.8.3", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^1.6.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "c3d58beb8131fc1cd36050fcc0979ee02f735416", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.19.0.tgz", "integrity": "sha512-55LGXEE5/usbDnn+hq96TslUUIlkfZ0Ds642+H3w7Eknv4oBE9FQR29NeTIqUQwLf6UKSo2PxCJjb/Gu0LNPtQ==", "signatures": [{"sig": "MEUCIHK8yNk3xyREsXoDzZkBZ7FP1oPnpQf7AD5OHuTCrsXiAiEAsmAZG1xuNlzNGtYtgnQfx6iiXkx8uBM+owyJwC17NV8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.19.1": {"name": "react-bootstrap", "version": "0.19.1", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "~1.16.2", "react": "0.13.0", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.17.1", "lodash": "^3.5.0", "semver": "^4.3.1", "express": "^3.4.8", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "bootstrap": "3.2.0", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "0.0.2", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.8.3", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^1.6.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "20562ecf84f6d65e3ea7c599ace7f1ce043c4a18", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.19.1.tgz", "integrity": "sha512-vzGWyTKkWSKXvTrKbEBsOAuGS044nCD4ZLa0sD5PyJXoerSODpnJsm/iwj58f1fML9lgvKdlQsnyiy63Mhbd8g==", "signatures": [{"sig": "MEUCICUIILHRUEzGl2Bi+ujiO+DhjhdgSGr8MHMC8P1UT/bsAiEAhwqlIg6rQHmcjjSh2NUqOueyqeq+c/yc0v1dSNCFMMg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.0": {"name": "react-bootstrap", "version": "0.20.0", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.18.0", "lodash": "^3.5.0", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.9.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.0.2", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "3c756d038276b48e165c12bab1670ffd3e95ada2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.20.0.tgz", "integrity": "sha512-jQLGqdOCq7r/Ku5YwtO2si4RoZMjKzrRRrVzvChl1Ir8XBslWyCjF9M/2iIJ0NUSlW0+3r0dKZaqxkizI6iwTA==", "signatures": [{"sig": "MEUCIQD+cIKdgd0Fe2QLc7tqBJ5Xw682vI5yx71GLgzTMG22WgIgBXjhoAGNDDO1MBGbeLdhYEq9B3YgDzSeL5IWFZ3opJI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.1": {"name": "react-bootstrap", "version": "0.20.1", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.18.0", "lodash": "^3.5.0", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.9.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.0.2", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "500ed3b184c56be0e7d0ca0bd3292f6ac9c96341", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.20.1.tgz", "integrity": "sha512-qpDxUEeQlbNvNb0ZC1dQdflCWLmhU2Jahqms3tu/ODN4YUr09IoxTDLOV4ecVQ0utwHTcyDH2rh5KQdIV0zGXQ==", "signatures": [{"sig": "MEYCIQCjrhZOUUz1Z7LrsvNdHUxejbkWSUGYVrkFsYW0+DzVHgIhAONYRTAPrxpNR/6uotC4QAVOFafk2vgbsx0kiBpgwMp2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.2": {"name": "react-bootstrap", "version": "0.20.2", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.18.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.9.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.0.2", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "47f4421faba0c2d0437829372a308891c39e29cd", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.20.2.tgz", "integrity": "sha512-pJ7vBcQeLToyvbweWJtrYcH/m+0obPXV0Xw1YkYPmCVymUBneq3JncTJQjCwJSZlvFHYNkQ1ERHSWcZGdd/abg==", "signatures": [{"sig": "MEYCIQC4XWOOPI7Imu6BjQcqr+1UFqd2p7p7u+2ET3Pq14pe1wIhAMtuiTuQ0qtmAXth9kLWDdaFmugjrw1whsKFZvYpUuwH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.20.3": {"name": "react-bootstrap", "version": "0.20.3", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^4.7.0", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.18.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^4.7.4", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^2.0.2", "babel-loader": "^4.1.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.9.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5210e8af7211bd4684b21180f2871d12aebb4ea1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.20.3.tgz", "integrity": "sha512-T7Vf6qstGm9fXIBrYSJV2guv8uw3Fj4o9LPFnkvmZxhCepWjoSO/tejbIeq731GaM/KPex6HKglqnjGdcRbjrw==", "signatures": [{"sig": "MEUCIHm7S9sXKM6GM1v3d3safEfyciOZdrjXcM/HMDkUg6fFAiEAzhdbxsp00YjaDcqyq4gYxBlq05dWGeF6ktN8sMXbV8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.0": {"name": "react-bootstrap", "version": "0.21.0", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.19.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.9.1", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.9.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "f44f902612ba67f7fac6aa4b64164dd85d5c90a3", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.21.0.tgz", "integrity": "sha512-Q1ONeN3Xj1dicSBaNcURqlQ4C570oRTilEqGhDl83Wzqfkh0vusOlxnUsW8tyyF3eZ/mOCIn8aU5qn0GzjLlMQ==", "signatures": [{"sig": "MEQCIAGRL451K4+3lWyzzwn8fHSrPg8eUutmP3ppDnUbF4vPAiB1GCvjudBEq/uVbRzKITSRNrldlWUeAGlQ3K/x0uScZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.1": {"name": "react-bootstrap", "version": "0.21.1", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.20.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6d9b1b4082d58dceabe94839d30be7dc23fb3ea2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.21.1.tgz", "integrity": "sha512-88eyNEHb0GuSWtXfN47taUcKiFuOmNRyZmo1BozNZFVy5TJP7+UQ2KCjVLAH7oqRBgWouoXqrRdPwBHp7EVmiA==", "signatures": [{"sig": "MEUCIEpHGMB5i2wmfFkaKvALMoMz1xo2Wdi8kwqMtvIruv7BAiEAshKNlNX3SaYAIumv6ob6coAU3Q7PICZ7xLVJkD74r0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.21.2": {"name": "react-bootstrap", "version": "0.21.2", "dependencies": {"classnames": "^1.1.4"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.20.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "karma-chai": "^0.1.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "client-loader": "0.0.1", "karma-webpack": "^1.5.0", "transform-loader": "^0.2.1", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "webpack-dev-middleware": "^1.0.11", "karma-phantomjs-launcher": "~0.1.1", "extract-text-webpack-plugin": "^0.3.8"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "9183e1008684517bc99097c1817c18bdfd70f564", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.21.2.tgz", "integrity": "sha512-72x8bj3kdJ+RDwOxMxYcATJald5Homm79430kx1CjsfVU8J1J6nsKHu2hO2evw3McCdxP4kbz2nUp+VC6nY9yg==", "signatures": [{"sig": "MEUCIQDLZmMdZqEfGhXjNJ8rQZeXOmqw1UgcdCIUef3wUyrscgIgK2vMKYY1fo6lAgpC6z4csG3+FugQup9hHM9oPHskU4U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.0": {"name": "react-bootstrap", "version": "0.22.0", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "19e9257d2150f69ade2eb9c2da7e177a99b72bf0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.0.tgz", "integrity": "sha512-grGNSC4+vxGu3bY/ruSmK86IfAf33o0VtZKEFrIvk8JHiWzzoV6BYUCTv7RmnbgfJjUPtTyNPRBuOkCy7L8xMw==", "signatures": [{"sig": "MEUCIQCMpTPdYYUf/jRGSIST3I2Rvr/qRAaa0WK0V3JHjqHnGgIgENAPVhYM4kDGwr8zcWZHU1qQ9CvDPx7EWTIDQJ+ggic=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.1": {"name": "react-bootstrap", "version": "0.22.1", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "1c69472e8fb577086c8f722fe33525ebaa1c5deb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.1.tgz", "integrity": "sha512-xeDNTx0N68IbW0IkDhqtBxRY7w8QKBBEJrbUp5RBJ7/DX57HIKlOe6GJi1m56HkilG5qW+wGOmxwz9PsUxULbA==", "signatures": [{"sig": "MEQCICMjeiJICst/H2JMbhuEG0nRSF9mjxTMfEzqLHWGwRdKAiAqtlUq54cJ/U0th8fWwOR8bil3+xKNqnsoOiNUWC5jEw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.2": {"name": "react-bootstrap", "version": "0.22.2", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "fd08b4443056c79f855ddd1067fdaab7019cf2df", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.2.tgz", "integrity": "sha512-7lyUGjCC8jLjw02l2r/fnBXNwVyWjgDM2gjmlDVDiFTLlHkDaThEhKTAiPSd1owW4s1x9+UNFbRKcEm1uUjhAw==", "signatures": [{"sig": "MEUCIC4DpdXrzmto8zTSkse/wuALMJiR5jTU+SE0TxaLxjFUAiEA+DQPeQY4UZiZLVKukrHJP4idztjEpVsZ45I9zGD2hTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.3": {"name": "react-bootstrap", "version": "0.22.3", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "8f0485054b1575442d2512595d5028d2b4a96ede", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.3.tgz", "integrity": "sha512-9fAC31LSM7ZpJmXuTPG98Sx0eKZfCjR7CHg1mDyxbJrJe4+dgmOapbAtbRp93xDYplmhODMyz8k/qR1mh1opvQ==", "signatures": [{"sig": "MEUCIEe12+tbVFAejYBQ6vFrjYq69BQw2o9BFTu1jOqHBtv+AiEApQ2RClrnUDJfTLilsn2570Erc5Tz5vx6VMyMoiU4y+w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.4": {"name": "react-bootstrap", "version": "0.22.4", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.21.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.1", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "2f54863e2bdacbaa6a8c66ba27d87007f5645d7a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.4.tgz", "integrity": "sha512-5JTZf3nqkF5Hmirw6HCJWI20G3PVPlaz4ZzKGRTibjg6o5L2a59P0CPB3ZqEYcyoNjgivWzC68E4ouywlxMV1g==", "signatures": [{"sig": "MEUCIQDIwiV3JurLBuAwoiRWlXU1GNfCoJdcIOmlFpl1D922oQIgXOUVtwkq/0j655rYzFqOHvPe7aeD52u4ImJ3ukMWlAc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.5": {"name": "react-bootstrap", "version": "0.22.5", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.2", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "0daaef569c362a8ea0a4d602253e15b952ceaaa4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.5.tgz", "integrity": "sha512-cnfAZRul0Uw3wuT6XVy2oFZ0QUp7e1bec/IJzpPvNU4/X5EnMXPzFt1TYn0xqQs6LE33KpqkQIydagIEDAKxOw==", "signatures": [{"sig": "MEYCIQDg5laXZ4UEt0Br2iYcOomrdazBaIwk5kO+5aJ00U1zWwIhAK2+BKW0oUV6i5ywJeCRHnZ3R23mNZnLh13LPTZP5qVA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.22.6": {"name": "react-bootstrap", "version": "0.22.6", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "^0.21.2", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.12.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "f167dfa7b37c2b84339fe977b8bea098b39bc7ea", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.22.6.tgz", "integrity": "sha512-7u6x/ppRUK35RAN9axV/hVLgMFBWp+P6l9GKbHRPn2ZE2ZtiFcg9NIH2kg0bczjO9BPaSvEEiCW2H+Er7n8jkQ==", "signatures": [{"sig": "MEUCIEysz97rWZtn63WAUUJS1VgFxwpbHPAO/lBCdkunHlVBAiEAhDzTyLjmxOS8tUgL9qrC3gwSDXRiXHAGiep4arIilrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.0": {"name": "react-bootstrap", "version": "0.23.0", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"brfs": "^1.4.0", "chai": "^2.2.0", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.21.2", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "react-router": "^0.13.1", "rf-changelog": "^0.4.0", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.1.4", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "65c428f54a07def64973482f8e09ee137acecfd0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.0.tgz", "integrity": "sha512-r9FA+WhzHhh/z6uv+uEPZwIDQzFjxh+OEVkqFDfrQPQcPqUDaDtA5M9YJeh9f1wAyl4KxX4Ja+mHiQb57bX+ow==", "signatures": [{"sig": "MEQCIHhEvnFj1Pm7jPNMqasTAJMJBVBaeoQLLP2hk4Wvq78+AiBgL5/IV2vGdqeYetsnFV9tkvEr8onPNOaU4V/Qvt5WCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.1": {"name": "react-bootstrap", "version": "0.23.1", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^2.2.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^0.3.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "434dda69012f1e5d1e4ec89c0aa4f971d59af0fe", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.1.tgz", "integrity": "sha512-JRU2rV8euKCoyOXx2MYxG4mtURcReld/O3vdKprPl89YlsuJtKptIghfRd4e6l2ogXaEUSpE5AfgMRlcu4GP8g==", "signatures": [{"sig": "MEYCIQDYdZQN7H93uBIZGSPI2qU2rL4FtXurfGquyFFJkR/nWgIhAIKSpXfRjBByhbqTse0/Fu10VSX1zluQICP2dpVlJcOR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0-alpha.0": {"name": "react-bootstrap", "version": "0.24.0-alpha.0", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^2.2.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^0.3.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5d4f21920a8dbb05a8df1e2332deadcc5a3f3935", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0-alpha.0.tgz", "integrity": "sha512-Swe8JrKnJ/eZi0KeGLPXdakJZt0cmYw3FIUmlFLaNa7N0MOaAl7KCjhCiyR2jWLqarNd0ozYorivtNyJstt5sw==", "signatures": [{"sig": "MEUCIQCViO4cyXPixAv3y2b+Ewi1nLeN3oc3DYOW39MMlAedwQIgAtBCVj1cz5tlhhlfTpoivNdOa0j7fei798JBx0ZU0xA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.2": {"name": "react-bootstrap", "version": "0.23.2", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^2.2.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^0.3.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6268858f15644ecfaa0a0081bf28eb66151694be", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.2.tgz", "integrity": "sha512-Kr92NsoeNihtBvBtyOlV4os0KI+fcyiegeyK9LCAwu3/l3a3Rqkw1VF2PkktOQJonLpH0GPSxmholauwfl0/xA==", "signatures": [{"sig": "MEQCIGH+NPpgih4F/oPcbiIpFvRadso8uPZUijindDIrv8gHAiBm90BOzSS0JV9pDkOVwXI2uADD4ZKQ4lAC4UR3uZ0vbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0-alpha.1": {"name": "react-bootstrap", "version": "0.24.0-alpha.1", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^2.2.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.18.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^0.3.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "3175dec40e6e70c7e36ab2f8af6edb2a7f99660a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0-alpha.1.tgz", "integrity": "sha512-uqTMW8IYk9kyyb5u1u+nf0y0RB8TOuEvlecJvYGuZQGjydb5v8CPxxUwTmdF/R2AKggOPIr1bjghrdWZNrKfiw==", "signatures": [{"sig": "MEYCIQCOGAhsGnP/ekkkgL7hT1BnZkNY6mKbWs+9y3hTFKyBWwIhANxC/gA5WXy+1MHqh/5zyVVyaBKKsrGJZpqOBDRTYfJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.3": {"name": "react-bootstrap", "version": "0.23.3", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.19.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "026661c322f0f7d900d05b0df18cb0202c3142e0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.3.tgz", "integrity": "sha512-nkOIGTx/0s2fP/0peRGfHWZ9H33MAXIJRMyzAu6We7fOgP2YKzAyb/TEodCaYTKGQabyNHgp6n06WBtyE1liYg==", "signatures": [{"sig": "MEYCIQDYL2dOEoGooh+JwTayNsMD6pt8IGQTCO5o8okx5Bc0KgIhANREMaaIWnd4RzsrkDisDb4CP9GxI71K1uyXTg0OLTQt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0-alpha.2": {"name": "react-bootstrap", "version": "0.24.0-alpha.2", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.19.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "58af07cb2eefd0a76427c2227f5273288263bd58", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0-alpha.2.tgz", "integrity": "sha512-uXM5B0VI/0yDLW7vsvVGx6x36WsPJJuGjyJHO65UIfhoeXLumu8zIYozwPgmTZ/iGAzWBGXhY0IDNhNwu/l9+Q==", "signatures": [{"sig": "MEUCIEMnnK2JC7cWAIJNfAxOTNGxlxeZ1zGjZeN8yZtyxebXAiEAzyym2k4UdrbT1oSOPnPuVjeJjiJ99daoOBfxDQEq950=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.4": {"name": "react-bootstrap", "version": "0.23.4", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.19.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "7a2ed186867799566aa80d0218085d1d9fba341a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.4.tgz", "integrity": "sha512-AcoUrI/+bYi+65xYlCT02dKS0Mgk0H/elTJjEPeq8GKY5PX2CbTJPhNKw8S2QxT1kuQQbpWZ2igEeBCHlU5ZYg==", "signatures": [{"sig": "MEUCIQCMkLdyyNPLcYnylyU2is/kb9EFi4q0LgPnH/o/SLJMXwIgPw7k80/zbujAHWvXIeX/oAvl7TU7ln0oUqaYe2uRbNA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0-alpha.3": {"name": "react-bootstrap", "version": "0.24.0-alpha.3", "dependencies": {"classnames": "^2.0.0", "babel-runtime": "^5.1.10"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.22.1", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.19.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6ef32cd12ccbdc6c3fb1dd36eb7b01c78c392e25", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0-alpha.3.tgz", "integrity": "sha512-YIADq77zFVQXqsoHeb8c/2y49eziTE8G4fXIJsKF7nXFzKQbEg47WZSclav9cvyEj+bxnhs6GALFFMadD/Ldtg==", "signatures": [{"sig": "MEYCIQDgkk3y5YeNRJ3tUu7FM63gq6MzY6CUAhRRXe6s1B9hhAIhAM1zXNtCI2gz2NAQFFLVjiIsl+DdKizy/UwGbr5vPmnx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.5": {"name": "react-bootstrap", "version": "0.23.5", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.23.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.20.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6c763c611ce47696539cc13d3ce7c6880f9fbea4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.5.tgz", "integrity": "sha512-jqZp7yVQh3qd9X5XGSE2HnwdwhiFE/zkmaWH1ua7bFgZBOVLLwV+a5wx29a+x3o6td68NnqfS4ZVfKZna3mqdg==", "signatures": [{"sig": "MEUCIQCCvKGSXeI7w7P9irqyM/EZBFjGaYqhwjVQ1GNjJf72fgIgftsdM17eyzCpSN/TZ7J1Wk3+Q99x6x8p+iw1aXp8tvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0-alpha.4": {"name": "react-bootstrap", "version": "0.24.0-alpha.4", "dependencies": {"classnames": "^2.0.0", "babel-runtime": "^5.1.10"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.23.0", "lodash": "^3.5.0", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.20.0", "bootstrap": "^3.3.4", "karma-cli": "0.0.4", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.1.1", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.0.0", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.2.2", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.1.2", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "3d0fbed2e9253645bf7a447faaae58fc2be07abc", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0-alpha.4.tgz", "integrity": "sha512-3Rg82HzPe9Mwxbb6s396Trx3KQly77Q3DLlD2Dg+nmnPk5ZYf3ITBZOomKx9rYyjVMvfut3nJxu/Dl2wCkN90g==", "signatures": [{"sig": "MEUCIQCZUj+Mt3wi/f2hI5z9BpqjNWoVXu1kUS+xwY8epn15KAIgaPhzjNqd0QzahDNSiJ6w93p8QTWY9msO6FAK58YfbQM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.6": {"name": "react-bootstrap", "version": "0.23.6", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.24.0", "lodash": "^3.5.0", "marked": "^0.3.3", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.20.0", "bootstrap": "^3.3.4", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.2.0", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.2.1", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "4cfcca20e7117ecce498643faf5975dd1142a745", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.6.tgz", "integrity": "sha512-of4Dg3uh9Q0BFebv5roY2Ak0il90/RlGSz0N2m7B5S2rGqx7DZ8/vhwowu6H5kszn/DSPYtV8coSuHynKF++hw==", "signatures": [{"sig": "MEYCIQC85jmXf/Aob/lMM4c3GgXj1+8r26GEEOA+0SF1kxmy5gIhAJuo+C/TzQ5sOR4OfNHKPfv4PGQZzLuF+0w2wkR+2sXs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.23.7": {"name": "react-bootstrap", "version": "0.23.7", "dependencies": {"classnames": "^2.0.0"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.1.10", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.24.0", "lodash": "^3.5.0", "marked": "^0.3.3", "rimraf": "^2.3.2", "semver": "^4.3.4", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.20.0", "bootstrap": "^3.3.4", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.1.10", "codemirror": "^5.0.0", "css-loader": "^0.14.1", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "karma-chai": "^0.1.0", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.2.0", "karma-sinon": "^1.0.3", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.2.1", "mt-changelog": "^0.5.2", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^2.1.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "c5e239083c3d579b67b884bf4bd4565dc91cdfa6", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.23.7.tgz", "integrity": "sha512-PObdyBp9/P3qLzzr9AMGK8AqV1NdDt6S1uVkAfccKGXIvagbZiCROtoxmBMnIpAxFarIkuuTHg9UvUjqtxeDzQ==", "signatures": [{"sig": "MEUCIQCn9E38z+3Dln0m3crXk2dT1QJtRPHMHxGw1PYcBGhD0QIgXG5blnwlCFzgv32mRHmK9YP8pC20JMS1iC7FmKXm9v0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.0": {"name": "react-bootstrap", "version": "0.24.0", "dependencies": {"classnames": "^2.0.0", "babel-runtime": "^5.1.10"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "5.6.x", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.24.1", "lodash": "^3.5.0", "marked": "^0.3.3", "rimraf": "^2.3.2", "semver": "^5.0.0", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.22.1", "bootstrap": "^3.3.4", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "5.6.x", "codemirror": "^5.0.0", "css-loader": "^0.15.2", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.2.0", "less-loader": "^2.1.0", "babel-eslint": "^3.0.1", "babel-loader": "^5.2.1", "mt-changelog": "^0.6.1", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.0.0", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "672a3d7be47bc2470c96004b1a0c42ff05f2f320", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.0.tgz", "integrity": "sha512-PkKPsOnTMlD7wclfRIjaNYlGF0kQljjKjDD2XshAJPkEncYr2BSVCHcb9FYWg/jjrN2YHRSnlpFvIRA3pTxR/g==", "signatures": [{"sig": "MEQCIBq2w8MvzW3xEdMnBvrA2ZBhhE6/oGA2ranLtFHSqvzUAiBl2sYdrV8KDK/P3DjlEVhEuWJAIHnBhxijFKlCyw8Vrg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.1": {"name": "react-bootstrap", "version": "0.24.1", "dependencies": {"classnames": "^2.0.0", "babel-runtime": "^5.1.10"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.8.3", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.24.1", "lodash": "^3.5.0", "marked": "^0.3.3", "rimraf": "^2.3.2", "semver": "^5.0.0", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.22.1", "bootstrap": "^3.3.4", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.3", "codemirror": "^5.0.0", "css-loader": "^0.15.2", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.2.0", "less-loader": "^2.1.0", "babel-eslint": "^3.1.26", "babel-loader": "^5.2.1", "mt-changelog": "^0.6.1", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.0.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5b179f4e444dd27c9b46bbdad7cc80ea8fbf7393", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.1.tgz", "integrity": "sha512-lbvR224vsF89zwrKd3bavxn7R+M1H5mfIEjZcvHRrmaktWGZNDqUawSH+XVJAkkGhd/yF1CP0s4yNOlwNXo4kw==", "signatures": [{"sig": "MEQCICRgT/eXN8IVrNs/mJ65vRDa4tjzmpFQbHgqBrvcU6QKAiAmKcCCbOyLEv9LKbYDVXqF+jzHpi8TRmfgR9KDvHc3vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.2": {"name": "react-bootstrap", "version": "0.24.2", "dependencies": {"lodash": "^3.5.0", "classnames": "^2.0.0", "babel-runtime": "^5.1.10"}, "devDependencies": {"ip": "^0.3.2", "brfs": "^1.4.0", "chai": "^3.0.0", "glob": "^5.0.10", "less": "^2.4.0", "babel": "^5.8.3", "karma": "~0.12.32", "mocha": "^2.2.1", "react": "^0.13.1", "sinon": "^1.10.3", "yargs": "^3.5.4", "colors": "^1.0.3", "eslint": "0.24.1", "marked": "^0.3.3", "rimraf": "^2.3.2", "semver": "^5.0.0", "express": "^4.12.3", "nodemon": "^1.3.7", "webpack": "^1.7.2", "es5-shim": "^4.1.0", "fs-extra": "^0.22.1", "bootstrap": "^3.3.4", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.3", "codemirror": "^5.0.0", "css-loader": "^0.15.2", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.7.0", "file-loader": "^0.8.1", "json-loader": "^0.5.1", "karma-mocha": "~0.2.0", "less-loader": "^2.1.0", "babel-eslint": "^3.1.26", "babel-loader": "^5.2.1", "mt-changelog": "^0.6.1", "react-router": "^0.13.1", "style-loader": "^0.12.0", "karma-webpack": "^1.5.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.0", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.0", "react-hot-loader": "^1.2.7", "transform-loader": "^0.2.1", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.8.2", "eslint-plugin-babel": "^1.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.0.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.2", "child-process-promise": "^1.0.1", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.3", "karma-sourcemap-loader": "^0.3.4", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.0"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "88a238f5ea21c11581240b039e03381584a23807", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.2.tgz", "integrity": "sha512-HZmkxDVM1r9OatZsHN5gJPWvbYBZ4igtpmN6OeVIxONzxWZA6BD1XvODBTgsmq4W6qt8DsvvDiGbu4q0EH3TRA==", "signatures": [{"sig": "MEYCIQD7SUcUPRyBVJjFeygyVaMOT1OlyIN/VYSwTBDrsanu0wIhAMTH4tAnxjlySEXR4A8fBVqP4pEzWtMAHPH8lfx93OEt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.3": {"name": "react-bootstrap", "version": "0.24.3", "dependencies": {"lodash": "^3.10.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.0.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.22.1", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^1.2.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "cef84773469ac9cc8a5b224c5d222cea16dab73e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.3.tgz", "integrity": "sha512-PdoBUXagGx2yp1pH1QohoPxjzN9mmprkOu2140iZGzS0ixwdj4fR7CRLi9yV2vtATv0cl189VRWV9tlQ7Oucmg==", "signatures": [{"sig": "MEUCICXsAxZDAslTblXT7qA4s+LTf7PopFfN6kY4r0S8ASg9AiEA0fEDpXek4gvmo1hYsx6zTY5wxtyjH/g8MLuAavBYTS4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.0-alpha.0": {"name": "react-bootstrap", "version": "0.25.0-alpha.0", "dependencies": {"lodash": "^3.10.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.0.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.22.1", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.4.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^1.2.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "735c9268d22d423218b327a00cd9f31b050538f6", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.0-alpha.0.tgz", "integrity": "sha512-9BpIJeptFp9rb06QAC8GNa6oClnkp5QnW9s/qtn4oigtna2QaRmPPi7QoooIkbostZMvH9Rfgc0kv41/bDUW8g==", "signatures": [{"sig": "MEQCICKdEQTgpcTRnqo7SXd4qAyYXBSUv5e2g/+1iHKaG1xUAiAfmDX0BKXJG9sRGdz6U/XAKxMWdCdYBRPGGTzKzCs2Hw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.4": {"name": "react-bootstrap", "version": "0.24.4", "dependencies": {"lodash": "^3.10.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.0.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "eslint-config-defaults": "^3.1.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "a3d41f4f57a9117a189391044a42debd9eb39345", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.4.tgz", "integrity": "sha512-UX7RN53AZneWfG5hNl+DEi5wxvalSvzZil2G5OQ0ByFYCNR0AdnnrJWlpcretCfqqFqmDEq5LLqO7N/V8UB8Lg==", "signatures": [{"sig": "MEUCIFciAwtkjY9JqZ7/JBsw2GiX3ZoV3zGJG37Sb6zTtDuNAiEA3sqj20mblZ622LA8T5iHredd+QQdc5wLXnFYZomanzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.0-alpha.1": {"name": "react-bootstrap", "version": "0.25.0-alpha.1", "dependencies": {"lodash": "^3.10.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.0.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "eslint-config-defaults": "^3.1.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "7c04d6bfe9f70e9ac7db5b828320534708521fe2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.0-alpha.1.tgz", "integrity": "sha512-9480XH4ObZMDNaEP86t3IeijZjuJ7HcMp3NdQyXi+gqNfUGM8Elqn8MoJz1CdpDZ53LWRpIUH11JX9JumVtpUw==", "signatures": [{"sig": "MEQCIDtUBiWOcXy35IhyO0ckn4WY5lU+OSZDYa5n3GhpTEwVAiBYjE4hMxD5YXpeU9hruorUEIKMjinWaXaceP+crknd8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.5": {"name": "react-bootstrap", "version": "0.24.5", "dependencies": {"lodash": "^3.10.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.1.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "eslint-config-defaults": "^4.0.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "cc5bbca2ab4fb5513613e237a44498964f170210", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.5.tgz", "integrity": "sha512-qGnQmv9BFPnyyBA91R7RxUo5WZ9FWyMVUsgxRvfibf8HmXhP1agy4VDaneTyShGMXlDWe8ysWqvuRu/P8XWGug==", "signatures": [{"sig": "MEUCIQDRLBeK/Goj2cY+8kOfIsxFekJGcIpiyJmDcD3tqbD7PQIgFHuAbFAOrafS5lsEsgd+PKvb9Nd0+EnQWLJiAb2UT+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.24.5-react-pre.0": {"name": "react-bootstrap", "version": "0.24.5-react-pre.0", "dependencies": {"lodash": "^3.10.0", "warning": "^2.0.0", "classnames": "^2.1.3", "babel-runtime": "^5.8.19"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.1.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "react-dom": "^0.14.0-beta3", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.15.6", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^1.0.0-beta3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "eslint-config-defaults": "^4.0.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.2.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0-beta3", "react-dom": ">=0.14.0-beta3"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "ee304ce44ce4e01f17b76b2265ca6c63762babf5", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.24.5-react-pre.0.tgz", "integrity": "sha512-Y+9Kw2tnEw0q+LfaTi4yb617blrAVjdwK5vDtoP3qDyGwuPINrWupOaYc1AMs1+Np3S7/S156QY4jdxm8WpX9Q==", "signatures": [{"sig": "MEUCIQDUaWZNQfbguYBz7iARuM5yVW2lu07DoO13vVevqffrFgIgaAEFqgdpaHPwF2DYFm/OtWd0JrmGlMvOrI0JVUNZz8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.0": {"name": "react-bootstrap", "version": "0.25.0", "dependencies": {"lodash": "^3.10.0", "keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.19", "react-overlays": "^0.4.4", "uncontrollable": "^3.0.0"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.1.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.16.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-config-airbnb": "0.0.7", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "3106daa3aa10a6d2a3f42c017e99db392e754de3", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.0.tgz", "integrity": "sha512-47Rg9QZFp6Yf+ghc6bpiAXgbuCX9ob+8uv2XnbEL+zdi/8k6E/XquWsarDQD1HqJY9X6PSWjQWw6MVPdHvUgyA==", "signatures": [{"sig": "MEYCIQD/a4Wmbfw4p0X1hAQRjxYFNAg743zwJXUVFv6S+yS3bQIhAN67Ow1+e3ELM5DSvF+fEkbeo6uZkujRJR33u2Rz64dn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.100-react-pre.0": {"name": "react-bootstrap", "version": "0.25.100-react-pre.0", "dependencies": {"lodash": "^3.10.0", "keycode": "^2.0.0", "warning": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.19", "react-overlays": "^0.50.0-alpha3", "uncontrollable": "^3.0.0"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.14.0-beta3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.1.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "react-dom": "^0.14.0-beta3", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.16.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^1.0.0-beta3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.4.0", "eslint-plugin-react": "^3.1.0", "eslint-config-airbnb": "0.0.7", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0-beta3", "react-dom": ">=0.14.0-beta3"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "ac6f6a0f5c617d250b187d5839a17cbd43c379d3", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.100-react-pre.0.tgz", "integrity": "sha512-CDEVFGAYGUqDwbYvgO+vBgq9WAVKUngbl3weq4YcCn8Rb+aYYeF5heP4+ej9bU28ijP/dWVv/lSvlbq+VhETzQ==", "signatures": [{"sig": "MEYCIQCyDb0OD6Ca2xUD/piUIGsTLXG7m9qBOzFdenaMju7thQIhAMDSx6IGfL+UhHubqhiUAAIqkS4dlYEjFLESwoHKWiE0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.1": {"name": "react-bootstrap", "version": "0.25.1", "dependencies": {"lodash": "^3.10.0", "keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.19", "react-overlays": "^0.4.4", "uncontrollable": "^3.0.0"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.1.0", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.16.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.5.1", "eslint-plugin-react": "^3.1.0", "eslint-config-airbnb": "0.0.8", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "8af0825fe765e669ee6b8c7d6f954df6f57228bf", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.1.tgz", "integrity": "sha512-Zi9XO6fupOQm3XFNM/XHuNWjvHghx7aDYTygTq8sVMn+AcHkHpvMjA8/vE+gWaQOi3WTuTiG9hSbXk1iiRY3dw==", "signatures": [{"sig": "MEUCIQCrezABEiSVmLpForMM1bYHFVLM9z2nZdcMrfp8XT85ggIgMULlQ05S2rDjDHFX6jFOkpwIv4GrgG5v57niqz1G64I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.2": {"name": "react-bootstrap", "version": "0.25.2", "dependencies": {"lodash": "^3.10.0", "keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.19", "react-overlays": "^0.4.4", "uncontrollable": "^3.0.0"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.2.x", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.16.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.5.1", "eslint-plugin-react": "^3.1.0", "eslint-config-airbnb": "0.0.8", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "f7b31928f4bf1c9acb1aa637de7bb3fcf535076b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.2.tgz", "integrity": "sha512-tO+Z2V8jRQ+Tgs+mCPdrU9olvm7M9PDWGswbADF276XyGqrnkrrG5GleEtyac87Brh2IB/LBLEe5pqbVoeIH1Q==", "signatures": [{"sig": "MEUCIQDRqS55MThXozTS9wEKwXNMn1DzrhijMeEwoQKPQfHT1QIgRFayThDHhlghl9Nmg6gUGro6HFa/ePYDORDf5gfp+LA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.25.100-react-pre.1": {"name": "react-bootstrap", "version": "0.25.100-react-pre.1", "dependencies": {"lodash": "^3.10.0", "keycode": "^2.0.0", "warning": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.19", "react-overlays": "^0.50.0-alpha4", "uncontrollable": "^3.1.1"}, "devDependencies": {"ip": "^0.3.3", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.19", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.14.0-rc1", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "1.2.x", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "history": "^1.9.0", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.0", "phantomjs": "^1.9.17", "react-dom": "^0.14.0-rc1", "babel-core": "^5.8.19", "codemirror": "^5.5.0", "css-loader": "^0.16.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^1.0.0-rc1", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^0.2.0", "karma-coverage": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^0.5.1", "eslint-plugin-react": "^3.1.0", "eslint-config-airbnb": "0.0.8", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0-rc1", "react-dom": ">=0.14.0-rc1"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "e151bc4f9850179e55a410859013cc72d069161a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.25.100-react-pre.1.tgz", "integrity": "sha512-9Aaj9igsgYTd7HEuDKHFdpuUs/lZLQNLA28m8yj6ZkGQGydxeBi2AuDJcTksMf2hE1JQNo+4IXFTzLF+aYY/iQ==", "signatures": [{"sig": "MEQCIHae3EH3Z2BAoKZeI+AtsUAkAK2Te+4sxqF0iXaVCvLnAiAjfVYXtMyMhwr8fe0LQRBMGWZ7BqmdxXQk9jMDbeYVng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.26.0": {"name": "react-bootstrap", "version": "0.26.0", "dependencies": {"keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.4.4", "uncontrollable": "^3.1.1"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "react-prop-types": "^0.3.0", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6af5806b8d1f3cb47996d9ab5aa3ee9a24d53edb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.26.0.tgz", "integrity": "sha512-NrJ4+h47iiWPM04HdIijoFdfLHTWVP0GDeCTgZI7TQJnZ17L2nYkFh0XhK03G5rClva9raLSpCw/BWx45rzJZA==", "signatures": [{"sig": "MEUCIQDjHXORq5oIIN24/vw1MVXVf5SNLl4cSV76TOb5Ug+gUAIgIapDS5UDbQ4Vi8q2r8bRpFTFI3kUrY62+Q4TB42iuw4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.26.1": {"name": "react-bootstrap", "version": "0.26.1", "dependencies": {"keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.4.4", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "react-prop-types": "^0.3.0", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "ea194cb71eb70be074af0a099465aa3fa15e6e8f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.26.1.tgz", "integrity": "sha512-F1m0p9XSxquCv6Y31Ug29GISw6yNBdsg1n9n2SGljfwt6kj9jrwQlhKIdIa63k+mxUORvU5d0bzR4ppfjMDeiw==", "signatures": [{"sig": "MEUCIQCIa+qYOlsWS92ysYy6bwQiEwjtVSK5TqnNt6mM1LLWxwIgYJpTCPWOWUARZqq/TF3uFJXoI6ZqttZy9kjWswL/+pE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.26.2": {"name": "react-bootstrap", "version": "0.26.2", "dependencies": {"keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.4.4", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "react-prop-types": "^0.3.0", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "b880d2084fce9bc552793e41fc3f1b45128097af", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.26.2.tgz", "integrity": "sha512-eEPj11y7DLpO/Sd+mcLXyo7aexQ7R8YS1qVVdCgvjSpDgjPECRcK9Yg86X9LjZgNSRDS3EYqWV5WrCn6Ho482A==", "signatures": [{"sig": "MEYCIQC7Affp+qJqa2lpEzynaG48XC6nxuDZWY469yWwoKkISwIhAOSMYyF21dbN8VUwJOvNHz5HZ1QM7UcH+qfXKKBBy08B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.26.3": {"name": "react-bootstrap", "version": "0.26.3", "dependencies": {"keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.4.4", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "react-prop-types": "^0.3.0", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": "^0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "929939137d07757dc86eb64796bd9e36cc81f387", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.26.3.tgz", "integrity": "sha512-MXz1znp5PsrIe60N+6l8Xi/MROWoakiqDfRLyANGaLL2J+8OYQ5+uLnMQar5AZosAcGjJqqHF28TiZPf5IoYbw==", "signatures": [{"sig": "MEUCIQDnx1hOZbaE8McK4mOdQtI/WaiOD16BtseZQ0lqXU4zXwIgJD4oIAu/uOT7FOlaSgZKvhVuTjPQqlKVm8NrisQYe3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.26.4": {"name": "react-bootstrap", "version": "0.26.4", "dependencies": {"keycode": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.4.4", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.13.3", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^0.13.3", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "react-prop-types": "^0.3.0", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": "^0.13"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "a5fd431f6f0c18a55ed050258e7a775814de899e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.26.4.tgz", "integrity": "sha512-ecZfVxHoTmB8j8+FKaN0EP3f8WctdUcDs89N61rxIWeELyKBo1kkvTdT9cEK8gZEx0vYB418jV5CgqISAeeldQ==", "signatures": [{"sig": "MEUCIC3ge3PFolWfxwjdEln3rjLLcyj1SaHpb6kxhmuM6iYxAiEA/q4iqlXIAJ4aidglxt2QOci6wNU9E4llTXM8Inj/Q50=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.27.0": {"name": "react-bootstrap", "version": "0.27.0", "dependencies": {"keycode": "^2.0.0", "warning": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.0", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.14.0", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "history": "^1.9.0", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "react-dom": "^0.14.0", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^1.0.0-rc1", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "4fb6779a6752cfeea5dfb8519b5ce09a51b2b971", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.27.0.tgz", "integrity": "sha512-NCWYTFhohTXh9Y346pCHu/7AGirUPNzRmECLYcATvpiUAQteHmQDxeiV1jIAdJQwEtCrCeLXRQRAiQoKQHNKxA==", "signatures": [{"sig": "MEYCIQDqmAMJAayzCcIGHxB6e+T44CoHsJReBpAhnrA7pjSR0gIhAPT4TVrg6qgYIt3DQ6vcDUiJrfdQkpa0w+xri3TQ9rNb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.27.1": {"name": "react-bootstrap", "version": "0.27.1", "dependencies": {"keycode": "^2.0.0", "warning": "^2.0.0", "classnames": "^2.1.3", "dom-helpers": "^2.2.4", "babel-runtime": "^5.8.24", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.0", "uncontrollable": "^3.1.1", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.0", "chai": "^3.2.0", "glob": "^5.0.14", "less": "^2.5.1", "babel": "^5.8.23", "karma": "~0.13.3", "mocha": "^2.2.5", "react": "^0.14.0", "sinon": "^1.15.4", "yargs": "^3.16.1", "colors": "^1.1.2", "eslint": "^1.5.1", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.2", "semver": "^5.0.1", "express": "^4.13.1", "history": "^1.9.0", "nodemon": "^1.4.0", "webpack": "^1.10.5", "es5-shim": "^4.1.10", "fs-extra": "^0.23.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.17", "react-dom": "^0.14.0", "babel-core": "^5.8.24", "codemirror": "^5.5.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.1", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.2", "karma-mocha": "~0.2.0", "less-loader": "^2.2.0", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.1", "react-router": "^1.0.0-rc1", "style-loader": "^0.12.3", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.0", "release-script": "^0.5.0", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.0.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.2.8", "transform-loader": "^0.2.2", "node-libs-browser": "^0.5.2", "webpack-dev-server": "^1.10.1", "eslint-plugin-babel": "^2.0.0", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.4.2", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.3", "karma-mocha-reporter": "^1.0.4", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.0", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.0", "react-component-metadata": "^1.3.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "096f3b894fa0e333623fdbae6e906b3112013bbe", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.27.1.tgz", "integrity": "sha512-zJfeDEYiVdTobZiG6xTlGfCE8N73yw/8wxCkTdzQJ4rFJ7ZC02escNWBIqMb2Xm+tx+wzCFS7wmYWo7pgb0AIw==", "signatures": [{"sig": "MEYCIQCgd33SfaE6Kecipo5WJO9GJDFBgB6fispAp0KbvZAtNQIhAN7WqkKCiXYa8p5Ka+gTClQFgf6o7Z+ginmUVU6r8Q13", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.27.2": {"name": "react-bootstrap", "version": "0.27.2", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.19.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.12.4", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.4", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "668e3a1219cf3d0d0f7094f2f58185a9c6f0e7b1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.27.2.tgz", "integrity": "sha512-5sKj2/ceuDqbA1dLziXcug4Gz1+f5eV5yxuSzuPf++doBHy0W8/uArdNDbYC0UyaEDEtJ00lUi810cUPyEI5Mw==", "signatures": [{"sig": "MEUCIQDxLi/WQonWyqD+8rDHK/xnM7ZdMzieYLmuwu5YARzmlgIgZfAQR21wfjq8WIrKF54deqlawRkoG/x+N/4MoqbHKSI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.27.3": {"name": "react-bootstrap", "version": "0.27.3", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.4", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5535eee1073628d91f33440cc71f7fe73f7df69d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.27.3.tgz", "integrity": "sha512-Uva3fpe4b46c0Dx+oHP48ZHVqucY49O5VfWfYBXgOT9NetSAjEdR3/qd6gFRGFQWPhxt8CqU4QFPNK2Da3KQ0Q==", "signatures": [{"sig": "MEUCIBXKtvOHyJ1oeb2UNrj+R5RZ5e41+0DhBG1sVN1D94BcAiEA2SaEkDkKvisEzYKkSnPz7ttay2s8a0iSGYjYPTmGJWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.0": {"name": "react-bootstrap", "version": "0.28.0", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.2", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.4", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "736cca4784752495137d991e20bc79b5d8347519", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.0.tgz", "integrity": "sha512-31hXxvmzEOfoi470aXcH4ypgA47Qu2tZ0XnpbBOBCfUjEKI7jMrahK+XXBimO3dyTwYFIw+5PUXI97sv9O18eQ==", "signatures": [{"sig": "MEUCIB+uECsdpw4aXE6AlEdpNw0c8pZzhKvtnz9TjvB6wpxgAiEAttmgJHczWs4HpzYHjTedPeK+7LH4KMn9wmto9SM4W3c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.1": {"name": "react-bootstrap", "version": "0.28.1", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.2", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "lodash": "^3.10.1", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "eslint-plugin-lodash": "^0.1.4", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5d34255baba8138a2d940ed6308419168ce5d620", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.1.tgz", "integrity": "sha512-IRlgJ7s0CyDXZAGnBKfol+nQWBoJHKCShaJLEJDmp7t/9TP4AOmViUz3uIcO0mGfk9hK42VZJmio5pmFEADObg==", "signatures": [{"sig": "MEUCIQDqrOf4YJBxz4w3rFioi1oE1hIEq50IVSlQVZ95xKBZEgIgbofH0hB9RZ5sTQgkKrvMcAcsdgeraiWe7yt86H3iNDI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.2": {"name": "react-bootstrap", "version": "0.28.2", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.5.2", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "react-waypoint": "^1.1.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "5ed71ca7378fddd6c780ce9ce7b3bbb4038682bd", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.2.tgz", "integrity": "sha512-gLMiU0wnhSPg+i8mnYghwzF0GOhz5+6wqqN+0vxHN0HG10nxtJccWeh8yzbI4q1IQS+B6JCJBlTuh0OExEmJkw==", "signatures": [{"sig": "MEYCIQCXbworIXa781smLD3rkQgk9oCbbIGNwA2vziOLKmvs1gIhAMFWAzS8WQ9UEMlnxA7C9GRFPq3QfRD4Qs1DyN5pyA/l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.3": {"name": "react-bootstrap", "version": "0.28.3", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.6.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "mt-changelog": "^0.6.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "react-waypoint": "^1.1.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "f06723536e2c32d309d4410b0e200c246571b83b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.3.tgz", "integrity": "sha512-BGgq83JJ3pr6mkcTudr7pfri71qYicHa7el4qfyIMYN49sx7lpF7t3bbwzLp+tIgkQvNsi1KXMOabyqpX4W6tA==", "signatures": [{"sig": "MEYCIQCxnLXjqtG4qeN3AmOMv0F9KNdeJOSCONZWv4Y3wBXgRQIhAMW/fommlvTnkcmgkwmQUtHDVKFDTaL6p+c2ZHZ6Sapb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.4": {"name": "react-bootstrap", "version": "0.28.4", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.6.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "react-waypoint": "^1.1.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "9cb131317e3d7d2debf7a9e62b9eb0f5bc05c437", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.4.tgz", "integrity": "sha512-36geFNRn+sUG7uegl7maB6gPfZz2Hc6oyfqkm+062sYvt+y0UiTkR0WrjwmKfvi3Y/JlqHvjukrofNV6BEfA4Q==", "signatures": [{"sig": "MEQCIHsC4zERt/HCpxSuOAPvq6Q9GhpwpFi3tzzLz/rWfHLQAiAaL62ALBHkT7DJmNchXiBaiPk+aQ3V1VwN9fIWUOzA+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.0-alpha.0": {"name": "react-bootstrap", "version": "0.29.0-alpha.0", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.6.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "teaspoon": "^6.1.1", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "react-waypoint": "^1.1.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "c0a705f7136ccd9468b9b4835e3667490e419d5a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.0-alpha.0.tgz", "integrity": "sha512-xXkcIpBYT5r462l4Cu7mtYv34yRYdAcca20kGk1a1imiG9Qc986Z2TSU3s+OE62ncd5/GVLhnmoV67Ea0OwLNQ==", "signatures": [{"sig": "MEYCIQCxut85eidnA2e307hsk6d5SsnVRtBYsAfvZxaCwwb6GwIhANpBKw5re6boBCe6rIJwIe/PYPJ733zuF64BjM74teGA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.28.5": {"name": "react-bootstrap", "version": "0.28.5", "dependencies": {"keycode": "^2.1.0", "warning": "^2.1.0", "invariant": "^2.1.2", "classnames": "^2.1.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.25", "lodash-compat": "^3.10.1", "react-overlays": "^0.6.0", "uncontrollable": "^3.1.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.0.1", "brfs": "^1.4.1", "chai": "^3.3.0", "glob": "^5.0.15", "less": "^2.5.3", "babel": "^5.8.23", "karma": "~0.13.10", "lolex": "^1.4.0", "mocha": "^2.3.3", "react": "^0.14.0", "sinon": "^1.17.1", "yargs": "^3.27.0", "colors": "^1.1.2", "eslint": "^1.6.0", "marked": "^0.3.5", "rimraf": "^2.4.3", "semver": "^5.0.3", "express": "^4.13.3", "history": "^1.12.3", "nodemon": "^1.7.1", "webpack": "^1.12.2", "es5-shim": "^4.1.14", "fs-extra": "^0.24.0", "bootstrap": "^3.3.5", "karma-cli": "0.1.1", "phantomjs": "^1.9.18", "react-dom": "^0.14.0", "babel-core": "^5.8.25", "codemirror": "^5.7.0", "css-loader": "^0.21.0", "fs-promise": "^0.3.1", "http-proxy": "^1.11.2", "portfinder": "^0.4.0", "sinon-chai": "^2.8.0", "file-loader": "^0.8.4", "json-loader": "^0.5.3", "karma-mocha": "~0.2.0", "less-loader": "^2.2.1", "babel-eslint": "^4.1.3", "babel-loader": "^5.3.2", "react-router": "^1.0.0-rc3", "style-loader": "^0.13.0", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.2", "react-waypoint": "^1.1.2", "release-script": "^0.5.3", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.1.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^0.5.3", "webpack-dev-server": "^1.12.0", "eslint-plugin-babel": "^2.1.1", "eslint-plugin-mocha": "^1.0.0", "eslint-plugin-react": "^3.5.1", "eslint-config-airbnb": "^0.1.0", "karma-mocha-reporter": "^1.1.1", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "~0.2.1", "karma-firefox-launcher": "~0.1.6", "karma-sourcemap-loader": "^0.3.5", "karma-phantomjs-launcher": "^0.2.1", "react-component-metadata": "^1.3.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^0.8.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "393ab59ad66071a6a7bae0fda7518cdd228f2baf", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.28.5.tgz", "integrity": "sha512-PmgsKZX6eWsXp73UwPRcTlaUrUe0xAIuptkvblGr2sLIxE5S7YJQbwIne0kgArbdnmhlai5i7LlzbsH1lyC9KQ==", "signatures": [{"sig": "MEYCIQDCTsT3YUH4UgOtqW1JQ7+uO9z1TP08n3BNV9Gv+sI83AIhAKRSBMOC2Ge87ZZP408+fdbXWkJ55W/npA3kQDKY9x0Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.0-alpha.1": {"name": "react-bootstrap", "version": "0.29.0-alpha.1", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.2", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^0.14.8", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.2.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^0.14.8", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "6f8e810f570d20c149f554e13db600cf6d4fc6d1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.0-alpha.1.tgz", "integrity": "sha512-j1zfAvSAjIujUPRKahmQKa52QCsAPRuZqORvqffJ4P+scuzzFIoPcSEWIKnDio1SxYyou4+ci9EXFNhYdgcfcg==", "signatures": [{"sig": "MEUCIEQdYE1zHYDRb1oHVTrqZoYOL8J5IoV1EKv0BZxr6e7qAiEA7SjW/gk+jPJBjuE1VSl3mSkibnXjDVm/+2FtBDW0UUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.0-alpha.2": {"name": "react-bootstrap", "version": "0.29.0-alpha.2", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.2", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^0.14.8", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.2.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^0.14.8", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "7bb872801baf1c2f16665446ff62eb7d69956ab7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.0-alpha.2.tgz", "integrity": "sha512-kZ04Y0al/X+7j8krohUz68ZzFFkwHv76+Hym9GhstEnnu4vzX3NebXwF2k/Yn0avSSjIt+IqQybBPadv7J5byA==", "signatures": [{"sig": "MEUCIQDNbDA0uhuEyMT+/VWPwpiH6cFrrBRbDvwtIx3qIMsAXQIgKND8rEzvlEzARauRRDM9Mu+VVZS3JZDaSuoKuPdiRkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.0": {"name": "react-bootstrap", "version": "0.29.0", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "da6e26bb7073c3a1f9e2eb27c3b58fa8226434fa", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.0.tgz", "integrity": "sha512-fbJPO/y4T2Ol8ncW6yc5AZxPyqAfwwLxU9L1Vg3KFBMbk020RUqqOcUJA0PSXSROiUqBG2k2vsH1EMZvfg9sOg==", "signatures": [{"sig": "MEUCIBwc0bgo47aMJjaIkC2jtQzl8kce3L7g7pOY+1hSP9bJAiEA1gXZZCbq0j/QxXV9shCywhBVAlhcloJU1QDeoDijg0E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.1": {"name": "react-bootstrap", "version": "0.29.1", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "df10fbe5484164cb908b9521aae13bda423fc824", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.1.tgz", "integrity": "sha512-O+KvLGgQnNiaD/ZdtkPLka44F0wg4ULnjWPwTvr7SdYR7rg16f2L8ZqmWR5udtIwOI/QkrJb3qNbd9Gd4sIZxw==", "signatures": [{"sig": "MEYCIQCjhps2aWSbgmVZdZf8aylXDMuHlprK027mWEH8QjIgFwIhAIPMZyW/QjehlAkfKSEbLew9HCuDYfvv9szMjyQmhQF3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.2": {"name": "react-bootstrap", "version": "0.29.2", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "e2f572359a10611d915f869c4d2a958e74126098", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.2.tgz", "integrity": "sha512-e0JPNBmyEAhHQ5sh4ga6LYRcuV5eyoWmXUeRaA6ivdnzuAFG4DcLAkhGJvly6jVTh0nqueEjOkcpqEEllAhz9w==", "signatures": [{"sig": "MEYCIQCcxYuszy7WHhperjBZ4+PKT+FtuAN1HJOULwSSAC4ckgIhAM6709arfee3iUg8Cafdh2GgB+kAXXokjfwquxPk5io+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.3": {"name": "react-bootstrap", "version": "0.29.3", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "58221871c17589aeebc313913904a8892a0da284", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.3.tgz", "integrity": "sha512-OWdjZZjGQfp2G41Dz5nsTYyqk5OlMcmyhTA++aoLqTDpAHhMCmqZJl1SSXLLpyjsYOPJdv8vy4H9Jjvtqy0G9A==", "signatures": [{"sig": "MEQCIDAxHfw4LCTc3WruNqmMoYzip53Z9ye7KF5H447oQJytAiArlBQTAHYUh1inUBHoKK8smWD1+uXoqidHBc2vFP0ctQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.4": {"name": "react-bootstrap", "version": "0.29.4", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "fcbff8a75b1f1f6feb0b7001f0cca9c055348800", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.4.tgz", "integrity": "sha512-0lMwPFS53oOPpVsTmUA7fngj1PYNvxgegWoGwFYTiqIw7cOsV1py+F0u5wjPNN1HQIj/3LgcSwdMR4TFQCU5Xw==", "signatures": [{"sig": "MEUCIA8SP0VoOa91yhp9aBRi6H+h3gImflNKMb8AOG2GojF8AiEAzdkwcUDz/v4GfJQS/MpoojjhYTimw6Slsz8ciK4cWjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.29.5": {"name": "react-bootstrap", "version": "0.29.5", "dependencies": {"keycode": "^2.1.1", "warning": "^2.1.0", "invariant": "^2.2.1", "classnames": "^2.2.3", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "lodash-compat": "^3.10.2", "react-overlays": "^0.6.3", "uncontrollable": "^3.2.3", "react-prop-types": "^0.3.0"}, "devDependencies": {"ip": "^1.1.2", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.3", "less": "^2.6.1", "babel": "^5.8.38", "karma": "^0.13.22", "lolex": "^1.4.0", "mocha": "^2.4.5", "react": "^15.0.0", "sinon": "^1.17.3", "yargs": "^4.3.2", "colors": "^1.1.2", "eslint": "^1.10.3", "marked": "^0.3.5", "rimraf": "^2.5.2", "semver": "^5.1.0", "express": "^4.13.4", "history": "^1.17.0", "nodemon": "^1.9.1", "webpack": "^1.12.14", "es5-shim": "^4.5.7", "fs-extra": "^0.26.7", "teaspoon": "^6.3.0", "bootstrap": "^3.3.6", "karma-cli": "^0.1.2", "react-dom": "^15.0.0", "babel-core": "^5.8.38", "codemirror": "^5.13.2", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.13.2", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.8.5", "json-loader": "^0.5.4", "karma-mocha": "^0.2.2", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "isparta-loader": "^1.0.0", "karma-coverage": "^0.5.5", "react-waypoint": "^1.3.1", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.0", "output-file-sync": "^1.1.1", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "phantomjs-prebuilt": "^2.1.7", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.1.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.0", "child-process-promise": "^1.1.0", "karma-chrome-launcher": "^0.2.3", "karma-firefox-launcher": "^0.1.7", "karma-sourcemap-loader": "^0.3.7", "karma-phantomjs-launcher": "^1.0.0", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "directories": {"lib": "lib/"}, "dist": {"shasum": "0c6f7a7d06834b0db85a18ccddc04b129c5f2058", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.29.5.tgz", "integrity": "sha512-JUjx2G5lkqIrNeBmjzvxe+MX74L+HarnLzeyRl8Z3VCRVzt7KKQt9poPB7Dj+wWN+uGMtTkAvPwPFqkMuGMU5A==", "signatures": [{"sig": "MEYCIQCxGHpqFjtDuKTZL6bA6ygdEgY9DjF5CpE+9rHRLcfnKQIhAPsUw/1mYsgTNDcmCngjs7rRjJZdP2yi10wjGOHMVGRA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.0-rc.1": {"name": "react-bootstrap", "version": "0.30.0-rc.1", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "react-overlays": "^0.6.5", "uncontrollable": "^4.0.0", "react-prop-types": "^0.3.2"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.5", "less": "^2.7.1", "babel": "^5.8.38", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.2.1", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "express": "^4.14.0", "history": "^1.17.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "bootstrap": "^3.3.6", "karma-cli": "^1.0.1", "react-dom": "^15.2.1", "babel-core": "^5.8.38", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "webpack-merge": "^0.14.0", "isparta-loader": "^1.0.0", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "37dd8bebcb97224743c6cc3efd24f8a247827b3b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.0-rc.1.tgz", "integrity": "sha512-hNLIfTLeWScrDMUSQCj1I0AooioCJyKrJEquKuE5frDawMMkJrh5jTihBeNcoIr6fJ4LKuZT5A/uxiHwdo9jBA==", "signatures": [{"sig": "MEUCIEkqnc2WVOF4uopH33bWB93n7KiBDGUInMY6k17fFT3iAiEAo5ISJjImjx4m+RwZtiEyPqV+KkdQuZkVvLrFYyFs9hs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.0-rc.2": {"name": "react-bootstrap", "version": "0.30.0-rc.2", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "react-overlays": "^0.6.5", "uncontrollable": "^4.0.0", "react-prop-types": "^0.3.2"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.5", "less": "^2.7.1", "babel": "^5.8.38", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.2.1", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "express": "^4.14.0", "history": "^1.17.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "bootstrap": "^3.3.6", "karma-cli": "^1.0.1", "react-dom": "^15.2.1", "babel-core": "^5.8.38", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "webpack-merge": "^0.14.0", "isparta-loader": "^1.0.0", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "24e2c4a4b6d8b6607a1a00ec403297de13db55f8", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.0-rc.2.tgz", "integrity": "sha512-HVO70kaJyTVFZnSuF+ejzdvUHBaykOijitBcCJla1cfQ2U+2Q8PiqlZnJSuJzlx4KBLtqa/kzs7PO8ApXGxKBw==", "signatures": [{"sig": "MEQCICQJuLA+NSUKrEIUaSDSc3SsRTyx+CF6+qVFC87+5LZTAiA0KPjP45Bm7teQ/CG8SyF2R7vNZaCG6DXaOeCoJCNrgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.0": {"name": "react-bootstrap", "version": "0.30.0", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "react-overlays": "^0.6.5", "uncontrollable": "^4.0.0", "react-prop-types": "^0.3.2"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.5", "less": "^2.7.1", "babel": "^5.8.38", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.2.1", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "express": "^4.14.0", "history": "^1.17.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "bootstrap": "^3.3.6", "karma-cli": "^1.0.1", "react-dom": "^15.2.1", "babel-core": "^5.8.38", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "webpack-merge": "^0.14.0", "isparta-loader": "^1.0.0", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "100606ada1473c92a6b35fe41e160531c90a64e4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.0.tgz", "integrity": "sha512-m38hjs5fahX+PEqlDG7k6yQpd8gRmuPDIidFMZn0LWu/iXxGQwQ8gAUeiaNCVBzilEUJkXYx0RJ24PKZ6azBqQ==", "signatures": [{"sig": "MEUCIQDTEAHLKPdhwdzimul8Kb90y1/c0+LuL5lRX55zHvNR3wIgVGjeJUGI3wtUf7CRLzqD13hvIMecHPnXitlnok5xmGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.1": {"name": "react-bootstrap", "version": "0.30.1", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^5.8.38", "react-overlays": "^0.6.6", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.0.5", "less": "^2.7.1", "babel": "^5.8.38", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "express": "^4.14.0", "history": "^1.17.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "bootstrap": "^3.3.6", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "babel-core": "^5.8.38", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^4.1.8", "babel-loader": "^5.4.0", "react-router": "^1.0.3", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "webpack-merge": "^0.14.0", "isparta-loader": "^1.0.0", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "webpack-dev-server": "^1.14.1", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^2.1.1", "babel-plugin-dev-expression": "^0.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "b264310e6f4ceb52d99dfd1fe26b05720b7673a5", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.1.tgz", "integrity": "sha512-lwsrA8zDA58C3IrCakNOlWy06IRWOuumZ9ATmfo0RRgMk0BOVA+9dxMAgLZITlekLQjE0RHM1NRgx8FNA90Dqg==", "signatures": [{"sig": "MEUCIA8UxY8BFqBxtjSQhdyio6aYpb7KJ5jVsxSMg8NwmsU3AiEAyNFQpXmQRof5YipvXHx1U3/kMzDU/IkhKrJC1YKLUuU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.2": {"name": "react-bootstrap", "version": "0.30.2", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.6", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.0", "babel-loader": "^6.2.4", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "webpack-merge": "^0.14.0", "isparta-loader": "^2.0.0", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "karma-coveralls": "^1.1.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.9.0", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.5.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.0.0", "babel-preset-es2015-loose": "^7.0.0", "babel-plugin-dev-expression": "^0.2.1", "babel-preset-es2015-webpack": "^6.4.2", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-es2015-webpack-loose": "^6.3.14", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "33efb3312243705385ff1ea5e7d81dc90a913496", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.2.tgz", "integrity": "sha512-loEMcUmo51hcQuEc41x6eHrCuEnoExKWrczniZlnkZeD9I7uGN1z/Gr/c3//0QZEalSHXAaHrYypK2e9P7vc7w==", "signatures": [{"sig": "MEUCIDADqtu92820l4Sxu5AT/6+GCoIb20OxH3Vv6H/RXLKmAiEAkahJc6urYJW878JFV2Sq3PHXJKHNhuQqvgkJ+zwUN1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.3": {"name": "react-bootstrap", "version": "0.30.3", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.6", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.5", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.0.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "918ec4f4e23aa8db087f8841a775516a0e22c819", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.3.tgz", "integrity": "sha512-z1qn+Rmkpw1SwGxzLK+UNWH5EcPehrPoeV6KvEgMwmx02XcRmlR4j/O2u405PdUjqwMB6NHJxGPxH1UxrRzNoA==", "signatures": [{"sig": "MEYCIQDVm8nzMtMIxL8zIRDj4/Wz/MEzeiuz4t3KgLFFABdLRgIhAMwWJ9I+E620bBXlS09BQ+l3nY60e0dUpuB9m6T53baK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.4": {"name": "react-bootstrap", "version": "0.30.4", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.6", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.0.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "48c53510b72cda7b442199e1614af993166e792f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.4.tgz", "integrity": "sha512-ViBq89bGJK7qpnwWYpPqld08EbnyffEFbqLatjVfpfTOaEoThXJqICxI+iLdrM522IHJkFuyRpAOrN2VzhFjZw==", "signatures": [{"sig": "MEYCIQDznrUELR4kiK7KM9Vs93nLWSSUtEMxUMAAnEMGMQd/HgIhAIAm/bN7qqdSFk5U4Y/7Y7LlAKKlcGbTd30bollxGcWZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.5": {"name": "react-bootstrap", "version": "0.30.5", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.10", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.0.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "22d9bdab13b9eec04918206602709e1c8a0d76e2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.5.tgz", "integrity": "sha512-ogdMdA20RzIGFNnENE236PzAzN/LGvhwEp4jGc11KphPGNaDbyKs1AHu/52TXRqbRKEIKUkfOxZeKz9aC9wPUA==", "signatures": [{"sig": "MEYCIQD0cfQi59/+bDlXlrojYrZUd+OlVNW26fJuPnrNXHSqZQIhALHrmqf6+afGLEqXWyKPmuRN2sOOfsQADTx5KY21D9om", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.6": {"name": "react-bootstrap", "version": "0.30.6", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.10", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.3.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.1", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.3.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.0.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "288662a245f9dbb79f7740ee595e4ec931d6a4a9", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.6.tgz", "integrity": "sha512-nCr05AXkA0QK43I3QAqWq1NWxJKHHqj7kI8jQ6U7+8MEuI8t7IcEVzO5IrK+NGd7aKL0Cb0y0CKFjHYtqTMAtw==", "signatures": [{"sig": "MEUCIQDw5q3fv5XRwrx9bkywRe28jHEs0GBVN/xQusSpx6LQjgIgRCMBl6J9FBjsDywE7xTeBodA7Yn7wYHZ+k9x+qJ5mJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.7": {"name": "react-bootstrap", "version": "0.30.7", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^2.4.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.10", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.4.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.4.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.12.0", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.4.0", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "39da80088693ecb71e8e63b5bdc313571fd993d1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.7.tgz", "integrity": "sha512-Mqx0bZifdP1DXWBqhdB8LeuuwjGope+WiUfIex04FAE0Uz9mcdeXlz5OnKeR57M736D9lp+jNaVlmRSStKdhEw==", "signatures": [{"sig": "MEYCIQD3MJw2QMTQK+kP03eAIgqFF4E7UO1iH78qmmVNF89XwAIhAIpZffKDx78JIR+KeVfVU6g6Nbl3awx1ryNa6yO/aWIy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.8": {"name": "react-bootstrap", "version": "0.30.8", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.12", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.4.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.4.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.4.0", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "4ceca8e138ce2351228c4a58d59db00c003ca9c0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.8.tgz", "integrity": "sha512-riFkMtwy2r3DRYyXoVo/zoVNodQ7vVaou/q+/5p2bY74Wgj/KP9107jpp2dQKdmnFJuKF3Q/dDgicjg/1TypVw==", "signatures": [{"sig": "MEUCIEEYRCmPgaYXZoWql3mNCWs/a/qswgAWQBGXc/c/dpKSAiEA2DuTo9ois2dlabw0AYchUd0DwRUfxGDXl/awLAM66QU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.9": {"name": "react-bootstrap", "version": "0.30.9", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.12", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.4.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.4.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "prop-types": "^15.5.6", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.4.0", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "acdb20119fac189fd6546257cb741f463d4cd965", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.9.tgz", "integrity": "sha512-0m7XaKAwITJ8VHP6WULU5Wc8271cNwhYGOJ9+z3W7XIXFy+/s9Dc4kgxDIx1FOOCROVTgLTJPaRYuP+Ovp9OIA==", "signatures": [{"sig": "MEQCIDu2QTAWKFD9f80ClXnnrFb9gGZoOKWQXdYxQ+xpGJUbAiBqxB7lugjlu+BekLGuSHatEmZ0JRisMDUtx7q3t1TJOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.30.10": {"name": "react-bootstrap", "version": "0.30.10", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.6", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.6.12", "uncontrollable": "^4.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.4.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.4.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.4.0", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "dist": {"shasum": "dbba6909595f2af4d91937db0f96ec8c2df2d1a8", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.30.10.tgz", "integrity": "sha512-gcaLHpHc8WGx/0+HQ34GdI8d+bfPQEhU0rf86kmvQWk64tONsIxaEdcTgWeTgmkZGYy4YAfWcf1rsLG+j4UZig==", "signatures": [{"sig": "MEYCIQDVW8NJC+ckkezyO0NagHJsR1DF09b3jIvcizlAK46GeAIhAM0+AkA7VRMK60WjtojYA1oq05CLuDwt28+dP4p0Tsy8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.0": {"name": "react-bootstrap", "version": "0.31.0", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.6", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.0", "uncontrollable": "^4.1.0", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.4.0", "sinon": "^1.17.4", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.4.0", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-addons-test-utils": "^15.4.0", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "bbca804c0404d9c640102b2b656ae4cd5bea35c8", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.0.tgz", "integrity": "sha512-UfR4KaVj4ddTSQzwBzO6mDVq2JQ6HbJ+kEBiAQSqUn/SXIYeiETJlT7b0Qfx8lGTDEMuxuxEq1yYkcFtdTsLDw==", "signatures": [{"sig": "MEQCIQDghHQlE1CzHYK+rd8IlQCniY0E06EJOxSwhTGJ/L1H3QIfcPlr3UovNfV3aNJ4qBY1NZewGgSvgp6iKo1DyH9EhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.1": {"name": "react-bootstrap", "version": "0.31.1", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.0", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "679c9f73ae77ff207867d536496207291f3a3ed7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.1.tgz", "integrity": "sha512-hV1684IGmYok4jMT9agTFXBMJrGVYzixiMq6ad9yDgHR4Pj3uH7EBVg+Teo2Qz0VGtOFrPCbKUjdJ0kpCPMPBQ==", "signatures": [{"sig": "MEQCIHS5ZcYoypmMjna9p9tFrxkLsvV7dkRXDIsmBbPAzDXlAiBwR8ja+cuZg7hc3pcnQoAJw5yB0sqR4vo7e8c+5AftiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.2": {"name": "react-bootstrap", "version": "0.31.2", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.0", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "f59184676ecedfc4c572d29ffdd6f9126ea8fe6a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.2.tgz", "integrity": "sha512-6rEK6/Z0UStWkwROhNZ2RW+88AJ83d5i5nGJYoW88JoiAhkOd3MMKaJ4AQZKu+nZ3RWSNzHIKozuBb9N+ewOeA==", "signatures": [{"sig": "MEQCIHnBqCfZl17QwbwaKBvNTi1xvCr5QrkN9T76Yw3bQrvXAiBvRDH6rLleKfaTP/APa7o7s+85QAvsGwSsJuahUP2yGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.3": {"name": "react-bootstrap", "version": "0.31.3", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.0", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "yargs": "^4.7.1", "colors": "^1.1.2", "eslint": "^1.10.3", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^1.0.1", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "teaspoon": "^6.4.2", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.4", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^6.1.2", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "babel-preset-es2015": "^6.13.2", "eslint-plugin-babel": "^3.3.0", "eslint-plugin-mocha": "^1.1.0", "eslint-plugin-react": "^3.16.1", "babel-preset-stage-1": "^6.13.0", "eslint-config-airbnb": "^0.1.1", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^1.0.3", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-es3-property-literals": "^6.8.0", "babel-plugin-transform-es3-member-expression-literals": "^6.8.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "db2b7d45b00b5dac1ab8b6de3dd97feb3091b849", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.3.tgz", "integrity": "sha512-n6OcS2IsxLcv1qmma14zKYBJqCsIS4ccwEtl09PLjbYTkKBLwkBmD7hB9+WQUgGfD21TP2UoEI87kIxjVwkP5Q==", "signatures": [{"sig": "MEUCIQDCI3ZOuossLd/OLBcGBolkvN+hozITMKm5xkuFZr8UlAIgZDtkdZ7CyLLAQ1SZd+mALIlAm7DWw24PNSCmjgZL66s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.4": {"name": "react-bootstrap", "version": "0.31.4", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.4", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "yargs": "^4.7.1", "colors": "^1.1.2", "enzyme": "^3.1.0", "eslint": "^4.8.0", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^2.2.0", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.7", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^8.0.1", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-preset-env": "^1.6.0", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "eslint-plugin-mocha": "^4.11.0", "eslint-plugin-react": "^7.4.0", "react-test-renderer": "^15.6.2", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^5.1.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-15": "^1.0.1", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "14fdec042df9b6a1a8a1ed1ceb2b57483a473b3e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.4.tgz", "integrity": "sha512-fs8MmA1psT4P61iAZS5c5OCBkS65rtCptRw7gnjmYj/3vdM6a37hvFcJGmKKFvDhinKmVnjIP3iDVAyR7Aoh3Q==", "signatures": [{"sig": "MEQCIGGqR5nj7cKcCECrJ+vR+54GG4pwCXP3ovzqiyW9mKRUAiB6I+4xpH3hfCibNf6C0i1s1BglJGXn9uLpRbP7VuixUg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.31.5": {"name": "react-bootstrap", "version": "0.31.5", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.7.4", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1"}, "devDependencies": {"ip": "^1.1.3", "brfs": "^1.4.3", "chai": "^3.5.0", "glob": "^7.1.0", "less": "^2.7.1", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "yargs": "^4.7.1", "colors": "^1.1.2", "enzyme": "^3.1.0", "eslint": "^4.8.0", "lodash": "^4.13.1", "marked": "^0.3.6", "rimraf": "^2.5.3", "semver": "^5.2.0", "codecov": "^2.2.0", "express": "^4.14.0", "nodemon": "^1.9.2", "webpack": "^1.13.1", "fs-extra": "^0.30.0", "babel-cli": "^6.11.4", "bootstrap": "^3.3.6", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "codemirror": "^5.16.0", "css-loader": "^0.23.1", "fs-promise": "^0.5.0", "http-proxy": "^1.14.0", "portfinder": "^1.0.3", "sinon-chai": "^2.8.0", "file-loader": "^0.9.0", "json-loader": "^0.5.7", "karma-mocha": "^1.1.1", "less-loader": "^2.2.3", "babel-eslint": "^8.0.1", "babel-loader": "^6.2.5", "react-router": "^2.6.1", "style-loader": "^0.13.1", "karma-webpack": "^1.7.0", "babel-register": "^6.11.6", "karma-coverage": "^1.1.0", "react-waypoint": "^3.0.0", "release-script": "^1.0.2", "babel-preset-env": "^1.6.0", "babel-standalone": "^6.21.1", "karma-sinon-chai": "^1.2.2", "output-file-sync": "^1.1.2", "react-hot-loader": "^1.3.0", "transform-loader": "^0.2.3", "node-libs-browser": "^1.0.0", "babel-preset-react": "^6.11.1", "webpack-dev-server": "^1.14.1", "eslint-plugin-mocha": "^4.11.0", "eslint-plugin-react": "^7.4.0", "react-test-renderer": "^15.6.2", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "child-process-promise": "^2.0.3", "karma-chrome-launcher": "^1.0.1", "eslint-plugin-jsx-a11y": "^5.1.1", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-15": "^1.0.1", "react-component-metadata": "^3.1.0", "babel-plugin-dev-expression": "^0.2.1", "extract-text-webpack-plugin": "^1.0.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "57040fa8b1274e1e074803c21a1b895fdabea05a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.31.5.tgz", "integrity": "sha512-xgDihgX4QvYHmHzL87faDBMDnGfYyqcrqV0TEbWY+JizePOG1vfb8M3xJN+6MJ3kUYqDtQSZ7v/Q6Y5YDrkMdA==", "signatures": [{"sig": "MEQCIGnGniPHlL3OR4/m3VtEzpHVoollwrPd0LLxtnZMloXeAiAh8UQR1keQw960vzzzbE/H7IXtMqpSkhPD30sIZRVOvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.32.0": {"name": "react-bootstrap", "version": "0.32.0", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.8.0", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "react-transition-group": "^2.0.0"}, "devDependencies": {"chai": "^3.5.0", "husky": "^0.14.3", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "colors": "^1.1.2", "enzyme": "^3.1.0", "eslint": "^4.8.0", "lodash": "^4.13.1", "codecov": "^2.2.0", "webpack": "^3.6.0", "fs-extra": "^4.0.2", "prettier": "^1.9.2", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "sinon-chai": "^2.8.0", "karma-mocha": "^1.1.1", "lint-staged": "^6.0.0", "babel-eslint": "^7.2.3", "babel-loader": "^7.1.2", "karma-webpack": "^2.0.4", "webpack-atoms": "^4.1.2", "babel-register": "^6.26.0", "karma-coverage": "^1.1.0", "release-script": "^1.0.2", "babel-preset-env": "^1.6.0", "karma-sinon-chai": "^1.2.2", "babel-preset-react": "^6.11.1", "create-react-class": "^15.5.3", "eslint-plugin-mocha": "^4.11.0", "eslint-plugin-react": "^7.5.1", "react-test-renderer": "^15.6.2", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "karma-chrome-launcher": "^1.0.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.4.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-15": "^1.0.1", "babel-plugin-dev-expression": "^0.2.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "7f656be7b0f88e4bdda819135956bad10d3f167e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.32.0.tgz", "integrity": "sha512-AbWq1qjDpuYZzXSF0N6oXwwlWa1MWNEhqH/d5I51Q4AN/zn8oTBqx7HgIICR0Jk6xGy76LzJQGr+31VP3ehfVw==", "signatures": [{"sig": "MEYCIQCnSVeYjVJ5jG8PkC9jEk06y9nPMnrOjXmjKN5q0pLrRwIhAJTzG82zm8xWNc1p/S6Gb26OBhJhORDLfbMqFkG5+JHT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.32.1": {"name": "react-bootstrap", "version": "0.32.1", "dependencies": {"keycode": "^2.1.2", "warning": "^3.0.0", "invariant": "^2.2.1", "classnames": "^2.2.5", "prop-types": "^15.5.10", "dom-helpers": "^3.2.0", "babel-runtime": "^6.11.6", "react-overlays": "^0.8.0", "uncontrollable": "^4.1.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "react-transition-group": "^2.0.0"}, "devDependencies": {"chai": "^3.5.0", "husky": "^0.14.3", "karma": "^1.1.1", "mocha": "^2.5.3", "react": "^15.5.4", "sinon": "^2.3.2", "colors": "^1.1.2", "enzyme": "^3.1.0", "eslint": "^4.8.0", "lodash": "^4.13.1", "codecov": "^2.2.0", "webpack": "^3.6.0", "fs-extra": "^4.0.2", "prettier": "^1.9.2", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "babel-core": "^6.13.2", "sinon-chai": "^2.8.0", "karma-mocha": "^1.1.1", "lint-staged": "^6.0.0", "babel-eslint": "^7.2.3", "babel-loader": "^7.1.2", "karma-webpack": "^2.0.4", "webpack-atoms": "^4.1.2", "babel-register": "^6.26.0", "karma-coverage": "^1.1.0", "release-script": "^1.0.2", "babel-preset-env": "^1.6.0", "karma-sinon-chai": "^1.2.2", "babel-preset-react": "^6.11.1", "create-react-class": "^15.5.3", "eslint-plugin-mocha": "^4.11.0", "eslint-plugin-react": "^7.5.1", "react-test-renderer": "^15.6.2", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "karma-chrome-launcher": "^1.0.1", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.4.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-15": "^1.0.1", "babel-plugin-dev-expression": "^0.2.1", "babel-plugin-transform-runtime": "^6.12.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-class-properties": "^6.24.1", "babel-plugin-transform-export-extensions": "^6.22.0", "babel-plugin-transform-object-rest-spread": "^6.26.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "60624c1b48a39d773ef6cce6421a4f33ecc166bb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.32.1.tgz", "integrity": "sha512-RbfzKUbsukWsToWqGHfCCyMFq9QQI0TznutdyxyJw6dih2NvIne25Mrssg8LZsprqtPpyQi8bN0L0Fx3fUsL8Q==", "signatures": [{"sig": "MEQCICeQph5q20TCQp/VC8kVrpHxpNA88I2PyrR991oqsgwUAiB9p9LeQmwBCkKTFutoPDS27PmZyiesGje9WnhOHvS5GQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.32.2": {"name": "react-bootstrap", "version": "0.32.2", "dependencies": {"keycode": "^2.2.0", "warning": "^3.0.0", "invariant": "^2.2.4", "classnames": "^2.2.5", "prop-types": "^15.6.1", "dom-helpers": "^3.2.0", "babel-loader": "^8.0.0-beta.2", "@babel/runtime": "^7.0.0-beta.42", "react-overlays": "^0.8.0", "uncontrollable": "^5.0.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "react-transition-group": "^2.0.0", "enzyme-adapter-react-15": "^1.0.1"}, "devDependencies": {"chai": "^3.5.0", "chalk": "^2.3.2", "execa": "^0.10.0", "husky": "^0.14.3", "karma": "^2.0.2", "mocha": "^5.2.0", "react": "^15.5.4", "sinon": "^2.3.2", "colors": "^1.2.1", "enzyme": "^3.1.0", "eslint": "^4.19.1", "lodash": "^4.17.10", "codecov": "^2.2.0", "webpack": "^4.4.1", "fs-extra": "^6.0.1", "prettier": "^1.12.1", "cross-env": "^2.0.0", "karma-cli": "^1.0.1", "react-dom": "^15.5.4", "@babel/cli": "^7.0.0-beta.42", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^2.8.0", "@babel/core": "^7.0.0-beta.42", "karma-mocha": "^1.1.1", "lint-staged": "^7.1.1", "babel-eslint": "^8.0.0", "karma-webpack": "^3.0.0", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0-beta.42", "karma-sinon-chai": "^1.3.4", "@babel/preset-env": "^7.0.0-beta.42", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0-beta.42", "eslint-plugin-mocha": "^5.0.0", "eslint-plugin-react": "^7.8.2", "react-test-renderer": "^15.6.2", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.12.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "babel-plugin-dev-expression": "^0.2.1", "@babel/plugin-transform-runtime": "^7.0.0-beta.42", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0-beta.42", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.42", "@babel/plugin-proposal-export-default-from": "^7.0.0-beta.42", "@babel/plugin-proposal-export-namespace-from": "^7.0.0-beta.42"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "2420ab933c375968033d465e5f4523aa7bb7ceac", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.32.2.tgz", "fileCount": 216, "integrity": "sha512-Jvi6bCOOfqBZ9RKafPnL1uUQVW54eJ4ymppKo9fNJBE5+Ncp821e20HQsvJ1xv/bfMQFbG9UtXzoS5v3R4eRvA==", "signatures": [{"sig": "MEYCIQCaSH3QC1kR+lE/FMHLNvu0vBsR8fMaZJjyplxxISkG6QIhALeLLuxcONJ1+tw/1enzT5gsLyVzU5+2JoUt8BER7ke+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1466030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbd0ibCRA9TVsSAnZWagAAiE0P/jxPffQrzjgeoKlLTu4Y\nUSi8Zj7i10uQB73P9p4r/0LAWLDvJU3/HAaM+vOccKipY4Qs2GF/H09qWamT\nm9fisPJgeFH3VrA7ZMb7nB2L/085isoIZol/Us02Pdfl2deii7t200cQWrND\nKBKigPaO6Fw1QsQ3HJV0j7/HTkthtsOR2KEHLqUzX6yzwj4sKqjgCd/earWQ\noVAmZX6xWUJYCxy54l6kBGj9AkOpigH4tNeNl/8gE3bNAvdrGM+g0/JLJGrj\nBpVrx6xFIcXrJ9kJmGy0TAHGLpwK2P5dsK03W9tUHLTNATHKTempYl8wpDkQ\nAPNcZCaovfmizbE/Zm2agEJRMK2+JSJB75DjKlaTzZNlIBLyNXUyq14bSqpY\nvzy28xYesY+OOEvLjqj2LJbp85860jMDhWahNffVxI6DJPcXj4gAughzq1E7\nuF9jIII/Pstk3lYTiObn/rPyVJuWq0YpM+5wJznhq2/UPbR8GXUJUpPyMCSB\nsZcS8sES4EHFgRZ4uhveGMQU8D3pZ3/KoST701cQcpr70XIJ8eAG0K7GXDL8\nScyGS/dE1hmE9cIwaxWp0EsxHHSqCfQS9U0w1TZqnCfwHIIDIPuWcaBinC7M\nMQF8b0tgR9KKB1TtWKIOnD1vCxN+vaquas4uHEeW99SKzinHV8qBM/TIeTzb\nQQ5S\r\n=h4rL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.32.3": {"name": "react-bootstrap", "version": "0.32.3", "dependencies": {"keycode": "^2.2.0", "warning": "^3.0.0", "invariant": "^2.2.4", "classnames": "^2.2.5", "prop-types": "^15.6.1", "dom-helpers": "^3.2.0", "@babel/runtime": "7.0.0-beta.42", "react-overlays": "^0.8.0", "uncontrollable": "^5.0.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "react-transition-group": "^2.0.0", "enzyme-adapter-react-16": "^1.2.0"}, "devDependencies": {"chai": "^3.5.0", "chalk": "^2.3.2", "execa": "^0.11.0", "husky": "^0.14.3", "karma": "^2.0.2", "mocha": "^5.2.0", "react": "^16.4.2", "sinon": "^2.3.2", "colors": "^1.2.1", "enzyme": "^3.1.0", "eslint": "^4.19.1", "lodash": "^4.17.10", "codecov": "^3.0.4", "webpack": "^4.4.1", "fs-extra": "^7.0.0", "prettier": "^1.12.1", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.4.2", "@babel/cli": "7.0.0-beta.42", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^2.8.0", "@babel/core": "7.0.0-beta.42", "karma-mocha": "^1.1.1", "lint-staged": "^7.1.1", "babel-eslint": "^8.0.0", "babel-loader": "^8.0.0-beta.2", "karma-webpack": "^3.0.0", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "7.0.0-beta.42", "karma-sinon-chai": "^1.3.4", "@babel/preset-env": "7.0.0-beta.42", "create-react-class": "^15.6.3", "@babel/preset-react": "7.0.0-beta.42", "eslint-plugin-mocha": "^5.0.0", "eslint-plugin-react": "^7.8.2", "react-test-renderer": "^16.4.2", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.12.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^4.1.5", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "babel-plugin-dev-expression": "^0.2.1", "@babel/plugin-transform-runtime": "7.0.0-beta.42", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "7.0.0-beta.42", "@babel/plugin-proposal-class-properties": "7.0.0-beta.42", "@babel/plugin-proposal-export-default-from": "7.0.0-beta.42", "@babel/plugin-proposal-export-namespace-from": "7.0.0-beta.42"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "7ac6f3a8ca099b22d2a8ebb091ab2ed7d39050cf", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.32.3.tgz", "fileCount": 216, "integrity": "sha512-vik1usdUd13M2MgB3edC0ZA0KiWi9JL0mWDElSp7HhvFIuQFbIhEhd4kaZqmGupYY0k4brl4LyyxKHp4t1idgw==", "signatures": [{"sig": "MEUCICTaysUzGxDqc+MLmgaMRRUylVJVw4iQlJbGruciqpVAAiEA+6rKBuUADA/dAgqlDup5UdSbREBfuV5fIG0Ug1xLLvo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1474464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeuHhCRA9TVsSAnZWagAAZd4P/jKDV+ei8dIIm8+YC0TJ\nCoIMWCX9eaSXGiSWP8jSRcX4PFfyB4LtPKwrGIcE8oGUPhWvDHmuOBBpNV6b\nGFiqp6ECdxOYefM2j3A2n0KFFVAYUcbR0lZvyuK9ZSyTwBiBJbGdpnLwPnaA\nIFP2V6HZtB7SfX7rg8tPi2HV0MksJ3uH1zCLA/Ze1tcwAf0niVzA0DVitNqV\ndjNlBL0Eo/Pmid7tItUrjyei6lHOhN9fzTQ3FFhywq/boWaQb2ysv8po8Fyt\n3/CjFbwbFzUUZHn9TaszcQRrs0Rr7TRxo6+UxXqqrxKHLOQT1elZnAe45tmt\n5rHu7KQBW0TRm5thp+nGFxGuwER5QNjFfAGhJa4Eh9VwS18xUkmkHKQGY+xw\npb5p7nQxbtxp0CgjZAupJ/S7XwHGfBaJzxxvIyngcmDkZTNV1LKFhZ+N0K0w\nJLlkd+E67EprIDl8t/Uf5ZojmpwBoI3kaz/SiYX4e9f2EarLLjY40dRsMeSG\nFnVR+Fy0izFR9FYv7qsNSU6649yYaHG34jhdzJhnD+eJs/c0uUl6EolRn+OI\nSebd/L+OTzhSmPYyhEzgbJniAHD2w4mFBJROwWIzB5Hv0z4HRwVAZz7JHSHy\n8wtTQ2uLvA/Oo7y7lvTNHvsKvGLu58h6wBA0K3QclhHYR1tATjwzvFN4qKjJ\nDGhw\r\n=+IzW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.32.4": {"name": "react-bootstrap", "version": "0.32.4", "dependencies": {"keycode": "^2.2.0", "warning": "^3.0.0", "invariant": "^2.2.4", "classnames": "^2.2.5", "prop-types": "^15.6.1", "dom-helpers": "^3.2.0", "react-overlays": "^0.8.0", "uncontrollable": "^5.0.0", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "@babel/runtime-corejs2": "^7.0.0", "react-transition-group": "^2.0.0"}, "devDependencies": {"chai": "^3.5.0", "chalk": "^2.3.2", "execa": "^0.11.0", "husky": "^0.14.3", "karma": "^2.0.2", "mocha": "^5.2.0", "react": "^16.4.2", "sinon": "^2.3.2", "colors": "^1.2.1", "enzyme": "^3.1.0", "eslint": "^4.19.1", "lodash": "^4.17.10", "codecov": "^3.0.4", "webpack": "^4.4.1", "fs-extra": "^7.0.0", "prettier": "^1.12.1", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.4.2", "@babel/cli": "^7.0.0", "sinon-chai": "^2.8.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.1.1", "lint-staged": "^7.1.1", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^3.0.0", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "karma-sinon-chai": "^1.3.4", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.0.0", "eslint-plugin-react": "^7.8.2", "react-test-renderer": "^16.4.2", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.12.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.2.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "8efc4cbfc4807215d75b7639bee0d324c8d740d1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.32.4.tgz", "fileCount": 216, "integrity": "sha512-xj+JfaPOvnvr3ow0aHC7Y3HaBKZNR1mm361hVxVzVX3fcdJNIrfiodbQ0m9nLBpNxiKG6FTU2lq/SbTDYT2vew==", "signatures": [{"sig": "MEUCICns4ocaPuE9ofbhX4Z4Ty+bzpjiJEKJu8JHdOl0/q/rAiEA16rpEGo/57m7YGtZfHP0csfGgWXr+Ibmojnu/XIzi9Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1425038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbkT8yCRA9TVsSAnZWagAAWB4P/ArKE0gLSb6QpPEY0pOy\nn8sFG35Qpmmtk00jAUwytBy5Of/1YmlmdFS3hvYmVviTyvM6jxCyIayNf/FE\nduLsvdHAWD/giIek00V/zi67kUKHo4ei0UxbXZ6jnL/gX1Vm7O0LKd54+294\nU9+1AN4WM+mAjxRXKtEllF5gMFYEjmW4hkFiWQgJgwL+hgAVkkQnjbw1U18I\nK7WM484r35nR3BDOzOdj/TZ0xUfcf/xTkszdoUqe45O9f12V6dR+9Pt5SpQD\n92MQLgqyQ0pbPB68769ThHqgplD7zDUXNvHEcd78v8CVed+J8lnuo/LK7Jmq\n6C2+4bjSJ9WmApajQ5D0gde/v1gekHZHuHXadb5Z79SSNsCWFhzPAfFqY+Gn\nynXO1vLpACQGFErmJ8IVFy3Lwv0n1yNjXEKB1U3j9bIUmB+ZQnHGNYG7Jym2\n9NLFddi7EefqhHdzr/foURUYIQhufqqysaLy4ccL/mCE2sW0Fl021YTSvdyT\nuFzJ9V/IZPLAxNA84ekZtUqrrKLFI5gJJ+rxATyi6pZtF9zP4HSFmDUhfDeH\nHcleVTC/3wEJfy6dxxEihGbRMB6+tMRL3cUcUTd9S+Hyrj4jhyliYOIWeSpR\nyxSSgrltl/TWhCSOWbVTV9ZLMI0DGpqe4zhnJtOO9z+LBOGQe7SmUzr1D/fs\n0SD9\r\n=1gXq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.0": {"name": "react-bootstrap", "version": "1.0.0-beta.0", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.3", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.2.0", "@babel/runtime": "^7.0.0", "react-overlays": "^1.0.0-beta.17", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^1.2.1", "react-transition-group": "^2.4.0", "@react-bootstrap/react-popper": "1.2.1"}, "devDependencies": {"chai": "^4.1.2", "chalk": "^2.3.2", "execa": "^1.0.0", "husky": "^0.14.3", "karma": "^3.0.0", "mocha": "^5.2.0", "react": "^16.5.0", "sinon": "^6.2.0", "colors": "^1.3.2", "enzyme": "^3.6.0", "eslint": "^5.5.0", "lodash": "^4.17.10", "codecov": "^3.1.0", "webpack": "^4.17.2", "fs-extra": "^7.0.0", "prettier": "~1.13.7", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.5.0", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^3.2.0", "@4c/rollout": "^1.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.1.1", "lint-staged": "^7.2.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^3.0.4", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "@storybook/react": "^3.4.8", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.1.0", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.13.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.5.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "e8fa5e79dc9a86bb58147eff1d1940f87f6bd2f5", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.0.tgz", "fileCount": 216, "integrity": "sha512-szSiUcR+hLaPdkpOsmQSbd3LISs99qxvl38vdujVd6zrn5HSlEkjhOEa5uvlc/ilzw64qlMT9duRPEMHX+WlhQ==", "signatures": [{"sig": "MEUCIQDUY9k1rWtK2okbGP49wvDQQGDCsKeHWbXzQRQ1m0iX4AIgD6Ym1SIkIxGL9LgSEUVV2Zb44naHn69cBcJ29FU/uBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1425155, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmnZuCRA9TVsSAnZWagAANGcP/2iq4X8zPcPrEZG6R0el\n2FZrTAnMdOr/c1Vyl1V8Vp708TVyJcjzmcOdZt4fC8LrftOqXg5q72fTFnPt\nLfkro4asf+kot4Lc52lSctF5+EgNc8s9Fb71MD+bbyCZ99HpgNj6WzCeqH1m\ncJLZgK5MGT6EZdswh8ZzswJqY0ZIhmFS49ILT9/iykg62CTnXB/vMupuX1my\nzmixUI0vq8ncfrGq6759uVtDf4uzs8yovAjaSRwbSz0mO+dt22MxIj8VtzDz\nHg3V1xuLFFMmthHK0XsiC9BgRvVUnh9Li0f7FqKQlHa9oIPDlylndduP60rr\nLE4riRGkAQwCQpzJYSWjQSqtRkie7owhP2Uir5V7WEBCx75ZlgZa9StirVt/\nh6K9pAl+AXSfIvQbrpwCJuiogF5Ad2cQEidKhbXrsU0KvPsYUskWto+EJ/8b\ntCbrcU6F+I1Mvz7ukYlykhCuZoIZrNtcR3uRixlu1bWl6GRHedUcxRGpHdF/\ntbC+bG01hdxQ0mHE5AYMk+P3DvTOdB4/RLpqV4eClXGy2Km4MCjkFJNkhyQw\nSyOSiUY4JS5urIEjIAvsAJWLnHVexS7AjWaalT8DbbCljZavSVkdhAKvqBlY\nz0luhG3O7YeJXfjj23WeK++sxi05+d6+S/a88cjtN12I7M9IQAC5GIHH06Ij\n3sfI\r\n=4sfm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.1": {"name": "react-bootstrap", "version": "1.0.0-beta.1", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.3", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.2.0", "@babel/runtime": "^7.0.0", "react-overlays": "^1.0.0-beta.17", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^1.2.1", "react-transition-group": "^2.4.0", "@react-bootstrap/react-popper": "1.2.1"}, "devDependencies": {"chai": "^4.1.2", "chalk": "^2.3.2", "execa": "^1.0.0", "husky": "^0.14.3", "karma": "^3.0.0", "mocha": "^5.2.0", "react": "^16.5.0", "sinon": "^6.2.0", "colors": "^1.3.2", "enzyme": "^3.6.0", "eslint": "^5.5.0", "lodash": "^4.17.10", "codecov": "^3.1.0", "webpack": "^4.17.2", "fs-extra": "^7.0.0", "prettier": "~1.13.7", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.5.0", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^3.2.0", "@4c/rollout": "^1.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.1.1", "lint-staged": "^7.2.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^3.0.4", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "@storybook/react": "^3.4.8", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.1.0", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.13.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.5.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": "^0.14.9 || >=15.3.0", "react-dom": "^0.14.9 || >=15.3.0"}, "dist": {"shasum": "976cd1f94d86785f374b00d7674e03ae72d37c0c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.1.tgz", "fileCount": 208, "integrity": "sha512-xuImr1uQorUmqCePlQQKzuYATk09q6sSTOSVHtLkUxGDm9A+3mckmZ5+KyWXK3aSM6VQ/0e63g2JPzAyneAnig==", "signatures": [{"sig": "MEUCIDcmusbzK7m0FugYpxjacaJ36eZo1hUc/6zpSpRZZCa7AiEAqq/eMb9xq+9hOW5/gDbNxklLIPAENhmJpGytXcnIdhY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1450927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbm8/gCRA9TVsSAnZWagAA2UcQAJIpGrgRlWlMFXQC7uAN\njq7PskXJjh8x123bZLOyvWjZe+6PaySp3FOQsOCOxLe14UZEMWZbyV1JqRkm\nF/5k0bn+tCTZvJKEVz9KO5QjGe51pHRfW33rO7nwn52SN0jjfMfH/I6/Mpoa\n3/EE03l/SgWGLLJcKND4kdGkqepjalvcoX122FFkVCs6KeoddUumqXmtqVjI\nfDPge5cgp75Y49W7EigOsnz9a8HLyVPwwgoOVdWrU1l9+JrNAz/cmchg8UFR\nOJYJP6Gxz077BapmnSjuakgkHTQ4IvsKqpb0j/5s9yYWXuS2Q8rslYi/VyV4\nI1emzO6ixfsbJC/PtHPowoodrvpNhByr9EMak5k34wkkq0uA8bbvpMVj2a6z\nhKYdLtmUVpjL79y5RHNmfhJcxyHlswzLqFB4bijRr1eOQ48lPNniQlSujixb\nMt1kynnaZC380FoIzFQLk7OL3g1cfRTWZ0FDlH09c0wbsFLlZxrgtbL0NcFw\nmSdbjv38AifBqJam2BsgE8PsnZOu0Fk6biV0vWwsQ543uxu1togsNDO9CZhu\neQubdYdAXBT9du0S9VLSJrBzOqWMe54EBruA5816ce/ronun7Ww4HRVTk6x/\nCvZnJBtgzlQgpR9a0GreThzlvmeeogmVqr4QqTkZ8W8MvLlB2s/LJL/4A0IB\n87Bu\r\n=J12h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.2": {"name": "react-bootstrap", "version": "1.0.0-beta.2", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.3", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.2.0", "@babel/runtime": "^7.0.0", "react-overlays": "^1.0.0-beta.17", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^1.2.3", "react-transition-group": "^2.4.0", "@react-bootstrap/react-popper": "1.2.1"}, "devDependencies": {"chai": "^4.1.2", "chalk": "^2.3.2", "execa": "^1.0.0", "husky": "^0.14.3", "karma": "^3.0.0", "mocha": "^5.2.0", "react": "^16.5.0", "sinon": "^6.2.0", "colors": "^1.3.2", "enzyme": "^3.6.0", "eslint": "^5.5.0", "lodash": "^4.17.10", "codecov": "^3.1.0", "webpack": "^4.17.2", "fs-extra": "^7.0.0", "prettier": "~1.13.7", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.5.0", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^3.2.0", "@4c/rollout": "^1.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.1.1", "lint-staged": "^7.2.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^3.0.4", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "@storybook/react": "^3.4.8", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.1.0", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.13.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.5.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.15"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "8f4483ed39d534fcc3bc3ab03e2c26f98f1cb5a0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.2.tgz", "fileCount": 192, "integrity": "sha512-ziCXOyoGEh2YzPxgYhbg4ESNqqVmJgkNnutJA+a1a1t2ycOpYS5E7uIaYdr/QnRNd71/QdDtEDgpN7ZvVqsEhg==", "signatures": [{"sig": "MEQCIDjpMP9NWX6a2Q66LqZqOwQv1eRldyI5TECGKVm/GFUGAiBzeX0ZzqeQtoEkoSOoU5R5gmyj+kpMf5R4PIZYv3lQtA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1240546, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb40+5CRA9TVsSAnZWagAATdEP/Rh5RJeMQBRHUgPrvVqK\nJGquOymOevHD3SGSzKtIpfO4Woebej4WHFuUBRZyev7ZKX7ydXPRaAaWdTzr\nfyuZboUyojZ3pn0srJNxV7eeOaWAP5LcComOmh+BjFXK23dmk7cWhnoXpxgK\npf1ZFWv7RvKLuClPPHRb5veKT/ZJwyAAUHyxZyFxUiV33agY+tnNdQtDNG/k\nBk0CGqsXkntr+RqzXmNlPC8fcR2dmy8QdKP7Qtwqj7wPvh29DhpxBf8BeVQF\nIoaEo6YRhDDqXuJL8RrTzfMKhtV5ixYf9Aozu//hfLkzW4jlc8xzJAaGoQit\nz6uLEF7qeBi1ASLiaWHq8GerjLCju2bTpB+6wADNSyemJexK4oASNqGAaqeu\nS4PMWpH1u/3ZIIgxIdwkrPoapqi5E52UzpOxPfn4tJxHigNM+a61ZL7kTMHd\ny8jPFPGO7RGyRybyvOYRWqgW50QjZoBFY2D7DkZ0V8CVF6LGEuNAcC0lVWDB\ntFyKHsdlf98jHoUCi3Mb5N4zXPHyG8eAFCDd7nbuHK/WbhA7/GzzVoZuGT6L\nhRsjS5uwMJfBt33MuNQLp/8eNtMDnAdGdhTRsf7/PoyFFvo4SKFyvn/ti5p5\nlYe32cBtHniqzn3ddZZtqix6z+VG1hK8bo+v0qeBTtXISt9vEfUiEK6l7QjS\nLkpD\r\n=NJe0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.3": {"name": "react-bootstrap", "version": "1.0.0-beta.3", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.3", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.2.0", "@babel/runtime": "^7.0.0", "react-overlays": "^1.0.0-beta.17", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^1.2.3", "react-transition-group": "^2.4.0", "@react-bootstrap/react-popper": "1.2.1"}, "devDependencies": {"chai": "^4.1.2", "chalk": "^2.3.2", "execa": "^1.0.0", "husky": "^0.14.3", "karma": "^3.0.0", "mocha": "^5.2.0", "react": "^16.5.0", "sinon": "^6.2.0", "colors": "^1.3.2", "enzyme": "^3.6.0", "eslint": "^5.5.0", "lodash": "^4.17.10", "codecov": "^3.1.0", "webpack": "^4.17.2", "fs-extra": "^7.0.0", "prettier": "~1.13.7", "cross-env": "^5.2.0", "karma-cli": "^1.0.1", "react-dom": "^16.5.0", "@babel/cli": "^7.0.0", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^3.2.0", "@4c/rollout": "^1.1.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.1.1", "lint-staged": "^7.2.0", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^3.0.4", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "@storybook/react": "^3.4.8", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.1.0", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.5.0", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.13.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^3.0.1", "eslint-plugin-jsx-a11y": "^6.1.1", "eslint-plugin-prettier": "^2.6.2", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.5.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.15"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "35748f1ddc1c508cf9c9a5ce0eb62b1e2f87290e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.3.tgz", "fileCount": 192, "integrity": "sha512-/eiSmRZE92q6m7uen3oAsOTGY4uBJkZDv32fwxUeyjesf834GUDaEhu1dzj10fmxSeVHW9O6UOKj9GkbwIIkMg==", "signatures": [{"sig": "MEYCIQD7vXvY/c2HerMsLUY8d/NIZi/5HumMDbRtEXtCjG9rpQIhAN/5RgnB3o8EpzailTojl9tc0YEEDHo5aey/E8Xu+V9V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1247783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb7ENICRA9TVsSAnZWagAAI3QQAI8lKLmBoTHEPxCXeWLu\nRyXv5zdoWxahkji266L+EEq0cfs4jusTeIHun0+KF9oY+Kre/PHdEM48sxah\nOnj9+eEgUSJBkEFGi3RgelxIBKqDQ22XpSpbQAuRagHJNB0UAT11FiYM24eb\n3wkAOI2TEPhSZid+q3W9i018zlq/4IPfLIWhxK3Z8fOw44kJTkkMeWVTpnF7\n+C1N3JQMm38m0EAdF8yvjut39LyOGHKQC6X1TIjj2WzXByEqoRaStEE8jWIn\n2I1Sy81YD9h/XLg5hH8i+wBItJLuGlgAvZES6U3J7tXXZKY/cbeNVP6QU0Xu\n9Il1eoEp2OtB0vSbIsHnLjwmuDNcZPQ5heIDevVn9crwPv15afkEaxSgBKX1\nST6J0Il+YvJnwQNFpbTmC9Nuvu8bhBc4n/PgnX1ZGvZcFmyHN2vkPbC/nX3B\nfCUChy3toqmn140pehbpAVjoCH5pGzCNGWmJUonE3u2+ZoQp8SvI3ru8K+R+\nN6rXBAAuhj4qaDjOcIPBTAMCucAJyGxnT+V6izKG2O3JEfisNBe/4G0nR65F\nbMnJJXLXWiHVwcsHCxDka17xaxC4OVZSJoPuvXlqonQDk9TjDppNOY+iskyy\n9YtI9/Qz2TGhYNFtvJ77KuAmf1fARBZ+KiLxY0SxC/7lvYdeY6SUgStTEW3N\nztfg\r\n=ItAh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.4": {"name": "react-bootstrap", "version": "1.0.0-beta.4", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.6", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.2.0", "react-overlays": "^1.0.0", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^2.0.2", "react-transition-group": "^2.5.1", "@react-bootstrap/react-popper": "1.2.1"}, "devDependencies": {"chai": "^4.2.0", "chalk": "^2.3.2", "execa": "^1.0.0", "husky": "^1.3.0", "karma": "^3.1.4", "mocha": "^5.2.0", "react": "^16.6.3", "sinon": "^7.2.2", "colors": "^1.3.3", "enzyme": "^3.8.0", "eslint": "^5.10.0", "lodash": "^4.17.11", "codecov": "^3.1.0", "cpy-cli": "^2.0.0", "dtslint": "^0.4.1", "webpack": "^4.28.0", "fs-extra": "^7.0.1", "prettier": "^1.15.3", "cross-env": "^5.2.0", "karma-cli": "^2.0.0", "react-dom": "^16.6.3", "@babel/cli": "^7.2.0", "babel-core": "^7.0.0-bridge.0", "sinon-chai": "^3.3.0", "typescript": "^3.2.2", "@4c/rollout": "^1.2.0", "@babel/core": "^7.2.2", "karma-mocha": "^1.1.1", "lint-staged": "^8.1.0", "@types/react": "^16.7.13", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.4", "karma-webpack": "^3.0.5", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "@storybook/react": "^3.4.8", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.2.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.1.0", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.6.3", "eslint-config-airbnb": "^17.1.0", "eslint-plugin-import": "^2.13.0", "karma-mocha-reporter": "^2.0.4", "babel-plugin-istanbul": "^5.1.0", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-jsx-a11y": "^6.1.2", "eslint-plugin-prettier": "^3.0.0", "karma-firefox-launcher": "^1.0.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.7.1", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.2.0", "babel-plugin-add-module-exports": "^1.0.0", "@babel/plugin-proposal-class-properties": "^7.2.1", "@babel/plugin-proposal-export-default-from": "^7.2.0", "@babel/plugin-proposal-export-namespace-from": "^7.2.0", "babel-plugin-transform-react-remove-prop-types": "^0.4.21"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "04f290c9d0a75ff4b703bbb1628b349072f00467", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.4.tgz", "fileCount": 350, "integrity": "sha512-vjAHBKBZl7gWbq9gQ427ckh1mLMam+kHF1eMVKR0ZkMrB+iQi+Gj79IntWtnV6QxLhHouD+HNG9O5z29z65U7w==", "signatures": [{"sig": "MEUCIQD8wf2fvbmY1feuKETb6bYMre4gq74qn03obc35l3mKFwIgKNinmu8Ep21BjjP5HaC0Omw0pmLOTxr5O5OgssFQ32Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1256625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQjhQCRA9TVsSAnZWagAAAYoP+QBuh3m8XstMjB9HDR5y\nWzBZfbXl58E9sxmvyj22HczSWCIHS34bZlrn6IdYcC5YtEnOw7lj8+1V0KPX\nxZYNSB6fuQwWdP2d+4idDiC9nXtd91JOuPkmutP32cMyXCFCGAUz7vQrNfkF\nnwzF69AOQkta0CSg2iOadWXwzH8qbmrzw39L+5aFEaz4AneLzOyeGnZ1QKwF\n7prTBcsSk35UKIYOY2uEwqgWO4tXxss4B27pMc7zLnsEMecwZMTEwnRMPESH\nWSKEef1w8GLXUSGK8EB/Nwn0p+OwIWrtvrUcR1zIBMX7JiE6VW6rgvVWaq/M\nOThnu+kWP/tOZC5+qkkGVwR8xIJNBYiYsvQJk5po8R0tMx1S4yF2XWbabeS8\n4LIk9dRLnPoDNZ/ydbox3Si6Hht+A6tZVyLzfefZGS+xG5Tm9zAHJzo9hk8w\nPg4z/YzkR3XOogTU9dvqbiwbsx/dcm7DBFLoYgdhtEpY7MDuTWqCe/8drtkK\n+I86U3fzClZgFOjeDe9IPJPjY8OxdCv5B5gKIOrhY7m26Cj4M2y7/LLib/BC\nOxnSKBuh3nTyLxcsCrw7lYbkLqU70LFaF9HJbzZC/XUePGi5BFQtsV09A+wh\ny8PZ5vmjd6EkJeOkyB5xVsAkjW47luC1ynh7JGw47vg/RUqFU+DFVLJckxLr\nO0YQ\r\n=lozR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.5": {"name": "react-bootstrap", "version": "1.0.0-beta.5", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.1", "invariant": "^2.2.3", "popper.js": "^1.14.6", "classnames": "^2.2.6", "prop-types": "^15.6.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.2.0", "react-overlays": "^1.0.0", "uncontrollable": "^6.0.0", "prop-types-extra": "^1.1.0", "react-prop-types": "^0.4.0", "react-context-toolbox": "^2.0.2", "react-transition-group": "^2.5.1", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "cf2c45a78bb0603c086d3fee3b454e21c3b5de6e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.5.tgz", "fileCount": 349, "integrity": "sha512-Osm0OtTbYwfsT1rpu88ESWuAHZxfaHFNKFiW8w3w+6YY9/bLEPHbGRZA6W21fg5yvcuKN9hJKT857TTHgY7SoQ==", "signatures": [{"sig": "MEQCIDkUOnU0iPvd+mMb99NlLav4R3RnP27JooG75NSjVXeTAiBtou7uNztKzaMHgycODKksuKWCM5eIMTvMNoSsxwWCiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1199297, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcSxppCRA9TVsSAnZWagAAHa8QAIXHEdMG0R2umTVOun3K\nln7sTLdoF5WaM2fPQqAJi1dXwc1QqRrd93VnYKIQp0HjHCJ+OmQ2IhWT3onv\nE6Cdduyy84f8umUceYttATaw+MSfvvJMoUl7fuG8ccgixX5D+Sq3pyDQQ5EZ\nj6VW7vaisbvnbl1Z17ssPqhBDq7CH/EhsigwxAWgAUlYslV70X5YfdBGlNJu\nMEllLFwZV+CSUKqW6QDmb+t9ylL5VQnwdBtHfzGenU6XanGi+vWZzNw5IO0v\n19VmeTZMLPI8cShh7tIv1JfrzgT2bZMVZuhG42gj92HyaFSankfPLhU09G1m\ne5Vp3+wi0UBN56jfwjkn2z6x5tpld6WhkmlrTA5h6O1MTbRVWbeGz3ksP1yS\nF7DKlHYIXITmcNat7PfzSSioNCvXrFVnHOlo7rrkmSK/GLfgRFBS+GwFNjXD\nbEGqSZRYP2+oZ0YTm6I1KRbkw3/dF6BeMYXqqDSAHWYWs04pWl3w+bahxC3/\n2xeF4JT0xd5bnTV8H1x3yEQyjBLLaMq3VEQiOiPv7OJf9Byb3BKGgr7VbacU\ntg+rRQxrfQuk9te1gUBU70bVLC6izA/s36tnxV7MKB6c4QoXZfsnNjz512QF\n+Hs0EVYOKOnb9T2tQ1MwIvMx5cMAEVSSvOwd64zQt22hrHHUfixuZLrf+eFJ\n2BD3\r\n=+CI5\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.6": {"name": "react-bootstrap", "version": "1.0.0-beta.6", "dependencies": {"keycode": "^2.1.2", "warning": "^4.0.3", "invariant": "^2.2.3", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.3.4", "@restart/hooks": "^0.1.1", "react-overlays": "^1.2.0", "uncontrollable": "^6.1.0", "@restart/context": "^2.1.2", "prop-types-extra": "^1.1.0", "react-transition-group": "^2.6.0", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "b9b8b01a0fee668ebc2b6148b30fc57e3aa140dc", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.6.tgz", "fileCount": 355, "integrity": "sha512-4RwXBCm45dHar1mMq7Wz8X9dI3b84R+Un696S+amgjePcxb6DqqqcxYla1FR7FJ+IIODr12HTjxu4LRVtIEsSQ==", "signatures": [{"sig": "MEYCIQDVAIWHdJA+90kn+m2CvPK12jhzrSpoIWDD/EXbaW36IgIhAM/VkdD9koiw3MjFuXlZcEC5QVsTserUiN6fqEVSUEm0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1167377, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJck4WECRA9TVsSAnZWagAA6JMP/1U+sYKLUuf3V7fP4Yyi\nqdkxN3DqMBPVUaERevQaHNm5RdjMAKC9dk7+FulLyBoWwvU9nL59rWWlT6Wn\njhCXjcXD8DzCpQqkOOFaPMPXYKDmpjjkWYy9VvAoOxBbpyawialqnTqNY5/b\niqlFApBAuQBqQubu1vdKorNzSw/GtemH4ZfoihJ9l39obhYpmMDOfpaS9u6S\nHkISplNkC25654+fpdOY0kodO7ZvF1zlgE18rYoe5lr5SQYdltxU8T5PSZsI\n1VS46+8VPrZmfR0dB4jkCMHJf0+g2QCgRrVFIz3PQao1fdBXZwIFD40pQ3im\naXwV903NWkZsmy6c22wqMnVnKF+DiaC5wBCp6x0neolN+xxjyjAHsP0+ANoM\n68IXQ8pWNmitW7mv/jYDpdumBmD7ipoTvu6ckTxQogzKqLeIu7z8YfkxFIr3\niXg+RDz+kSC6CyxeBjx2ryggNDf//65eKClvqnEE0nujYXxK/dHhp5DW+FG1\nu9e+9VGsFEq7Ll4te3NodPxyRjxiHBYqt4IFpyCeScifDsNxCyTY/yqCukw/\nVZGBnrcAzF7lbJxhQlmSteqkoehcOA8QX0fZfwpRTcp1iOVy0EYe7rGs2SaH\nd1E/kS9z6GOpyvceF5zqa/bjJPQAL5nzbA1UL0jx0AWiilQ4o7n56Sr9Xjb8\ntP1C\r\n=XI7C\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.7": {"name": "react-bootstrap", "version": "1.0.0-beta.7", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.2.3", "react-overlays": "^1.2.0", "uncontrollable": "^6.1.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^2.7.1", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ac1d623bbec14c94a98670f54909fb374ec701d3", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.7.tgz", "fileCount": 363, "integrity": "sha512-jQFT5p3N+aH9ycgmrXbrmvr3k8kAxdoyWXVMhQMhFda8u5pLPeku0J0PDLlHSuf76TPWExt4fzbc/aTu7KkwEQ==", "signatures": [{"sig": "MEQCIEkSjRbmH+OQRMJW41qFAntQ/uUjy0WOnYHHGqKQgV2tAiAta0ToXGAAOrzqPLxstIkkMzNPeUUa5doZFABh+GfdXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1183861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsOQuCRA9TVsSAnZWagAALL0P/j6Rc2Y21eMHM/MvwG5s\nGk5Ke8e4JztjciiKTF2NIureJVSTRC+gL5CjKCZ++PhsoT3mp34Unh0AHsuN\n5RZYXjQEM/oSzEGoHejGG5pGoQ74XoptDO3i+y5oWptd/KEWOoiAwnuoMWLO\npy2F8XEbvf5vI5Nf81hhSJhJFMCVbEz6TqLELPpJBiE98baRH5BjTYa2qbsw\nd+eT6zK+kIXv86/2pDmDmVXoCacJ/84/jQwTCQBXKdgGE4HFsag8Vqglcvn5\nhStu4P2ccGPpsUxWe75EwQO/Y0vBOHpWqkcrOf22FKZjigrPgn+r5ady4X+X\nAuCcnveHLlIDNZVsscrVkZCuZdSfW2LlFFhIl/8qiKGJ+yu7V1ZP35KDn6r1\nFOyZFGjuto9ujomSvgYVsTwz5MNbSCRytDremF1s5UhKu+HNOR1ex/ySwDB9\nkw5mtlinPB4J7JcJcf2jrnRS3CNdzKNNEJWUKetOhLxqc/uFF/9ChdZs3kNY\ntWmJQWgOqmu9XmN457t2j0o8jrQnp0LZu8QawFvmSDQmy1zsppukIhZ+AK/6\nW6w0NJPdBeHjaOJ8nHhSjaWvJf7PybT7WMFfTn4RVNthh3waJqDnvO9bH6Ok\nLRlG0GdX0wJdUEhcphYZ8T6km6L57P7LPdBQ3FBYv3sv4hCx8VuUaePQWxss\nb84R\r\n=eJ8r\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.8": {"name": "react-bootstrap", "version": "1.0.0-beta.8", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.2.3", "react-overlays": "^1.2.0", "uncontrollable": "^6.1.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^2.7.1", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "88d545526abe61c591d4eb84abad82e7f432c111", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.8.tgz", "fileCount": 363, "integrity": "sha512-rdCJbjBMIVzjeKrploQJMpmpVkndsPDFH+NBGM5npefL+oA5WBEzURgllWLbKdb3mmuuJamilt4j7+Dg7yTxBQ==", "signatures": [{"sig": "MEQCIAc4Ill6rvKrjVPgsX3TuC/1uFWb6O6AhC6y6QukxsxpAiAIxv5+03r031i+SzPOfntxn6GBMM6B/3WqsaSkIrIuVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1186125, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcsOY7CRA9TVsSAnZWagAA2OYP/3ErNeqKYW3JVMKo63ru\nXHoU32iqDnvnoKoCH5UGNY/c61EXmudQ/yTa6tC7wMGJ9jYkKQJGqfY8SNcl\nzEt/26T2H3sVEi4r/ZHXd6PDq0vFrBLMzKrQZD2UU3WTtYiUW9bdb61mxt00\neJDWNqrgO4G9obYQJVuJu0f/QY4EIkNIv9oEgbZFISQfBEt4S7Y0BBCZ68MY\nYcSrSFgKOjd8V85GQotmCGaCwD5VoQ/eg7Zhg4Ji9ud+/Qhsx3cEsYCkhZne\nkT5AzUjnjYkbzLZt3eihyPhVneFD5rYliHt2HANYoeW0BAo5F8EdgYS7NvTn\nXyWgxUvXZqsGOAHVUiQwWUf0t1QL4kdS0ciHbpGqrn0XZmd+BrerkEMY9umn\naRrdJqulPzbH04sG8cduAhjVYA8JGEjR4oRtmDLnaqyX7vtEEvoUjUvOErz1\nCDvvDUG/ui2qV7zfKBnpMlOzDF9ElSzMPnwlLtEFmHu3NtcAsgXBm7UnHzjI\nBcQL8J296MivNuDvjHK9E9AFTr/JAdaUk41KsntnWxhxpAcu6APuBoFDQzqN\nZxYkNlweJU+/6RGv77vgaGBQNv9X7qdwyK71wOZgp47vVPI/hKO6s0xQRwtx\nw7sOeatZ9aLLnP6l8r5NrNHoWiToDveSGDphr09TS8aoEO0vev72pD5sA+GV\nLM6w\r\n=61aX\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.9": {"name": "react-bootstrap", "version": "1.0.0-beta.9", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.0", "react-overlays": "^1.2.0", "uncontrollable": "^6.1.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3d9e429fd3d7a81db241aa824646c73e99a8566c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.9.tgz", "fileCount": 383, "integrity": "sha512-M0BYLuuUdMITJ16+DDDb1p4vWV87csEBi/uOxZYODuDZh7hvbrJVahfvPXcqeqq4eEpNL+PKSlqb9fNaY0HZyA==", "signatures": [{"sig": "MEYCIQD9rU7eC3dBcYNZ888Ac8bgSkAsJporSCVUdBHwsCB5vgIhAK6ipoPgRcnMQOKMg+I+0GvPeRyAuVaTw8xa1eCCyhd4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1207951, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc8DFmCRA9TVsSAnZWagAAPrMP/A+InmpGGAId5zinSW9J\n06hSdxoVKi4X8IKiofSzEoh8iy0huQfkCyjhPIezL6d9SuBDuouvgIARbuNz\nnibF9nGFPwpFlcm6JDtvteKNUyjU4oed8NDmjyhb5LmHmUwlw93rGsp6TVUT\nh64M3nwlqLfXthaQp21BTShxE5bc9ZmB6U/0OtFZfSJlNWMombNln3+qbshj\nlhK6us+qxBvcfIP6t9Db9AZtVo9DrZL36n3I1vfimdHLmOPLvsZFT5JKooJY\nY7KPMq+z35/HdMk1dznaerdf2U/BtTI138YVjZeTCeNIBEPBM1tM3nAoa73o\nSrjct0X454z2QBAn/ouGAjPL2RDAtC1mhKNbT5WAdmSq2AQ52/lXAULeoZI0\n/wid20DkQIsnWMYLmcFv1KtpABQceL0qdpoMgXh71tY+NbmNOe2d+84RP7mD\nXDs+i4682/alq9ZIyvjWdmHvPrySjzd+cwb6SgFRtRkFg6fErQhOhghxsPex\npE36ZftP1SsclfRm9b5ykp5tw0xFwM3vSE+H6lOrUZSaOGamDoDPxsO9PsgQ\nWwBFcOMDoa+amBTtKPmRD3CVjH7G6tcvt6go1T0PhyDX4xTHegMsxlU1Lr6U\ntJ5PaQJvJBBxKhFCKXAKjxGtlnWbB1sgNfh5N3cCyWskRRubL6j+V9J8q+4q\n8BHK\r\n=1IuU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.10": {"name": "react-bootstrap", "version": "1.0.0-beta.10", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.0", "react-overlays": "^1.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "369785084a8f5524b9a37c8d9c7c84a94e387a76", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.10.tgz", "fileCount": 387, "integrity": "sha512-hSvUrVbm+2uEpsGLVbAKJe/HOBT8ou+AX8Oo3VHlXSbG/9dHonwNP/wXu1NcsJ+TemMk7UnzdX2J8BN5MDnIxA==", "signatures": [{"sig": "MEYCIQD+geflJyk1vpV6Sl/DV/HVwd6qBbB6ABW3acWjmW0/kQIhAL3s0NGaG9OfcrtsIywVk3pXGDsvpoYPY0Gbm/fuiIcQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1234557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdLIIbCRA9TVsSAnZWagAASk8QAJZrNXkxyqdLd55OsZGM\n1pbIasWYFS6kOMBpLqbU2DbEh+PyHUusf5t3qRLRDhzp3L+FSUyTiFXznx3+\n2YPghQb6CqVcuPtDz4dLakdnfzQBgNVZWq0TBq379hHns7Ldh8VNbxICLpr+\nCZ0WrqvYhNtCxBQCuUVDbSJfpPIEx+fyYD88rik2f2JbMr6gd9vW90XPqzSO\nzmo+71rHTtpFyqSstz34ZlNka6eABBgMgNEiFpVRJEJQ2oJzpFBWzsI9AmDO\nZ0tY8fYdBsx8v4uV9l1diOxzZHh2DWMxJGU1QfPAM4eQri6YVSByoBXwOZaJ\n8weAqFkHM4QgKpRIKPnKcx3kSDGcGuu4xBSwKtoOJp3q5n7Y196x29oyX/1d\nc7M8KgcsIHrd72rnNlD/KTDZ/BUq/jKoMKNRc5Oy4Y8dTZ1iqVrdr/A/iNxJ\nZNSjTCtZKG/j6jKl2u6DqPdFqBGeEghcZsdTkTrX/m+LxCoClufGzWnDW4uW\njQHM0VqUsw1g/laUDxTt8RCCP/umqhqv8/PqJomM2KrpUTKZKa9rJSfOIM0k\nOYfYAsKFYWYROHwGBzbswYbNWheJ+5hgTHAiOjD/PuYwpVdhaiICIJTaIxsH\neNS4zhpnBETTwi0y8wmwa1Rk5xpxwmcLIdktaJWV2nBNcunKPs8v364PKp8v\ndW3d\r\n=Wf6Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.11": {"name": "react-bootstrap", "version": "1.0.0-beta.11", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^1.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "4570f5a1379fb05307924f7e8857fccd66c31100", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.11.tgz", "fileCount": 391, "integrity": "sha512-D8rAORkNRMf0ECvB8LHCTibeo1JeWptKs4CA5/tn1x5fTu5W4DUD7fQxLm9QdbzOM0qg2FjcfTKeM9gCqhTcoA==", "signatures": [{"sig": "MEUCIH7Is61sTWy6WbCZUPnlvDqLN4dOkqk8noDd0KxqtPCxAiEA4nk5BCGHbRpAlKQ/MEr+2GL6N3HEFJZJZ5SF0CuMNWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1218387, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdTeaFCRA9TVsSAnZWagAA1BIP/jB9WCxCfMyVtpTXmP0/\nqsv1RRtmwe/4OoWA+lZINU4Lw0D5kEDJDrSUDejBZOZbIq3+pWbxZ+XU7ZR6\nSMHqD4/7eF4/WR8zx1zNX3iwfK+PjWu+E+xRTK2fA8y+P9aWa++4HbeyFEo8\nKcyfAhMeCJolYxNGF6AFocJKy9o0g7zf5oFL4DUtpd3a0muM1Gzs7trsFsdU\ncCTZ0YNYIRJsz6RbXsr6a7nlSPSGd+GKmlYIa28ui7AOMkljLGJ/AxhDv2CS\nCI0BgHlyh8O5ZYQC/JObVUdhA9T/1ELXMWAUULBCQLEM3TBsuzjePnZMLDaA\n8VsquGGN0jI048DTrsv2p0/+rGgLfZcehWDs0naYXILaDdhjsmGBqfhuUNnd\nSDtXK2I27sQn7pRITTAqmXHoeL2kWw/Hkj2vy9GP7CdqacFgof3m4qz9iEjK\n7xBz2wF5kGzwoq3VpmS8W42QxTYHqGNQ+K4k7SQGql+q4QYfbkTeUZxZrtS1\nnQ1NGteip/QOQK/DaaoXPHQzKJAi3VSxqKcok9DG2WhroB/Hab9KnngBfQrQ\nmC9YGncxY1Q8gtDRpEOubz6M+Ul49o5zFKI4aVb+2cDQIaAyn/NDmJ9uzSsx\n5zsuddBgyw9Dmq7mwlzt9J2LWMVcwFdTYV70tS+hr1WjsLu1j74mtxNw7OZ2\nz29x\r\n=dizp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.12": {"name": "react-bootstrap", "version": "1.0.0-beta.12", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^1.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0", "@react-bootstrap/react-popper": "1.2.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "49fb83e288b1b5fa86e4c51e145630ec8b8598bb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.12.tgz", "fileCount": 391, "integrity": "sha512-qBEAthAzqM+OTS2h5ZCfV5/yZUadQcMlaep4iPyPqsu92JzdcznhSDjw6b+asiepsyQgiS33t8OPeLLRiIDh9Q==", "signatures": [{"sig": "MEYCIQDDFkRr/dstFAhH+eJAxrkZrGrZyLT6xNZwkIOm9IuUKgIhAMv2VmSqH2YvmqSxStsPtqqiN9ToUi602Fxo/uEoDKyS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1215181, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVvxiCRA9TVsSAnZWagAAItYP/jGd+zC3/O9PNvLg308B\nGDo4MzMRP0C36KnN/1+wW03hnlkKw/BRkkJftQqZPA7EqXcYdrC0nrh3rhsN\no7nO0BYbJRztp0feHpntE+sa9nq8y8vrZsr70iQnxkEy71H+rvPog6d0u5oy\nXSycu4bHB+SToMfjvi054PC7dlT1HA34wHmOAXj8Q7RMj+NJMc6Gu988Jopt\n5u6vJRu8NkizbyUoNWUk6C3sctGmjYIB32D8yvxcQ8yrdHQkkTtJ4M/saoy3\ntBp2805OMFbzAXKulEtRXljNwZCnnPZ9+x6kUEw0wMXnAhJqaYP1o/sr7G+0\n+R9O2G9rbm0ihv0c/xUP/jvDITobx4XNeaqOFDFO5eufQp5vBMcdWCZp+U5T\n0B/ISFFYV5I+JTySFRw3DB+CLnCX+y3zG3Kp0uByITIO0DTbSZUt/T++IeLj\ny/QcCRWpm35NpAzNzmf2rySZRTbFZSNJyvkvShQFjVm1hEuywrAE1aGOg27J\neFyhDGTpXsF0TKfHODK0ecsICnoGH7zdQJ5vrRwT8q7pnYCOMwRuGnP8QFMM\niky+mnOsRJKuzbgfYdPvegrp2DehXW1rybpciUOrmVCxmJxSD15eV+AFmKzY\nG60hNd0IZTPGxgdMb+nwVLzHoyE6Od7M3svLnOzHSaEIOgnmHXt4Oy8QSP9/\nFozf\r\n=cdm3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.13": {"name": "react-bootstrap", "version": "1.0.0-beta.13", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@types/react": "^16.8.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^1.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7ea2b4497a9ec75f7be0e9732424fb0ac5defca1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.13.tgz", "fileCount": 489, "integrity": "sha512-UuC25/SFz7gTqPXz/cQDS5/0bL/FFppZPKHChQ3EQtr0MxF7CpGhjc1JeVYwvseNGxHIlHL6ISv1vi/jr6nYTw==", "signatures": [{"sig": "MEQCID9pWVupNVY0bc1cri3m54l1zUutAbULsJzLAk+B3EcSAiBI8XqbaSDpGtGXDkWhGyOqVz7J7kgx7+j5Q9RNKNy1Nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233911, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdllWrCRA9TVsSAnZWagAAgYoP+QDUokWvnnc5XN7x7lPE\nSsIKeU96MQgtmShbOPIlcwUZx/iVmM3SZtDaiRcyITwkZEoydJv85B1umHGB\n1Z79jNGfr2TLyv+pODgghUtRZN3UO94/6swN/kZUQF7+iz8i5tsenjnvEW8s\n9nSjcKyaYGtr3zkxTXAK8IFYm7gMBCZl+36Kc5mEXP+a5BFEFGtSgickJNdy\nP2TPHVxM5FWFRigl1ZcK5kOMTuZVPt+uk1tVihlytCvMLMvxWSZJ7pB9cXrK\nJZwwpXFqXjmYaa28Z2hcboyQW435W2Thhwkr4jLgOQoNrDDSNG3w4UicqSqm\n7vrkfXcuJxMLqVaeDqEdyTqzcaMCB1JAFdMt2kcuj+CZyI9o0GDc8VedoRNo\nJL0qHaqVwVR5OhDMNhaRGtbvkAhGMIGBGiAsVElOpEg7utPa9/Q6ciQiRbeB\ncmDtH5eBJxH3Xg63hWH3k4KEW8tUKbiz8KMOWjJm/LkWgSvqnYR04qWs+/F1\nGXYjvE4nzI8B+Cg0Nj6Bve0TdwDDTEX5PHmT89pb7BScWcDwdqwDWYNI7oZl\nUdtuzB8moZYei7dtHJdtH1LVhvuuIgCVkPxknRNEgo4Lc/328eXugsykVOBm\n79DH8/Igx4lb3TCpvBislSx+qnnlciI3ZSQFlEDLzf1QwTXT6YHhHY1FtbvB\nOkAO\r\n=BlWb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.14": {"name": "react-bootstrap", "version": "1.0.0-beta.14", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.14.7", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^3.4.0", "@types/react": "^16.8.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^1.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "30330df61edbed1f0405f75363ef72d77c1fed57", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.14.tgz", "fileCount": 489, "integrity": "sha512-UGK5f78FE8wAei1YL/oSwFlJZLqxJ/h4S8DCwHyY8hQjFCrjEW5PoEBTOOhQ6PQL6WOsZe1jkiOJG7L5TZWu+w==", "signatures": [{"sig": "MEYCIQCOltCNa65U2qSDy+qOT+tG+0C1zV1LveqGyu17dghiSQIhAIKLbRPXoNY80ijoGGTZmjhEW+WfV1xgE6t27WbgcML0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1233916, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlqYZCRA9TVsSAnZWagAA6hUP/1u3desTkMyeDz6H4Q/2\nYzK3WbqxjRfri/f1stibqlQlD/mpeKifM2lmMU9mr4q+ELLMUOJGr6e/mcCh\nehbzXRzdTa9UuzvRvLySwvRxR9m+wBcVbmFJLPet8V+6vLEK1QGk7E4B0pGx\nV+E9nSofpRWMRR/Hty8zElpRtyxUdz3GJhPBZgSoCsWh3+R+rzZwSo4oFMwe\ncZWFtSHL+p+UDG3xkhA6rr6u3oU8wuyRjo582BoygSsqbqYfFCVxJzeSIgZ1\nlcUNDNc77ijuT/pTUwyWo1Z5Ikju9MXm+56G/mqnsB322QOi5qAilWueYjvQ\n8ffxIjsrhvxqIxNH14c+ym8Uzch7QQNuieRWUWhyw0UYRzOILr3I2YlkdbKf\n3phxhDh5x3J6tOnAFlbmO5g5jfw0/GBoFWhy8yoNthd89PjJwkaEFpbpyDdK\nvpVFYvAbQ+XXtKaQq/8LYeDM9OMvG+1FGOFFT2suZ3AWFDkRC6FvKrM1u3M1\n7o3nslDAU67EKsyeZs6yyzsqvo4zvy+HZKL4VeGILvOBeZ0bM8frXrxJkyq0\nAtP7C8fPZ3R7DH/VMUbnjNl0rkf1JVOmmCF7XE9j6P1dTIb/oyL800qLAOe4\n3pRVzmK6r0UbfCERCnGS7+Uyhh8DAFgfSSX7Gz8xufcyL6lwB3D55oPZNPpR\nFO9B\r\n=Pe1o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.33.0": {"name": "react-bootstrap", "version": "0.33.0", "dependencies": {"keycode": "^2.2.0", "warning": "^3.0.0", "invariant": "^2.2.4", "classnames": "^2.2.5", "prop-types": "^15.6.1", "dom-helpers": "^3.2.0", "react-overlays": "^0.9.0", "uncontrollable": "^7.0.2", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "@babel/runtime-corejs2": "^7.0.0", "react-transition-group": "^2.0.0"}, "devDependencies": {"chai": "^3.5.0", "chalk": "^2.3.2", "execa": "^0.11.0", "husky": "^0.14.3", "karma": "^4.1.0", "mocha": "^5.2.0", "react": "^16.8.6", "sinon": "^2.3.2", "colors": "^1.2.1", "enzyme": "^3.1.0", "eslint": "^4.19.1", "lodash": "^4.17.11", "codecov": "^3.0.4", "webpack": "^4.35.2", "fs-extra": "^7.0.0", "prettier": "^1.12.1", "cross-env": "^5.2.0", "karma-cli": "^2.0.0", "react-dom": "^16.8.6", "@babel/cli": "^7.0.0", "sinon-chai": "^2.8.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.0.0", "eslint-plugin-react": "^7.8.2", "react-test-renderer": "^16.8.6", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.12.0", "karma-mocha-reporter": "^2.2.5", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.2.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "db0004ae6c5b4822921c23448ff214de0e300d2e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.33.0.tgz", "fileCount": 216, "integrity": "sha512-UcxxaczDIPEsY+O4EqCIIEPDjxvLqqfUfXhQO+yzhk0yXzR0Y7+6xqBW01sy3gN717GUXhrkpwZiyn9uFX1s0A==", "signatures": [{"sig": "MEUCIQDNFl4m0znaqwEuYIUC1YMf6CSxtd18I05IMC2KxY6CVwIgNTXqDdCr/DPaebbNN+jqUAom9Z2IoV3ATbVT+iWBpzU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1421210, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduekPCRA9TVsSAnZWagAATSgP/iIU7RlNDOzL/0rqEaXf\nT3eONCINEl7CXfOnIYqfT6/Xn+EalS9mEreAyBozpeXOPN+bJWeHXNJD5yZ4\nnUQYUTDGIgauA5dMart6pnhFc8fGFacMuqxm0MXZ8zmj32DR+YBSstNvsQq7\n/ryldaLCYexc7O6f9tk4FStfzO8xbJrLQBPSWwjjCZACt2sjllP4aRAZes7Z\nt6aVWMLO9YXpFWt+EmCQqF+laFHF/K3ixPFTJwOhFJAQmPnmY0sdyZbrKUeQ\najzzarHshGarQOAN0gvKDvFqQb40VPU2qKdREY3W6z9sP7/wutFsCph6RJEf\nCSzBR6JaA3e2rT8QzHh8V9JEBsP+D0jP/xGXJACFBS8bru9BzQZM1TAofCen\nhB8Zg6xsqpHCHCf9Wh3DnF+ebftWqxrie59Id8XhYaMXZ968kvDoz5ircUw+\nGabqAvkbHPlX4MzFAyFpS0HckksFf0AnVGtUv8f2LBQD+iw5CE3WUEpU/iAE\nUvzekXyL6PRt6Vvr8LXjPaJ5IVkTqNFEhb/wXFHmiKEv8wfK9d1IIBSKVEzt\nJL/XCNKLGgqoETwq/Cr3i8NpTP0K/3d96iwDRS0c3T93Kzbcc3ikycZAbft0\nDg8c9RwtEZrUqfXQg6Y1vp7zXmmPa9We3F5LpRTxPQdJdRUdxNIELN30PhAm\n6EkP\r\n=P7vf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.15": {"name": "react-bootstrap", "version": "1.0.0-beta.15", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.16.0", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.8.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^2.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "c029440691098497c967e97a3f241e16a04db777", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.15.tgz", "fileCount": 498, "integrity": "sha512-DybdjxDxm3P2xncpHlxxTC/JXX1PGPj2ca0x8n8b+FQTKVqmmCmc0wokpjsSyaifxO5AX+yQzeTph52jNtHYkA==", "signatures": [{"sig": "MEUCIQCrCTo2Itjcm38QmsN1Fam/dN4/O55vOGLQOamCY274dwIgFcOe4g5FinkJGx3rSlGEwobRTizTq+gX7iRUURJQUbg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0g2PCRA9TVsSAnZWagAA5c4P/iDZZ6DPwNHjUjs0CDmi\nmve1UH3P2WjVSQ7pHxozXNL6Dw9DW8rB8xK4/zYWEc+YadAOkdEkcU98EgdW\nPgUV8OgGpbIYYdwnWQjQfceaGCUAfPv7RIYrA4+AFweQbCB09ZtZ9o7axJ3c\nb+epfbIJEdNHe1T/eCLgWFUihT9gYId5znFIbROfTzccqq2TAxSTcn6eTwdr\nFA1bctVOzM3Gf3MFKyYGvja63VuzLb2r1XcKuyVaIRRwO3p7vIPwexXD+sMI\nBOaMsEhyVmLTU6im8vMXhTciad1Fqiwj/nbBqIICDY4AEi/Qg83ldWMovfxm\nVF3DHnN+NU8ZlomsjJGUT6HvpYvxRIHajWQC5DNyAY0G20b59dIX5gC9GUGU\nNx1z/HwLv2hBl3FmjN+c6ei8VzVTqIsF8XKQvJnn9ylldRWD2xvd2yv2M2fc\nhkyvqJShpLUNI8OcvV/AcDSPYu+Ou3IFy4SMA1JdqlkEGmOtW/YzGlrGoCUo\nEO3u0R5k4Xqic0ZTpXPqo0UExAZoc9nFyJw1r6rrTClhS2aK817YWUmRvDxh\nE8GNV8g01/ZV/Rc/jJRBGELab5QocOzK7F6pmztQw6UxFBhRdVX61H443vty\n4/ZIlxp/yjKfIJkqD1n13pOCd1kYKOF9xAH6iB+7FsDQXDV9YEy3iUEY2veo\nQFSf\r\n=e8sY\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.16": {"name": "react-bootstrap", "version": "1.0.0-beta.16", "dependencies": {"keycode": "^2.2.0", "warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.16.0", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.8.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^2.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "42da0314aee6754494e478687b8e6953de1aaf62", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.16.tgz", "fileCount": 498, "integrity": "sha512-wjb+3CwviDWAaz4O3gQpd2XMDNqbOiqOOzpLm5aLPcp1wTsQsVRhyM+rTPmO3hYU8auA2eNpTYLz08/fAcMqDA==", "signatures": [{"sig": "MEUCIGX+Ig3lDxnlUQbaIShofsvQAXP9ZRTWuChJYZ5XkAXJAiEA5xQVp7iq88ohNw7K784UskoECjM+Ncev+CEX5h9EPAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1130906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd1WdRCRA9TVsSAnZWagAAecYP/iKEJVI+Fv5f0iQ2P0Gb\nAoIr4CPmVfWmUWGdtGsafllnuaFOK9iDJHd60SNoaxoueRT3+3RwH/EXcMq7\nfBc/p1JNdRJAtwwBmeizf+rCobCZ/ZmMsCKSrAb9dL1jLu4TDneb1DrwaH8+\ncE2+qzMCgl9UxH2OJrHgtBsWOwhGV5S49ntK3a2YxzHc1JOuqBHvIKX8sWEE\nLcs6sn+ETgTXEvzxfNifIAmzPOofpMhU4MsJIyRiu2CKDNvZMv4yMnasO9F2\nVRFeStBZKDFmkRkRdk4gbN7UGcXhbnA4mZoo1lZXwmGkE1nltI6Lgl7fCo2e\nrTyEMTMuwaMYu04saTR1ZY7oVJ/EScnHruGp/L/4Utd0hkbW1J1UAHktVHUS\nVY4TRk1ajlxj0o8qK4CSu7RkhCC9tOXZkcmvYCflu63fsYOck0c7JuJrE+vp\nZ+UfO+YvUpgFWdFMXB+vTZwSAzfN15xJnHBCu3Dat6cSVDl/fSnJuNbpx40v\nzoLji/STbllCMiYymv6uW8LTNmwkmsg3tSZDceB56FhpgtkRCKVWQK1cLAqd\n/WAjnWG3zNpuTA781zP+15nXZxtl0tWRGKn85lPT06Hl5pi3Eut1BYTXFLdE\nTQGxFfJr+t+gApzsjUp9bTRlThCAUM1DuezPIAlszNzMh/bKXCcc4dE+hXSj\n5MnG\r\n=eqAc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.33.1": {"name": "react-bootstrap", "version": "0.33.1", "dependencies": {"keycode": "^2.2.0", "warning": "^3.0.0", "invariant": "^2.2.4", "classnames": "^2.2.5", "prop-types": "^15.6.1", "dom-helpers": "^3.2.0", "react-overlays": "^0.9.0", "uncontrollable": "^7.0.2", "prop-types-extra": "^1.0.1", "react-prop-types": "^0.4.0", "@babel/runtime-corejs2": "^7.0.0", "react-transition-group": "^2.0.0"}, "devDependencies": {"chai": "^3.5.0", "chalk": "^2.3.2", "execa": "^0.11.0", "husky": "^0.14.3", "karma": "^4.1.0", "mocha": "^5.2.0", "react": "^16.8.6", "sinon": "^2.3.2", "colors": "^1.2.1", "enzyme": "^3.1.0", "eslint": "^4.19.1", "lodash": "^4.17.11", "codecov": "^3.0.4", "webpack": "^4.35.2", "fs-extra": "^7.0.0", "prettier": "^1.12.1", "cross-env": "^5.2.0", "karma-cli": "^2.0.0", "react-dom": "^16.8.6", "@babel/cli": "^7.0.0", "sinon-chai": "^2.8.0", "@babel/core": "^7.0.0", "karma-mocha": "^1.3.0", "lint-staged": "^7.1.1", "babel-eslint": "^9.0.0", "babel-loader": "^8.0.2", "karma-webpack": "^4.0.2", "karma-coverage": "^1.1.2", "release-script": "^1.0.2", "@babel/register": "^7.0.0", "karma-sinon-chai": "^2.0.2", "@babel/preset-env": "^7.0.0", "create-react-class": "^15.6.3", "@babel/preset-react": "^7.0.0", "eslint-plugin-mocha": "^5.0.0", "eslint-plugin-react": "^7.8.2", "react-test-renderer": "^16.8.6", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.12.0", "karma-mocha-reporter": "^2.2.5", "babel-plugin-istanbul": "^5.0.1", "karma-chrome-launcher": "^2.2.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^2.6.0", "karma-firefox-launcher": "^1.1.0", "karma-sourcemap-loader": "^0.3.7", "enzyme-adapter-react-16": "^1.2.0", "babel-plugin-dev-expression": "^0.2.1", "eslint-import-resolver-webpack": "^0.10.1", "@babel/plugin-transform-runtime": "^7.0.0", "babel-plugin-add-module-exports": "^0.2.1", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-export-default-from": "^7.0.0", "@babel/plugin-proposal-export-namespace-from": "^7.0.0"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}, "dist": {"shasum": "e072592aa143b9792526281272eca754bc9a4940", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-0.33.1.tgz", "fileCount": 216, "integrity": "sha512-qWTRravSds87P8WC82tETy2yIso8qDqlIm0czsrduCaYAFtHuyLu0XDbUlfLXeRzqgwm5sRk2wRaTNoiVkk/YQ==", "signatures": [{"sig": "MEQCIHndIuOWS7zXlOGIoLkxrEZD3DWxL4K+lhiUpcm0jvvfAiAMHM/1Q9t4IwYyE7l6tx6l6Thdpuc6RHrsUg49IOj3GA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1423587, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3s/mCRA9TVsSAnZWagAAnUgP/1KjL2UVP0ON9VyJciGQ\n2jSJldCoCEdlM3bqTGKSEcQGyWAivOlal7Nq40cOMB6YqjrMSbVt3Or+2iUb\nJ3a+QIkBF1NHwzNp9hajuy+rhns5X6sib3EaO6Ch+zbT9d5rXvZ/lC0EdNKe\nCt1c8Za8o+RMBYEtYQDFh2LOFvYJ8fMqDm0DbpmFY/oG2YukrOGvFmh/0NGV\nyT+h2ZjZzaYGVyGoHCWWMPFjrNkQS23PB3cpvUXKrdWt+D2U7BkXDeLs9WpK\n90P1l2sGASzlr1wPfvdEuluvCfbcLqSaIOsBt9KDnb+PKrkfwlbBidVKcyqD\nTHyfCzWVi750zoMjDjzJV5I/DHm9R+R1eU6NuGCWjR7v6Qvsor3oajCNi939\nYZe2NbLhKp9w8SQRnufnjMwWsoluIBkbgF4vR7vryI48PgHj2zLK5subsl91\nVtyJGluwW4JtAfv+U0tQyR6sPSKJAI0g4XgxBtxdjyPEOeUpO5+ULHI5chiW\n4sdf9uujKLnu+QYbEKldcglfyWqBC5wOLjCCHWBP1v9O4e9f7g7MudvpOV03\nzr7/fzNEB/tc43wCAGa6OqPHNWKv/8Z1EJARXTCpdMHX3WcD3voddPY/oMLA\nX7Umt1MGIf/sInKjpAM4tInEIK8U7HNR3LxnkSRaNGEol2jJwnCvUK55pisv\nWfTs\r\n=yCzF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-beta.17": {"name": "react-bootstrap", "version": "1.0.0-beta.17", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "popper.js": "^1.16.0", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.8.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.11", "react-overlays": "^2.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "09d4789633b2fb24d408fa493b4a80a496e87c82", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0-beta.17.tgz", "fileCount": 498, "integrity": "sha512-7VP9doezV4rX0EcajzMvyD6ywtrLfulF3ZAev+uTx8syWQybUkccOpecUO5kPomng/bJMgK/h+44PkZ15Dv44g==", "signatures": [{"sig": "MEYCIQDtXNJeOBQqDalqAQJw84TFKXjeaQ2/cIzLp7z4KCKppwIhANgKT+8CSIPwybNtdZhGCqz95L3w07OiQDDtK4/uKM4S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1132030, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeWStoCRA9TVsSAnZWagAAbIAQAIifk0LPkepMyz/nPUPV\nxQeaQISS1JpCeirQBi1DZrINcKQVOHP7OA2LcfYe1ym0uAczl49Sy9y2feAR\njmyNnm5zNNcVxjJVqBnB4ongmR1M/CcWR19sd00am+mBFcoZN3i5dbVQ0U1O\noNIXKh3IB/cOJR9V+KJ5yDZrsPnTpYncVtxg2u5dm3NRznu1DBvxI/XlbBCC\nxpzrH8P8a+aD/XeHVcAiHW53qbxvkvRsusRdpGe7qSUi+mCexGN2m97Q2uf3\nqRg80DClDZsdtWCgi3+PPhaAcmTHLVbID2XSXDPbjkys4vhxx6Aews9VIyQ3\nQAJ+KK1P3D+sFRQBq1+5CAjuCrUXFCM1W7FOfgbv4+6QaUWe0IcOpp23bBue\niOubgeFIraShH/TxoLv4kcJe6CjR+KgKDiyIJEy5mukZ48Idp1+Wx0+ao7ae\n72jl0TjWUgEYyN3X2y4TWbDwkcrchZlr1R4DlEhSd4HpnQ6leHxj8qZwCPz5\nBrHU+oXRejd7hLxB6XTleN1vGNp1DMkPMCUol9QP3Dj07ndwFJAoldd/z8jW\nTlZpgXjKB3geRE3XTxI7N6senn5CYz+B9yTcBjA+Ww5g1xOJVODC3WMyLKpi\nSIfnC2xwlYLt8qPvUNi1V6UtrTaSb9DXU1vClnmENejPjBJBnHNcdnozxBON\np2aT\r\n=qDic\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "react-bootstrap", "version": "1.0.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "react-overlays": "^3.0.1", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "ed812810205e75b8e4a84b9514d79de090cdc627", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.0.tgz", "fileCount": 513, "integrity": "sha512-Ep6ZNH6wL5m9bytOS6T9mjSz0YE1bEkc+uHItvenRcA3amr5ApkpKYzAWgdglhRPZHPvm+pnqs1z5IPwv/2UZw==", "signatures": [{"sig": "MEUCIDBC5iv8rzY/pBejFxn8S/PVTrDsCx8WDEbFFZbWTldeAiEA08iId4/7dxV49Fsbq+tJO3EHl0TeUW8EpV9qsO+7eqA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1139881, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeQHVCRA9TVsSAnZWagAArzAP/0KvI7k0+2QLFtUhyn6u\n60LD20y2j0glThoP4hoBKbYo3vZyTLGHPBOrqAfH3GY2oB7mfKFodax30X63\n0lNr36sctpgqzQ47tDEdX+c9yJXrG16LvgycNLRjj/LRs6EhtjywBomk5ilX\n8KYTcTgAG+0Beak0UQ/hU+HPQ4MFVMvjBQ/VBvIsccXuzUQPjxpWH+2xuOA9\nBch9STH9TDs8ypcNqBj7DRoFtP92zx2TJTnVl2z7O9T9mmnBL5y7JVVM59UW\ndmI00ubP8+8/DS5Czkw9a2i+pgaZZw7yQ2gD4MwTiHtm5t2R/jlVOk6PJrWK\nl1U0bioLbUNYtZOQQhdaYwYtnab66yl0E5/a6ySrqVmCqghgJ4c3KbvnMXmU\nQVs4EvlGlbFg1jB0ZiSR75/G5Bob31lzMpg5N02+mHp2jkAjamUuWfAnevYg\nSlWmNW/w09IhO63d4ertpMxbtq2dwv0Z8HOUgPiF22uXRi8PJgYpgc9ZfypN\nHXTb/MmkjqTPcls3KTH1vKkryBxVKep3A4m2sIHdQf2VbfYcOM1SaqmkhSGD\nwIEWqBwbe96Ocn2p6piE8KbTq1JIjbjdNktoU9gZlGuHD+hdCET1VLaurNvQ\nFVWZFK3T62yfq1sXO1hWdzOlM9NPdgFch2haydtndfycktjwGT1ACjcBRn/P\nMQyF\r\n=lnxB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "react-bootstrap", "version": "1.0.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.23", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "react-overlays": "^3.1.2", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "044b51f34a9db8e17dbfb321a71267a8d6ad11b4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.0.1.tgz", "fileCount": 516, "integrity": "sha512-xMHwsvDN7sIv26P9wWiosWjITZije2dRCjEJHVfV2KFoSJY+8uv2zttEw0XMB7xviQcW3zuIGLJXuj8vf6lYEg==", "signatures": [{"sig": "MEYCIQDitRQv5YO6RFngrPTG6StwUtIKty+See01QMY+VBqDIAIhAN0RRxb+FnyEwbJS+PDFgvL5UJH1A5GnKDHGGDKKdYS0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1161661, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeoJb6CRA9TVsSAnZWagAAlN4P/RI7dtdlR87q7sEQpEPd\n676WtZFoppgeyC9MN1Nz1vEVK3+Px6btrZZbjWrpKXknNUQsTGK3Ejc431Q7\nIpGoXHGMnhSfEItBtYj+3etw5VKmuulMytQ54JEYTqoib4XbrRqARKnFX+v9\n1j4UVPTIEEacuCFylahmC8Vg6krIVxdjr3De3wfV9BgkoIbY2SENqGy/OrBU\n5lCBD5fsc0llfo92dF3RRYdOz4NaXm6fv5u11E+ASYzz+MyDB7VMvBYsj5fe\nO/Y6MOtJ2ae52v7gfFJHi0OHLf78vQo0/EyWFBNeJph3LhibdxSFqVll3iei\nNsSmksXxUuBZdjCxAopf6gWOHeTaXYomW9l5rtcBlmwx/tbMvgr+0Dv5tkui\n1yutQJOXoPtxRF+4FNVx6QIouBc2p2fsyNBUQLpaqNiJ/8yf9W8xbTRktBp4\nfuMMcykMK2xgmyZhgck3ZTUN0g9GDB58tci5msL4V3lBn9AEcviTS53dkohm\n2iSOAiy0s6S4PbNjOxTPVbPJ9X6iP2Lvh2M25HNUV7fIH4VLVRTwTxb8w/uw\nV25AJhjJwTum1BSdINmcRKoeMU9ySIBbGt+pKkWmJcorQ0ML1afFQvIPWj5z\nv5sd4qC8Hp30CZLyXiE+08qTftugLZdLnwApI3GaOHLxwqpxetd9INAz1bwM\nhEbN\r\n=G+yS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0-rc.0": {"name": "react-bootstrap", "version": "1.1.0-rc.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "react-overlays": "^3.1.3", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "463110293ecb8eb20e6e1724e0fe00209ff54bf9", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.1.0-rc.0.tgz", "fileCount": 564, "integrity": "sha512-h0S4ny4mCAPSFM4u0bPWxpnB3DMWdkx9Bvyro96kNZ08Ku4L1LyzTydqBVmSXhxDLLx8IB3DkofNg9/7jRpHBw==", "signatures": [{"sig": "MEQCIAtj51i133FKZ6/NU8DGQPWqR6yBylKjVYM5BhRG4TUyAiBEDH+DyFofXb5K28HifYyo4by3iOen9ABZCaOJuHtENA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1272300, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezo8uCRA9TVsSAnZWagAArrgP/3FMTTs3R1IOu45C+CNd\nppGCQ2RMsBTS8TVrea/WRpXdJWJjV/h2VVCFQzaGMh5MP8P2d2QteUtUtThz\nrnFsBTwG/DfCAeRL4hpalKZDzU1AGbpiQipHYLUea5IX9+5eMVyl6LhLpEmh\nAmIqlKFNdIZrjRkNnwaD1RA2odqU5Pox1yLyyCW3FgDNDyZxIt0pVgpV3UkD\nAPuRa6PoWlfdVGAMadqcpZ32qc637HrVaRQFAS9F7oF2vXMctNiLZPQJxwrs\nqBs7D6kMPYT93iIt+Nxc2AmVXD163P6c2TBnFNaLaKuh+xYJaDaiGb9lAZlk\nQQEFxfiE+obp8RmeLIc1+rV27xZHgIiu6RUyxSfAJbWI1tuecK+w0brUX42N\nUmn0FwdX8e+4zEMPdfRcSYkbx+vph4eAcnFH26dZPXjTUF35CD++XRs2CwWM\n3HzSiQNcEyRNxa2BdpE/tJCkY8AQ7XBaKq2cgW+fF5wihwQqctlRAcrN672l\ni34A6/gHSKX9fNKFUF2dyJT8BKGq59p70cUahZlYdGwMcKvn67awMBquHpXs\nrYm6adC1n4ZsA74kkzUQMOs7pu2Z961Gb2xi+SVuygDup8Ge6aAa8r8nXgqU\nsRvAk8TlzByUTNae/Zpg+8C+vBoRfDQqj1RWXyDaHR3fJx3hYUrN2jVF0QLN\nAHV+\r\n=Qyih\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "react-bootstrap", "version": "1.1.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "react-overlays": "^3.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7a427b5d2853e37b210006feb13bbe4da0b3b13a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.1.0.tgz", "fileCount": 564, "integrity": "sha512-TOzLlYQRyQnMeYycqA9telJMZy4FGB3/jPi9q6xruVjGfHjnTIMWKCz547fmnEF/ewSD0YaY/mXc5TridvqUAQ==", "signatures": [{"sig": "MEQCIFFhN5m2SHASH1pCG8OoSan/uv6FTaXiAMwCDO9+8sSMAiBnzBysv5XhTpcv7XFg3L928T0dWRHaOM+8xhtiZdR0kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1241290, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfA333CRA9TVsSAnZWagAAvWUP/0YaBQf2VxLdxuZLD9Tw\n9L2mHGlakXFecwo6lz29RTjTKwZ2p4ClYbQapcxC8Ygp6W9biQAGqqBp6/tW\n9rGtAj/H9c6Rk3z3f73A0JrBHU+xDGKu7GzY0qXFf30AYZhlJufsnPTDux6p\nnyjK1y1M02Eoqdrle1ZXcKtg9oPf9CWqRm4ekoy2HqIbujKiKpozynt9feW5\nxMPtrk6g78ltwrwSd72dnpEI+1GlBzsG0e7LXtBn8rg6eWlem2/g+Nusm1c+\nZUD2pP4D0YU/3WYuuFocy+F5oeNteGDh/BcY8SEq9G9UT/gvVsyzyF7dKHS5\nvua49iaRgtUwIQinNFFfgZruPb9VFl9uGzJzYZHEThBPvbtEFlb9LxIebFMp\nVgLdrW6JSGc3bawDsPsVzzU6SHoiVqfbk/4qmI921Rtu/VXWTX73kwat1nQh\nr3c2UTwK/+G121wHw/hFX4UC7k4h9aZ9QYRiSqZkGup2d3s1JH25DfKenxiu\nnlI02BVxMQ5JimCt4/qgsQ7q4GWuprlOxhuIgDG3TJP3rmfE1Db+U5tBjFQp\nBTDQ5B0wdlEJ6a6l1t/IHtLx20AmAr7RH3VY7ziZPR7MnrkuRhPFHVokf57w\nEveEIue0uxM91WAb6hwzxWlE1ugB2lZIX1zMD79H0SQubg5RZrmlAVPhunXk\n6Sqf\r\n=CIHR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "react-bootstrap", "version": "1.1.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^3.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.0.0", "@types/react-transition-group": "^4.2.4"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "986606d8777b97e4eeeb365592aadc154520b557", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.1.1.tgz", "fileCount": 564, "integrity": "sha512-DsJ1aaXQPa3ob/Bh2JXnbWmZJHNxS18HbiJdyBhumf6OHk6rVXo329uwa2lHI0ZuxPO3mA8OJLOT0lighV/e8Q==", "signatures": [{"sig": "MEUCIQDD4MzwMCvgrCMXsympzOkpF5PfDhcVuBkgdXl6PJF5hgIgXGe6vPjq582K3fC5yAQy2+cjnop6+a18sFRvqfG2tyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1221564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBJ9iCRA9TVsSAnZWagAAMZkP/0qFv2rSuFN60uBHf7p8\n5kFIc8miMQ/3p52s2vv8USus0Vf9/la1YooGg9ElluYLuBwxmhJjMd9e3FE9\nctlauf/wND2SC6C7SoXsFSNBVhrHsoxfi8f/SIZC4f1y+5PSUuSDhufF+zkm\nS2XKWa3HPnGHS1Bn3N/LTrG0QPU2v4SJICIdYbM91VKmSft2h13tP4HxFtik\nrimQlPFw4JEsx+o56z9LJ6ZVA04r6uAx5mfD/L4uYEWcRUOVvKbswGSWkaqj\nTAoGfKKQTyK9XBl81ctzrfGVG95xY1A3eFISP+EXc6Es6Cr4GGNK1o8ciPyS\nz2JnB8BVc8yY1JgsDtEw3/1KoIGlRoHaP6QQZCCJ5UHRa33N9uSXXD4pwfgU\nEV7r4YjBivrzF/5ogxfAynSDphAVBW4HbZK1hwLef2/ngwmNS/u2y7dvxj1M\nA+o3vJu6c2TXVqTIbp5IOf4iI/MzV/WxtM5jNgNTpTx/cPm8DEfpo0Zz2hyB\ny7RfdyoAXdTcK6+QT15ueLcmd+C8HWE4Op2NYTFNaZzAmxXlZagG5vgob5pd\nOy4NGnr1e5gjGZffVp1kjBlUJdjHDbZJaeFrSkEpqXyAVmkbCv7FaFf9GOIH\nK+iX7K7vXuovJaolgs2ZZ9uuc+tx9RoOa0t8/PjTpi9yCTdx8LpZMJq2DJym\nLSlY\r\n=ZyQm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "react-bootstrap", "version": "1.2.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^3.2.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.0.0", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "9aca4f4c602d97e7154bf9cf7b0c0d6c58bddd0c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.2.0.tgz", "fileCount": 564, "integrity": "sha512-HHYPi6aukpBkuKKDxTle9Xle4w259ARERjf9mqb84LAe8lqXLdIyiUsYLdU+8PH6Ka9/sBnxgx3ahf3iW23T3g==", "signatures": [{"sig": "MEUCIQDrO68yLs5OxPnVccU+qYM64yMa4FL+lz6fv6UqYGhktgIgR53ef/M58BJIengXFChXmLkL8zaTcWNmWgKi6Bfn0D0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1283999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCGC7CRA9TVsSAnZWagAAGm8P/ijOwz9KBc33AIBb8sfQ\n9skshy/hKovgb2kKqHv/ZT45q08E0CPkb5U8nYV64k0IOz21wo/ApaNns8pG\nQhxDEyM5ehoyWsRbU150/zSY+Wvg9qlDGugojoeDapNQFispcUXs2ZUFNgIe\nprTRRfNx9Zwb6Sznp+//WTItDbHU70fthCKvh2rZQOCkGwZNTrsaEjyFTbw9\nvYWQnLs8aRq1Tes+bbl4jy0A7hco9B0WMt8i3skkhU1k910D7IF05svk9vWV\n7Dq7ncgB10l+LgNpP6wIgB52PQnVxT4AWAWUe1RlOwkBpAtN1k379zr8SjgL\nPiBbYEMVWgRbaJOBaO0twCeXZRDA/kUx48oYMVz6794jN+LWw+OhehUfi/wz\nMwsozjN4FxwVSA5XqCC4vOk/cXHfpbN/bgqbi0p1XTKAZ4TdBwqsVOJLB9t2\nEB1xfc1vRN3k/xb65bKzfeILbDg0INTOSuKRfmZu8qEmVpwBg4lf1Af2kZoi\nF8WI7vCbSbFOGUaKp6j1kpEcnpfr9S1mrflOQ8z11n90r4HFMh184sX7DcFl\nS0j55TDPAVrK1cCYberDJmMbtz/uRphwC/9Xdz5bht8critWcJobKKJyJx0c\nJ2mniEkXdRYVPwwt+gi+FfCvdnE+1z/FY/ccjhEtW/P9YxiSa92D9S0BttOD\njidD\r\n=INNK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.1": {"name": "react-bootstrap", "version": "1.2.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.0.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.0.0", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "dbbfb4341502fc9505f042cd38af3b44880dc2e5", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.2.1.tgz", "fileCount": 564, "integrity": "sha512-fiAI2bzKjlHq8gsWrZ+FED5HdEdTWq0JarTfSjsV2xKWUP4e1ucCN/XRDHrna0Ss0/UzuxizwTtwvVIo2d6wZg==", "signatures": [{"sig": "MEYCIQDggS0J+f3wH2UQ6hN7C9olL92rAg/6tORZsLCPdKMUowIhAPX+CL66PQtDazn7bzl5wLs9/IUqI96j6CObP9ippnAM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1288334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCL/YCRA9TVsSAnZWagAA1z0QAIQ9nGYNc5s2Mbi3C8UV\n/m7sfVfWRPU7Q0Wvuk9VivuoTDfZEoj7a31aNRYp3VuYwn67gkTtbuMztPl0\nWYT3i1E74+4oSHyqDoIrqsnTxuZoIqVqOEODj0dXaFE8/+qnNMqUDf/WjxT8\nzfwMNiGU+W4uhQ+DPlK7w44339cvLGF4mcW6c8baMjc+HU15GF26uh6v/o+q\nvHuB1JZjkT9tPpCK8P+BUFNf95425y8O/qBQe8FxfH8uhbGCZcby74jP4dh8\nrDIrbS34pAGMjpXAzFzJDKZQl6y2ojX/mgyfUFFz57RKOYAn6VyHGfo/qDOH\nj/S8Rk9l7wcrZY3mEZ2bnvcjYuEDD1FbdFGBAwfCywrKlXIm0heDtWGnCFjs\nMB3xAVAVF9TLwJl9tt/za5c6tAppN55Qz8/920uP/MqJ2jlxEgWFYUH0Bj5i\nA5dMF5nyenKSIhgDnVPs1T534lxyp7JDP9OQZws/5m4EWGGCgc6AQFHKBsjF\nhHnkcvei+F0bbq4dBUHknsWcD3JkotLAiai7KfjJmuSCLrm4uYCo8YXz735p\n06UawROo1IGV6NJS6xrzxloVeaMOtsR+ympmxMK3gKYSL5dyWBrQhEjgIyxk\nH5oQQbpxidoZOK32k/B7uxRwA5UxcBj1vPgYSsy4jWEfqXnrY5m9UzEDLgkk\ng28z\r\n=4iQz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.2": {"name": "react-bootstrap", "version": "1.2.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.0.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.0.0", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "dbae0c3d4fb20cd545c8efc62f49190f690cea2b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.2.2.tgz", "fileCount": 564, "integrity": "sha512-G+QcEyBqFtakBNghdDugie+yU/ABDeqw3n+SOeRGxEn1m0dbIyHTroZpectcQk6FB3aS4RJGkZTuLVYH86Cu2A==", "signatures": [{"sig": "MEYCIQCPh2o5iJ8ntbcQEPIjrJPZ8RjYKvUi8/c1FaAuJ6AbbAIhANvuw3T6BF1gktW+Beu2AYf9XA5nxhGtYLrPfGrWkaBR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1289391, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfC1GPCRA9TVsSAnZWagAAD1cQAIuG/zHuRpJu7kZBx4Ml\ngju0ZxMhc8LTSmKJ4icCgJ3bxpOwaTW/DUMlMFWyI8RWdmh974z/OcpCnxz6\nkdQBR0Lf41aDPp3L6syS5iALUW4NlWc+gnvqznaPHBhU6vljYAOIvKjR6jYm\nVOAIPhKv/RhyXnJ3Nsc7rC5m9mvtwjCsla47UcrKXkrVrgwA3Jvp04+pbzG8\nOmP1l+rZa5d1FDF3jcoVBGK1J+fU0IpZ292ndfd0YpN2VfktgitOYwwokXhs\no6xWYarXOADew3WyGiLoQYtcB5gIRPxymd5HMbQSfkRMfJBNshXZtFLmvFPo\nLsfdXA4c+NcMZtjRg7C1k3OFTU8GPjxpKEEDjGYvisC0LrcjbeAlozb6B5B6\ntOQlX29OM4GBLnt7WLOWle+QI1zsMGtjD8fmLhtIcMq/PNSNbzoiVuvNaT0f\nN2lsGziA2U79HU7qh4vBSqOxx0J9ySUNk5gGG0KLlN8Khs4oPo1fUsk2i7BS\ndG8WWi54tDabaztN4EtiCHFaS02DCtx4sbkOKyBk4R18xndEehR/bESzVZhD\nfHOyqPTE9MxwufNvu1TRUTSpOPREJgmCSxKE0cKmoiMqn3IXwM1/9hEqck6Y\n7XRQQ4haJ+TO+jkHG+sMm0EqCFPeoAm/e7rDcAQokSV9GzPu0Vk7nOZqnOYi\nsbxi\r\n=M1jy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "react-bootstrap", "version": "1.3.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "d9dde4ad554e9cd21d1465e8b5e5ef6679cae6a1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.3.0.tgz", "fileCount": 564, "integrity": "sha512-GYj0c6FO9mx7DaO8Xyz2zs0IcQ6CGCtM3O6/feIoCaG4N8B0+l4eqL7stlMcLpqO4d8NG2PoMO/AbUOD+MO7mg==", "signatures": [{"sig": "MEUCIQCF0W1NfTnB4zV3EiEu9SY/KGvOTR12H/1IuhxVzYGmsQIgUhzAFqQi67UX7KkO8QdXARcwrsfvfdp8L4aybq3KRyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1291847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGcWeCRA9TVsSAnZWagAANlwP/2MURqU+k0vxjPJN71cF\n280pGQ1m2xyzr1bM1RAwNDcIPAENB++8JuJPSW2WYSC9Hl5/st4fo6mO2RyJ\n1ESXwGk2x3ldyIQx11sDyR9hZTastqWCMvZoxhdXWEXAAeh34JrqsGLr9r+o\nljROlanQccqhexieonL22Ey7re+p5+TS6E2U0fV7Z1uoZ/n1PayXaYtYAgsa\nzierX034hW6zGGJ6jEK4WOnjddK9GYeVYC20x4GIMVZAjwKl0rRdUd2TsHSB\nl9ejrI8auihnAXUsOtsZSaM758h8gZf52mlIsqXyvkvn7A0pbj+nlXtIzlNs\nurwR+ZtZkvwmfqPnct1eiugfyssG2NISRqd/qUcXlSIz2iUqXNauVhR1c/n3\nhqYzwPwUIZlW08bf1BjiXe1/77nK0bdZXo6cz7hkbA8mg8kmUdpUGUUOxEK3\noTJOuglXkNx49QbnUDg4r7DiEL73satwi6yplnuV+Ffc26POS89fSp40LN9x\nQ6oSi92f8xnQK7FXkvQxeBNzuJXmjGKVkDgsaOw8T6159J3COqJ/HwAOTGSS\nMPQ5XdG+9stBubNZWZrB7ciCIgh+dAZZlZ5KJXjJ+QU1c7JaXP03QoO/Xpyx\nunViu4o5oQ5ohGRSeoPFbY8Obxsa5uIkbTYNJo0bk/cCk6P/oXXYUECFinx0\nwsW6\r\n=VVQO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "react-bootstrap", "version": "1.4.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": "^16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "806a8b48b065cedfb28c6c5c7b0c0e3c3b53445d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.4.0.tgz", "fileCount": 564, "integrity": "sha512-0BMzgeUAxH126v7VYDzIXbHxQVHSnniPVKpz9fblumdQpWaiElMnnzk+u8h8DoELX0nCXwPlcUzgXqmpncdc2Q==", "signatures": [{"sig": "MEUCICsHYK6nIWOugW+U2kY/MdsXtetMHbUJCOBIqGKQ0f4+AiEAviIV5LWRPtgGpr2RuaFSfgNaMSd2407N6VIHmDYzxYI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1310757, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfkCTyCRA9TVsSAnZWagAAscEP/jM0Qfkq890MHKNm2Zv9\nH3evRDIPrLBMkBsHJ7ZfoQuiY4tQzvLoA61ATtqsphE1CvTjMpyN9dXJESBx\nIxclHKAxf+r2EiF9iZKYd3axwHeuPboGml2OvH6WLL+cL5e1X+iyAwLBKC3L\nFkZjnQt/NJdZxgohqVHkQUg6NrtVWvLi/tUrJteAztjrDMyuFECW76DMd8QG\n5OrnoyUhsYomEKwaJr+bl9RxZ8IOe0UF2mkAvwekmKtS3aDjKqy2Cfo+3ZgK\ndPBOXsyF9YCLZTc1Q1OTPq3jUsOjXQBadoMXA9B5oknEETj/8FDqcdV6+2ML\nrJUTJfMZkpbHGX0r/IILKcF2S84qgaGtAAg6/cyU0aLc1oRIS85/ztjlALsr\nL8u/ZwsS+sEudS6r2n681wH4OUTiC9MSyGUc8SJ/GgcilPqozC563Ofk6Gqz\nlyhOqThDj+y3oQByl1IgOE55jNvBaD83X/bcG9X+5TCAjurAwHguKuE8NjqV\n+DmWcOP+Z12i0sZmgHgTvPW2qZggvpjC+fj1WQip/0SK+rxKJt7dXqi8i7Qd\ne6wWYn7zWD+ku9VbP49qKLafjhtGjNPIoKBAfl173kWDPD8onqLWys2A8Mws\n9y/Vhjz+NKMaZdaVE0nx6ZbwVjmTTAAQpQH4za3Z7JGtRBYimXtPLMqbsuBC\n+VYW\r\n=WMR4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "react-bootstrap", "version": "1.4.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "bf54235ff9299038e6985a31963697056c9a059a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.4.1.tgz", "fileCount": 453, "integrity": "sha512-6u+wiRZwTlRRcUWBakS9IHtuMUIJ5V2gTRnOEO2pdk4cBOsPg5teKR/JODSF3ZS6euLXFmnT4UNV3a+77Lc8/Q==", "signatures": [{"sig": "MEQCIDGFw5UHW1cwzt6to5onU6mB64XrqBtC5BeZyaiZrs2OAiB0TIk7+bsOod5Rx57sofVHgATkQ2SkeeyTFUSM/wFJfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1307053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf934sCRA9TVsSAnZWagAACWoP/1llV4t2pwoPZpvf7iQR\n2MAkKTjAbHlXzw2PLXWM0ckpPcnl1A7tvXM1JVYkhx38i5oubSRE4RMtfGfs\nCIEP/guBXaEKZEiRWhbW78PdFXsXBaxZ2WqgaIz+YjUoVagsJzUF5irRKGND\nZEccGaM6wAvpMlhiQBN60eUNULfBst1Y+cAM+c69Y65z+WjMWwLdHeTqPuxg\n2ySX+qTmeQf3DJxWgfElXf8teG0vRm3Fzhe+6dExMcRUGBwZfGDnFrX2TYCd\ni8K/5tJyqcktkk3/Zd/AoqcUrohpcfZt7s3zVJ8faONhgOkbk6RrWKh7PhhU\nlsor/kS5ZtM5371Fw+4f1LFwDW2cfTqUozXFvwNx7Q6+8MmLCMODB0qTeUmm\nWoUdK2jJKd6H+X8Rb/yiHZTNqIoYIx57TA6xPv7mbeQXd/wh4vj2WpOHoPYx\nLbaPsjtb7axtBGiOu5UyjLhoKGmIeOU5zojzCmnVGA9k/mRGTA9gdhFgfbzy\nEOiOXXE9MqONEX/yInle8xV3k0eG2NfTKHEIMDSZ/PWlXRgk0R+0cEf9m9DR\nMBNZO1XdliqaFTEbPtITVG7FwaoSdsBZTMsgNl7oTTt/uAlSCJ+eaqw3UR3V\n3CSMkPM+8wVoAlkuUMHoe83Yr4t00chlKyaiDVENSQrzz0IzroYQhfRTfoKO\nFghi\r\n=Gote\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.2": {"name": "react-bootstrap", "version": "1.4.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "b3a7c39fd15629be81f70c0b9ae8ffd053e84c02", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.4.2.tgz", "fileCount": 564, "integrity": "sha512-snz4XhYFeG+TsR/xhF3s4qAOE6ByeHWrQKMrXAV0Pgb/EYkp/K6b2GFiDyguHt4e3C1rE73gUxCVyInJlIAfEw==", "signatures": [{"sig": "MEUCIQCGZ8HgJoT7BZflqBeNseGppi844hq1wnv/XZHzsfJd+QIgHs5NrS8+gcVlQJZP+gxa3ob9aTrqp7QGaN1QmduHcNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1324475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf94xWCRA9TVsSAnZWagAAMGYQAJ/me/puUozRzzln7Yz9\n0i1TeiWq2Dlz9OQjSFrVRKmvGfWggwovdkUoKy8OfvCCR+LfLzB55DO6IAmK\npSTWXiyQX6Nur3RUy6Ns4joj8g9+AmEiypUcwv4dEKe6TMkq1TNkRkMxWN6M\nW7qjrS3VYxFThKJJXoJKmQt1OSVXyRmF9E+oWhXArSWj0sdlSqRlrQpQ+9Re\nrbNeDf0RBnYjQNPUBF3y63JB8EuWodIzSdbpN9SPUHMXiEnZewxOw6O1DuwP\nTQXKCMqlE8Rj0y51agjr68uJ0ffJMHFOR+bmuhHLWthiTa+nV5tTzD53ARCL\nZOqW7UWdRJmEC/hfkdmzYq+N6J4IXVvQ+Cd2xFCS9g1wcpx6ABdqPzecqBnf\nNsUyEIICCGlkEbp8MLulD5NM7xCpeyIXvyzKr3WPKbYiipITkNgTm49JLWp7\naJjBGfRELZAzH1e39eCO2c5ly3sI5DyNplmTMKlh5wNBFLzr0kGBY3Es8Bck\nU8cFqBXZYSHJXxHvnGIqEx1Pw44zXSyMr8CXmF8WJvfTvfcEiZuXbqlSJKBn\niWTNrF0joZuuwjOAk58WSivYwPqBDC4HT3K5QDf7ehZjQMKvvRu9UEfhF3gf\ng5VA/AXvXk8OBd1K30oKsRLjev0sKYRCg5oP0o3nZWdo+OKr9/Cwk++HPAgU\nVsyM\r\n=Fnpo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.3": {"name": "react-bootstrap", "version": "1.4.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "9a76a66800b57f422dd824c9dbe4c9dcef634790", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.4.3.tgz", "fileCount": 564, "integrity": "sha512-4tYhk26KRnK0myMEp2wvNjOvnHMwWfa6pWFIiCtj9wewYaTxP7TrCf7MwcIMBgUzyX0SJXx6UbbDG0+hObiXNg==", "signatures": [{"sig": "MEQCIA+WPNfFReHX7FZY1q+8dAFLkVdjXaj62iFf9d2uFhsTAiBe5IGESJeGPrlg1xrowk6s9xqEDG/UsOMR+kbPN5Ot9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1324475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf95XtCRA9TVsSAnZWagAAIpYP/2OVGnSOgNIpyFrPuBFu\n2ZpeLNa48TorCDvfBjqecX3QVvukl/0Oa/FOvsFPY2MsJIIHR/rgWXDa11Hr\nJCBFwnto12c7/erg9hNgquBMbuDbll1ayREG7q9HMrskTFrWpRMCw+mbgEWl\nQM5Ilc+tqeIcOl6FR6jEz9Y05+H4NYVJ6B1Sk34ofxaRorxEComrNpXFuFaa\ngaPhsxI9U+uxTVbK+oyLmoje5BQCrS7/XLKFPxmnZ5zdroX1ahDdeywSwGXD\nlNfsl1AyP+VCR2Kn4ZcWeLKMbkkXqws7WpFF/j0j3aEdBeDZEReoqD+fbObE\n1n+20MA2asGzPH4paKEzC9ABkGO4gcDB1ZGRU9GRb6ryboHm0vbUGVxhV3tr\nVbV3udwszW3FWOyaTLQJrex/iY8lVKMktkvUBVE+7ogMC5NBcyiYOWF8kEvb\nTrdQqt0M0ucr6z7Yg0gH1HEH9CuavErtkYW+pMuAbB6yzjTcUuIaq6GbIIp8\nVOv3IbE7Py3uPdB0Us0NaMio2zJRUziipiube9zmSRBAHgFU5iPfbuwW+hlb\ndDBfe2m3twMsx+6zG05y1jwaPD+iH1n2jjIGScevgKvTS3jthDXR6lk/ZLpZ\ny/2buyhi0x4tAQPzTdjNZ5sSBwTltgcsuP00pAsvSeMbl+IZxcp0qZrMlW9v\nuwMv\r\n=55Ju\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "react-bootstrap", "version": "1.5.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^4.1.1", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "86e4bd05ca3a0bd7486f7046643e9be5e2d86bbd", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.5.0.tgz", "fileCount": 569, "integrity": "sha512-t1o4kP3coj2HIaBepJxGAc7HZ1fWGria/s0Yr9JUmNkCilyrnXtK209qn28vuZ4muhnA0uR0GMMXAMrLsLyiIw==", "signatures": [{"sig": "MEUCIFk0flp1elWo5VrBk5lqIQuCmj8lCcbRudVzFTtLSPw9AiEA3E0DCHtwDxrhVcuv+dkChEqVdXbjDE+pYS4prtTPrWI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1325768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgLCsACRA9TVsSAnZWagAAksAQAKG9VYsjSe1rHXfVg7Ss\n3U1WC2y9qxSTt33zHPU6UYgBYTvDZ+a1aBDGPP+TpIPxW2LCc7AX+I9EyhB6\nnDyeGnAEFA/AGoGNkIJeGVKS/tilUxCKTfra5SaWZYGxYLmu8fT5xIpsfCcK\npa+0Qu4W/MUUQssn+InzeDvbNXFUkXZp7UEg7EkVnk1/nQKrKoomqQMmEvXC\neUbz8C5FQd5aIYtA2eLDl2mLPfQalp/hkcoz5ByLW7bXreLKqBaoFNKRJtve\nQerpd4Wkec/qQE7h+bhFqbzm3IEIRvIFnnNGPHpdDkVnDoOtJXGdUHIYkZ40\ndI+7SL+IUqdDlaqiatapj8tjD8YrbBFa3jlHLJ64zaQUpcdAbiTF/uuDTuKM\ncsCi3/w1ON7Br3gBvkYiGFV1xer5VwgcgOP01zwWCbmgdsUQxjVSEwrTK20L\nhDiZlj7Jd06o9lpzKyyQ8UUZ8b3ZnSHLzncpI1aHUwcEUWxkZcdUJp0xRaGE\nx+sTrRcfsGuzZqBo27qfkwXAiv6i4PHY206tYvbhQwsNTOmuK31SnzYcwHIK\n7H/6O0Iz4Dss/svBwmjjnSWLrptSPamK1Kbhxv7landeh1oTi9uOLX9z9jmK\nFvCyR5HQvCzSu7zkBJRM/2RAvVRNht73unM0l2pNSa7fYetBB2hmdURbv8cl\nh702\r\n=mPY4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "react-bootstrap", "version": "1.5.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.4.2", "@restart/hooks": "^0.3.21", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.0.0", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "47cda280b5208c8a9f24d04dafd38d9d90c9695c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.5.1.tgz", "fileCount": 569, "integrity": "sha512-jbJNGx9n4JvKgxlvT8DLKSeF3VcqnPJXS9LFdzoZusiZCCGoYecZ9qSCBH5n2A+kjmuura9JkvxI9l7HD+bIdQ==", "signatures": [{"sig": "MEQCIB814E3uH7pPBR5ngObnObw1QAeCzRTE9eDdYo5KP4AJAiAqTUvs2k1OltaK/DSrbo8k69P4P6aD928TxtiLYjZtig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1327581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPnsBCRA9TVsSAnZWagAAOL0P/Rq0jEsgHjSLfnSjijWs\nlpZ8St7Ljw6Sqv2438KpeS2ze10/FbulwTCStES7ojzQ0u+Gdl7D5aRci2F4\ncHxdI690ZYhr92BAMRQNErX2VCHhHb7KpSUaA7FUm1Yp0Yz4Y2E+Nk3tG/0M\nkHLzPCDx/fxIfKgC3vFOgrVr0QiJH0SnNHDmElREk72vNZSOpwBAVZBcO/Im\nugBLBAOWiDzkOimZlCYcBjrAuyQ1XKqUe0E2UlviVOrinqryt484KOwx6BNz\nFSsTTi4cSv+cE/sV1XCHd8bo/c6tIxh93JRAjqpLJi9Mhd4tKZw8NCzOmdJW\nzYfW+9Lfl1ATJiH8yJiPEtDyfJkVLqxMKb+WgN7gIcBum+OHzK8YKibkFaLR\nwTDlrHP6szziZ1qXDJIDZuRVikSS5j7UikKQlJu7AibE6+cTymI2MDfrnvJL\nQdaeF1W+ZENGYs/YeeeULCXqLnSFmS6rRL4iiTb6JwL1Ggcv+dN4mfAYn62g\nISI5OxcbYjLQL9sfM9asdmqHDfGsdoXgrN4Lv4ex3dcmTyCOX94nI/1CG8pk\nEpp67CCmUCLDt3FXZnOgPEoYL5vQ4W1pvBuD5IT3NU2FfES1xsrk8hpBs3i9\nXCjWW1QiTFfhbw8UamxKNDiquHytrCRctuilOvLsyUbT4C6AfvzlPjAuz17l\nWa5h\r\n=zt4N\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "react-bootstrap", "version": "1.5.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.13.8", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "07dabec53d10491a520c49f102170b440fa89008", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.5.2.tgz", "fileCount": 570, "integrity": "sha512-mGKPY5+lLd7Vtkx2VFivoRkPT4xAHazuFfIhJLTEgHlDfIUSePn7qrmpZe5gXH9zvHV0RsBaQ9cLfXjxnZrOpA==", "signatures": [{"sig": "MEQCIHdcIqL6qkOU9FTcfTQuQKLq0TEErRSfWlrxgn5SRPtwAiAiL5JBGJ96RVuq6A6+4Kr1SRV4kvPSoiKQEMGiWAwInQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1277723, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgSmDNCRA9TVsSAnZWagAAlEYP/A6aZsioxZ+k9oFKog9s\nxo2fa03RG4pHYIC3uzY4dmT/1NORCVZkFaeJMpDrFt9RsBvzuyW8Tgezo7BV\n+uJt4IZ4EW9rpotyjtzJDMEzurnRqbKrXXwj8E0DkKclj/z0wlEAddf+3JWj\nG0BaV+0KgBDQOeSvqjfV7OTM3fYii7Nr6XJh1aYtN0qalY3U5iDTbzZuueDD\nQzocDmdF+Dv42AquZ6+XrTH7avs+jMMluGtoJ2Emyr9f/rXIoLi46jSS3VTp\nHk1+JZxIZePH9MREmC71sH5qOTvOReZ3x+FmHRmNBHFVAbO2sxTNTVq+uoSm\nJP50wyJlvaBIHyjabdtk6UYMhfB8WXkH5iVtCtJKEI4FMWg3ldEnAdtL+CXt\nfMQthS+EOU1BmL+TGNxhnU8DTeCgS5Rm7QyO1merkvlfDxB3TywiLyHzw1kk\nmA4OAtY3SEhTZEeoUod+nds2Y7Vw1syYghzSC9/Lr//QLE10gy/dTNM7tF92\nXD1d9156No6ov2kbdRdif3WJl/toQccuJ8DaWeWhT573+kyI0WrnZmit4PHi\nO8ChQ3ijKfCP0SGAL25Fhea4a7FFwpgXpKXQiGLwVaFSlTvUEIDVqAkqMGQV\nGtrQdWu8r/z/z/8Kbho83yReZzDtGHKlnBar2qaWrgrHiGp3LxOuhhjgNoEK\nGQ53\r\n=8zt1\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.0": {"name": "react-bootstrap", "version": "2.0.0-alpha.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.14.4", "@babel/runtime": "^7.13.8", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "5fcf14a87144371321d767b5e55d4ba194be94a7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-alpha.0.tgz", "fileCount": 600, "integrity": "sha512-PueJkfsNDtF3JMeea0jBLy6P7hgn34apWfhnllksix0iHYjcZQKu7wUzLWPQSSCRg6mi1XYcp17XKY32ev5k0w==", "signatures": [{"sig": "MEUCIBHmXUg9DGCYfm19j5YPsrgHk4RZxH0O5d/h832dENckAiEA0WevW8oR4pLHupUP2EZAUo3DH3e3Lofk8w4t5Y8TYQs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1297928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgTF6uCRA9TVsSAnZWagAAVncP/2tw8GqJQmtXVwEZFgL4\nNMQ+C4RdqCbopZSz2JZvqeCegGODfGvEUlP6rL56YP6diIgiwmIZjzv/V1RY\noaieMlDmWEAnUQwzKyi7YhnTWzWu5MMGogU4RITzlem/Gkb1f+o2PBbkYdQw\nmtkSOgk44kYbLcuzrOWJnUskRprK1GGnrM5v5l+71KVttLHfcYVh4kAk8ruK\nFrYf+ChZxfuYFueuXZ5Kn6VTX50z8YUlRI+AdEgypBY/opkkYOZEg2QXzECx\nh+W2J4OSdFsNcrlKt7/Tf72wJcq6MP7+P/MDEej6ILJzTFtCxKS1TJxjuNwS\nhagPeNR47usX/7qPunaTAV6dlbMB4BUeu8ZHGkVlmGE5nWLUmf94xYDsAecr\nC+43RzJ3AhDy4NsjT4LYbRCBzNiEHEXxXp4pi7UxgbHcShmS3vIk4EY3SHP7\nVrq3I9XK+czp3y9jy8dwVxwFkWJ031SSFD/fQl7aYarT0MiBaR+KZB2WFAA/\ngsUUG+gkrAH7x55HMtYiPkEcYRvCQsCpLTsO37EiaSUmhml32iCFnEdLcmQs\ni853I+83BYbQn8/HK8IQ/uTvDSQWk2Ds8lIN2WwCIWGnRPQgLaqpnP0UPVtO\n5sEDsn9yknL5UXJWmUAKABZaAr0uVAllnNwgWR+u/9LjEqHIC83kMvcMmc/Y\nqC/f\r\n=0pel\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.1": {"name": "react-bootstrap", "version": "2.0.0-alpha.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.14.4", "@babel/runtime": "^7.13.8", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "d9e1144e1507356ffb656a892ac1249a1bd95295", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-alpha.1.tgz", "fileCount": 637, "integrity": "sha512-zoIGDzgcBoGiKd/0Rgh5SRyvqgH5OJPMEtXp+1v4Ek8gZDVSPZf58Oy4yhXs30pw4HKBS0s4yzJZcGMFKq3Yuw==", "signatures": [{"sig": "MEYCIQDEkBpNqHHh+TpFgUhk9wZYB+Y3XZx69mZyr5QUViy6swIhAKjcBIE8Rk2luIlBlSxxCxNUynsM5b7OB/IaRu65uKdd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1353420, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgdSoTCRA9TVsSAnZWagAAH3oP/A6+3MN9mYl194miRD3u\nLEx+wY6iSndqSpyPbqvqyYDKna2Cz3VImef5E6nSvrK5WPg27tI6s2EaRcYN\nhrwFaejBu1BdIxsEkzjThMa3xJUsNv9BXyN+mUiGkZFVgCFIfcj67bmeRBJX\nz2w6lqrxkL4Fp4miS1EDncERXksZd5iZpxrvouMgJG5lTqKZq5UHbqyukUz6\nrzGXHuHabEUAVHh95ATUq5J9ZAa36zeXxwCAh+1RDryJcKLUZCqFgcJI1EMg\ni0HgaNoX+faNq4q42Ylupp0uRIoQvfdTFx7wasO/GqL3/HfxFAOyEGHVYhiy\nbRwXTedSR8C8MeONq1U2Bjv4xmB45Hu64fHLKhVePn5ecbjKM+isZn33xqYD\nb7LPUgSNxczyl6gTZ7J20+byUR67vLUUwFIlHBq3pei2q5MQaSPB6zGc8f9S\nTkG70hTEz3fMBNKIrbMgeljd8Fzxb7+pOYhQYvLGRAInFyoRtZtFhgy5nidE\n5+syvXyQlvX7GmjC2yRx96bxvYh3FNZvwr6cqktcVDfBRzgh1Nf7lO5YGU6r\n+F3Fnlk8aPzDq7dYs42tO41v+Ha9zoAasCH9etX2WuaI8WvvyMPNPGfae3KV\nXMA9G4eHgTPnvshoKmWnKd/G04U8C963YrIdLeA4t/bKDAyQLosUozbJr4tj\nARyO\r\n=oNnS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "react-bootstrap", "version": "1.6.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.9.35", "@babel/runtime": "^7.13.8", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "5b4f160ce0cd22784cf4271ca90550b4891fdd42", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.0.tgz", "fileCount": 610, "integrity": "sha512-PaeOGeRC2+JH9Uf1PukJgXcIpfGlrKKHEBZIArymjenYzSJ/RhO2UdNX+e7nalsCFFZLRRgQ0/FKkscW2LmmRg==", "signatures": [{"sig": "MEUCIBdAZlTbBw2/OcxA7JRPq3gN+6xPf6J5YPD89kURUKKMAiEAqx7URtOJv5dJlWHAzlGidUaI1KdEWNBuRMeLBdOrkBk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1340991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmcwXCRA9TVsSAnZWagAAr7wP/2ScOisMJ9EYftO4U5ay\nAsxzKKuYQGHlC4IcTjFh3pgKMg0Ju18YsYsvzCqZpkdblF8d45p3Wr6GOMiN\nkI2RrDk9TgRoamyzOcm8Xaq8V6oIpJ1h6OUYEQyD8746M0J+GYlDkO8PQazd\nZhR1xw/c1TfL/WmNyY4F1WVEr3tTufl4LIfTHzOaCw8MFItO3MGJWfyqcmaa\n+aMGzMjp04MlOgJAlM9ZkPlRFmG3Z50LebHRo0IdvoGscQ5XHdTepajT+har\n8v/l6g26D0jEWwWjuHbyOUrV1h7qjiRBzhzBLRBnCSnLwgw7Dk47jIQ+VzSj\nWGtyge25c6qpnrJoaOEzc//X7vvSkGH2yM3Z7sqSyNO8E2O1sv6HuepGZpNo\n4GaTz4oDR4yFjnP0rtgcEDjcy9gWhGYFw2up2sxPwYLkhDOFVdejM7xSG5qP\nMOYMHyWIn1C93TW5nAr/bVTLnpTYKorJNjZdhv3h/71v6vqW/5emu9lfGU3B\nQEro4ZsEG3sxCTgY1iG5fiuOfllFumF7EtZfKxluOUsmtIzfhjKtnGpioS2U\nObn+R6e/cHHaXXRd+2aHIOnrjylROsU9BGHbyaP622dxiaJYgrL3JH3t3EKo\n+puPRAPpKim6E3HAngDsBL45ixtlDVaza9PofZUu6qEsaikVqmI3DiM9uMug\ngQan\r\n=G/+w\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.2": {"name": "react-bootstrap", "version": "2.0.0-alpha.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.2.6", "prop-types": "^15.7.2", "dom-helpers": "^5.1.2", "@types/react": ">=16.14.4", "@babel/runtime": "^7.13.8", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/classnames": "^2.2.10", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "7ad65c269ef330fa78f34a0cfd36f57c56de84e1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-alpha.2.tgz", "fileCount": 659, "integrity": "sha512-Sjaq/EwI0ZJaN+yja4FLeJczvXuoYDxwNQsJsuzdpTAFCtCvTAh0P/PUA8n1CTxePfjbPzcnSLhKGN0k0FGdAg==", "signatures": [{"sig": "MEUCIQCUIQ/1qpSGrJAClBMndw+J3oRhmf+bfdoOZwE7NecLTgIgWIc46SrkPf2nWw2ec2z3KkOZYT11XyNnRaoXwvD3/Ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1331746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgof/zCRA9TVsSAnZWagAA7W8P/jUFwp2UyNQbxi+z5ELI\nMoD/dtm/CYdU4cTezZf0zLg5T/tRkA7XvU8WeSv8grvIT9z0w5MArHzf8drV\n8Fg0KAyXUC1ixEbS7Eoj6fbm21mmIWFFHErx5HVagBRUeBT3bmINf0o46Xi4\n8RMy4uZF4eS8emwHAWZiRJ+hD4JXsYIiYjuWi53V1bFGSs4A00FUwtrbXBqm\n2udaPJG3nFblgLfhBd8OluFqiFHCFdM4maQcPhNVKxLPomiQhZ84izt81JqB\nX3bAO9d/N8qDkgITy6m6aUafcwLyQjd5f3u3OlM4fuKB/EE17q9/usB+b4Fi\nU6WBQX58XfVqPoA+2RVJ2QWtOBYhVnv6ho4IU0sVBvX/22LzAx2hzmqZ7qLY\nMkZrp68uL0DxUq/Kb6RVDRJgG9tWQxWYsOnbfmaYRVk/8t3g7zBq16o/sTwk\njD1SeqdPCaSY33dZJcOOmFmcYJbDbqW94OpDcZTKC+ENSKDztYKjni7/g2eD\nrlNKOLIw+xw+mjiATOxr91/zl4rhK5z8cpwR21BYqZPk60kH62tHZOicEymw\nfYUnoek6CtJYcgsEFWFCJrhtiDWk1HK025MspUMbF9DShvdqnxE0vCOFTRFd\nLLLlg5ngBUKY84+uLPwJ9sXtJYLygVnhEI9+Ze9PJ89kZeYRu2h5n9YTKHGm\ntdBJ\r\n=JA3f\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.0": {"name": "react-bootstrap", "version": "2.0.0-beta.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "555bc901e5cca7aa4bf27b6a493af3d0c1cc386f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.0.tgz", "fileCount": 659, "integrity": "sha512-9grXyFdznG7r7HT7etkiKHkgqVVgasDFjkTtNCAx9xAQllAjKl6vuBGBVc3F+KLNpdTzrxrx5RWNIgd8ZpascA==", "signatures": [{"sig": "MEYCIQD3vv6GwyZWiO6/IlWXbf+b999SQM1AFlzvHFbiEtnN2AIhANR0NhTn4HYWWOhLg7P5ATxCtvvdsEdNtQhAOV/w9xqc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1332625, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguB+mCRA9TVsSAnZWagAAV44P/RV9hlXFrSf3ICbVpX0w\nQMGJlmNhVXtyx2dqpxr3cA0zM0jSJnHKH0hZZtyz43BCV92t5UTZ3LpCUFvX\n/Gk5nyJitKpG+a3hniJwIpnw765kxxIUx3ZbwU+VQ4qnJ8ZE1TL5VnarbXp5\ngl+45/5pXgi061VY2JoB3xuzSLADjlzB0KG42LPfSYh/nBroKv3DxVCW/age\ncDSuMRHgxaHP1cVLtRZ0MbqaxQbWltO45MdseBTFrQSUSVE5OTwEfePegG0S\nBBhCAZr/dHUx5ScNrSXGBLzTO99w9SWmCBOxn8RzOj5A0BTJ07Zz4fNbb8uA\nfqBvkJND8PuNBClGMnVXDQa8CMSCka1XbZjJ3VabouVLA2/QeIJgbr6HAPFd\n0kMUzUDcgBguI6LSGfMuRtXKOCCHZiAb02TWqZ6Rf5Y+vOZfzchRDeTp5AKW\nLgw2jrXXYoDvBOmWh+prSRywW339cg3ulg1el8kik60oUAubZYoK1AtovMRo\nKxNJLF9Hs5zfYtvl/BIeYaAeweIvc7VqR3K2jEDp1OyGcYLAkrFyndV936uM\npo0gqLx+yZC65EX46K+lWxnPRp9nbqY0mW7d2QYUcznBVhH0H5W+NS4h/ErG\n4Hnp5CvMlv3W5D3IS4QEBZC2gFa8WbRCik5z9PjnvKt175iNj65g6oSCMBYU\ndf2R\r\n=Eh1W\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "react-bootstrap", "version": "1.6.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "c5493c028ede885826b72eb31e66f6f0a52ab007", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.1.tgz", "fileCount": 620, "integrity": "sha512-ojEPQ6OtyIMdLg0Smofk+85PKN6MLKQX3bU0Vwmok/4yNa8DQ2vCGhO2IgHJvT+ERQZ4X+gAQcdn6msAHSwLBg==", "signatures": [{"sig": "MEQCIBaeyYZPMYsKShJlKwgVFxgF0/aX19AzzquJYvUCtXYZAiBAYsW2c8sLjcRqpFp+OILn7cP6h1EBNYUfzBbwL3KRYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1357787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJguW2wCRA9TVsSAnZWagAAUU0P/jcmr6hrQCu+au0wgc01\n/D+mSMgGUbijDUyjjJgfCrzMVph+h0N6o0CTPzErZ0xPBhTYtoomeMEb+Ez+\n0DSy1CifrVxVImyCbwh5BdgrlkPGlQ0kS1V9aBdEKhtblfja88p+AwxH7YFg\n1SdVpbmAf3/QLnXi+pMnZ0tJpw7XETKYdEppndMeEdPB/nBblblt7bsAleBo\niaaE1GfsBGIH0IqKGg+WKG6DpK0CCZAt+Met9CAXJVRRWlW4M+cRzKeqTkmw\npKI5g2OkaX6ROzJTpatoLF/ZGd3wEjUeBreMHNUgaRoeNVZDSDT2Ay4HraLB\nWyzPTKoAqFvIlmAj69329RIJbbx3nU9bH3YO/VmqsEs9b81xtrfTT7LTwWKy\n7dMRog8Y5BqzhSDblj9KQ8uzXyYiCUhsXWvyg/KBX3mMsnhUQLdp+JCY3ors\ngS5nqt5vp3KxJ8pcQzecocrqdp+t0veSZk2sL/azoM2e9kvsxrQryrjPzFqe\nS3+35Ji3RbwGaKmT/uZL1DApbReUEAAGQSyMvXBmPMBj/5LfHwV0K6YZqffU\nmFl/IadPcbBwiOBZCGmdiMO2koLksQaDUP91nx79gd2cH74a7kLJNFZXSg2v\n7B6c2Dr9Z2Daj/PhzjQbFUyIr+3Z0ID6kOQLh/+9Aj7jqfzrYXQTv/7nYE/K\nsm2e\r\n=KHe3\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.1": {"name": "react-bootstrap", "version": "2.0.0-beta.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a77ba6d3f93727b727fd63263babfa60fa85c2f7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.1.tgz", "fileCount": 659, "integrity": "sha512-W0HMNnWLwgcEbC678anTkPwp1CkKciPlUccU4ToST24/eYPHJj9UVd3waD9hS1qXcdnaaG3poylnlMj8yoq8aw==", "signatures": [{"sig": "MEYCIQCpuZap/yDWIm7gjALCPQvP86qxCm4Xy3lHYdoif6Q7fAIhAJN2uI7ImhRWM9IuFJ8u/dIgBoU3Lm1Be4odn6orZXc2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgy+OICRA9TVsSAnZWagAAvjcP/1wTuwhbM8HqzF1p3syf\naW1EhHrWp7R5r2zCE+ifYxjd5PWs5MQphm182p4XiXyVhx9agITxRJG0yc0R\nPK7Fi/9aidBdY2/68OjOeZe+K1A6abkvd/nnZUYslCNU5izkTXBvrY9DdmzC\n/6Xl4FjFdjuwO2FgfgheRrl0d2UmbGGSjlJ3QTVJH6l9aAzYnVHeDYF7aP+L\nRbPwaA03rHu412bjNFWkc0T79rNGrWBMqUEJfwMTtTEa3R4dDEPTJ0O2k3j1\nEg/2TTBGtkmMIqWb9hzHQ6sWbAKuci6aR6f3C4QMAkmND7Lyg3c9GBQx/8OW\nuZGv0AzfmiEjSozQCnOKKSCcz59Gi+5NPkHQfumdJ262ItZsJVH+gR4xwYLl\nxMw+u0vXCnOKfrYHZ4ftuEhQG3bzrn6WAoJ8SjJoX2u3cd3HN4F7UNQdIlJm\nYg09IfV2vXexoE8IrRgIT7OvhWKWMp+Oge+NxkI4j9Zd3LKCPgsBh/7sLBwG\nS9WndWzCsDOyXRv+dn8XJQtJ8cSpIM96S+Avegpq2+YJ49eYQN/wYYoVqwVb\nxBzclgiEyOIWb/58U2lU5AsojewAL0skI4PE69XZAU7PWaBly88s5IG+TS4c\nf63WpkNNZ4uOZHmZkOAARMTRuSDQDD9lwpite22p2L4gIWuIXVBPQiAPe0Q8\nGQ1+\r\n=OBYV\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.2": {"name": "react-bootstrap", "version": "2.0.0-beta.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.0.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "3ad0ded5e854391eae549e759ce9d51f213b5a69", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.2.tgz", "fileCount": 659, "integrity": "sha512-2WqlAj54LaSWtJ6rsNs/uKP8kzomNrELymaaekajkIE6jQXPRNFe9mSIKYP/MtqLQGc4MYPBB3rywZ7UIvbV/w==", "signatures": [{"sig": "MEQCIGgm3Wmx8k78Y++NNB9Vzm8dLOVMXh7Cw/rk4QVgxJQxAiAizyEOWRr4YU6pl3xv/9z948qb9N67aKIooZRf48bXkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgzTZCCRA9TVsSAnZWagAAllAP/3erYpbaEMQC56phkdEZ\nNEy/ALvlB7tdCGs2oQjNO23TquoiB3fCJD0ceLg90OO6xG5YY3gIdOuSpew0\nYtqHqOK6EnZgwqLMq9xtcpv87SXvKlJ37C4AXrFkAsNX6sthY8tVy+ATUyTy\nLjYyx/Js+bh0WFgLIrKMmK9ocI+MZC/MlpMRDo4tEaiETgP2wEDQ64ItH5fu\nmmiw/vJ8AbFg6ApCj7w3/qSSXGa1QZ+y/Ie7vUqrl6t+FUmHwUQy+Ga5pVr4\nhcXt0H5H/GJkpCDAXOK0EulH1Wz90Lu32UA3S9jP8AnoRQfc+CDyaTGPuyRR\nWwqB92YZG+1XucFJ/AfMhClj4JtzG2/ZZDGeozTOYXqVoWoUpSTprLeqI5CO\nkeZvPiU2o5SZFFBrpGiiUCTg7HLXb1Eo8F1qPGGEr9msEyzVgrghrmD6AJcg\nrv4WAyggJv8cS7lvQmOAjhT4ALsCDV+ku2IJw3H/EF3sSW5w3ZUYlRdbR552\nOc+jAKzJOc4h2QIrtrGJxsZoO752fdn4vyYvSK6lkTNZ19p8L8gcMgf0OPnu\ngzeKNftA2yrQBjZwB9VLWrnAdPWk9lgTOXSOmL8FDEn8L/1xzHVoQuZmdaQI\nGkCy9PxTkpEF6lH8kuKa1nmt2TdEQiq4luN9h71hqPPExaFrA3Kko9l3wO/v\nCP6J\r\n=7N5C\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.3": {"name": "react-bootstrap", "version": "2.0.0-beta.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.1.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "9556780b37a16c79a923c41c39e5cdbbb31a6652", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.3.tgz", "fileCount": 659, "integrity": "sha512-+Axymk9JRsaqM75W1Hq4lgdKzesNu5w/0UL88k0iCwu0pV4nmKavrXYkZxBeBwQxiagkrn1Ct7xPnNT9eGuYuA==", "signatures": [{"sig": "MEYCIQDr3Glk79x2URAVA/Ewxg4ih+B4ZJrSx1A3IYsjAfs6/QIhANdIOsBVAuMG4oIe5VrrY3qaR2VvQ7ZP7r56e1W8y4+y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg8F1qCRA9TVsSAnZWagAAmIQP/2jqjoUeKMhhCh8L5tWZ\njeZRijM0UE1QMExrlytSFUAZXhh6yZroJ21fre0uVBhr8O9PRsy8CI5uqCdB\nt78gybABq1EoMdQ+nxSf8lhL13iqLIv5llkdBL/HjsvpHtqp0iYztC4mSQ++\nYfCLmvFCJHt3yu5oP26Ps9uNaLCIxX6mE4kOaiYEz33/H4Cx8pbyQT9WeJJ0\nQ2ZOhA7i7JzaM0/pZ+xgGdcIXNeJKVG2NyhG5HJtwGR5iu6iJlnRAe7Bius/\neHahV6KqdWMelFL1TnNRdmcEq3rAAluNd0aqLCeLuUK5iV1j8KU3wJ42N1jr\nmIABMuTAzB1KEsY9rgNlgBLsMKYT8pE5ngeIiI4pXpW1QBbDX6OKZSQ/9lRS\nlX6YPGPZ513VXS2oW5S3f+GitDGz4aO2v9C7bCKmMMXnSjtgkEspm6MSE8sK\nORG4rgTe5AqXvdMlxRaH6vkyoH+JxuLJRDQHTxsNhHMOcDaaZZ/1QFzMne1D\nwR3G+h2QyOHKkjkayxdG2G/2h+2K86AKb+tv2980z/a/qpnfJ+m8rSABW1U4\nEfZiNGmnb+glbgxZEeMlGwsk+ZqHFgw40J/yHHOA3PBBnTnCunVbbG5L68Le\nMetwwzXTpW/x5UeqYeH8CQqzrHvzcWYTuKUF4e+442kwJFLQ3Z6NXmbI0esS\ngZ3O\r\n=NOU/\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.4": {"name": "react-bootstrap", "version": "2.0.0-beta.4", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.1.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "670382f73044b7b9618292d041b9cdcbb53ef45d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.4.tgz", "fileCount": 659, "integrity": "sha512-ETCvuZV78orCqGg4XA4WrhytXxTcnvshPDveSc3uK2mvnj6IRQue5O5188xsazbD3nnhX52T63uQjM4HjeRBEw==", "signatures": [{"sig": "MEUCIQCcltKtppBEvQBs78lIT2MSJdMcNxgHSwD5ETI6pwUPFgIgaan0P3+DRdkc9sbl2CPEgt7XIHWcsoK3gpjbUnHbmik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9hXWCRA9TVsSAnZWagAAX2kP/3IzWcjoFyj6wRL8aUtY\nBdANOWN0+Z8zvK4xhrtpdkUf7TnL//gylT+dVOvj1Rmsaj95R36uJCleW3eg\nGEJvgVOkyslCOXMM9/pFbMAVDBaujdxvC/Dme9hmiVpB+W+Q55xqD04W5ZD+\nm0h5XlAkE7+rkzqRQ2HgdAgdvjpCvpStx7IsfjqDP0jdoABMla69cu9FAbDw\nhTfaG9M45sOJoNmPDhaollDKugOUU7+DHJD2TqZBb3Q2GRqicW/SS6djR68s\ndSI48EpH9/mhz4OCHkMtGxecAQ9MK+k7YIkwEFFkwMmzG/slbPswKpweoKzn\nqJNOmtp6PyyMN3/wuP1YEqGtGEq/MzsLqC7JwsDsnASVP2rhUp2wTrkVqXVT\nf0mR6zOuIcB7fpA8ca2pA1kjISSw58tJ3jPVKUj9b8HPdWXDSEMGXAghH5Sa\nmh8nN7uqDrwBZCRwvnc0JG4U4ypA5G720kyfVCP5pt7HyhKMLcp7Bh2a0Unz\nXaKeLL3YbhQzqBLDNDlhOwW3xZ036piBuG8HkuVPkKvH1NZMDRc9TPyuJ+tu\nVeJDRvZUF/kiFSKuTlNosSxfg5yCYlZz0Jw9CXRPYZJ8rm2PPhePDIhg9MV/\nkW6+4whdPlumiHembxiX1x93yDeG3cTZf82hf0fIdQCnOFprVf2NH5RclEsq\nm4Q7\r\n=yXOx\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.5": {"name": "react-bootstrap", "version": "2.0.0-beta.5", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.1", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "3bdb532cd8c13b360dd277102607e4ff7ca58013", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.5.tgz", "fileCount": 659, "integrity": "sha512-vMRsO3akQ4utRZkMuWtJ82g0vOqzQUoHK7pZs7af3P7c43zrBWzFYM161aKBAIY6Kfhc9Wn2Ms7tZcdpc3pszA==", "signatures": [{"sig": "MEUCIQCwN23QdT1QDzAqnmSa0ealVWK2Ztl1kQIt5njrD7xxzAIgUwltQyLdqXeYUNF8PdIv3qwcbE//fZMtTnwvmc6VvAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1320992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHExOCRA9TVsSAnZWagAAHR8P/RJB3KLgv8ZiXnH674c9\noX2oLhjsBKhnAGiULx/STWNneMa1WaX9MdBkma7MKNJjtXxSV2cTq04S1rpv\n5skx/4swWYTPUKer6jM0mBt3UMlNimqQTGjlbYSsTm2qs+ETuD8qlSQDrKFl\nwrMFVuU9yJOvcDDxb64XVPsHCVcp8Z8PGEuLsiReIAND71V6m7R4qD4f0Bpo\n1Z/O0may+hhbpBFPPLl2GQsjEVqZzM82J2LWpm3r58GdIEva9+V8pgRnlkGw\nhznGDNmuz66AbBKHH3W8fLqVM3WB70wg3Keia7IVZjDO6YmU5Mb8rMavM8iY\nZ459EuGHUbLjXOWbWNtLrD9cd4qtLplAX1pR+pqTKFebIUU2MVqg1lRAidQF\n72s8DUt6q3NK1BcgXw6HaEitEjzziykDX+UkrztY0iaiZoJa5vMIaId3VQ81\nzPt4jNVF0RdkOseSD7ErgBcMG+pEo6vGiC7/O+EM0Sw089tv95uhcaU3a6vn\neA3JJMJ7Ds4C1VxzYb3uXnwt0IqGe1vaF7kxGPctTTuk/eYeQNEcy6vF57s3\n+nlxLadL3BwbxVrxV3IeVZDaJHNP5dme2Q3F/wFXyHALqO2h5zmsAvpWppUo\nw1e9veSYEU0xhSDSi1/2UKLfJypIZlkBktLvRRDQlU7B4fe912PXIiKk0FVT\nK3zB\r\n=WIN7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.6": {"name": "react-bootstrap", "version": "2.0.0-beta.6", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.1", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "983e0e2be718384aded800de10e9aceb581a0188", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-beta.6.tgz", "fileCount": 674, "integrity": "sha512-eHEkmESWYYgNLjqbz31G696Eytu+6GeF8CPHQ8t9Se12dUEej8OjBakyQP0OGms9yy1ZZeLG/Fvuo7VxiwMcuQ==", "signatures": [{"sig": "MEUCIApLWqg8AwsvjIVJ+fxoB4w4ZijloJZofITFtHPo+haUAiEArb3FIW+p/3rMRnj/JZ2ZV5XVjHIDHSlfTGvxOeWWvAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1337898, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJUsICRA9TVsSAnZWagAAUVEQAIUonMvXyRw/6jlE4zOB\nopJuLmINl8ZTJtLbxXJ9dIHPn5dVFxl6LQ8AkEo4koWBVKiQT7/CkBRtQU5y\nBPaIpaFJHiJTpQEJwj+vC/oDYeh8LfGN+VP+/B2tOB3zOdbiT9UzBLeM/95F\nqgQ/gEq+yXMzwafeGUCkCfKwub6IZp8cPN2GSppkJcoJ6P/nbhdni+PbOgNb\nSBmKERrlUqEhwdbosN8htNMvTYXV/y7baEXsmgxWgu0IkXqEDhMquwx8QnNk\nTy0EJcz5wUZPrMq7Xhx01n1znsfD12hbNf6cR+Pt35W/IheFvzUUCZ+82re4\nhG8lWAtNdloaktPAs03g+Zcbzr18lK2wqw2LGHuLFc91kgsURwT5l8BoqOta\n6Pb4iKLS+dKvahIP93cW8CigEVVbvXFMiwcTHRUJeqHapMniwj/3NSMFm6X0\nwRss76/TycyhtcomizFBLiHdpWheEULC6eTJa3LsN+jTEaoDRQzqtSGNf/nX\nt1OcVZ+uuBFUcsTHRbRfe31ob/YmbUM5y9av1qN20nqaDKh407yKWzG1+b9l\nVG14vwiSaSIU6w3GaqIGC+z7zP9g0V6W2EjJKiCDhoHqgKBpVEcCQOTLkyWY\nvFivPDSAEYygAfucMNVaXOeRJ1EcVT4nH8L+jywGEOmbMbyDVEw9Va7ZPMkN\nLDXy\r\n=Nb+Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.2": {"name": "react-bootstrap", "version": "1.6.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.1.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3fd98430cc44a68ecdb446c9ad6812c14988759f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.2.tgz", "fileCount": 632, "integrity": "sha512-PZn/LqfMQ/bQDukWOw0vjXHtyrVPW+8R669O8VNU+jpt2m7KlXb+U1CDYiGmGePkVDISt2qTKnEfJhgDzB8rng==", "signatures": [{"sig": "MEUCIQDh7RHZsKBSby1dhHI3zdnz0/oTj5NTORDkqA4FgMyvDwIgToulAUYwwahGvYXpnjB0JmcLTKM2tPvT5rFzUYEyL+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1359710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhN/oVCRA9TVsSAnZWagAARbgP/j36tIlVqPZwyQ7PJowu\non7ctLcBODqi/qFxSgzUTk0uJ1lIiuzLmV/csd/duBOLURb4iS4M7v2LrnPm\nR9CE/uCEXvTLZgesWe/t0zLHi4I/GMZOk9bfcU4iJOuGvkJc5m1ZzX2Ba61r\nf2ZoiozC9WgJ65g+XvizP0hiJAwaM30idVGhgZXnK7Bku3jDIP+tP6Cs/3SA\nCxM7hMl15XMJ2E7+KV48APBoLMY6tyqn6PCw4cYIcoQFwl8zDW+jYWiEmG9D\nSZ8dM0IPvl4Ky2Su9tr8tD4Zk4X6/W63ciJGiGppeJiucaofL4djX347qq7I\n0JrlogHWqzpvNjRf7bD5thZJhG3p2mnQ8U5rR815T7RlC8zH28duwtBcb24o\nUXgceiZqr4nBcYEZFdh1f8+sG/KaEcT1ZWeYuaI99g15L9c1HOOQgFQPcyv9\nh/V2flcNMDCi/nqXGt6BuAx8S2Yzk1UbHX+wkVjldjdQn6A+mSlt+e8P8q7s\n03cg9QDS3lruukVT4+vnXvVJhD5JiKtIsx7AzGkZP2TuTcDr1NY7RtILWyQP\n9r3NcAKpWciqMuRGOTa1QPcFWGuXUxuV8C6acXGILiGAZEiG1cvF3JcGagNJ\n9e7OTYaxZ9VHmQBWvbvdBFYeB6a+Mowg9SkmebmThotzNaivgBPLiZkga+FK\nbqs3\r\n=1Xml\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.3": {"name": "react-bootstrap", "version": "1.6.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.1.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "6c2b27ed320b95a690ac416ddbb11983de1f954d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.3.tgz", "fileCount": 632, "integrity": "sha512-zsd4l0g68pusOmJ/R5LhTfofT+9RniCwcZsMMNFGJo97d1vT1H2nGlbhLWp/j/pfeXXj9zzR8ugUtKkadcoWnA==", "signatures": [{"sig": "MEYCIQDCqFxqdRsF9w7USg6TSag74FmL5qZ3HvLMVMXPpvATigIhAKoqao/y5Mn4ejUIwWCZ0DIkZJjtMmxnv/2Mq9z46rFy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1359736, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOFK9CRA9TVsSAnZWagAABBkQAJJJR8WDq2eIUbf33wrs\nMl/8Not357rpMZs7IcVz5Y5U0tC7J+iBTZpWLPYHmrcFvPgbEOt7KbqLU5SQ\ndvD/OLGPQpdmQyn1bJyFQDcZVJedyYslR5vBW0pM5LFKROjxdyVsI+cCUYZc\nC3vKNulzplI54LyJEoOrioWf6aUUC2osZZQaWEQcLQoRhK/zYRTwbxLrvC1s\nuDNWTPKbKOzdT83nRf9UW1ydeV6RqGUDRIWS85VjI+rsgc5nStVRD9zZH85g\nlMFHkIJewcepqmoF/vKLOu8u7RlZ120AoIDCo34RCSSTMpEU3jPSXIytf0yJ\n0CnhOl0WiFAEERLtTxEfF7q6aEWIKMOdu9UB1Kyml1LCIP27H3APbgY34hr6\nw0Axvdp6eOXk63mLePg3VbIfb7P5ykqBG2Un2LMJra8x6tZ0W6ArvJmrvqPG\nB908yBMYfNzY0T9jLpkfFnJRauHCzrAUyHKtgen0DA0Y4AYLlYpXScHxor2P\nXlqmy0lyk8AsOYYWWreeaFl2V+YZikfUhT/Qm40qHeFtydm/2J0HAW/diOcg\nmVUgBumafX/+kE3uCumpAFKX20xVLM2Znkf8QYJ/qymvzx6gqvD/wggp/SSp\nNDHYusukSSAw9hYZvf4t5TWPK2U693/V9dYXSS5WHrbpHDRccV/Z7F21DcoX\nrz6x\r\n=F6Ns\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-rc.0": {"name": "react-bootstrap", "version": "2.0.0-rc.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "490c6c7c28fd5f94d060691f086cf675f75eaa2e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-rc.0.tgz", "fileCount": 691, "integrity": "sha512-E2z/FQ/sDUfyFTPxNwd/BjUq319151TAQ3vosArgljBPLMiEAP9PHKCqxW82auta3IcM1T/zEl7ETU7nk3V3aA==", "signatures": [{"sig": "MEUCIQDnemxEAoZQl3I/xZXxYiJadd5t4SxltnEvOWIFp95upAIgcnaWTzbYngMSXgx9+C2DoCEsaSxa743KAqDIEUWSNLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1356314}}, "1.6.4": {"name": "react-bootstrap", "version": "1.6.4", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "react-overlays": "^5.1.1", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "94d5d2422e26bba277656d3529128e14f838b7ca", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.4.tgz", "fileCount": 640, "integrity": "sha512-z3BhBD4bEZuLP8VrYqAD7OT7axdcSkkyvWBWnS2U/4MhyabUihrUyucPWkan7aMI1XIHbmH4LCpEtzWGfx/yfA==", "signatures": [{"sig": "MEYCIQCWVWscBv0DVZg+8y2x5CMV9iOzgE/4JPZTstV2tXEUdwIhAPkOzW74G/XBixCPzGEKwWA9TwpbtR6Lv5Hqqh/jp4o+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1362299}}, "2.0.0-rc.1": {"name": "react-bootstrap", "version": "2.0.0-rc.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "c216b58ab53341a4e3870dc571c9d4bd82249b65", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0-rc.1.tgz", "fileCount": 696, "integrity": "sha512-iKKtbEZXBd6bZ5ES5yh1nl4/gykGktL8vXh/VP4fHUZRUjIKnpvQ4F5gR33FiDyEHRXlKIlE3LRtGMZosujRuQ==", "signatures": [{"sig": "MEUCIQCUfOuc9Uh/Tz0eXdvZRGRlRWFQjo7s1K+qXufKGuK5PwIgOV6tvGIekqzSvGaO9CC97z/faZ6rca7316dN4h9kYzM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1362256}}, "2.0.0": {"name": "react-bootstrap", "version": "2.0.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "f0fd0b4638f8d5fc3ee197b5550af619b723632c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.0.tgz", "fileCount": 696, "integrity": "sha512-6nIIPxv36tvyL9T+7x167qTv9oU+wEwxxNgqcQSsbuMuwvXfg2Frt2mRMX3O/f0HCQmBu5Syy6UdClRS4fGqQA==", "signatures": [{"sig": "MEQCIGh1Uj1JDzpxGs/pGguOaI5bp2uOgzCjjNeo/h6ISgqPAiAZ1AxPObyWjQhulwILAwpmZY+8BWgiK5sHupqzcD5H/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1362246}}, "2.0.1": {"name": "react-bootstrap", "version": "2.0.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ed9c8e6a77d13f9f5d5e63446f2c65774551659e", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.1.tgz", "fileCount": 696, "integrity": "sha512-vx4fi3mHWWIzaAOJEzFfmTn/GHtirpASRk0C1PDBW5OZAt/VxLgUt+bvrSSh83eRmInq3wy8mERJMwuyI61KPA==", "signatures": [{"sig": "MEYCIQC1BccZ7LWKvXUkC1fazvCpGUGT+19PI0te7dmRIvjJoQIhAMxfEseq/9T3zO3la6PvbBzCD1h+7BSc8XERXTYYgULR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1362286}}, "2.0.2": {"name": "react-bootstrap", "version": "2.0.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ec3492513066038bcf612ab8fcab46f88d254ab9", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.2.tgz", "fileCount": 660, "integrity": "sha512-QuMqJ+WJmd6dUyOys6OF3nr6T/FjUVAoEMbSjsFrwVufJtvMox0SU1Dvz/cDID+Dl6Rz2RLcJzyqkdl+DEK2Gg==", "signatures": [{"sig": "MEQCIH2hPR7pUsotsDsJsZskBzq7DETvetWR9VqlYqzHexwBAiA4XqhhO3m3ITqnHkMWaafl1Ox6/udQpafbJ/cdfHu0IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1348258}}, "2.0.3": {"name": "react-bootstrap", "version": "2.0.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.3", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a53ee96aa5e1c205d46e73ae78c374a43017df74", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.3.tgz", "fileCount": 660, "integrity": "sha512-gIRzQf170SGKS09azr8Cl9+8FxBf3J9jyFf/8sWNWXtRcgbRzTog9bFCS4ua5Wv1U/A7W7hRlnrKxzy+wl+5Rw==", "signatures": [{"sig": "MEQCIECTXGRbqRl8SkR5cO+chSqzBfUpc+qQDad7fnrlKBksAiAXVNZ0MZnB8m5LJVLxexQHiVZNRms3ax1sk2FtOEe6Cw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1350120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpswxCRA9TVsSAnZWagAAHiMP/R8zcFegfveFBbvkg+s1\nKBq8dr62vzZx7k5vbClv2tmFIqlID7klJ1EVhLhgvsyUr9XJwuBh+QyHwVP4\nr7nQP0z6spuoqBpp6/BiarntNSgjs8zrCmGsw+SfeyQjGP4uGK1UNzG7qdYm\nEM8MDCJcLRraorcyUZhIH8FPBewoREdQ5xM9Xb1Fr9jGMRTIDOd+bZzt4X39\n9RPkBh149IIif1NHbizBTNFx+eFafKDNT75S2fydOTpC/JjhbgnI81MFg7C8\nAiZ2vwyNBKQeaqJxCI2YUPluZrZ+6wP9LZT3MdbbZwXZV8qgz2+FIDsReSXQ\nQKbulW7yc/PJx2iXOkkXn9VvUryXBnZ6I3plSentYqH4lcmDWjeVZqyI779b\nPWrslzIz8vEb1s8agW3UdriJs3bwV40JDEJB+o5wfY76z8ZBAIUOtljucS+O\ngUhx1QyWhb7AphyPHTFz6yjsAeRDIgEq4IuNsluW7Rh13kGbLJhTwYqvTt84\nWDwjYFSXGAjc+5xqHsFXtf1Wd/+vdNxjDl9mDoHW+fyL7Iz/e1xamgpqXevV\nz/06/2Q5MhdnopchB9lt1wBItKPzzo0kic1aBCRZk5/Xs203bFPrAXxV8iue\npePcjMJUzvoECCyBMx+YUzQ0pk9fkienZDt3J6xhwJBds3a1jS5Ml/Y1vfG2\nQW9j\r\n=bkjF\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.4": {"name": "react-bootstrap", "version": "2.0.4", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.5", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "dd0f4228db2093ec60663778f2a3690788773b45", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.0.4.tgz", "fileCount": 660, "integrity": "sha512-sfxhLKY/P5oeqkcxI4Q3SkJKLBq/7pv1wGykGlmCLWp4Pll3HMVl5VRtVbKsE4FzGsZGhXXauhi2HhRbmWLwBA==", "signatures": [{"sig": "MEYCIQC2qwzoc9ZPmNEcsTQAr8+oa78BLrdNNYxF/EU2BB0tngIhAJARrtWWjyueaJWvJQTjYxD2zSglCXDOSR7jI0BNvFJj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1351389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhwC5yCRA9TVsSAnZWagAA6BYP/1YYjxD0EwTkA2IPO42Y\n75GMDZIWO8RvV0tGnryBaymkh8u95p0g2ajo/ADK6k3pLeuP7cei8NEwaEka\npBiwRK2E1+ds+qJro/56yAP0RLF+JWERVWNsyBzmpBWB3IYtiJxTvIm2XnZ1\nXtpkDHfj/sUk6bUwNPVhGRHwo5zwGqsM1y70L3YTs1GVwqTtBXy3Z5FkgDUI\n/jnUNxeon5BzAuxLkYqFSZJtcvJGJ7eI7CjN5nluv6pAa7mMNuvllu6Vprwy\nTrqu7cnHuLF60H2CeZXWVfqcz+W3GjZKtfHJ4I4Bnt/KVQLT6Y7NOb4Y4+ix\nqzBLy/hP8VhnlCtQdrX5RGn8ThF96fC/4R15CQxaGroqvBs2uuFGNyX2vl1r\nvNhryAPFVPneE7hS8RPET4qdY+f3atzE6kOzl/eQ+/b7AgB+VDaoPyl83YkR\nEmlKkir9rn4DKR/vIB1M0hpsxFNATmQk6nWxZX0M4SPDanpiShExtObWdFbl\nfzWDcbOU39IUkodXH5/Md9UhnFMQfDS7xORywWxUH1HCYIY+Gr/z/IPcY4f7\nZ21UqCvpFjc0eUdcrlrkTzWjs+Hc6o0JEB/N0m7eI8n/SA6lHPOiztOqWPP9\n4yif1YGEbVDPV4EIIhQLjUnulVCptL6t3hnkkyUE3VdiD1BQ/HtIIxzFTxzC\nmfbQ\r\n=dm+g\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "react-bootstrap", "version": "2.1.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.5", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "53d24c2ede7dca005444e73f96f0c468e8ea3215", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.1.0.tgz", "fileCount": 660, "integrity": "sha512-ZbEq8in6XzoDh4dyrANVwqeqrj1oRqL56unlBYzjsvdWPIaBP/B7qLMHCvnwuzpANwMrh/hSNpIocKn6rvOFIQ==", "signatures": [{"sig": "MEQCIBvvkLF0VIrrIy8jM+5tvyREhoV0pBAIQDLYi7luUQroAiAnrR1D4T2QIdEo5haxpICzOXu9EhcldTNoJQHbnch2KQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1354787, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzlQQCRA9TVsSAnZWagAAjhAP/2a/sfOQ4vUsXWJ5Npy3\nMxngwcFQbnn76W04DD3wNfSLAzcbQ7lOVyWM6+rpLi9C8eYPosqJblP0KeYj\nAJxuVFndfce1/G8BxqRCeTTiC4TzqHk2g5rmsUTkTFhSFPLms8QZ3LQne4Dm\nBNjohB3QHk7aMxYwWmmtEupOaitW6kMbp3kUZj7pnxQDqEiNs2cUnC9Jp3lK\nEUFpiTm/WPsR5KYZcGGaRYnBextrrlR7Ck8NHX1KDNsYvDdm+wiqHM1JykRr\nWpglqeag0CUNkD26ja/RoiO7jFcb3+72G6Nl3lWXO0kdwxZ9ec1fd3LEUoKR\n3D6QjTYGj01DWihzKrwip3O1MTw/4qYe+EkoKiC3kErbcKvdcLlbeU5cc24y\nO0PMnIhCphVjLtzjSwKXpa+On0vAb7V9IQy8789eaV21ksO637gYyTFgtHkz\nn2M0hGv80GgEJRKrSRidXHoA7VokM/9I1neBb4JPlCgDNg5j0909z9qd+Ayg\nGIbeRQs3S9jxel1tDRqFjVQOzhxONj6xUILYK1UM2qSkGCLv9cRER1WBMwKU\nhJS77C4A4Rn3uGkVt+oPdv9J9zk+x0sALicEDWV6p39nqggjXJ4BT+1CdEsq\noc4W75W+kxmAVqCCLNYeettfw+h4UaLImM8Cd2LqACvxIsQGUqctJoP1iFS2\n14B7\r\n=C/tf\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.1": {"name": "react-bootstrap", "version": "2.1.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^0.2.5", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a2f162fdda5275e89712ce4ca525ace661a48131", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.1.1.tgz", "fileCount": 660, "integrity": "sha512-Igagk6LziNW/HgBlMVx+QiwPVt/oqrZ7tiBKgv31VYc/56kJEU0Y+BCJS6hrQP6QmmIpdVtX8TRaanv9xsmW5A==", "signatures": [{"sig": "MEQCICC4187Z+dvTNctj0To7EztPOPK76pxZpz5adp5jIWWAAiAjx+2I7MMzFjuAsfav4hzFXr15eFnYrGj0n1IXTa1YTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1355534, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4MkKCRA9TVsSAnZWagAAP2EP/0wIl83IH1t5w2M0Z9q3\nlmTkfAll/7RU7SWkblDc3+jICYgKd4qBh4mjGiJ7R6edcXRYuNYta76qlFXp\n2Ebe35nyGmCkCl3IOR/QsterQyRZN5AjKGULwPXSBnqWGV2TV5LXxWs5u6Cv\nyzxZqp2KUmm0H4/OCcpLTAbpmlHLyk6JtBfJGts5a8B813f32nQ6q4AuzZQr\nuNROLfjDkCLsRMf51vxvY3X5adzVK2kW6uIHqED/zxsQ4xKWaT5fLpN6Fm11\nth09a4Sh8+lw38K4niKEBjjNl1Y2k8N/Ik3EPgcXSCinRy34vqxux3WpYB1A\n/PAqrHCwof3djNySVie36qCGi28MK1gy3aiLuP6e5cKav8i+1EaDsm4C8eUf\nQ/b8h0S7agPCLgnrfRrhi6xAyM9HH7pGKO+mZrvtL9IOUdSsXLItdiTRGKMs\nFUO8cMV6ZWywZLA4scMJ/VDw5iGmTOzDvh10hQi/ZA/3dvsfE64WhN9ABPey\nXfzC9eE2FPh/gfhoXTCq76tNZUT+RKzCYFFnmI0TYsppAQzyOpkRFlGa8Fvc\nTIDsEh4Bf6eusbUGc69enhjjqywHdx6ge1vSGoq/M21vqE3TzZFAN5BHNEGx\nCLhlj1VPKjGQ3zyts6FL1vCnIX7tuUoFaNU4PPQEdiqfCnBDHbq5YDzciBIG\n0KB3\r\n=xgJ4\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.2": {"name": "react-bootstrap", "version": "2.1.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "@restart/ui": "^1.0.1", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a81161821c351d5e0eaed5bb85d50659736f2aa4", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.1.2.tgz", "fileCount": 660, "integrity": "sha512-E7PR13cVsEW70gw08BWplENwn6PHTshskOsQygZqyc65jQlsnr9MsmuW/lgzAN2OiMBnc0KaNpuZ/FohL7dchw==", "signatures": [{"sig": "MEUCIHe0mXGseET0Gf/ejmn+Qb5eD9L4cXQRNGHqfDwqHZTAAiEAr6Fh0sDoc0CFeiSrFxL930iQ832+m2VxBwPgsQVggSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1357411, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+BnxCRA9TVsSAnZWagAAygIP/0/F86oakDdQz/Mj1g0i\nt/kkBO0nSvyET9Z+9VFI/0AuewHeYTPc0FG3UUh5iU2GvAZWspLXIYZmzduq\nQ2zzCJ1oVj8y41toNixMaNjQhOVN/oI5oMb7UVjioE/+eEREQuA5lr4CozpF\n3F9t8PvLqwlg3Qb14jFaOdxRQyT0CPSTvw3zsoIYcUMWUQEa2Naoe/eiwiiK\nI5Mwnj6WCYXhU0YLoWugrQ+FuYMVjUMsZjzTsyxME9Sh8hqwkdla8nCweoni\n7Jya8lyycIAEcB1ysG+tTic8+eP/o2jWbiVSZalH6RM2YkgzUaOlBGyPWloZ\nJ3EqR3fh0I35QgVNjQ58KQwY8YDnOvw4Tg3sTqTFVQugR/CIsRv3ld3ziJEL\nT1Vh55b4WKNwzxr1nPJFXAONgeQ2NokEAKbKQEZcgyx3g5JVGTk2ZtRbSyrb\nmEaZmGrEyAbBq+/6L5b1PlOlwAIuUYlj0sjQ9H6fwd+XL6Z6U9uYAst0xvPS\nUpCo5rJQ1z9c1I7vnmWSO929FcLz8EWk8BR9C2iFT1jNeWQbVl8QCPHcy3bg\nd+lqa/5jQ2wFh26wCz63fxtO5RcyBlVFeXmOKWcvmQllZyop8vCyztdCUPO+\nzqlENzyCqQjpFm9BIdOeYVOn99JlV85YDjjMV5OrA+VWqBISDlKs7uKPFQDq\n6GbP\r\n=u+IM\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "react-bootstrap", "version": "2.2.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.0.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.35", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.4", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "cb8557f19cd20b1b19b2c5e7d350b99104f91385", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.2.0.tgz", "fileCount": 660, "integrity": "sha512-5d3hziBjNOAKWpss2B8C2qtSXp/Z3PiyuRdFJelvJeTsGn8vbFYOoYjmf+0Agq+jh46TG+mtBvrCoa4nBk0bkg==", "signatures": [{"sig": "MEUCIQCAo1NohJMcerB3qHsAFsLLQXSCG0ms+VzSHuZQI3FI8wIgE8ZncjCqiXqDG74b/iPLoUiDnxJ87fbbHlqX5OqCgeY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1357798, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIah4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6WQ//dmDAGHvj6M8W5+v1YZkCLwVUm61LPrqHUz31yqfCnrttvDMM\r\nKmXlY97D3l0zAw/Earh/xUVtQ/2E7nInDOdrQQGLRqIfzOppCG5Cz8mJAY0D\r\nJ1ox1A05XN410AhzSer8j7V+GOfbeYztZ6cdEOJvtJhpld00yOqzD4XLexB3\r\nfnaTjI2Y8mRESCX3N3c4vNnufmsXZWAd4DQkfXr+0VIWmx6VfptefuTV3lmr\r\np6RcPYU6rm2RPXog5hc7Q0Q19YD9TfBSw5UTiwo5i2tPsOlecm5kVr90fvuU\r\nhXfq16jiAQ6EWuDPB7I6d84NoaNcSAypPCDgd2uM7yO87HubosyFkh5qo04q\r\ngw2Yez9vk9n30r6tKJvRS7iOkeoYkQeVt5ig7wHYSK677OuE0FGizYJqU1Km\r\nHJSsZ+4TTYGJ3jcAsdrYsO8GcNOpu3OooTVOQsrz0WKiSlBggf9bqtv37e7v\r\nJhzMOJCV4IUXEeBynS67TEmm8YpVmNbXz04bhKvvOiwS4bWPiOaKB2NrA/l2\r\n661bcE1qvjnbZXL//c2pA5A89+29TNB9bt2NxwCRr4u95kWDCLoCQkt8NER5\r\nJh6gQtXBAJ0kcMQhbNsfhmLozHUc+GbFMJssjUWzU9v6TXR7R54QG07K88jW\r\n910zoNqMHpajQeA3IgZz690bjv2AbqVnJgE=\r\n=GJTs\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.1": {"name": "react-bootstrap", "version": "2.2.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.0.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.35", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.4", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "2a6ad0931e9367882ec3fc88a70ed0b8ace90b26", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.2.1.tgz", "fileCount": 660, "integrity": "sha512-x8lpVQflsbevphuWbTnTNCatcbKyPJNrP2WyQ1MJYmFEcVjbTbai1yZhdlXr0QUxLQLxA8g5hQWb5TwJtaZoCA==", "signatures": [{"sig": "MEQCIGnv039OvMfCDd4o2qq8ZNfpwtljkkzECXQ5H8Yi97HuAiBL8uJJM6n1ICNC/tsxYJ572oYvbycB/FGSk7FnjS4ebg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1354274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiK8CIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeqA//XBCMxDOAFORCrjgKdm/LT09QbmCOObBDD0h/YO6F8la8UyZC\r\n54d0Oz5uCnYXZY0t1KDOkmaclzwaJJPL69VMUe2rU8TtPnoRluEFBidQw4px\r\n+2FIuqZ/4bhkY8tChV6usYHD2OLxF2SgCLmzwOj5BRqY2XFMJRN9fSXKnk0w\r\nEG2TUIAcJpxGQEvXzN4dmrwNg7LvfK59+Jb4899XxGnjf+XXwiT7zEWS+iIY\r\nVAy5bGm44ikElMt7sqPEjfrD0aUPLdvpChysDf9pIQI2S6BBN42Zozb2GQK6\r\nZhsEp0QmMOXqioBMgqORR0bflU4jKXpmb5icsUAU9FecoDXYpHecEoM73EFC\r\nCsBsNAdtv+pMaZyPiUNVwddoUfpegb/lTqV6q21tyHxx0vNtoV/5ALUFHeL6\r\n7KaUp86a9Grj254khF4jsKUy7unKbMg27iCoTXniy//rj7QGPFdw692segjX\r\n8zMBaRgBD79EFIvopb2MNps81p8QVlZhmk0UGsHdjwnGtywL40RbARbKK5vu\r\nqpkeHkD+h/YFf3f+YSqIlZDNpG5k9uJOGgt9CzswVfP34jrLvLuR+jDnCSl5\r\nJGf6q1F17dsxPjUdNn7YwsnmVB+a6kv0iPJoWAKN9gdohipdQ6XESv3zXNB0\r\nXhL05eWPFH5aMmft7HCgIfTS9xCdYiJy2d4=\r\n=JZ4e\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.2": {"name": "react-bootstrap", "version": "2.2.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.5", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.35", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.4", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "cddf53174df295253c471df54f131e1730a6660f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.2.2.tgz", "fileCount": 660, "integrity": "sha512-zfVGUU14BMZo7KqR1QHXBAqpbWa6bu4S9dZ6O4rd/hDZi1tiNeGkISbuBednb1TxyXrOwpvnvlHNk3OuYQNq6w==", "signatures": [{"sig": "MEUCIFdFFUAQWtDy9ZyNzHM1A7ru/gIi27MaYRwdR2M+3//6AiEAneFk018S5a67u0M7M0lFZU4z2yzTGwnxfgbpZOlc3Dw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1356392, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPfmBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLPA/9GvlCP20b8YDQ2J1QJ+b4gByJLruqDrRKRV9Pfy4jajtGliKV\r\nEAQV0KIbvYmyFAEPXhycJOGG5fcSjGXHQVdrcS1WYutsixyPCQT+2bI39ARy\r\ncRkQ7Dcx75taN6X5dEwq3uJQ0tzef6jeNZkg1Ga+da5DluzTuOUFoFOWPdDm\r\n3K6XVQewQUtCc4LxIqiHNg9HKmhzu2CdgfnAdZKOdrWZrqhFr5y2GJttNIL6\r\nKdkG3BLwl6ImIgy7pkQqd7ota8ImkkR1x/C2BmBlALF1XtOPFd1xIWHjk0Ht\r\nsaxF8mNRmH+hcVMwObweAbLbyD/go1qg901mF9mBv6qIolXTwWf+pApa6YT9\r\ntd9BPkHoj+QsFa9NDqdxlCX5YW97fpDUEEy351hOBvduiBjFnFjFOXq2C6rN\r\nORbMR7YnVZcOJhvWFEsbbFo2dHKg5QiMBk+wPiUSPkTxdk7eWT+zAMvJgPZj\r\nE47e2q5hOgMhULpVsGgb6daottIZfTtlFcKmVVSUkrxKF9/j7TpkaFwCzxVF\r\niSM7rYFQMrGnessQcFxBerszeLT2drRs1mK7u59UIsb71gPNPryuiNk5FmwM\r\nKFbSZEHK6/Fruaxlbn3r239Q+p2djNFLy9ZiDvB/9cZiIMWgueaRttz2W+av\r\n2fDZKAE3OTECFwHSWnXS7Hk7YoAD+4p7EzE=\r\n=m9qj\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.3": {"name": "react-bootstrap", "version": "2.2.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@types/invariant": "^2.2.35", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.4", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "1c563018c8b856071334dd5a35f25507185136e2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.2.3.tgz", "fileCount": 660, "integrity": "sha512-gXsAEBdDUHnOpJ2C+DDQ4mFt7tN6u6qWnTH3tqiE9jUvV6gGY8uHFp0iGBsM+yjrBwmR6bqCBFh8Z82aQj1LSw==", "signatures": [{"sig": "MEUCIQDD0iTMbLR4fcQsFYYvNlcP1QkmXKk4M/Ppn16gSeMcpwIgV1anqq0qBU0FroKGq9ZrjLoixDalA9upOo/IKHX/pGE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1364180, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiS9DOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozBw/+KwQHbLlSJi2hTgK0DWfYeUsgsh8thrzI/5pR1ABxhbmgQkgr\r\nP2fOZfzJDnfcMntQZtc75cjJ4gkkntJ51VV5X1AmpHEL2NtIPs6n4568BD+h\r\nkjMfnuqylTrr0HlV+zuKQo9QHE+haMMKIJrYSJF9pxpkslF5/4vWMnBTKMmW\r\nChNjk1GeK8vhqItA3G9CBjz331e1HXCz4mBHATlD7oypngyY00fq1FiJr6MQ\r\njchfcicJPMPBtMSCcitVutXDHUQN/Dfocz0pX7U7G3Sw4rBjtQPfEvMEWyas\r\n0Z3R0Uuc1HxwUcyc3PQvMHfioHHDkdANbyD9kU6CaxdD4Yx6NY15/g/fU5af\r\nZYa/yuN48e3dkANf16dZcprxPF0UH/6QLUySYjnUvr5F65MVBvdyC3zynJhk\r\nUzu3uxddAaFHIjRYyfGaweXU40RjL99ezS4LyacvFLRnI+uwonZIWYmi1lcz\r\nOyp0sLHIXvK3rapWZOeqge6Ywv3OpqTWZU7tJMsCbSQkz0Emn4J2+iS7xhoe\r\nGLN/YZOqaKZVApCRaL4UGmvZBPcb96G47zWSxiSUW+XiaAZdjXg48s4viZet\r\n5ZqN7IIwHNnEMIpN4f8/xe2GyNqe/h0YJPeKOmkk4BfYyAuLsHbv+DCfN0rK\r\nagH/nLem79lGGH2JtJOJ166foeKjE3J/p4M=\r\n=0QzC\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "react-bootstrap", "version": "2.3.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "bb62143f9e4d2bf0839e3e1bc8dcff582925ad51", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.3.0.tgz", "fileCount": 660, "integrity": "sha512-O8DU/R3CHLqj1IGfqZD1mOm9Jx6tm8wmfIIshNeaIdd5AZnlO7eOPF7UyzijAxNIogN0/U3U4tWIH+9gQYlwVA==", "signatures": [{"sig": "MEYCIQCdj9a0proCgZeThaKfkSaGdywPwi/YtaxKYjnCHgVXyQIhANjpG1FaZIezjTDrjnaPnNJ1vwK5efwoWCAWtxHnpziv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1363967, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiXuBWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqjhg/9G70HovV2PxKyS8HTIcgxwWJFk/A9Hnsg1a+RZMnAg82D3oQ7\r\n0iup9qCV7y8FPuPhtjgxwOxfo4cPNxlGXKXDe+R1aG7i9T6Qoj+JOAlflWWd\r\nPnZ17DJc0IWyEpmmDEzZTQ0/0hHis/3ug+YvIu44Z3VNDDRefXugapVDWENo\r\nnmESPA7YnF8A62cH0KNk9MBUpF8aUSj/J56Kqgpp060BUSDDMk1xUY22mNWR\r\nouWcv54SMbAaUMVZFLFhu5ZNeX6auqybC0RifQlrRH2RdOPMWDZhl5g0j3Iw\r\neLFyCzhYWzDqL+ZOkD/u26+wqHNNyJhjw6KChNReDa81kJhb5UM0mrvKLkT1\r\nOG3KNVXY9JJ5+3f8XYWDhHMMKRJiI11HmnlNCI9wDVp2SHBT27sB5fJFgqEl\r\nZ8urXVObOnWt+dKKG9yQr2XzU0lDahJlnqWFlDk9uB0B8eFfnqnYh8e+4fay\r\nKRVLLnqnKnkXbuVjgiU7Hb+5Yz3h87NFRE23NLT4HfAVunnc8snp9V6j7192\r\nCDRDNQpwW/htv1prddf0TayxBQPSv6Vv54zZeFdCboRpRQHnXxEVtY3g8So5\r\nPAz4Ra0PhhEM1i4s3cFt/gBTWpOrnp7ASMLaxoz9Axchr1Fmwt0YvB9X/mWi\r\ngOxwtTIE6HcUPJVj3q9uguOlc4dAuIWr4ew=\r\n=rNWn\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.3.1": {"name": "react-bootstrap", "version": "2.3.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "f14239efb89ca24044766e20cc829b7a264db11d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.3.1.tgz", "fileCount": 660, "integrity": "sha512-+k68LdaSS62Zc/1gr18NC9QpDk/wwhNk+90QgcTMYSA8BzlXC1G2ogWWrz2LFuP2FlmCtVjcr/UXw3mpdxVmWw==", "signatures": [{"sig": "MEYCIQCqsc2Eu9Tegoc6gWdsEeELpYW38/eGn5ZlTxZk3pq70gIhAIqqWdLSfW7F3Yjvcu1OZD8PyoZrk4GPqhyCk/4piTJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1369770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiadmUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUKxAAl6d+H1T0NWTxVlT2j602FZEsZOf8Z/TRqk7m2SF5vyHE4Wpw\r\nIxXT29R79+ajxnRN+22IsWb5LAUbaUfmJpaYRvkMIGVs49UbFMIcd+tS3v89\r\ngCz7HQLJXEpqbPySZg5NlvWGzL9WA4MZfNBgkeF0RxiGsaj5V4BvA8IgQr9I\r\n//yxzjdhf6ENjpwn210O605+yWzVi+4ssjymGFzqf41Gc6PmJJN94mx/+pnz\r\n2YSCndBbjMa5ToAfIEDZE4KHJ9QB5Y98kDzpK9SoZ84d1Yi6LEGs3uSWigZW\r\nHG3srKpGvL9kqD99b6mJGkliMlSMJT7Xj2RP81m815WCbe2iZNMwhrtMW0BW\r\nrAwiWAzvmQBwal/kJu+lc1sYubG5MMExVmcpGt/MY0uwsyEpFLHzm743FIIt\r\n47KRwAq5BttuKaA3FNwOngpKHj2wDPvAav6CoFjZyfCXeuzORUawWNHe6kLW\r\naxz8xMxD3bPHnJpdf1DRZuLo5s4EA6KNc8TEMwDKrOkoNlUus2JQyHI0+056\r\nfLf0xJFGG6XqevGwOVxpW0w9uqNIGmxYaBMyBMuOmJKwXyKnmGfNE8duBUPm\r\nMN3j59BBqirM27gwPau9bKY6EMne7CVHMNYfsb4w7EQlFkkpeUY5QB0hAshR\r\nW0qAIKZpS/DFJJxOJ/Sygz7+ZJHm6rf7veY=\r\n=Rb7z\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.6.5": {"name": "react-bootstrap", "version": "1.6.5", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "react-overlays": "^5.1.2", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "1e5e953a8efc4dc70897f61a6985bfa40f7c55cc", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.5.tgz", "fileCount": 570, "integrity": "sha512-l2rm5LtDI7JMtdGrzaxNl4OJwH0fMIJDlvwQ2TMvs9h9d0E4ELLpG3J45Pox6xUkpuFfXdWUiGazZXyIuv/OKA==", "signatures": [{"sig": "MEUCIQCP6CO5bT3neSh8oB5FlgSUrKo+EpsZOnC5wHVmXVEZNgIgZFk0axuUnz6EbgMxkCUTDxoqxX79hz0ncQLuxYymCyY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338138, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifBMPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmok+g//VH8qvVYPfX6nkV8T0Zmw+I86Yvuht6UFpgave0WGRqG9aPA3\r\n0KWg2h0dEf7BJZLG1SewMpz70BuEBo3f2WdeppVDCt4ikrvDCH3BmtHXLof5\r\neTcxNAQR5AomIHcq2uq4aklsK+4gAmY6xdtCMvlalcje6XkXf4mTCSPVB/HD\r\nCTjKokDlWilGcOfBcIodh2FNPx3RWZo9TqfYusNZ1qyfRkQT/idGumZvDh/2\r\nRELO7bxYJ4Z2k+EIQvnKgl4KxlssYA5lsuAkZgNPivYXDyOSW4wDOEAmQOad\r\nVxeHkqwJ00Ur5K0vvrvznWgsm1oc94hdHpegVsRwWBQeP2ONhOASX6jQQRQk\r\nyxxCEmrG6cjYsRljyEhAu7b3VkXFI+hupOqbeGp5EP9vLNdOX8fd3Dbj1sBo\r\nh3Az/dGYYGcpiwDJ5zV03orY4XpcnQdYfZRh2El1mKzM4v3MzaoDarAEsoMh\r\ngjZTPCuYtfhsTnZORa+dSs57HUBh7YFISSjSe6TEgSNNDvGaA/Sy4AGRSP+E\r\npFOEMvVBoxa2idJxLpxl8YwtU79FjAY+DEfc+Rv4MWwSjotA7WSm+dAyroAm\r\nbYa9zalVcrhtQI3SZCncXFrcPyOim5nLSfUqcg2o9PNmKRakgsiNu1L3pDt8\r\n1fgEpOdaEoJZs8POKEH/CYJbRrSrTv1/0P4=\r\n=hfQI\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "react-bootstrap", "version": "2.4.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "99bf9656e2e7a23ae1ae135d18fd5ad7c344b416", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.4.0.tgz", "fileCount": 660, "integrity": "sha512-dn599jNK1Fg5GGjJH+lQQDwELVzigh/MdusKpB/0el+sCjsO5MZDH5gRMmBjRhC+vb7VlCDr6OXffPIDSkNMLw==", "signatures": [{"sig": "MEYCIQCc9EFhLs8+qJI1TrGQ6x4TlROYdMws6TrMZFUtoZIBsAIhAIMNzjAOytEOAZIQKhOrOI+Exwgm5+HIZSwtdv8Kho+Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1370267, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifqhLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrgBhAAn3GQH3proABa1bDzX+/1k4yEpAm4KAY8LeoW/1GRxBlikARb\r\nS1A5smw5NVldwqWxlizNWUjemE1QGrt3O/GtF/VXuBwHj7XxtpuTshGq5vUI\r\nwheHL7IlxuTUkvv0g67cxDBTYsDtFUQF9E1jBlxmXEf5tx4ulN7oyByoRItI\r\nHpOPDHXkH/vVeHunlCnWcGfFkJrZCHwBtzcuYmiil3D2EbDL6ZKlnuGm5lJt\r\n5gSJ5S5LD3iiQCZndXx/M14XZafbmuRkHwaTLwEtf5j6AxerATXaj49LOyiy\r\niwSYzd9G2ftARY9T8+TnkPdG+tGM37+ATzvsVsrEW6n+8ZT7TQHvw6r1+cqK\r\nl73GkxoGhfbcTuxtX0SskVsZiEEwQ3HRmWYgy0r7JYLeP/gqPPQS//eyqiu+\r\nXhUqrzyvpLHRbRJyelbnqF4MDdR5a+QSmwPdAJKn90tbmSAWnNyAIjpZTODE\r\nsS3Ox4KRyLQsRjPf2BOo4rap0619q/Hc2Rsz5symw7+ujWVoe77K+Q0R7TDX\r\nk/XkoNXWPLUzAln00QzR/MQAr8HXCI4bl0wggUpxVY/0N7SC+t1CkH5k6gAx\r\nTx4zCu1c+I0V0OVoELXGzj8BMchDIwiSoppN6mGVbl9SC1wdCmcVe47WaU5V\r\n66awLyGOGh9wQmpgZDUXhhLHrSck+VvZ1lA=\r\n=kFk/\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.5.0-beta.0": {"name": "react-bootstrap", "version": "2.5.0-beta.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.2.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "63056fa9fc8d8aca67ee7ffda1fb3a8d8ce8c19b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.5.0-beta.0.tgz", "fileCount": 660, "integrity": "sha512-veIb8hyqhGyj8ymUpa0pYWCPujIf8PNXkbwmf6EnDZ+A2dC/B8M/z3CHqmh6N8CNhuQ0tPPssfHI4LXDx1gVAw==", "signatures": [{"sig": "MEUCIHFSplQ2HJXxML2MBWl3jcPZgOPxz6A9RYPH/jKdhjd1AiEAxowSj4h9vG/m0CjOYWV5BtAr1mhHiMCbCTV3Xn4Aw/M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1365878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio5yeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsAhAAhOmk8t0Y5QcH7mNLypUR3KN0T1Cxu2rT9zCcPLM40T7CXB3m\r\nie12pLrAoVEW6AOhb3AHbdvRiWiVcwS/Rj5jeSdV55eaamW7UMC8LP7l2+/4\r\npfbxA1E2DruJf6pGE3+0cNxDFfrmzYtYA4lN2gFxQfOw+JnupdR/tuNIjDx5\r\nTLabfpnEkfzfCCvBKhy7OGh4CiZ35Tf4692/GH52DnNkBUwifBYlP3U8d28/\r\nOKzRDkpsgf2BTB/xIRVy7E9N8JR+XK1EnoL1wk9rOZaQyqHRSrtz+LNKnnEP\r\norsxKwTyvWruBZLk3125+zCMmhv9vomVzW/jQRlBOfvcKMV0f4hh2G+w4A4N\r\nXyYj9TqCZuZzH/8yuAzJHPmBqx1OkDbFolKUmL4ebha1xmZHOEuvRHyTM3mo\r\nouhJw8OvH07ptn+08h693qcVpr4Vb4kByK6N5VfDWdosJ61PvYngggEznLt1\r\nGwiIqU7Plb3gWkJfULqgDMhuLfPsagoX0EfopgWha1QObk/A6z77NrLMuUn1\r\na9mZkMB/gEnU65pNw3oZCbGU8s5svEKGKUIzRgr7AzRwHAYf5YRbVOdLQk6x\r\nG5W4rO57W9UBT+g9yUjVbdUrak5MXta0xlYlgv9m7lwLnyaS93bbH8pzsNE2\r\nrc9gOEHfkH6EEJQdSmx0U/5ieIXklcmAd5M=\r\n=YO2y\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.5.0-beta.1": {"name": "react-bootstrap", "version": "2.5.0-beta.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.3.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "779b98d2d4d1e09218c56a54875c2011fdedabdb", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.5.0-beta.1.tgz", "fileCount": 660, "integrity": "sha512-brHUvpjJrNmShg2xjT3qNfZ7mKLEUyGvh+wp+IgxcFKrh7BPKzpbAtJYa2S1QJx5MuPBT/D0ywMFbz1LBY0Qvg==", "signatures": [{"sig": "MEUCIHPTceOAOGtXiGYFV1wJVuEXk9OKfiFxaJ43DO12SdI+AiEAmCaWgfwzWs05FVF1U3aSPiuHQOLkhaKZmHAyJVgzmfc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1387554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyJouACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbGBAAhx/BsPkpMJ44e6FGLN5iWMDg7OoYYCg3wJd3eorklE+WjeXj\r\nj1HEdPaVTZIY0tLdotj3dADD6k9Xa8vzbtSvTVSHjAPeAmUxVIaYLaf1tVLv\r\nksybh3uPzkHOgqE3BeCeKiUGAPV29FRL3VIb+eSNyPPM0Tt6gVzmU6e+V/LH\r\nQztTJqBhwbWP1lhAzJK8P3IPvg2Y+gWNgTMS+56RbnaPOmNrx5dkQp+4wrHk\r\n80mcKga9uVq32p/3uZjWMR/vMpeLNd5cic7yjCqsqxl/NvdJrVPQ4RDAHPly\r\nttXQjDlKc/g8DfxaqcKrHpCWl9ECPoYZQoNO5DAEqTvxhqVmSjBMJvLftbtQ\r\n4MrDknM2ueDZFFuDXkbaimP94DT8gm8X8VilB007U/3qKaOhXK80hNKEb/LC\r\nk5So2ua5jA8Gs+k+4ifcQF0QK8WDcESUuh8/e48Md2Ng2/5VeACPI9SgFzUI\r\nECOE+pi8rXuBHUI+eZ+LW3ML9duCfeFQarPfrFOGLZHjNLZI+DpRT74t+L22\r\nisMXzoJ+30L5t8fgBMOvNPVP8T0liXYfdf65mAHmtbIJGFlIjNvaW6Ri8IfL\r\nY0XangX4R9P/fFcgtzrrCOTILMoa857NaIakhYEBDDxpq43oFbb1jzdDpbr7\r\nEjdu5uce1l4rKFhdXcLpNRrZ1c4bk8LhMD0=\r\n=WQk1\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.5.0": {"name": "react-bootstrap", "version": "2.5.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.3.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "e25e649e37f080d38eeb92ad5b4ed562a1d7de62", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.5.0.tgz", "fileCount": 660, "integrity": "sha512-j/aLR+okzbYk61TM3eDOU1NqOqnUdwyVrF+ojoCRUxPdzc2R0xXvqyRsjSoyRoCo7n82Fs/LWjPCin/QJNdwvA==", "signatures": [{"sig": "MEYCIQDKIvzjYkDoGEWW2iL3Sw9GEbktEp6sRW/MJ2SzWwGsuwIhANp88NRf4CvJHFdarYl1lo8AP0FdYW79ApUIsUKkpdxb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1389117, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi7K97ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJcxAAkIWqKhYoFDsy96232Ky/I8PP3yeCpYotaJcC4SmKscbVwMYT\r\nTMCK7x+w7wbfjDdTRrpo3H9M1TwPShLoH3OvcgvdKeZkwCcoLmz+gSI2k/ZC\r\nuARNfXlBKUl90mv8B9Im0v1DYiReZCCa7NsxRvxWVpvRfmhYOreVwfOjHUfj\r\nv3S9qs32NAZjBuN9MauqwDxZNDNYqywzWuvrBxO2I7ADY+RoybzHmgqBY7pw\r\nBuWDUhjc5JReCsGurYxaqN0oEtbS7zwPxIDtErNA7WLUiKVezy7xVbhmhoEC\r\nHSYJsVl/9kUdp6av0BeKuiO7jE9T3aey1ddU7t6Z+fDhrfNZg/v8v2U1GEMA\r\nAc0hEfgfWGlwZdVWDti8BgnFsiBf+ZuU/X1KRq30NWFASTWDvM5cDxCg2P47\r\nKn9JoqmS4hUPWN7f4u0Co65vXgh5Xv3BpkopgpNYl+TnxvKbSVgM9Im5VELr\r\nKAehnSC+LSWIjnXl4GtayiLmo2AcayqfzdZKYZXt470zx4FCn46c7vi0ACKW\r\nVLpuIkWcOTLNlNHSHrlFymxYq2I017ZgGeT71uEhA8loHnr1dvkUkP17qDj/\r\n3r/akw2QsflLI66NDRTaSLd6pSl8T5374fSSTBQvg2U9k8jM3Tu75bCCc4AT\r\nvSsPU2Ozx7TJ3BQvuwAnnwEN7P5CG4zCdpw=\r\n=tVhn\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.6.6": {"name": "react-bootstrap", "version": "1.6.6", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "react-overlays": "^5.1.2", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "3f3b274f8923b9886008a0e61485b5ac9a2b3073", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.6.tgz", "fileCount": 570, "integrity": "sha512-pSzYyJT5u4rc8+5myM8Vid2JG52L8AmYSkpznReH/GM4+FhLqEnxUa0+6HRTaGwjdEixQNGchwY+b3xCdYWrDA==", "signatures": [{"sig": "MEUCIQCWBftkfVpbbmFkEDEAjPn+jUXdg2PnNZfWeP7riXwKkQIgbZBInbzWPCCd5Gpr6+f41w3rUg3Ar+ZWBubvCucWA00=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338950, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjB8xyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Fw//dQAM/VnOeoIoN+KHmO4orSyoVwWVe7Go4uy8W/4bI9O4fNDP\r\n/4kjx7RW2vR+KWkyGE26iibkQdIKbFjLmB9smKSEkrK2M+iIXz6lPYaXlij0\r\noW7zDcE85PoykXRDQUrEqE1Ar/L6OD99WrEa9ofaFovkl5RAGS+NkNAFsE/w\r\nbRSvKTKoRv3H/WjAfOQxtyPyGQe92d3WHCewcaozzG/XBpX521JOW2kuLYqN\r\nt8WBZ/3TooB3xSXrp+bUyP76powK5xm4VS/1ZfGosDxaHnqFwVe+Jcghf7yG\r\ntkDcZ2Y605lwxNuhM6/ZPanRXu0dBVZ+WDXqUd5AF7etY9XQuNDJBsmm9hBe\r\nvqXRiZCYK6RDKUqZ/ZMhFMA6McKdshaPXP2QzIvW/TKgi+Cjl54sco0V9wL0\r\nZED1ImZyezpW7ujMe3xBkxHtLyb7IFtYCRucruYsmusMRw6eLdSWtvQXj0/3\r\nbzy4BrV9Y+CuxGjN5VVO74sAiaxKSLn6rA20g17KPGKWa15H5gcOmS81S099\r\n1EvoAweRjexrtSXJ1G1Px2PTYuQ07IqLcAdCF0QrVPsd+86D8cq4aAxwkIX+\r\ni38QZJQN+hklA8noHJmoVH7J/IIGmKNDXFxs2xvyUT+miciCp6U9Fw2heYH6\r\nEQmUYhJ9Ku6ey109Rl+aBi4DzeCLJoLUBhM=\r\n=0G8W\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "react-bootstrap", "version": "2.6.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.4.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "bd125889cc9d2bbd7c89334a7c54980ff0a65b94", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.6.0.tgz", "fileCount": 696, "integrity": "sha512-WnDgN6PR8WZKo2Og5J8EafFi4BsABjc96lNuMNfksrgiPDCw18/woWQCNhAeHFZQWTQ/PijkOrQ9ncTWwO//AA==", "signatures": [{"sig": "MEUCIQDWFgjiifupMujmYYkYvIGrVmRR8kVFiMWxphBFGcDFqwIgboIrPZxtWfVio4FzpCA8u8uKf4wqTjONPSfJUBNTw2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1397629, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaCXjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZKQ//Qg8jubsGJabwgGyi6t/kdh3j6ygwBebSZlDCJxmU6hZjSrR3\r\nFzkSwu4np1AE9cNuYBq21sl2/IvoG4IFVobaKqV7mfisyfXp+OnyKxWBpAC3\r\nk+4EWcx5uDWaxjat+Wn2lu67k+1tnmjdwHVD3Zsyhup4ONAXfbAWJzdV35oh\r\nkTr+IzDeQb9jl9AvW62zhVmVvHZBbyUDRymBXE5KwGbK/VL8Mt3X6gp9qUG7\r\nCWrrxpD84BFKL70Ui7xvYbgDpCHa636ume6ArGFoN62FZ/xS7DiNmmlzRXFT\r\nlPOJhCmk6bVifkFt6rb0ltPGKfb+603QXFXeO4ywskGb25y7s3p9f5A/ZHuW\r\nfnr7i794q5E6YUFsH+lBRkaSi5RTl21xIXQxzL0X5IxJvfkGrjRs+WHZdyar\r\n5TyQdp5YK/tKvN9ZJXEgS9snVaDy1arLvZlwsmgqEiuzboAwD7lGzZbs+S+w\r\n9VMUqJq+4ng0JJlr1hEkcH8mbapXYnyVFsqEXdQbtiZOPKqaXRayJgghxn/g\r\niqY71RqVD+By8/sqKgMCBEtPErX5D+R437DhsdHfs61GXJs2DUl+51szTGgV\r\nmSkooGRiwmNfE97OFA8nB0F1ER4jn1LYNvbd+/+6+EsN6NITHDvu5KPWkYWJ\r\niTr6DIp+RuUKDqL6JhWatsuThaH0ny86iv4=\r\n=sbds\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.7.0": {"name": "react-bootstrap", "version": "2.7.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.4.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "4a8f0311bccac477dc925366763c536f46e4393b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.7.0.tgz", "fileCount": 696, "integrity": "sha512-J<PERSON>rn6aUuRVBeSB6dzKODKZU1TONOdhAxu0IDm4Sv74SJUm98dMdhSotF2SNvFEADANoR+stV+7TK6SNX1wWu5w==", "signatures": [{"sig": "MEUCID3HzYdOclrVQWalI5Fd5N0nkCteoBbFjQp3sJV5Da5uAiEAp/OPtfrlLZSvyqPwwqndH75QdRftAdtMELuh/x2DQwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1396728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjkM72ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOtQ/9Hyf/juol/DP06yI77F0xGfk/VJcMxBf6oRzqbvcIX1iNiQuj\r\nxONqAOqae4XlDHZNE22pj8+2q9ei42NLpcyJ4+T7cVGObMJoyGDAJaAwbr6f\r\n6qrZP428IuclSe3Ei/yiAGgnVfV/UqT+YAecRx3aLjKpOVTqFE0tAuRl7USL\r\nXYflPzG50MZfS7yyNuaqBXbVCg9YJpwDKYNoNe1Z0sR2dPbZnZVWBJ1xq2sP\r\npu0W/Icv6PCuNiPYELceJ8ZJ4O7EI9c5huGME/y31VsVfuPgBiQEweF/IYwg\r\nj/G4CSpGvYeY4J674sFzp10sHj80WLO5zaoxGuIuKr8obuhKjJ4ZF18n//IY\r\nDeXVGAargMKPQjs7pn3quMG7k2vINjQVTHtqs2QadIn5mwleCRKjKw1f91Pu\r\n/Hj4cznxQMPcvxpZl9dl1FXGHc25RRZsjHJyKpT1GcgWQ3te8cSRtfFjYbin\r\ns2nkuXk1CICnt+CM1CwS01Z3Ch32ykzlSx9yMzpbzGJr1tuFXrcdSMREUMK/\r\n0vb+0vP187ZbK66HOHWxIDTx9SR7OOATX7XHkLuSTAmj2qtQ2so8DJfU/A17\r\nfwrBEIRfrRqTEG0CbFtNH8R71BYPvxN/VWxxmU0m5ehdR1aW6zFa5phBE8Pr\r\nJYJJ8Z37HWoDCQ6hQ+cgwiG1vPu7L+H2bV4=\r\n=Vv1G\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.7.1": {"name": "react-bootstrap", "version": "2.7.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.4.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "ecb245b06fd404700436e9a2ef9a998767225bf0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.7.1.tgz", "fileCount": 665, "integrity": "sha512-x+wjk5ZkLR2Bw74iDx/YR0LLheyV72eehjnJPfs6RvkWQfJGVV89dceh1dhJloeazHZwiLWA4LHHwHNGONkUHw==", "signatures": [{"sig": "MEUCIQC3K2uOkYEOJq4qPo03ai+9NTjJ21nVLM47twmslAVN2QIgcY83d817RZeaxWqLBHyggqOMzDF1s6bNUbz0P4mCz3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1387553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5peJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrY+g//UlkWhPMYk+2gu2t0mCr2CW0WCGwzNc5ORp5AiUlNJZo1ltCC\r\n1kBJjaMZAmRSagEG/sLplWd/CgaNbAD11wSEWI64VFQVLTLnZ84z/o191oBy\r\ncmcQXcuPB1VfE1njLyD9/HnE09GZQ1YWdWwGISOOwgR/BYBEe/uOecWOAsFK\r\n0CCyPq6XpmyqN4vK+CMrpifvwv3kHSVgGoB2yUymXfV4SHNkPaJeGwH4493l\r\nct84UkgSetPrZA/AwvJSH2NxNHfUnoDqJn6AjHxyNglH2Qyj8/syJayL5FQV\r\nRKdVd14nqndBFxwDwVEB39Ivyfmnbdd06EUbztH0LA0N85InWsW4AhTsBSMd\r\nOHttoXEW3IIitwL5Fg/HmdaVrazUx8xstxf5YCs8sIeyq13jHcPfr1xjMQe9\r\nGW48aFirJBFPLHHrIEIKRcw6Ss0ct2S1jiDuxvyIaNqEvXrxBsEX8LjNCSr+\r\np20A9bNBdX+USJ/NiApnzJxSZXuK1QYzSQ7sUwA9817PJDiPLLPoNkB9j6dM\r\nzDZGUpwNN12kKUNPL3I5N5qKfOWIdK9RbsIQWZEI4dHIltuRh7istfc2JOyR\r\nv6nRvgCZVJlLV/D+FhrWkbW+jQWj0uo0s4o+z1x7VkE3Wpj0tzk2d5o24oKb\r\nGwP4YYYT1V5mQIgFmqfzldp1/Gg9VjXzVKc=\r\n=UQhs\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.7.2": {"name": "react-bootstrap", "version": "2.7.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.4.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "100069ea7b4807cccbc5fef1e33bc90283262602", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.7.2.tgz", "fileCount": 665, "integrity": "sha512-WDSln+mG4RLLFO01stkj2bEx/3MF4YihK9D/dWnHaSxOiQZLbhhlf95D2Jb20X3t2m7vMxRe888FVrfLJoGmmA==", "signatures": [{"sig": "MEQCIENb/xipfXcu3VK3Xqsla2NWyT2Fiwpe8b3gx1bg4vbHAiAkH1gcX8ZAB2QuJez5q9rTxFSCjwTFkNXE3WPEs3PlWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1387579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj6t9cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr1cQ//caIholbLvfRZzB3+bGV2FleSWzjod8IV1kvArb+3xE+pBjE8\r\nfxzLFvSFC+JQZXnZiwCRPTGJT9xg2dSPQ7ZqV4113P2wl3v1VamyZ8fYrPps\r\nEMPcVpZXDmdyS2wNsSYnVTp/VDrIhe0lGieFdp/hdsrhzMTkCPetdbVxa+8l\r\nPDPIIiaydbiEm1nwBVHskTpsiTOtoQF1ibNc1oSVP7tNYiivVLRSSqTEjdF5\r\nOzXSMVWPjlUE2pl4oEha9zEc8drIs0ZwWxAVtm14E0h1H7JwYVowhvcuZsxl\r\nDM+NzdsH+VcUEzedSCjOtA3MQi/3NilKFiy/JlahCmIqTpd6nawVe60J0nCb\r\n3fbDcMazNzf+LJs+wQhdjLvdP/BdrQCtHPiFJ65jHdA0BWPjjKsfH5gxRsLe\r\nnG651DuAfAPGzKRBRkMb++Xa1i/PxDRfZnyk2KRyybiSkSDQnMy3Bh3CQff6\r\nl+a/857ZclQeiOR1tnXXChYAF7sfxIAq9+hL6GZgj8eLabU61coouc5DIilE\r\nHuIgLmNfvV2+ZNSTno11BbyQU9dnCOobN6g+nHJwvehe2DHKrXLTd+UjVxzG\r\nD6f6yALTMnDnlSS/3HD4PIm1PbIaVuLN+fmWJY69s14JNP1XOHIQEE7DPsUy\r\nqDO8jIVYscgKIrCqFmoITbJlhFYGsei2T+4=\r\n=p/Kt\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.7.3": {"name": "react-bootstrap", "version": "2.7.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.8.1", "@restart/ui": "^1.4.1", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.17.2", "@restart/hooks": "^0.4.6", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.2", "@types/react-transition-group": "^4.4.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "eb464dd5e53309035a542db3513323864dcd53da", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.7.3.tgz", "fileCount": 665, "integrity": "sha512-MhQq+YENiMwnO8MblF96PYAVpb6V6slOjUs6+v/RthTI8zzNkAZmBe7EfXiRtQFvFpM/h16YK4ruaqh9zhlR8Q==", "signatures": [{"sig": "MEUCIQDe7r33ne/dacSVTB0dX0fSPQGvKi+5IcBTlsHVl4Qd6gIgBwEsRGvYqqcxCchazpxOWmtD3U4tbxwNp+3raOaLX9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1376955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkNjTvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0ug/9F4r4ZMYxHrcgBPXJBlxZdscUWRUtIJTr3uj9PAcGd6qfWRO+\r\n5agPacOVufEI+FQgK8Lxo3Kcl/nPVTVEVWhkCR6jCznBbCyS6HXoYMy1iOD9\r\nAZE9aDBe9UL+682pSzfBvZaoDVGqpqvygvYKWqk1/zJW0mHTwvug9ojeJR4j\r\nzAWJ0nUYHBp+4qDMzLHX7kqDFbji1V/ueEMDAzzRKLI0GC9NSmpK3owkRP6d\r\nTSPuGSmFZSL6hSmEmYsSQy+FqqMz+tl8C8HFoNYsJXS6n0LZgz9qq0jFsQSU\r\nf4CUG8xLZ/lzH8tlQgyNC0TFc4Q82pXJnp+pkjqTXlqhgzDzZvBiE5u40qMT\r\ndasnMkcWqZH6baUJSoslgQNrGk2GkoZR+sbc9nPLsytO1QFAriswRAxB7iEA\r\nYu9SuZfzPYaOMTZNVwA6xvvcr/8il4vn1tVD6kXJTCd9DGS+HdDNubNNFWOV\r\nBfRZ9n0i4m6O42xKfKElpz7XMBVNqJCsl2rD1v+DPQcwyBb73oOteoMUwSiz\r\nQsewZYoYi/bBPsmvcYNqDPdIBrXlw+6xZuCRYGDRYU1N0u4Ik+8Of+tQs+y6\r\n+LRk3A9uxD4riMZdqAMditRK1IdHimTSSvn3i4RiGAlSlypx5i5MxXj08fyQ\r\nPqjxv91Ypekipz5Vj0QWqqHWT4cfnxxxyS4=\r\n=WXe7\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.7.4": {"name": "react-bootstrap", "version": "2.7.4", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.3", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.21.0", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.5"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "db95ecdcdfae9619de14511b5e9923bf95daf73d", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.7.4.tgz", "fileCount": 665, "integrity": "sha512-EPKPwhfbxsKsNBhJBitJwqul9fvmlYWSft6jWE2EpqhEyjhqIqNihvQo2onE5XtS+QHOavUSNmA+8Lnv5YeAyg==", "signatures": [{"sig": "MEUCIQCBHr/uPNn2oFxst1QUuUhIXwWsznEeJOTNoZQsebZprQIgK07/ANOjbJBn52VPfoo45og+EjMUFQ41I/WIhSvL5ms=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1386889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOflYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLEw//VI0O6oPCGIn7lCISilYv+e/d/9nxqj8FEwXmOyneQmBzovnx\r\nE/vOx3BTFAgDM13xHGtlKB1pt/cwbWR6a5Ts0QH0q/R/AWc/xnlQQpxluvc2\r\nvXqkWKEUjaLCqCb1OYRnTD3udPI4KpcRZhNAif3BH10XwO+Vk/+pHrclZLVJ\r\n3k9cjdbNiErOLJ3mtUaEheyHBLgdT9OqG7eeGDFdc9oaBDaT3+km7L46affx\r\nE1aD5ViFN3h3kSqkTm4uvqgQ87FiNKsz4W1wwif5VeMMSqB2Uq/eCMowPg/e\r\nJcg6GN7cLRwgQayUe8/s7r5usjHb7mDndcnnRWzpq9qVu6gUmreH0r5+B26a\r\nFlrnjKxrxdXUGe7JiTsYfPZYrZF7LoWO6848dwCTcF3JN6fOAWa9JT5hs6Fj\r\ngR72AdLQSg0eAPathUXTVilnBEC1w0XfzWK5PpieWQlcmZrXrJvR3u9EMzwJ\r\n9Y0UNIoJVmZprWdOxVxNVJSoUxUwgKqUvsg2THHkvtJbtcmnj7tF8NSE8wUi\r\ntHLSGzX1tJ4iIBeCLgBE39aaWJjfuLpFqrXA+LncokEm8taadWZXy7lh1xZq\r\n221VmnHkcfBs4LhdpufkISKOH42qhPkfMcaz2QoxLAVjFthb63v+KCNCl5lV\r\nQWwWwZ3hoO7SupIT/sf1qK1mM11H3Z9TiOU=\r\n=Q6U/\r\n-----END PGP SIGNATURE-----\r\n"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.6.7": {"name": "react-bootstrap", "version": "1.6.7", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "react-overlays": "^5.1.2", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "7afea926cada8175d67df07b893dbad2998ae463", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.7.tgz", "fileCount": 570, "integrity": "sha512-IzCYXuLSKDEjGFglbFWk0/iHmdhdcJzTmtS6lXxc0kaNFx2PFgrQf5jKnx5sarF2tiXh9Tgx3pSt3pdK7YwkMA==", "signatures": [{"sig": "MEUCIQCBoI/G6EwhP1hvm1gAHlIiFmWgtIWcyKnSA/ERgJv7kwIgDSsoVrdbCw/FVeZUAJwgyxRjy8QDyrQvhygttz2BfHo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1338984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUa4NACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEJBAAnUeUFyVIx4U6SBjMsa/QGaQVySJwB6jVqJvr9qXvyvCFvLto\r\n2498J7+WwDugfuWuZ3U33kMeEwXcXy3PaV/srMIYYJf8jPcjEVrTYbm6t5gL\r\nAwiqEY6UDTmSf1E+DLbZuBGrcsYYRnJp3Z5sGqH+HofbRnfErYgaUpMAUuFT\r\nnfZ3YYAKkevV0N5A6yb8MIw50k6qU8mI+ppkRN2zlqUPMaqK27jGV7JI94Mk\r\nfIpSqHaadOvp+H3FZnrHxb6C55kdcA77KYSK46leAORIAPxEoWLn7lmM3T7x\r\nMR4RjB8OzygrCIJ5mzASrUA2Y+DYHZzTVu9/S5JJKbKdFk1o8gY9eVZyD6If\r\n9KVTo+atW6QHA1EuJMXzVtQAO5I5A4+PBtapKXkJKHWQmUuld1FP5J4BxCEV\r\ntJvR+jg+ipNJ2etbPOOGHUXTcclAQqLsyTzIChNyHoN0A14jAOxs9ATV4+u0\r\nCYnOY5Um1KoCxSTmuh+2CA4oVOLdo62HDuPmt7FzgKE1IuLj0zu6oUMj+eZ0\r\nndq12H8y4PMbcxPZhQdNFauOl08W+3qsvAjVrbk/CEqgN0PEdQxe6RJ3OEN4\r\n+jlDxl+RVoxMTL+Xks7yQdsEVFtKk6SWUhzFzpPsIG0R5Cksvsd0gdGc2ea6\r\nIENSacpPSiQdNpgPUf5cM6t8uLrdYwjM5u0=\r\n=x0ZC\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.8.0": {"name": "react-bootstrap", "version": "2.8.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.3", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.21.0", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.5"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "781f254b33090c1d50ed521b40697727267c6add", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.8.0.tgz", "fileCount": 701, "integrity": "sha512-e/aNtxl0Z2ozrIaR82jr6Zz7ss9GSoaXpQaxmvtDUsTZIq/XalkduR/ZXP6vbQHz2T4syvjA+4FbtwELxxmpww==", "signatures": [{"sig": "MEUCIAqMd1nAj59Q2shzikbOQs5BMuBrztDnhR0VbgT9S1V4AiEAw6qxNcHg9SeLqrs2ojscgHGQLZVIeT0/w1/vXlsk/xY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1402340}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.9.0-beta.0": {"name": "react-bootstrap", "version": "2.9.0-beta.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "fb2502a327ef7a6f03e8c74236b6637d4660ca1f", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.9.0-beta.0.tgz", "fileCount": 701, "integrity": "sha512-atmQhuRce7g/G+eclJn1eMZ1wbG6j8yYWrtr+Hlv/fVX0bw3QOUs62Q1G3qSqbkaZ4E625w/56GFR0rXf36Fog==", "signatures": [{"sig": "MEUCIQCwG04tTEr15Ir7yqwSLsAYZJjHfZgPMWES85tCmZ0IrwIgH1JrjhCRFqvvMSavHYfDJWjYgfAAGw7fTVh3CfxqKTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1407554}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.9.0-beta.1": {"name": "react-bootstrap", "version": "2.9.0-beta.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "0a3804094feed74c390506624b06e77fa7a83422", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.9.0-beta.1.tgz", "fileCount": 771, "integrity": "sha512-mfExCNBHYn1AthT3c8HpPhc4YvInCXmee0dyexbF0iaf0TtkklrDtbw0zqjXWV5tFcFlGQumhqLDESxru6CFnw==", "signatures": [{"sig": "MEYCIQDE+11gD8ZYH1wRzA0Cmo4qLvSvGnVhkUYSRdqOTBd2HQIhALEiZqtxyraDeukWuZvaISUfvyuPmORb/Oaykh4hxQ7c", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1524744}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.9.0": {"name": "react-bootstrap", "version": "2.9.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "10493a3bb7c0429fee886f5571b195ece6c436f0", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.9.0.tgz", "fileCount": 771, "integrity": "sha512-dGh6fGjqR9MBzPOp2KbXJznt1Zy6SWepXYUdxMT18Zu/wJ73HCU8JNZe9dfzjmVssZYsJH9N3HHE4wAtQvNz7g==", "signatures": [{"sig": "MEYCIQCYjjr+mo57bmMUURYCwV5GAntuhVDErRl5rfx4D4RtowIhAO6wrR33QnqDOfGnKRQr+VjEbnrRPOVpDM9E3rSaLapd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1526329}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.9.1": {"name": "react-bootstrap", "version": "2.9.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "c1ab48ae2b2cfe6d5ac957c2042eb36fcafdb1d2", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.9.1.tgz", "fileCount": 771, "integrity": "sha512-ezgmh/ARCYp18LbZEqPp0ppvy+ytCmycDORqc8vXSKYV3cer4VH7OReV8uMOoKXmYzivJTxgzGHalGrHamryHA==", "signatures": [{"sig": "MEYCIQC4MfU6dMKtcWIv3h54tO5jhJQUs7ddWC3hxtTVKW+U1gIhAIYwi2NY9O6i3DbK+fR7797bWJoqP+HsGyxGQZVw3MS2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1521245}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.6.8": {"name": "react-bootstrap", "version": "1.6.8", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.1", "prop-types": "^15.7.2", "dom-helpers": "^5.2.1", "@types/react": ">=16.14.8", "@babel/runtime": "^7.14.0", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "react-overlays": "^5.1.2", "uncontrollable": "^7.2.1", "@restart/context": "^2.1.4", "@types/invariant": "^2.2.33", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.3", "react-transition-group": "^4.4.1", "@types/react-transition-group": "^4.4.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dist": {"shasum": "e9380d81cdb92c433f61b37a9120b8edd49912a1", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-1.6.8.tgz", "fileCount": 570, "integrity": "sha512-yD6uN78XlFOkETQp6GRuVe0s5509x3XYx8PfPbirwFTYCj5/RfmSs9YZGCwkUrhZNFzj7tZPdpb+3k50mK1E4g==", "signatures": [{"sig": "MEQCIFrUq5uy9t3zk7PLUpWfPoYkyei0h3zmGWWOQR7NHOwmAiB6xG+UskC61t4ZB0TKE27uUYqNm+XNFmqkLPi6tHnP2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1329684}}, "2.9.2": {"name": "react-bootstrap", "version": "2.9.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "ee2bab4cb19df628a90868fa594a3b410fe0c0be", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.9.2.tgz", "fileCount": 735, "integrity": "sha512-a36B+EHsAI/aH+ZhXNILBFnqscE3zr10dWmjBmfhIb2QR7KSXJiGzYd6Faf/25G8G7/CP9TCL2B0WhUBOD2UBQ==", "signatures": [{"sig": "MEYCIQDgm8z2Qi04RHki8fYqh3wKUpqiyG4VnOc+yT5baahZBwIhAM6NG1AA5KexanQzYA9YNoNXbqfQ1EC47teSMTZlYRFp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1466938}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.0": {"name": "react-bootstrap", "version": "2.10.0", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "0d9a003dc32cc5d9df972f1e581285d56e93c1d7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.0.tgz", "fileCount": 735, "integrity": "sha512-87gRP69VAfeU2yKgp8RI3HvzhPNrnYIV2QNranYXataz3ef+k7OhvKGGdxQLQfUsQ2RTmlY66tn4pdFrZ94hNg==", "signatures": [{"sig": "MEUCIBsAudKg3O4EBYSWFEEQxxYemAmv0DFwE308bHobAQrYAiEAnvLD1WfTFgbhh11IpbuAj1hFNEgIvy5tW6o6QcrLaQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1467458}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.1": {"name": "react-bootstrap", "version": "2.10.1", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.6", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "48ee28fcd7973ddc0d0ce2161a918785fba4ab2c", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.1.tgz", "fileCount": 735, "integrity": "sha512-J3OpRZIvCTQK+Tg/jOkRUvpYLHMdGeU9KqFUBQrV0d/Qr/3nsINpiOJyZMWnM5SJ3ctZdhPA6eCIKpEJR3Ellg==", "signatures": [{"sig": "MEYCIQCetY6itYC81V5Wx0bYvq/bt64b3jhuNX92JfYDy9xZ8wIhAJmfIoOwzDFZkNlgXvMMSt8k5Lv+5QAOoRqrVirwtUzH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1467514}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.2": {"name": "react-bootstrap", "version": "2.10.2", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.8", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.22.5", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "3b609eb0170e31b3d9ace297d3a016c202a42642", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.2.tgz", "fileCount": 735, "integrity": "sha512-UvB7mRqQjivdZNxJNEA2yOQRB7L9N43nBnKc33K47+cH90/ujmnMwatTCwQLu83gLhrzAl8fsa6Lqig/KLghaA==", "signatures": [{"sig": "MEYCIQDSXYd60H8FJ7qlunoC6gPGy4B7yO1iOfDcFVLOvxr1OwIhAJL7GZrkmOVOQFBJ95F2aiFtxJn/DEfO2vK3H7L5wLt8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1473053}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.3": {"name": "react-bootstrap", "version": "2.10.3", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.9", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "612c7b987bd1b386cf7169f65b7945f2552400e3", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.3.tgz", "fileCount": 735, "integrity": "sha512-cc1KAaQyj6Gr3AfA0eRRiUMSlRi3brDVcjc/o0E9y9XNW7ISo8TITrq8G8G3QTFe7VIhCiDt38k99AEFoLOolw==", "signatures": [{"sig": "MEUCIQCeCeKQKlpf++ahnYfpQOmqIvILKcDkajPgtnyg68UicAIgAcM/r1ygwyRO7vhgXFOyMekzlokkiBccHUVbf2UC7EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1476328}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.4": {"name": "react-bootstrap", "version": "2.10.4", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.9", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "ed92f5f8225a44919a7707829bac879558b71b70", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.4.tgz", "fileCount": 735, "integrity": "sha512-W3398nBM2CBfmGP2evneEO3ZZwEMPtHs72q++eNw60uDGDAdiGn0f9yNys91eo7/y8CTF5Ke1C0QO8JFVPU40Q==", "signatures": [{"sig": "MEQCICthXy3SLNhMMlIxFuBzDI/jljOcrv7j/3Ki5e0VPzkNAiBaaDfqNKCLEod7gJyuMT+jGLfuypFgZ/6sRnnWxjj8rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1476347}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.5": {"name": "react-bootstrap", "version": "2.10.5", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.6.9", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "2d3416fb4178d0f460ddafbfcab0aebfbbf1cf25", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.5.tgz", "fileCount": 735, "integrity": "sha512-XueAOEn64RRkZ0s6yzUTdpFtdUXs5L5491QU//8ZcODKJNDLt/r01tNyriZccjgRImH1REynUc9pqjiRMpDLWQ==", "signatures": [{"sig": "MEQCIF2pecoiYfV7RnYmn0kycwal/fF6+TECmUktxh4/usA4AiBSmR2X95g2sjTytHhWudrkw0U5z6N03vmjjYfyVTu2lg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1477088}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.6": {"name": "react-bootstrap", "version": "2.10.6", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.9.0", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "cb8b6f3604480b99b1e3cfa09cf53446e760bba7", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.6.tgz", "fileCount": 735, "integrity": "sha512-fNvKytSp0nHts1WRnRBJeBEt+I9/ZdrnhIjWOucEduRNvFRU1IXjZueDdWnBiqsTSJ7MckQJi9i/hxGolaRq+g==", "signatures": [{"sig": "MEUCIQCD6bagN/MC2SWPFd2u4IG2Og0EPC+RaINPfWP4qyGJ9wIgETQCWtUwmfno+zJZc/FVOGaazdcmbJVpQpF0eIu/+vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1475590}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.7": {"name": "react-bootstrap", "version": "2.10.7", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.9.2", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.12", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "63954e8faa6f0d28d9c755e5f8fbd27b5b09764a", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.7.tgz", "fileCount": 735, "integrity": "sha512-w6mWb3uytB5A18S2oTZpYghcOUK30neMBBvZ/bEfA+WIF2dF4OGqjzoFVMpVXBjtyf92gkmRToHlddiMAVhQqQ==", "signatures": [{"sig": "MEYCIQC/JBA8qM5/NlAVJ/D9qKxTPRTHZyGJCCDpHqJlNJH3aQIhAI9gSQqkuQOEzqnQY/gZ4FrgAdj6MBtqy6UdKdtyXJMM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1478939}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.8": {"name": "react-bootstrap", "version": "2.10.8", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.9.3", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.12", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "65537cba24d1253d2c83aef32de56f06f1b06a6b", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.8.tgz", "fileCount": 735, "integrity": "sha512-Vw1B2QfGGsrcBge0HQt8rjhnw2syFbtlPQXl/50k/bAnA4Jwi9+Bd4aYYTa68oyBB2KXMtDbGy2oMGl7eSet9A==", "signatures": [{"sig": "MEUCIGXrrBYLrr2R5W7OT/0INmJ8Fkyn65XzUCwhUNf75XtvAiEA2kS5V0YlssK54nuLpPzapc2nPqSIkZ9h01b53M+yoyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1479126}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.9": {"name": "react-bootstrap", "version": "2.10.9", "dependencies": {"warning": "^4.0.3", "invariant": "^2.2.4", "classnames": "^2.3.2", "prop-types": "^15.8.1", "@restart/ui": "^1.9.4", "dom-helpers": "^5.2.1", "@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "uncontrollable": "^7.2.1", "prop-types-extra": "^1.1.0", "@types/prop-types": "^15.7.12", "react-transition-group": "^4.4.5", "@types/react-transition-group": "^4.4.6"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0", "@types/react": ">=16.14.8"}, "dist": {"shasum": "61a0e68ca8da03f4d6fb180e358d8ee613a93156", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.9.tgz", "fileCount": 735, "integrity": "sha512-TJUCuHcxdgYpOqeWmRApM/Dy0+hVsxNRFvq2aRFQuxhNi/+ivOxC5OdWIeHS3agxvzJ4Ev4nDw2ZdBl9ymd/JQ==", "signatures": [{"sig": "MEUCICBYzs/8DL+B5dxxTLjICnbbGGHvL2/kvJcLHxGApxu+AiEAxjxVqSTAELMkc1XZ/sG4I4aXAMhU7btHgI8m850JITY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1479172}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "2.10.10": {"name": "react-bootstrap", "version": "2.10.10", "dependencies": {"@babel/runtime": "^7.24.7", "@restart/hooks": "^0.4.9", "@restart/ui": "^1.9.4", "@types/prop-types": "^15.7.12", "@types/react-transition-group": "^4.4.6", "classnames": "^2.3.2", "dom-helpers": "^5.2.1", "invariant": "^2.2.4", "prop-types": "^15.8.1", "prop-types-extra": "^1.1.0", "react-transition-group": "^4.4.5", "uncontrollable": "^7.2.1", "warning": "^4.0.3"}, "peerDependencies": {"@types/react": ">=16.14.8", "react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"integrity": "sha512-gMckKUqn8aK/vCnfwoBpBVFUGT9SVQxwsYrp9yDHt0arXMamxALerliKBxr1TPbntirK/HGrUAHYbAeQTa9GHQ==", "shasum": "be0b0d951a69987152d75c0e6986c80425efdf21", "tarball": "https://registry.npmjs.org/react-bootstrap/-/react-bootstrap-2.10.10.tgz", "fileCount": 735, "unpackedSize": 1478159, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCzPJbCOITRzRqSLwiloqLNRxzjodpHpDY/Qg5ebjpCOwIhAJ01SAgxEo470Dqb2QQyYMp0eRbnmtRNas8e7Jb2gmtW"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-05-11T18:26:50.514Z"}