{"_id": "react-router-hash-link", "_rev": "29-45d97c2da2bf92a500de0cb782fd6f6e", "name": "react-router-hash-link", "description": "Hash link scroll functionality for React Router v4/5", "dist-tags": {"latest": "2.4.3", "next": "2.4.0-next.0"}, "versions": {"0.1.0": {"name": "react-router-hash-link", "version": "0.1.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "105bd3af9d7b649bf24dfbbf5eec3aa798919d33", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@0.1.0", "_shasum": "0c83b4fbd17120547e154a7d5b7b2beb16f61851", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "0c83b4fbd17120547e154a7d5b7b2beb16f61851", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-0.1.0.tgz", "integrity": "sha512-C40FAAwarrSymUpDtX7sczoN9T3RQ00XsNN82eFpVBH+kyJlbOsCgH6sYD5JjSVSM050O//GV0LNHAR40YCgNg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICx1InZAtYSCwY9iPIrA4B608cz7J5w2n2+tlXjlnIG6AiARHjyeUyXMXWs1oBiUwztCPrD16HjSENCUUPAy3g0Clg=="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-0.1.0.tgz_1489450778192_0.5861225856933743"}, "directories": {}}, "0.2.0": {"name": "react-router-hash-link", "version": "0.2.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "1663feef11852c96a1828359fce00272aacac7ef", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@0.2.0", "_shasum": "7805b20160601b697ffc0353a9f311793f7b53b5", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "7805b20160601b697ffc0353a9f311793f7b53b5", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-0.2.0.tgz", "integrity": "sha512-myMvpVMOcQ186j/GsdMVcr/4efEDvyALQ4d3u7uHnk8dCAqXSQBA+9R5elMGGj/IUgHJZUdIxaKWXQsdz8G3Vw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDUw0u6LvR4xgXSwEnIvn2lFppGrWn22sony1mdXTBqCAIhAP213ZgQtGZ/XwFrgyi1lvPbLXZdxcdQsjTuo0i69Wty"}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-0.2.0.tgz_1489532320029_0.8104821022134274"}, "directories": {}}, "0.2.1": {"name": "react-router-hash-link", "version": "0.2.1", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "b9950a7ab223c53163c8da60727940cdf2eee0a7", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@0.2.1", "_shasum": "589e1a18a31dd6186f408217cfa3964aaeffbd9d", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "589e1a18a31dd6186f408217cfa3964aaeffbd9d", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-0.2.1.tgz", "integrity": "sha512-G5dZGslTJa1uVg14qCSk7T5vlJgJ0K1wS2XynNtIScZIbZsSbVxto4qHmbwPLB71mm38mbujtR1s+3Pwvxz5bA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICGMxqn+PjywrTLws5rDa7fV/3jomPgN/4wi8RKZ3UBYAiEA4/MrpFhVAC+205mg3SxOZ9gqy67CjIPfAFcLXXDcw2M="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-0.2.1.tgz_1489533042624_0.6384752413723618"}, "directories": {}}, "0.3.0": {"name": "react-router-hash-link", "version": "0.3.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "749d0577570f59269e29580d1bf42c26e8e5b19f", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@0.3.0", "_shasum": "443366c5fab6600a110a8fe15c6de3f582544df0", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "443366c5fab6600a110a8fe15c6de3f582544df0", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-0.3.0.tgz", "integrity": "sha512-nUfz71Q2PWrKnM0Xs1f8pIEjyzgCnjV2eUciNDKFTYTKk9v3tcgoAabRWveKDXz1noBqjm3/oY6R/p2OVV8olA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICxMJBqtxgE8MSLZkunvlWcwnSt2xTRZvulSZ7egabMkAiEAizjcGkaJcbnkRxxl8aH34UduS0rN6C29mK8uzh0VEEk="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-0.3.0.tgz_1490899733449_0.9456437611952424"}, "directories": {}}, "0.3.1": {"name": "react-router-hash-link", "version": "0.3.1", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "374e2d3d8bd37b8daef6510cf2bf1b3a24cbd2ec", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@0.3.1", "_shasum": "6df464faa3c0696e9891758241164ac493fb3b44", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "6df464faa3c0696e9891758241164ac493fb3b44", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-0.3.1.tgz", "integrity": "sha512-g9D9Gz6sAvJ3jSjublxD24pKmxq9quxwa/ewlISRayZ+IBp93E+sFLf8jwIJEuCLBgHDI8DwWuCvUaieCGCuGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAybAhy4+A2Bz3grOkYPDENx4akUuB6TY/lNa+oVcuMKAiEA3H/lW35MbHpQUAnidRPBI2GJRGX36bpAbIfRE34HytU="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-0.3.1.tgz_1491067333712_0.8535101364832371"}, "directories": {}}, "1.0.0": {"name": "react-router-hash-link", "version": "1.0.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.0.0", "react-router-dom": "^4.0.0"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "gitHead": "40b21079dccd8820151f53b18b84d79458017b77", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@1.0.0", "_shasum": "6ddbd0ef4694413213d0ebe309565bb22c5546a2", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "6ddbd0ef4694413213d0ebe309565bb22c5546a2", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.0.0.tgz", "integrity": "sha512-xMMW5JCzbiaVRxwc9PWkStoGomZE/bt7ZZxaUkjK11TBo5sTOBCmaNiQQVhbJ3mFwS9uXemSQ4nKruYLx17Myg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD1v+2SPYM+mT4+9vaF7M5u/EmGnrpB/74MyABL/XmJGQIhAMks3eWzOzXyp6tuImTELYfOidKmO1fJxx0q+dAS9dNB"}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-1.0.0.tgz_1491150077500_0.007980294758453965"}, "directories": {}}, "1.1.0": {"name": "react-router-hash-link", "version": "1.1.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=latest,react --plugins=transform-object-rest-spread", "prepublish": "npm run build"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": "^15.5.0", "react-router-dom": "^4.1.1"}, "devDependencies": {"babel-cli": "^6.24.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-latest": "^6.24.0", "babel-preset-react": "^6.23.0"}, "dependencies": {"prop-types": "^15.5.8"}, "gitHead": "ed7d932e7cbfd9ac8cd81837612f299d1ec7c534", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@1.1.0", "_shasum": "4c009c8bccb2d15dd15f00c0a91f94c6d627176a", "_from": ".", "_npmVersion": "4.2.0", "_nodeVersion": "6.9.5", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "4c009c8bccb2d15dd15f00c0a91f94c6d627176a", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.1.0.tgz", "integrity": "sha512-Ie2BuGYFhxjX52MLymRzifD3DDpB6pZDM/0zg4voPpAxu5WHz2c4eHUpfVrBNqYzi0DVHTbDelc1Sy9cCy8KTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcuSgxdpVep5e9PAeXJ1JVr3pBSb66wvYsD8i/9r4nEgIgGUby96WrKss2EmjED179MuqWo69Xie7iSfhpZWgDDxM="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-hash-link-1.1.0.tgz_1493495483247_0.9552338407374918"}, "directories": {}}, "1.1.1": {"name": "react-router-hash-link", "version": "1.1.1", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": "^4.1.1"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "gitHead": "c424f2e590088526330b5d409f80501e74e1eeb7", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@1.1.1", "_shasum": "7380cc7a8809243d94385924af52dc9d2c9fbaa1", "_from": ".", "_npmVersion": "3.10.10", "_nodeVersion": "6.11.1", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"shasum": "7380cc7a8809243d94385924af52dc9d2c9fbaa1", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.1.1.tgz", "integrity": "sha512-S5bHhZRY5EBy3jHvC8Mzgkp8ICvm76UGpoW2y9Mn+yehJVFxs55aPuA2UfQG3H2OJ+4u42zeEAcqwpJJKy0Jlg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSkPOI1uy8FZAntEhd9XV5qB6LJ5zo4b1QDAfqqN4KnwIgVOozZ+Q8qnVztV8RdZblV5njgwTG0dDeAXdFZj9ALBE="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link-1.1.1.tgz_1506456305524_0.9306119265966117"}, "directories": {}}, "1.2.0": {"name": "react-router-hash-link", "version": "1.2.0", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "files": ["src", "lib"], "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": "^4.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 Rafael Pedicini\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "_id": "react-router-hash-link@1.2.0", "dist": {"shasum": "ce824cc5f0502ce9b0686bb6dd9c08659b24094c", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.2.0.tgz", "fileCount": 8, "unpackedSize": 10920, "integrity": "sha512-neOxMTO+7eXGhUZVvwuP7nw1l8ex33OnQELjHBM22ZtyblVkjeaXnu2Zeq1/QkYVlvUgw1vgvuSd9lKx9veqmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGwkU2e0715FYc0og7T9ZPMTAiuTll+bblDkeo6xP464AiEA8WOYg/aaLlk/7j/4uTmdrByLhVVllmS6D3HENCrFfXQ="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_1.2.0_1518911705293_0.20465403283331884"}, "_hasShrinkwrap": false}, "1.2.1": {"name": "react-router-hash-link", "version": "1.2.1", "description": "Hash link scroll functionality for React Router v4", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": "^4.1.1"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "gitHead": "95f28ea27c33efc6848d7e55e3f4623aa2a72fd4", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@1.2.1", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-ddkCtmk/JwMmuU087TGShQHYyNjsJ+/9CTyuVdvvKf6ACgqk2Ma9ndX2xogo7WWmyq9AjuziBm5bmJ12zBxtsQ==", "shasum": "da3b6384e5bff90e9b2172d2e689a994646f2f45", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.2.1.tgz", "fileCount": 5, "unpackedSize": 10964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb35TGCRA9TVsSAnZWagAAzdIP/A0LwSjfKuDhFSlLgQR8\nlZLlMC7HEthKEbJcuIcQJyAoKPOpEVKpZcbDTm3gf81HtbSpC8QpRgaT1cys\ncSCnCLXK3cc2uUYEdXnUsofz7wxpwfrms/ii6prEu6NQNXENmc+PyaERV7pZ\nNLjKAROmWzjjfuI+TfAq5BtOb1dD29He1toCE7okrCFNCxlbQQJX2e48enpL\nDDKw1hJh71IiUka7lxrDW/9AAD5oK4MPkNd+Vwrj0ingWCcpAEAofzY1Frxq\ndbDoEQ1oKYMgzjZlGpXl8eV4wlhWBSM75kzZXGBA5yCgz6424eL3jxD3PeZ+\n8tg/xDycyF1KnPwGSAVzYgPbzntTBVaNgcdmIWmNqg9zpuWNHfvPpWbwKs70\ntx38FBzKXMsv1uazbN5Y7ivHtr+DJrlvvI4Gog5YXeLjgS/R7twmaMKPE5b4\nQUBVaNJnjaIkCb7qH6yoraN1EE7ju8p9NTOJaeVSfY3Uqew45rLKWOAM1T2i\nKNqOk0bSNIgUuqPdmyASzQFwwtAwXUfwKISCpXOwm+iQMwoV1vH0p1Svt5cN\nB64xAtCAf+7toCSm+sK24WMtjsFYbHfuqhgWjOwmhqXEH3BB/iTxjjxLZnbo\nDxAMRPee9TXZ0eHvrJxz9gKfuXOK5FyyWZpRPVxQk9CzwrJji54XTXMjttHZ\nRthk\r\n=zGhu\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFdjCn1gEth49eVl619jx+3++8ia2jy9ZEXyTKObwc4xAiEAmungOpClOb5Bcuk/P7x2ZYV/wGIi7FrJRc6RSo4ABtA="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_1.2.1_1541379270269_0.6687264046301278"}, "_hasShrinkwrap": false}, "1.2.2": {"name": "react-router-hash-link", "version": "1.2.2", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "repository": {"type": "git", "url": "git+https://github.com/rafrex/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafrex/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "gitHead": "06822346273c0548d79dacdf7d73ae48910c6b6f", "homepage": "https://github.com/rafrex/react-router-hash-link#readme", "_id": "react-router-hash-link@1.2.2", "_npmVersion": "5.6.0", "_nodeVersion": "8.9.4", "_npmUser": {"name": "rafrex", "email": "<EMAIL>"}, "dist": {"integrity": "sha512-LBthLVHdqPeKDVt3+cFRhy15Z7veikOvdKRZRfyBR2vjqIE7rxn+tKLjb6DOmLm6JpoQVemVDnxQ35RVnEHdQA==", "shasum": "7a0ad5e925d49596d19554de8bc6c554ce4f8099", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-1.2.2.tgz", "fileCount": 5, "unpackedSize": 11665, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdMyZyCRA9TVsSAnZWagAAxOAP/3jd+Q8tfu+8jVqJBu2U\nyi8pAElg1/JD+3qdcnbcHvYzpFodcQWErDwe5gvW4vCWI1p4maTDOdBIUoyW\nhfnPE2JxFOqWw/Y51KWou4koTbJX0rMP7cyAiL7n6wq1kdNdjUbcg71BlCRl\nSZ6nKHuuR/A7VgOGa8OFmZwkhwA6xgPQgNPuT6x7paA/v+nnyF/vDee5r47T\nsNnLk4viQfzdRWPTlINTVhTI+Xj9BbVSkWdSL9bV7vWhrdL101FlRIDeluqX\n+9E10hAfhZhuswvtFJcr2T1z6FVNgHKTK1ZtCH1QZL6r9PH+0eKY5mPp1J/E\nNiubODZuVnJpHvuzsLVkqQodA0rwoj9VfrkxW8yTaccdgA44IpXghFLSM1xT\nLsE8D+dMWaEWXlGq7zZ1qf16NrrKjx7Od4MFAtDRn7tEYb781klsk+ngOONt\nBE1yi7SyBzlVmLnE7bb8tBfzM+TfIpAOMmu3AyZ1yeznvlHHf17uDyZUsNhf\nK1D/DpUJVGdeSWFperkvzqw25IVT/PA6ONhCQEj97qv6nf1L//MPu8CAiXjz\nLedPCIwF3ADIsRg+P8Asoq2MhQsbNh1RimN98nWpGzRlii0YQJsZ3Ztr4IJS\n56iaWMKiY5Ah+gkgXgTRupAarn1qrK9RlzJUHTtwcVzsbh8JOeY0H9OrwdxB\n/fAG\r\n=XSpV\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnXUjtKceDi8HpWt/Zd584jWQ91Jwu5dAq96JrJWkPnAIgaIGcOCz39nLjhgpGGCyEK42T7ghCAnLTyucadFe80Eg="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_1.2.2_1563633265241_0.6355162743577076"}, "_hasShrinkwrap": false}, "2.0.0": {"name": "react-router-hash-link", "version": "2.0.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "gitHead": "a1644cdd9c7c87da7fd3986c3d35cac1c41f8135", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.0.0", "_nodeVersion": "8.9.4", "_npmVersion": "6.14.5", "dist": {"integrity": "sha512-vAiiHUhPcT5NYwKutvbAgHR2HXoESkTyaeXQaW1cmVwZujhmHEfaXRh28uS52jQaja/S+Xrc0aFizfljY7rvAw==", "shasum": "c0baade5cb0957f4c6da4519f7992c825f1b3b35", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.0.0.tgz", "fileCount": 5, "unpackedSize": 11797, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDdVxCRA9TVsSAnZWagAAhusP/0f9DO7BVzf2MKPgLCTQ\ngkkWd+ixQraLSQ72ykRGKjHdhjDTExjnGNcpfn4/e/ITdyfpu011yWV6/d+q\nqIMcQcBbEIPLLjUpvtGV3LTqHdhYB47JKo/wpLX0amRJg5w2PnPG0iNdnX+F\n07a/KOj0Fh1+Q8bdfs1vb8kk/t0ocqxtc9NZ74RIQRjYF176pJNHmgGA0cHr\nyMYsmha/pSQ75wIiDWAV+4A8yvniVD32uP4WU9F9RkfBLhlRUU5lmqWDQFFe\nkPpmglAN1UMBJfQv2PIphHoGIs0i/BhqQCIy+Lkxlg4oNUaIiyOrX+VdIVne\nctJ12YtLwI2a7vpVecjUN1jZ/h2rXZQNkehIndFGtlsgUPIqOjxjIgAhowi4\nZPvBU3UqRsoKpqg/LwO/Dbexz8A048BdoL1YBdtkrEVHgk1yX3QcKPZBC7C1\nZUGn0Ozg3M16OFXZaI6iusoSIYTgmEbdMWgjegUDqWXsyzzEjnbrLuX1XPUU\nL/t5tPdO6q+F8qEEpzSMIZRNhIdkinEImIn2WDA0dKUDSgf9f6j7uQRTt0Sn\n3k2VvT6skcLUXHs320uiBZXN7c2lJDhCa0F4hltx4es5FZGezYAYbcfIpGUG\ndKRPz2GtdOJQ6Y5N3g+swWc7EEuhNl/bzjtBbh4BYRFmNHYEyEjhuUbBjgM8\nKnB5\r\n=Rkx7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAfsDHmE+/OioSLqctvHE52Pu8T5TufuZgNkdNoxhXdIAiBzpcPPeG7lZsNpAgasb50Z5YPZBhmFY/uXN6L2NIOSmQ=="}]}, "maintainers": [{"name": "rafrex", "email": "<EMAIL>"}], "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.0.0_1594742129305_0.990546304095955"}, "_hasShrinkwrap": false}, "2.1.0": {"name": "react-router-hash-link", "version": "2.1.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=env,react --plugins=transform-object-rest-spread", "prepublish": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=env,react --plugins=transform-object-rest-spread"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"babel-cli": "^6.26.0", "babel-plugin-transform-object-rest-spread": "^6.26.0", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1"}, "dependencies": {"prop-types": "^15.6.0"}, "gitHead": "e18d6ac4d4f665f32c56ac5f80d565926c9955e7", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.1.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.6", "dist": {"integrity": "sha512-U/WizkZwV2IoxLScRJX5CHJWreXjv/kCmjT/LpfYiFdXGnrKgPd0KqcA4KfmQbkwO411OwDmUKKz+bOKoMkzKg==", "shasum": "69cc93df0945480adff14e9e501aea5f356896a8", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.1.0.tgz", "fileCount": 5, "unpackedSize": 12202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXAIGCRA9TVsSAnZWagAAXEwP+gKnXKErL59Dvyxdu0NX\n2S3xzyjQHVj7sCYHNlNLal0rsHCF3ioORwAfxMolk3fpWd9SBv3aFGZz7hA9\niN7bpsX5IaxyyIxIzs42RVwUSu5QJBNCllvy7BsHJUxF1rQaV+rY39OzCjOH\nzCFQeBSBrH+dJtIhXNdl5pdSbEww9jsn/neBDJeQ6NlXFwAMBBm3ZiiNYqny\nXOgLmsuKbtLhOd21F7paEj+JrR+q7cbAyP1KZ4T5HU05CBsLHcBvnlLFWy5t\nhvbKNnAo4xiSxwRdI46n3djvvbWisGrDBZPnsgK3FE/dgdr5pe4xk0OP2Yje\n8ZoYJ+wVZPc8Rq2qNUIODmIjA7vh9nRjF3P8JPhHnKvq1DbNU5VtHU1yIte2\nDiv5diO0JZcWUfyhnXKWxGH2V0sd8Sqp72Br3f0CPYNYmyRz6l/ERgItFkiP\nWv3uKkxo9xN1haiCbXhZrGSw26PHBFCYql4mubO4m64cdmULYTfFKrGWyyX+\nEju9+rX6cKubz/liwUPl6R7vVGpiEYH4W13SuuuFBiutOWoKFmqV+f9xH0Y6\nGvHjAQh9RtAZSiLxPNc941NLeGPDbv1oX0588GE+8EjBySkeD6ZrPVgb1FXO\naIdU7lfl+PLLccDbVbyUK/x8imreukUCqlBFS810uyJXgddnHQ2v4THweBXE\nENaM\r\n=dsFW\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCh7HfMOl5/AoigvCTa6zg23nr1QR0hU6R5/Jc0E82fVgIgGxDNHZeJCICspEWCIQbd/VUcB+/qXB9w1EjDNppozQE="}]}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.1.0_1599865349949_0.6773575449364262"}, "_hasShrinkwrap": false}, "2.2.0": {"name": "react-router-hash-link", "version": "2.2.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=@babel/preset-env,@babel/preset-react", "prepublishOnly": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=@babel/preset-env,@babel/preset-react"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "prettier": "^2.1.2"}, "dependencies": {"prop-types": "^15.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true}, "gitHead": "61bf99d2925804959c3f72bb148b14b7752b7ca4", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.2.0", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-yEz+FRI3ihnR4pVbsvGP9jGJJntjTVApaJ14cOncIY95UU5zTKfv3Czzcsc+2iRxWIpm9n6J8PlFt9R+SHZqow==", "shasum": "e51251b17c4d933502a50f59026ba7d544ca541c", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.2.0.tgz", "fileCount": 5, "unpackedSize": 17040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJff+yzCRA9TVsSAnZWagAAC9gP/RmGBWjpMrpLi0ksdNut\nKNfM5HbmLLfjNEo7Ls1P5t4FjfXZ3gW8wVOJztBOkatIrumzQXnA2iBfJ2qx\n/c59HNilPzVrqOViYCdZ4IH9jX9G148DhGVb8dq0EeqIOJJjcbsEiETREGvW\nbS54/Oe4OpLX7fYRy6ukjiWHV2LS8jON7ob0Y9lm15grercjbNBgslyKWcsC\nwXBOqe5FxlfsAi7VrdZ6Si6sUghm5TJX14rrqFjx3MG9yst2d1LP4quFkgx/\n9XtevFLkEnWJeoIJWoLEmfivUTGcsUAZTPt2eckF4TSxJGMUSQDDZotSMNa2\ntAK6Q2HsmIX07ItxY9yZ9KxoOGq+3dS/UlG8WlPWf6TCSRyMPteKokTpLmrR\ntfIC5EWt39zqKlaDXfa3rNI6CxyN1PCfRLIBzv7A2BgEHZR9EnQSHGTwYV+J\nf6oGYs3xdTVEO3z2h2vrYdJFqLKcob40gyx4RNTj64/S7CN56nhCnSdBNPln\nPm/LpFJi897mr/Og7hvgCNVhq1kwrovVXEZN8zXV0dyi2pcMMoU+luit5aKm\nSfyJPT6R7dhIRqNtq+2ubhA8FuXKAwnyHqUfTrY53q7mHtNnOVZpgO5LU1xF\nWAPFjRN8afTH2fxy3IVrMb0+HbaNshtxrJm8tmfiby2oWt3CGFveuQnSp3Kh\ngqtc\r\n=2yfI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGgm3YcClFCHNZ7JXMvslWdz7aln9Sg38h2mLF2zkg4/AiEA5BcFroX+tpHahVBsGglGTI8y2HC+tTarBeyoRuCiqnI="}]}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.2.0_1602219187147_0.2520153857313079"}, "_hasShrinkwrap": false}, "2.2.1": {"name": "react-router-hash-link", "version": "2.2.1", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=@babel/preset-env,@babel/preset-react", "prepublishOnly": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=@babel/preset-env,@babel/preset-react"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "prettier": "^2.1.2"}, "dependencies": {"prop-types": "^15.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true}, "gitHead": "4e0f79ea3c31814938d45b49f64854266fe48483", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.2.1", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-zQ31sDvqXcEIwquG3dRhp3jC1yKeaLLRRzPdwCz6f+6Fp55NR3GVE4MEV5eNDM2jpqNe50XfQuzTSao+mwFSyw==", "shasum": "8efc85506bf52ef368ed185f99857bd6a6af1540", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.2.1.tgz", "fileCount": 5, "unpackedSize": 17084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgbjcCRA9TVsSAnZWagAAleMP/jbRBHDbGCANcwdAyjUf\nVzrIFammd8QyVmLbv0nlQ5+3sMlp84retiwBxolwOlqV5bvmgiWsuXcp1VWm\nERmnpNoTPZQAIXtHbbunalqviJhrCMKapw5K2XNV+R0dsjX2xn7Y40Teme/Q\nrBWe2HcYZgXDg+GuH+mlDFRcWejmYgMwNeau747Hd5FJHki1VYkC6CCQvont\nYym8Mt5L4G3s/tV9kop9d6qoX0qewx7NfW+GMBiXDInt+XZZJeuIJC7FgqfV\nRsyGJicNHOCafMqbgUsYR9/XYxXUnDW+hwud2ObG2Sy+d47oh82Y062sj0QS\niGz5SAjtAzGQa1Y1jntec+Pz6pjyM65Y9lH1CCWmLadr2p7j4jhvn/TshLM0\np2Lw38lnFXVAzqMKycrBc6hYYYsuUkxPpFpsJSe1FHq+a1T3QceHddchk3Qx\nC/zJtHq6DM6BPap+SJsWv/aLRU5AZXH5s9xt60SA1gaeugRyE5lSe2Ts268y\nhPTewormPuxqwjMpyWSQpC0I/i1vDQORkUsGIkDMP3TOZaw8Ewq8VIsanIqK\nza+rPCJ29yIYJv0ffsjgJodKP0PGRASPoxDM+E4OI/AJjsygFptMcaLy0e9A\nomYCr1eHgTUuw+ZS4PKKSNALSL8Wrhd3cGHRW3auY+wjSrIgovCIIVNqyjQR\nE30P\r\n=doEA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAQFOJK1Ljvy/2HovuUeHBuEw8mj1VVSeHQqVmozvD2IAiB1M39LpVrVyAPh7M4Pmt+UFIhfkojY9d9PDq7GtF6zsA=="}]}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.2.1_1602336987589_0.1586335745331946"}, "_hasShrinkwrap": false}, "2.2.2": {"name": "react-router-hash-link", "version": "2.2.2", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"build": "rm -rf lib && babel src -d lib --presets=@babel/preset-env,@babel/preset-react", "prepublishOnly": "yarn build", "dev": "yarn link && babel src -d lib --watch --presets=@babel/preset-env,@babel/preset-react"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "prettier": "^2.1.2"}, "dependencies": {"prop-types": "^15.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true}, "gitHead": "1a2c5eb6f7a77315ee2e1581bcef85d4a694b009", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.2.2", "_nodeVersion": "12.18.3", "_npmVersion": "6.14.8", "dist": {"integrity": "sha512-+8u5aAiRVWAFutH2Zwcm7UiuHmfed3Yn+d6aMVP8WGclMSv2ySAbGx836UifZO/+5o5QVhOS71soYHy0NVJN5g==", "shasum": "df8cccc57014a5fb9e7defe5bb28292370bb8681", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.2.2.tgz", "fileCount": 5, "unpackedSize": 18007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfgiRzCRA9TVsSAnZWagAAuqcP/1asf19YJ0Q+Gq5l85pW\nlS+MYp1QNObleWd+7bTOm0jHf9iQolJJg7RmkIVc1ZByaVEmigUhGLOFBgXq\nE17tYu5DsIxd39lXOlAC464cyB+HAB1pBmRHIalOQEMatecnvVkWwnstoXNP\nx9og+nQmaXFKeQFOzLb2eh+WebvzTae2CZ59sYHR2kcuBPYRHBRfUilbZByK\nmEwnAUsRHUZ/J2geVE1x+PwJALkJxw8lIS4bbN/YJ5RsndVg4MGXaReX0+VI\njvxm81+3AvYZ/ckZA6m4vc178r6HJEuHZKcTKBlMT5ZYAerNdQSV03MEpwiJ\nRfawxMY+otQVIvHm8xb/tYr8Fp4B2tRbIGikeEYFgyQj0Az3s4q8a9/CXziq\nulju1u5BxMBmwBfxono67LU4w+zfySf08vovyBJXhmf2x4QnYnjBOcehuogv\nFXVmJAmgcB+jTCGONaZG4woeHLUSfPEjk+NGbREscjsWckZ/9+SW6u1woWko\nVwbyddKk9gbZSGM7Bu+siKRnktCGXZcyTSzW9bAx8sNsVLZnzO+cVxGqMDKE\npvUDpZ/Xm6I9UQ0hpMuqVeonpCC16+Y6aMDH6VMS3R0I3fFylBXQ6d8GZUlc\n9OyDuCgWzAYg+y28rmu4c7AxxWRcezaRIp/eF+mypvE5v7BCxIvs92TfuRkY\nBWhp\r\n=t6jH\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAf9eoqdL8owxDUadZWqmWZT/GR4FECE5wjAgF40YA6GAiBc99qyiWBF607JHZULj0M7g14kKMV/LEpVL28gLiFNgw=="}]}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.2.2_1602364530931_0.1880222896030177"}, "_hasShrinkwrap": false}, "2.3.0": {"name": "react-router-hash-link", "version": "2.3.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"dev": "npm link && npm run watch && npm unlink -g react-router-hash-link", "watch": "trap 'exit 0' SIGINT; babel src -d lib --watch --presets=@babel/preset-env,@babel/preset-react", "build": "rm -rf lib && babel src -d lib --presets=@babel/preset-env,@babel/preset-react", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "prettier": "^2.1.2"}, "dependencies": {"prop-types": "^15.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true}, "gitHead": "9b264b3f3f8397f73eaf903a795b87e9de1263cd", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.3.0", "_nodeVersion": "14.15.0", "_npmVersion": "7.0.14", "dist": {"integrity": "sha512-5zqRka83Q+6BSwL/5+2zxPh2qutrd0KkA/BTXgvH32IIUOG4N0vPBe5D/Ct9NzhSzeShSh6SrWq33YTpCQZQLw==", "shasum": "f5b25de21ac3341d93615866dd66c3137928d05c", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.3.0.tgz", "fileCount": 5, "unpackedSize": 19928, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0O6ZCRA9TVsSAnZWagAA/2MQAJubN3x9KV2kt+1cu4mP\nfHiokku3g3IUNL4hoStBhF8ZVJ2aRGK8xhjQ27S8BKhs7n/BD2LF9+9H7nUp\nZ8bAclOMcOzgbw9Nl0kb9HwxTyJ1WefvdxDx2Bu1AHW5/bjQXz1hbTAp2owP\neNcCkjvdxSmaQFLW7ewnXo/MdoTld4ZXvLz5txgOQM/CfPiMhuCdhtDH9bbl\nOQVwIRr96SlDmD5Fp895453oE26DGxkWHChWv8tpgoEofi7u4giyxl0I1HVq\nxbvMapUUgW1kSnS8HcFg2D71wSUYyMhXorSqbrLGIbkGBywp64Ki+Tg+5mSE\nSrqcUtb5JC83nUykjNWwXvZKP3WNv3j8LmTZB4LKsDXsfYU4nVkNMUQ07g2q\nH9NlmneUWFBI8+sbY+G9c36Wciy9j3KlGZuSnq96gt6/+aV5MwnhJnFyFA2T\n8i/i0t5LRcCEG8EunKYXejTEkEtlNBAMy+HZNymlFg4qPTsejCXhQqIMgKFa\nWFDuL/DrAnDMF8uGzCWc2tHKKvPUq/4UyLLlol1Shb5px+4zTS010gDMRCXb\nmWEBkIFdeohWW959jBaZe9P71J7DDQ/NZmPwdk1s7Ows+XF38wm9bn34jyRl\nTEgow+j4W/Gyo//6SjtYnbMhYHEViXDe49nS+gBN8r9EvoptD6Kfg8idOVCj\n2tpy\r\n=+igi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGy72ZGP3Zhsb7qtzkCSBiqkwIpIukoiQJV21WS9SI5/AiACFFp1X0vvKEkM7t+yTYNNZdhSPi2l6XWWvO90cgzmtw=="}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.3.0_1607528088556_0.452161279923633"}, "_hasShrinkwrap": false}, "2.3.1": {"name": "react-router-hash-link", "version": "2.3.1", "description": "Hash link scroll functionality for React Router v4/5", "main": "lib/index.js", "scripts": {"dev": "npm link && npm run watch && npm unlink -g react-router-hash-link", "watch": "trap 'exit 0' SIGINT; babel src -d lib --watch --presets=@babel/preset-env,@babel/preset-react", "build": "rm -rf lib && babel src -d lib --presets=@babel/preset-env,@babel/preset-react", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "devDependencies": {"@babel/cli": "^7.11.6", "@babel/core": "^7.11.6", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "prettier": "^2.1.2"}, "dependencies": {"prop-types": "^15.7.2"}, "prettier": {"trailingComma": "all", "singleQuote": true}, "gitHead": "e4742d1ca370273dcb89cc48dcc6c2ec125cf0e2", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.3.1", "_nodeVersion": "14.15.0", "_npmVersion": "7.0.14", "dist": {"integrity": "sha512-QVYLaBLmRGovSbQv4Tbjqnl9JMEQ8c5rWebkZU16ovgZtpmNIf2znGj3uWaKkAL7lhuYBPcC3OAfhw7lk/QwNw==", "shasum": "2ebe3443caad4d478a76e484458f1974f24ee97c", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.3.1.tgz", "fileCount": 5, "unpackedSize": 19972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf0QMRCRA9TVsSAnZWagAAwHEP/0RggX30qAvrfOzRnTET\n5feu4F+R5o0NCFyb+QX8zX8lrDTS+2YBausGZRhBZ6v9x7cALgJ2ILBYeyfY\nejaKtH5qTD5xbY2cE9a6zeLLX9hPcUXYrCEA2BxJjtvGsRPSDe633bKI0O8k\n/ofjjCPKIdoBd2ea+q+pv+/iqCCWgjwub9GKyH0GgSFP1F5hImn8/tmjLvZj\nb9iNpodWTAyGkoHfJ7+xcUn13NEuy5Nrjhgu8CSC9xe6Nn6nrmO3fBM53Gpm\nUMaCTMngM9EDerHfDLvqJBz1qIhG5WlP+RPyCGyyvIDUPvrihiODIxFNE1Bo\nuvKfuoUmTeh4Uy5dWPKsJaHLzCbU6V02DtSp0GhW7brsLiS5Ef+vfiW4uzUb\npXtafzu/yH09K4gNek/GJjcOkt0tNNrZGd/axWyNo3iOEMo5QzwBJCfFV9DK\n7lo/9Aa4fkoQFoSTrCH0az59k1tz9tFi4+IEz2v82s2CT+Nb/Yi3eDkIPZxC\nh/hDTBmZLDN25/LK63rgIDLij5h4Ah/ebwwuE8axTO55Aw5vJ1mbY17EuZ9T\n7M4y3mB+8jNp0TIjkYQ6lk0QKXsGhTfhCQ7rrd7Qj9UVP8ozM8ZL87wRk95V\n27gCNJ+SNLWFiZdaDA1yK35CNc43VU5D+yAzDCJdFKhkQciQqxm3E08xf8bG\nJY8V\r\n=3Wkn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAzZEuCsJNjA1Kp7vWojNKQFfAPJotsyqqnCumf0lCS/AiBGZ6OyqaC5a/tbr4hz92tYLcaLjUoaSwPM6dRXStFtOQ=="}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.3.1_1607533329151_0.6757392293502154"}, "_hasShrinkwrap": false}, "2.4.0-next.0": {"name": "react-router-hash-link", "version": "2.4.0-next.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "dist/react-router-hash-link.cjs.js", "module": "dist/react-router-hash-link.esm.js", "sideEffects": false, "scripts": {"dev": "npm link && npm run watch && npm unlink -g react-router-hash-link", "build": "rollpkg build", "watch": "rollpkg watch", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"rollpkg": "^0.4.2"}, "prettier": "rollpkg/configs/prettier.json", "readme": "# React Router Hash Link\n\n**_Note that this is for React Router v4/5, for v2/3 see [this solution](https://github.com/rafgraph/react-router-hash-link/tree/react-router-v2/3)._**\n\n![npm](https://img.shields.io/npm/dm/react-router-hash-link?label=npm)\n\n[Demo website](https://react-router-hash-link.rafgraph.dev/) (code on the [`gh-pages` branch](https://github.com/rafgraph/react-router-hash-link/tree/gh-pages))\n\n---\n\nThis is a solution to [React Router's issue of not scrolling to `#hash-fragments`](https://github.com/reactjs/react-router/issues/394#issuecomment-220221604) when using the `<Link>` component to navigate.\n\nWhen you click on a link created with `react-router-hash-link` it will scroll to the element on the page with the `id` that matches the `#hash-fragment` in the link. This will also work for elements that are created after an asynchronous data load. Note that you must use React Router's `<PERSON>rowser<PERSON>outer` for this to work.\n\n```shell\n$ yarn add react-router-hash-link\n# OR\n$ npm install --save react-router-hash-link\n```\n\n### `<HashLink>`\n\n```javascript\n// In YourComponent.js\n...\nimport { HashLink } from 'react-router-hash-link';\n...\n// Use it just like a RRv4/5 <Link> (to can be a string or an object, see RRv4/5 api for details)\n<HashLink to=\"/some/path#with-hash-fragment\">Link to Hash Fragment</HashLink>\n```\n\n### `<NavHashLink>`\n\n```javascript\n// In YourComponent.js\n...\nimport { NavHashLink } from 'react-router-hash-link';\n...\n// Use it just like a RRv4/5 <NavLink> (see RRv4/5 api for details)\n// It will be active only if both the path and hash fragment match\n<NavHashLink\n  to=\"/some/path#with-hash-fragment\"\n  activeClassName=\"selected\"\n  activeStyle={{ color: 'red' }}\n  // etc...\n>Link to Hash Fragment</NavHashLink>\n```\n\n## Scrolling API\n\n### `smooth: boolean`\n\n- Smooth scroll to the element\n- React Router Hash Link uses the native Element method `element.scrollIntoView()` for scrolling, and when the `smooth` prop is present it will call it with the smooth option, `element.scrollIntoView({ behavior: 'smooth' })`\n- Note that not all browsers have implemented options for `scrollIntoView` - see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) and [Can I Use](https://caniuse.com/#feat=scrollintoview) - there is also a browser [polyfill for smooth scrolling](https://github.com/iamdustan/smoothscroll) which you can install separately so `smooth` will work in all browsers\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n<HashLink smooth to=\"/path#hash\">\n  Link to Hash Fragment\n</HashLink>;\n```\n\n### `scroll: function`\n\n- Custom scroll function called with the element to scroll to, e.g. `const myScrollFn = element => {...}`\n- This allows you to do things like scroll with offset, use a specific smooth scrolling library, or pass in your own options to `scrollIntoView`\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n<HashLink\n  to=\"/path#hash\"\n  scroll={(el) => el.scrollIntoView({ behavior: 'instant', block: 'end' })}\n>\n  Link to Hash Fragment\n</HashLink>;\n```\n\n### Scroll to top of page\n\n- To scroll to the top of the page set the hash fragment to `#` (empty) or `#top`\n- This is inline with the [HTML spec](https://html.spec.whatwg.org/multipage/browsing-the-web.html#target-element), also see [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#Linking_to_an_element_on_the_same_page)\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n<HashLink to=\"/path#top\">Link to Top of Page</HashLink>\n// or\n<HashLink to=\"#top\">Link to Top of Page</HashLink>\n```\n\n### Scroll with offset\n\n- To scroll with offset use a custom scroll function, one way of doing this can be found [here](https://github.com/rafgraph/react-router-hash-link/issues/25#issuecomment-536688104)\n\n### `elementId: string`\n\n- Scroll to the element with matching id\n- Used instead of providing a hash fragment as part of the `to` prop, if both are present then the `elementId` will override the `to` prop's hash fragment\n- Note that it is generally recommended to use the `to` prop's hash fragment instead of the `elementId`\n\n## Custom `Link`\n\nThe exported components are wrapped versions of the `Link` and `NavLink` exports of react-router-dom. In some cases you may need to provide a custom `Link` implementation.\n\nFor example, the gatsby static site generator requires you to use its implementation of `Link`. You can wrap it with the `genericHashLink` function of this package.\n\n```jsx\nimport { genericHashLink } from 'react-router-hash-link';\nimport GatsbyLink from 'gatsby-link';\n\nconst MyHashLink = genericHashLink(GatsbyLink);\n\nconst MyComponent = () => (\n  <div>\n    The default wont work for you?\n    <MyHashLink to=\"/faq#how-to-use-custom-link\">No problem!</MyHashLink>\n  </div>\n);\n```\n\n## Focus Management\n\n`react-router-hash-link` attempts to recreate the native browser focusing behavior as closely as possible.\n\nThe browser native behavior when clicking a hash link is:\n\n- If the target element is not focusable, then focus is _moved_ to the target element, but the target element is not focused.\n- If the target element is focusable (interactive elements and elements with a `tabindex`), then the target element is focused.\n\nTo recreate this `react-router-hash-link` does the following:\n\n- For non-focusable elements, it calls `element.focus()` followed by `element.blur()` (using a temporary `tabindex` to ensure that the element can be focused programmatically) so that focus _moves_ to the target element but does not remain on it or trigger any style changes.\n- For focusable elements, it calls `element.focus()` and leaves focus on the target element.\n\nNote that you may find it useful to leave focus on non-interactive elements (by adding a `tabindex` of `-1`) to augment the navigation action with a visual focus indicator.\n", "readmeFilename": "README.md", "gitHead": "15ed7aae569c7c5ce7258d36650ec46abc24edf8", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.4.0-next.0", "_nodeVersion": "14.15.4", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-2YNkiZGTx1DrwXEWDX/VZq8CDDk4UiPk2pzQ4ZN8Mtyd8yzt00mNUgGfqK2SaYCdlK+HHaZ7LTMwgxnulGG8Kg==", "shasum": "63fd5d3b70b41f0c0c3605e18298ea99843c06f4", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.4.0-next.0.tgz", "fileCount": 10, "unpackedSize": 87580, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJYthCRA9TVsSAnZWagAAzoAQAIxf2Y10AdmbbFFOCOJV\n5iKmRt5Ef4L6sWufNB4IpaIIWW9qckcZvkRwYOjIdZS43Xjg88r9yf4e7ZzL\nL5ley9p3fFY+5SgzjJ/Q79ejSQ1hwWXLOSBlzC/WnegZj+B0vpFWj0lVb1SJ\ntBy/ei45mkxukRzLsRYk/1L4DTpdg5k1NJtjBzhTItzzn19PCVIXmFMjNIX1\nM/wFGByJMF+W3B2XfNa1/tkrtSn8oX4iJfjJOXZuWL7khJXCy8ltnn8gyZVU\nOnCtAy5WFCMHegYuVRjmiiAFZkZDVB78UXinYtiyAkwJyYZeBgkNJCmqeH/I\n8sMkcpGo9vmfVPiVBNESrhGpod4V/im7y+1QZlu4cst5wQrL8iy3krfrBpEt\n5TfD1ZkUAlUnAigLPyQTLc2JBMUSmKh4ZHtciQoWHmg/TBkPwIFklrHE177D\nDFt5tVA8wIXU95R4N9bm8OABYNNDO4h2mZhwiF7lm2BM+j4rSBYWEySRXGpW\nYwhsQ3vX0EGQR54kdHhJoi52rZfhOXHEgJRGnXUvQLjUkTD/oYw3JIkt4DUi\nbK2CepNIIYl2LfrRsr7iC7953tv54MdaAaOZeyI8aIUTKGJ6hvE+/d4Y3j4U\nhWTH+FGUnhmG2rNuU4vZtKdKSN4SI5UHBWwN+DCrYtOIqyIImDb0uXrh1x8T\nIx53\r\n=55Oi\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDb1CPCkzUsOLldF0n2Lw5bkyYCMT94K5i4h9Y5H95zXgIhAIKuxOWJ6m00OUK5ZjuQTPFZcN6owY2CYBeT9LSFr4mE"}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.4.0-next.0_1613073248520_0.7537096852678036"}, "_hasShrinkwrap": false}, "2.4.0": {"name": "react-router-hash-link", "version": "2.4.0", "description": "Hash link scroll functionality for React Router v4/5", "main": "dist/react-router-hash-link.cjs.js", "module": "dist/react-router-hash-link.esm.js", "sideEffects": false, "scripts": {"dev": "npm link && npm run watch && npm unlink -g react-router-hash-link", "build": "rollpkg build", "watch": "rollpkg watch", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"rollpkg": "^0.4.2"}, "prettier": "rollpkg/configs/prettier.json", "gitHead": "7611e6c05deb7756a043cdbf15668573b4ef5d75", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.4.0", "_nodeVersion": "14.15.4", "_npmVersion": "7.5.3", "dist": {"integrity": "sha512-HGbB9kfODHKsHvMVsPbqDr057V4xg4TNNRaQcezsFMKitwHaaU51cM2+gDyX45y9YLLPbovELz2rpNx2C3Frng==", "shasum": "216045d9bb826e5f36f873dea8b04874a0708f83", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.4.0.tgz", "fileCount": 10, "unpackedSize": 87573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgJY1ICRA9TVsSAnZWagAAthEP/3ZJrVhdyrWqkAMADNE5\n7RaX04gGrjGEgNsMBL5YPH9pQfKZUuY+KrQxlgXcv4Ezkq/JEZbgysEjSDaK\nFgAWaex+7n9Mq4hACP1F9dvN+OOMRyeO4JiH3u0lfiJf0b3aoRqxM/OnuK62\n4lnVZtTpiU58MeHg4ty41lo3oZfrpodr6cTL75yPid52nl7CM+hdA/FNEkxz\nRVuKcKLgKN1QmjTTQrf76X4P7Wcbg2jcgb3Lcl0T89jRH9H+MAJmpHLIMG5q\n+ov3xWkiRhA13DWU3oEiom+QNRaNPGjvMfLMEQecVWZe1+GRMSVHegDJgrhz\nspzNWt2ixdLtFya3VwMTVqZQEZ2q3iqDvjyZuxgFO9A1SeJeqCqz7KSSCE6f\n9F1ZAb1A1To4JSNGkiYVziXHZc6CbrfkpCl8grd+uezMbqiJYjCuyTM8awzw\np1CMbuaClY+NR2No3XIBJHC5boSvRW31vTfL7gxWSWjEEBolMM3iVNa3j+DJ\n6txzbALX8s6aLw9WRxdSx3G+AZ7/K/TcxNR7kOtoHQnoE57AKvAtS+T/qhNS\nrPyj5FVYZdVHBgXYvYKyohB/lZ0XjoWfvFzrL03L8gULKkX9tHJy9hPjT9Tq\nXMnroLE6mWuKaszfz8oKbo4euYHV/N5Dl59R4daMn5UZst832shUOOGiU0c7\no4Kf\r\n=HiEU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCzpwu7XtVQWTrJtY3+mtC0OnCv2cjg/sv57F5/Kb3tAwIgBUgARm0O9qL8lR6+EP1odlqiYK5eSO48esSBCKDit7k="}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.4.0_1613073735756_0.5008010068295337"}, "_hasShrinkwrap": false}, "2.4.1": {"name": "react-router-hash-link", "version": "2.4.1", "description": "Hash link scroll functionality for React Router v4/5", "main": "dist/react-router-hash-link.cjs.js", "module": "dist/react-router-hash-link.esm.js", "sideEffects": false, "scripts": {"build": "rollpkg build", "watch": "rollpkg watch", "prepublishOnly": "npm run build", "lintStaged": "lint-staged"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"lint-staged": "^10.5.4", "pre-commit": "^1.2.2", "react": "file:demo/node_modules/react", "react-router-dom": "file:demo/node_modules/react-router-dom", "rollpkg": "^0.5.5", "typescript": "^4.2.4"}, "pre-commit": "lintStaged", "lint-staged": {"(src/**/*|demo/src/**/*)": ["prettier --write --ignore-unknown"]}, "prettier": "rollpkg/configs/prettier.json", "gitHead": "21c81d8ac6c14b4be4e1ed6fb7ca531841362ac4", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.4.1", "_nodeVersion": "14.16.1", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-ac71RVRhGzr2DXHPDYW+DE7oH/jkbMefF3giSlAzF286EAHVc1t4wwr4sFNOYj9b2Flfq+FCv184Qi9MBP0KXw==", "shasum": "91ccd904427f42e5ded684af0a20f45842ce0dc5", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.4.1.tgz", "fileCount": 10, "unpackedSize": 88034, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgkKZpCRA9TVsSAnZWagAAtiIQAJrETw737PtOQ+q4u2/8\nfRQ15m0PbumOrxTnj7vV040FdMzzgMJSBTqowS8P4paK9nmafMWeuR0q+2zY\nfJs9d7eMnwEfW0zYUs+mOsio+3a0wy6LzLJ4JLhLV7gvvFxbmdDzdpFJ5TTe\n8YQPizfS0B9g9LYt0dKHvYevZ6BT0XBdokP7IX/Y3GfFljyFzkXGQFCrWE+7\nbwgWJ2Vl4M7+xBoIaF6qI2rOxcQgRCm1rfuCjJoZfn02X32N/yIyskRtjxfZ\n315kD2UQkpDZmsbdUSLA0K9Fb/4BS53otdXNvKuU9pIkn7jhcL4+U0lX+7qX\ng+6lwpWgAk83bnuggctofnCZg2gcn5M1gA7/PdpIzmiPs8Ty2Jf8K6GUh7Uv\nxUNFMD/mMzst6EZh8v1DSzpyZa8Grbc04p0wvlykyUkPY29j1zSd/rgY3p3r\n/fVMYUqP97KqifObtQFNvt0qKzl6FiLMIY9uVd2tkx5F8+Bp1MYPIh4fB/g2\nDCxs60XRjpWMqq5U9hlyBRD2uYjUrf4ZftuNzgf0RtpZ8cqqdHHN9QJMKCwC\nSbxKOOfEaQ9KRMzDfTzgu0hqoVNbjose9XQCHMjeePVAyidOXAjud+MzmAXu\nqecvwb7QOQfksZI4S4d4QpEi6SVEDBPfzOof6P3CA8vXNEfrMCrjG1jiia2L\nzBfd\r\n=jyfq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyoE0Zy9nPAVutfj+mRofBfzHnSwU0PY/If+oq5FcY4QIhANvB3X3m+eACRYD3ZkWQ+kn0+xTCRf7xKv6tVirzeIHt"}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.4.1_1620092520829_0.3302098775294029"}, "_hasShrinkwrap": false}, "2.4.2": {"name": "react-router-hash-link", "version": "2.4.2", "description": "Hash link scroll functionality for React Router v4/5", "main": "dist/react-router-hash-link.cjs.js", "module": "dist/react-router-hash-link.esm.js", "sideEffects": false, "scripts": {"build": "rollpkg build", "watch": "rollpkg watch", "prepublishOnly": "npm run build", "lintStaged": "lint-staged"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"lint-staged": "^10.5.4", "pre-commit": "^1.2.2", "react": "file:demo/node_modules/react", "react-router-dom": "file:demo/node_modules/react-router-dom", "rollpkg": "^0.5.5", "typescript": "^4.2.4"}, "pre-commit": "lintStaged", "lint-staged": {"(src/**/*|demo/src/**/*)": ["prettier --write --ignore-unknown"]}, "prettier": "rollpkg/configs/prettier.json", "gitHead": "eb865c7293da68e167acd19bc677415dd4b98e29", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.4.2", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-srH5vsH+N7UvLHPI+eT7KpsRTCAoUVEq5ZzKtp9XGA4bTvnYYZWAOB/LzAaz0okFz5g4ZK9J5xlc+xAPJNmBxQ==", "shasum": "464a2bf6bd21f4101f17a63699972d54b51782df", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.4.2.tgz", "fileCount": 10, "unpackedSize": 92364, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnHsECRA9TVsSAnZWagAA9qMP/09O+2OJwO/zZJ3+6BDz\nG5wt9aArm9Jw7LekICEob/IBaSu1AmestWvUJvB/XmiThg6etORFaGjG51vT\n0v3fd9wsOx6eqCXZOFc395ADTRdsWF1p2S/d1OP8HT0Pv8UKncu3aXynFre1\nS7ZnHsZUHKjiWD7g+EjahT2ThRQgUn21cxIslkdjK0gdFhoAoz7toDcRKg/K\nxd07Z8J7tA18NxPcZA8hrkJutdFIuELmyfac7eW+3b+0k6XP3jn07YUiKQAo\nNP6Hr30Kmf6eM0g4b2IlmEg2w/DM7UI0FNGmI1KwqidMgtHTpzWeagOmufLB\nNU3oY0XY9Gc4MwI4t5ur24OSuU9ajHJ299yGMIkulQGdw9PmNpBRZuZMXnbN\nlp/HwIbQsV6DFI1N8ajWogz/efVMQDN6OK8i4nG+2xJdS8VJFBIGz4M4xWyx\nbVBo2F2vJHl+L2ZRksFIZ0fCOCiYtz/QMSPgJmbvUZcliTHTm5OxdJ0/NWWe\nKdnuhQ3HOH6jw4llalFWIfUXkuUqtKHngT6eHgstPF+309xtuVASj3ezQyh2\nYMLOlypTs66RmyGYJP2ScDseANg329b0OtNQhYfiB1shgb34cARYh/VKb5Ny\nKFEeY+MxyUgzGUCmLsCWffPQNIsnjNlCMzR1UxSkKjtdN5oV87D1F7j+AFGV\nOg3i\r\n=6Nsw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDS5C5F4YwVW3UhXEIxQaGCLqLNYoIhBLzUaOs4sGuK0gIgFiPqMINiWSKLOKvCyWm8BYBeuZtXf/1oHrBKPXzQRY4="}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.4.2_1620867842803_0.8542629833976223"}, "_hasShrinkwrap": false}, "2.4.3": {"name": "react-router-hash-link", "version": "2.4.3", "description": "Hash link scroll functionality for React Router v4/5", "main": "dist/react-router-hash-link.cjs.js", "module": "dist/react-router-hash-link.esm.js", "sideEffects": false, "scripts": {"build": "rollpkg build", "watch": "rollpkg watch", "prepublishOnly": "npm run build", "lintStaged": "lint-staged"}, "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "peerDependencies": {"react": ">=15", "react-router-dom": ">=4"}, "dependencies": {"prop-types": "^15.7.2"}, "devDependencies": {"lint-staged": "^10.5.4", "pre-commit": "^1.2.2", "react": "file:demo/node_modules/react", "react-router-dom": "file:demo/node_modules/react-router-dom", "rollpkg": "^0.5.5", "typescript": "^4.2.4"}, "pre-commit": "lintStaged", "lint-staged": {"(src/**/*|demo/src/**/*)": ["prettier --write --ignore-unknown"]}, "prettier": "rollpkg/configs/prettier.json", "gitHead": "e67866a910fba46225790988e4662f5329c6234b", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "_id": "react-router-hash-link@2.4.3", "_nodeVersion": "16.1.0", "_npmVersion": "7.11.2", "dist": {"integrity": "sha512-NU7GWc265m92xh/aYD79Vr1W+zAIXDWp3L2YZOYP4rCqPnJ6LI6vh3+rKgkidtYijozHclaEQTAHaAaMWPVI4A==", "shasum": "570824d53d6c35ce94d73a46c8e98673a127bf08", "tarball": "https://registry.npmjs.org/react-router-hash-link/-/react-router-hash-link-2.4.3.tgz", "fileCount": 10, "unpackedSize": 91847, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnH3iCRA9TVsSAnZWagAA8xwP/iDQ4SBbQXkpjwFaIdfK\nOoNhbgWE7yCwEd0/6vdnkidqV+y0EBVnFOePlNWj7wz1ZjNMr1AEIp+iZYFT\n1YX6jOCeVjaQgnE0774wYrLfxtAkbGmpgPECSdP7o6q1PdYEwUR7b9nA8reN\nRxYtKeu/8y8be+bk2fRlgHiaP9Y0nJa9YydmJgJ24Y0zQmhSCudAh8JHVtTb\ngSjkY5LQs32OzeE/ZmYrlg3Y29+i6nz1tf5fdnW3MLTgbpx1inbGmpL9iOWG\n6UAnGqflELBYhl1sjRf8Cz1nqBRfHPydNmvhAMyVdMAvJmeLIApaGjnDRSYg\nKGh6m5owl3hdhUp4/+1yupw914b6yX22JICgM1oMIVD4rglbDbCoL71xbkUc\nyNA2NBAU/32NGJ3pnN8ognmUMJXGwdxx8E34S9LuF2cezpPngyY0w1QZt4VK\nV4GSKFi+aPFrUitGxoCeAnHbaXbe7I24A1UhYIdaWz5Akwo8MPDeXxqbkL7K\nAMRKqNMLoe6iQtQvS/DwSu2ChYQoVAQkIBpZq/RZt7nWWg50N3N7L0teeorK\n4PUuyrhKNAFzfC8oUmzPX56oW6xDNsfv2qiK4cotB/6c9Y/PpdKOkmJPU3fb\nFSQH0AzTsr4ixO1jFEAMYMQDB7bwOBV8uMP3+QvKmiQPRqhYbzZc5zxxf91O\ngXTU\r\n=1dzj\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQClEi+qB5qX4apjEikqGAZHyWsD4prkTx2nsvGIEImgQwIgfm5QFADEIKT54h1ESI/VAduFWemPOyt1JXH1RmEkKzM="}]}, "_npmUser": {"name": "rafgraph", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-hash-link_2.4.3_1620868577832_0.393142411612609"}, "_hasShrinkwrap": false}}, "readme": "# React Router Hash Link\n\n[![npm](https://img.shields.io/npm/dm/react-router-hash-link?label=npm)](https://www.npmjs.com/package/react-router-hash-link) [![npm bundle size (version)](https://img.shields.io/bundlephobia/minzip/react-router-hash-link?color=purple)](https://bundlephobia.com/result?p=react-router-hash-link)\n\nThis is a solution to [React Router's issue of not scrolling to `#hash-fragments`](https://github.com/reactjs/react-router/issues/394#issuecomment-220221604) when using the `<Link>` component to navigate.\n\nWhen you click on a link created with `react-router-hash-link` it will scroll to the element on the page with the `id` that matches the `#hash-fragment` in the link. This will also work for elements that are created after an asynchronous data load. Note that you must use React Router's `BrowserRouter` for this to work.\n\n---\n\n### [Live demo app for React Router Hash Link](https://react-router-hash-link.rafgraph.dev)\n\nCode is in the [`/demo`](/demo) folder, or open the [demo in CodeSandbox](https://githubbox.com/rafgraph/react-router-hash-link/tree/main/demo)\n\n---\n\n## Basics\n\n```shell\nnpm install --save react-router-hash-link\n```\n\n`react-router-dom` is a peer dependency.\n\n---\n\n### `<HashLink>`\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n\n...\n\n// use it just like a RRv4/5 <Link>\n// the `to` prop can be a string or an object, see RRv4/5 api for details\n<HashLink to=\"/some/path#with-hash-fragment\">Link to Hash Fragment</HashLink>\n```\n\n---\n\n### `<NavHashLink>`\n\n```js\nimport { NavHashLink } from 'react-router-hash-link';\n\n...\n\n// use it just like a RRv4/5 <NavLink> (see RRv4/5 api for details)\n// it will be active only if both the path and hash fragment match\n<NavHashLink\n  to=\"/some/path#with-hash-fragment\"\n  activeClassName=\"selected\"\n  activeStyle={{ color: 'red' }}\n  // etc...\n>Link to Hash Fragment</NavHashLink>\n```\n\n---\n\n## Scrolling API\n\n### `smooth: boolean`\n\n- Smooth scroll to the element\n- React Router Hash Link uses the native Element method `element.scrollIntoView()` for scrolling, and when the `smooth` prop is present it will call it with the smooth option, `element.scrollIntoView({ behavior: 'smooth' })`\n- Note that not all browsers have implemented options for `scrollIntoView` - see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) and [Can I Use](https://caniuse.com/#feat=scrollintoview) - there is also a browser [polyfill for smooth scrolling](https://github.com/iamdustan/smoothscroll) which you can install separately so `smooth` will work in all browsers\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n\n...\n\n<HashLink smooth to=\"/path#hash\">\n  Link to Hash Fragment\n</HashLink>;\n```\n\n---\n\n### `scroll: function`\n\n- Custom scroll function called with the element to scroll to, e.g. `const myScrollFn = element => {...}`\n- This allows you to do things like scroll with offset, use a specific smooth scrolling library, or pass in your own options to `scrollIntoView`\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n\n...\n\n<HashLink\n  to=\"/path#hash\"\n  scroll={(el) => el.scrollIntoView({ behavior: 'auto', block: 'end' })}\n>\n  Link to Hash Fragment\n</HashLink>;\n```\n\n---\n\n### Scroll to top of page\n\n- To scroll to the top of the page set the hash fragment to `#` (empty) or `#top`\n- This is inline with the [HTML spec](https://html.spec.whatwg.org/multipage/browsing-the-web.html#target-element), also see [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/a#Linking_to_an_element_on_the_same_page)\n\n```js\nimport { HashLink } from 'react-router-hash-link';\n\n...\n\n<HashLink to=\"/path#top\">Link to Top of Page</HashLink>\n// or\n<HashLink to=\"#top\">Link to Top of Page</HashLink>\n```\n\n---\n\n### Scroll with offset\n\n- To scroll with offset use a custom scroll function, one way of doing this can be found [here](https://github.com/rafgraph/react-router-hash-link/issues/25#issuecomment-536688104)\n\n---\n\n### `elementId: string`\n\n- Scroll to the element with matching id\n- Used instead of providing a hash fragment as part of the `to` prop, if both are present then the `elementId` will override the `to` prop's hash fragment\n- Note that it is generally recommended to use the `to` prop's hash fragment instead of the `elementId`\n\n---\n\n## Custom `Link`\n\nThe exported components are wrapped versions of the `Link` and `NavLink` exports of react-router-dom. In some cases you may need to provide a custom `Link` implementation.\n\nFor example, the gatsby static site generator requires you to use its implementation of `Link`. You can wrap it with the `genericHashLink` function of this package.\n\n```jsx\nimport { genericHashLink } from 'react-router-hash-link';\nimport GatsbyLink from 'gatsby-link';\n\nconst MyHashLink = genericHashLink(GatsbyLink);\n\nconst MyComponent = () => (\n  <div>\n    The default wont work for you?\n    <MyHashLink to=\"/faq#how-to-use-custom-link\">No problem!</MyHashLink>\n  </div>\n);\n```\n\n---\n\n## Focus Management\n\n`react-router-hash-link` attempts to recreate the native browser focusing behavior as closely as possible.\n\nThe browser native behavior when clicking a hash link is:\n\n- If the target element is not focusable, then focus is _moved_ to the target element, but the target element is not focused.\n- If the target element is focusable (interactive elements and elements with a `tabindex`), then the target element is focused.\n\nTo recreate this `react-router-hash-link` does the following:\n\n- For non-focusable elements, it calls `element.focus()` followed by `element.blur()` (using a temporary `tabindex` to ensure that the element can be focused programmatically) so that focus _moves_ to the target element but does not remain on it or trigger any style changes.\n- For focusable elements, it calls `element.focus()` and leaves focus on the target element.\n\nNote that you may find it useful to leave focus on non-interactive elements (by adding a `tabindex` of `-1`) to augment the navigation action with a visual focus indicator.\n", "maintainers": [{"name": "rafgraph", "email": "<EMAIL>"}], "time": {"modified": "2022-11-22T07:16:05.017Z", "created": "2017-03-14T00:19:40.094Z", "0.1.0": "2017-03-14T00:19:40.094Z", "0.2.0": "2017-03-14T22:58:41.711Z", "0.2.1": "2017-03-14T23:10:42.873Z", "0.3.0": "2017-03-30T18:48:55.096Z", "0.3.1": "2017-04-01T17:22:13.949Z", "1.0.0": "2017-04-02T16:21:19.431Z", "1.1.0": "2017-04-29T19:51:23.525Z", "1.1.1": "2017-09-26T20:05:05.623Z", "1.2.0": "2018-02-17T23:55:05.710Z", "1.2.1": "2018-11-05T00:54:30.376Z", "1.2.2": "2019-07-20T14:34:25.378Z", "2.0.0": "2020-07-14T15:55:29.408Z", "2.1.0": "2020-09-11T23:02:30.061Z", "2.2.0": "2020-10-09T04:53:07.244Z", "2.2.1": "2020-10-10T13:36:27.681Z", "2.2.2": "2020-10-10T21:15:31.065Z", "2.3.0": "2020-12-09T15:34:48.664Z", "2.3.1": "2020-12-09T17:02:09.292Z", "2.4.0-next.0": "2021-02-11T19:54:08.749Z", "2.4.0": "2021-02-11T20:02:15.932Z", "2.4.1": "2021-05-04T01:42:01.024Z", "2.4.2": "2021-05-13T01:04:02.980Z", "2.4.3": "2021-05-13T01:16:17.985Z"}, "keywords": ["react", "react-router", "link", "hash-link", "scroll"], "repository": {"type": "git", "url": "git+https://github.com/rafgraph/react-router-hash-link.git"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/rafgraph/react-router-hash-link/issues"}, "license": "MIT", "readmeFilename": "README.md", "homepage": "https://github.com/rafgraph/react-router-hash-link#readme", "users": {"juanf03": true, "unrivalledking": true}}