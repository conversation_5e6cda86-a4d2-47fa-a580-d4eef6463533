{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-transformer-interfaces", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["1.1.0-beta.1", "1.1.0", "1.1.1-teamprv.15", "1.1.1-teamprv2.16", "1.2.1-beta.0", "1.2.1", "1.2.2-alpha.0", "1.3.0-beta.0", "1.3.0", "1.3.1-beta.0", "1.3.1", "1.4.0-beta.0", "1.4.0-beta.1", "1.5.0", "1.5.1-alpha.35", "1.6.0-beta.0", "1.6.0", "1.6.1-authHeadlessImportTest.0", "1.6.1-beta.0", "1.6.1-flutter-preview.40", "1.6.1-flutter-preview.41", "1.6.1-flutter-preview.42", "1.6.1", "1.7.0-beta.0", "1.7.0-ext.0", "1.7.0", "1.8.0-beta.0", "1.8.0", "1.8.1-beta.0", "1.8.1-runtime-hooks.0", "1.8.1-siwa-update.0", "1.8.1-siwa-update.1", "1.8.1-siwa-update.2", "1.8.1", "1.8.2-beta.0", "1.8.2-beta.1", "1.8.2-ext.0", "1.8.2", "1.9.0-beta.0", "1.9.0-beta.1", "1.9.0-custom-iam-policies.0", "1.9.0", "1.9.1-beta.0", "1.9.1-headless-s3-not-for-production.0", "1.9.1", "1.10.0-auth-dir-v-next.0", "1.10.0-auth-dir-v-next.1", "1.10.0-beta.0", "1.10.0-graphql-vnext-dev-preview.0", "1.10.0", "1.10.1-amplify-export2.0", "1.10.1-beta.0", "1.10.1-ext1.0", "1.10.1-ext10.0", "1.10.1-ext11.0", "1.10.1-ext12.0", "1.10.1-ext14.0", "1.10.1-ext15.0", "1.10.1-ext16.0", "1.10.1-ext17.0", "1.10.1-ext18.0", "1.10.1-ext19.0", "1.10.1-ext2.0", "1.10.1-ext20.0", "1.10.1-ext21.0", "1.10.1-ext3.0", "1.10.1-ext4.0", "1.10.1-ext5.0", "1.10.1-ext6.0", "1.10.1-ext7.0", "1.10.1-ext8.0", "1.10.1-ext9.0", "1.10.1-extOverrides.0", "1.10.1-graphql-vnext-dev-preview.4", "1.10.1-graphql-vnext-dev-preview.5", "1.10.1-graphql-vnext-dev-preview.7", "1.10.1-graphql-vnext-dev-preview.8", "1.10.1", "1.10.2-graphql-vnext-dev-preview.0", "1.10.2-graphql-vnext-dev-preview.9", "1.10.2-graphql-vnext-dev-preview.10", "1.10.2-graphql-vnext-dev-preview.11", "1.11.1-beta.0", "1.11.1-beta.1", "1.12.1-graphql-vnext-dev-preview.12", "1.12.1", "1.12.2-beta.0", "1.12.2-geo.0", "1.12.2", "1.12.3-beta.0", "1.12.3", "1.12.4-beta.0", "1.12.4-beta.5", "1.12.4-beta.6", "1.12.5-beta.0", "1.12.5", "1.12.6-beta.7", "1.12.6-geo.0", "1.12.6", "1.13.0-beta.0", "1.13.0-mapsto.0", "1.13.0-mapsto2.0", "1.13.0-mapsto3.0", "1.13.0", "1.13.1-alpha.138", "1.13.1-alpha.179", "1.13.1-alpha.183", "1.13.1-alpha.185", "1.13.1-beta.1", "1.13.1-beta.2", "1.13.1-beta.3", "1.13.1-beta.4", "1.13.1-ic-changes.1", "1.13.1", "1.13.2-alpha.18", "1.13.2-alpha.26", "1.13.2-alpha.27", "1.13.2-alpha.29", "1.13.2-alpha.30", "1.13.2-alpha.31", "1.14.0-beta.2", "1.14.0-beta.3", "1.14.0-category-split-m1.0", "1.14.0-category-split-test.0", "1.14.0", "1.14.1-alpha.38", "1.14.1-alpha.40", "1.14.1-alpha.5135", "1.14.1", "1.14.2-test-api-package-migration.0", "1.14.2", "1.14.3-sub-username-identity-claim.1", "1.14.3", "1.14.4-alpha.20", "1.14.4-alpha.24", "1.14.4-alpha.28", "1.14.4-alpha.31", "1.14.4-alpha.33", "1.14.4-alpha.34", "1.14.4-alpha.35", "1.14.4", "1.14.5-alpha.31", "1.14.5", "1.14.6", "1.14.7-alpha.0", "1.14.7-alpha.3", "1.14.7-alpha.4", "1.14.7-alpha.5", "1.14.7-alpha.23", "1.14.7-alpha.27", "1.14.7", "1.14.8-alpha.0", "1.14.8-alpha.1", "1.14.8-alpha.2", "1.14.8-alpha.25", "1.14.8-alpha.38", "1.14.8-alpha.50", "1.14.8-alpha.76", "1.14.8", "1.14.9-alpha.1", "1.14.9-alpha.7", "1.14.9-alpha.10", "1.14.9-alpha.22", "1.14.9", "1.14.10-alpha.40", "1.14.10-alpha.42", "1.14.10-alpha.52", "1.14.10-alpha.53", "1.14.10-alpha.62", "1.14.10-alpha.66", "1.14.10-alpha.77", "1.14.10-circular-dep-fix.0", "1.14.10-upgrade-graphql15.0", "1.14.10", "1.14.11", "1.14.12-upgrade-graphql15-2.0", "1.14.12-upgrade-graphql15-2.1", "1.14.12", "1.14.13-alhotpatchfeb.0", "1.14.13-alpha.46", "1.14.13-alpha.47", "1.14.13-alpha.51", "1.15.0-rds-support.0", "1.15.0-rds-support-preview1.0.0", "1.15.0-rdsv2preview.0", "2.1.0-alpha.66", "2.1.0-alpha.67", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-alpha.19", "2.1.2-alpha.25", "2.2.0-sync-fix.0", "2.2.0", "2.2.1-alpha.9", "2.2.1-alpha.10", "2.2.1-alpha.11", "2.2.1-alpha.14", "2.2.1-alpha.15", "2.2.1-alpha.17", "2.2.1-alpha.19", "2.2.1", "2.2.2-agqlac.0", "2.2.2-agqlac.1", "2.2.2-alpha.2", "2.2.2-alpha.5", "2.2.2-alpha.6", "2.2.2-alpha.9", "2.2.2-alpha.10", "2.2.2-alpha.14", "2.2.2-alpha.17", "2.2.2-cb-test-beta.0", "2.2.2-transformer-without-feature-flags.0", "2.2.2-with-standalone-transformer.0", "2.2.2", "2.2.3-agqlac.0", "2.2.3-alpha.0", "2.2.3-alpha.18", "2.2.3-cb-test-beta-3.0", "2.2.3-cb-test-beta-4.0", "2.2.3-cb-test-beta-5.0", "2.2.3-cb-test-prod-1.0", "2.2.3-cb-test-prod-2.0", "2.2.3", "2.2.4-agqlac.0", "2.2.4", "2.2.5", "2.2.6-alpha.7", "2.3.0-rds.0", "2.3.0-test-tag-1.0", "2.3.0", "2.3.1-no-internal-synth.0", "3.1.0", "3.1.1", "3.1.2", "3.2.0-rds.0", "3.2.0-rds-1.0", "3.2.0-rds-2.0", "3.2.0", "3.2.1", "3.2.2", "3.3.0-amplify-table-preview.0", "3.3.0-construct-publish-test.0", "3.3.0-nov-14-cut.0", "3.3.0-nov-14-cut-1.0", "3.3.0-rds-3.0", "3.3.0-rds-4.0", "3.3.0-rds-5.0", "3.3.0", "3.3.1", "3.3.2", "3.3.3-alpha.1", "3.3.3-ecs-tagging-permissions.0", "3.3.3", "3.4.0-implicit-fields.0", "3.4.0-secrets-manager.0", "3.4.0", "3.4.1-iam-auth.0", "3.4.1-iam-auth-with-identityPool-provider-1.0", "3.5.0-cors-rule.0", "3.5.0-fix-publish-tag.0", "3.5.0-gen2-release.0", "3.5.0-gen2-release.1", "3.5.0", "3.5.1-gen2-release.0", "3.6.0-data-schema-generator.0", "3.6.0-gen2-release.0", "3.6.0-gen2-release-0410.0", "3.6.0-sql-gen2.0", "3.6.0-sql-gen2-1.0", "3.6.0-test-binary-size.0", "3.6.0-z-data-schema-generator.0", "3.6.0-zz-0411-gen2.0", "3.6.0", "3.7.0-0411-gen2.0", "3.7.0-gen2-release-0416.0", "3.7.0-gen2-release-0418.0", "3.7.0-gen2-release-0418-2.0", "3.7.0-gen2-release-0423.0", "3.7.0", "3.7.1-cdk-upgrade-2.129.0.0", "3.8.0-acdk-upgrade-2-129.0", "3.8.0", "3.8.1-fix-sub-owner.0", "3.9.0", "3.10.0", "3.10.1", "3.10.2-api-stable-tag-2.0", "3.10.2-gen1-type-ext.0", "3.10.2-raven.0", "3.10.2-raven.1", "3.10.2-sandbox-hotswap.0", "3.10.2", "3.11.0-gen1-migration-0211.0", "3.11.0-gen1-migration-0214.0", "3.11.0-gen1-migration-0924.0", "3.11.0-gen1-migration-0924-2.0", "3.11.0-gen1-migration-1218.0", "3.11.0-gen1-migration-1218-2.0", "3.11.0-gen2-migration.0", "3.11.0-gen2-migration-0809.0", "3.11.0", "3.11.1", "3.11.2-gen1-migrations-0304.0", "3.12.0", "4.0.0", "4.0.1", "4.1.0", "4.1.1-ai.0", "4.1.1-async-lambda.0", "4.1.1", "4.1.2", "4.2.0-gen2-migration-0930.0", "4.2.0-gen2-migration-1015.0", "4.2.0", "4.2.1", "4.2.2", "4.2.3", "4.2.4", "4.2.5-grant-stream-read.0", "4.2.5", "4.2.6"], "vulnerableVersions": ["2.1.0-alpha.66", "2.1.0-alpha.67", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-alpha.19", "2.1.2-alpha.25", "2.2.0-sync-fix.0", "2.2.0", "2.2.1-alpha.9", "2.2.1-alpha.10", "2.2.1-alpha.11", "2.2.1-alpha.14", "2.2.1-alpha.15", "2.2.1-alpha.17", "2.2.1-alpha.19", "2.2.1", "2.2.2-agqlac.0", "2.2.2-agqlac.1", "2.2.2-alpha.2", "2.2.2-alpha.5", "2.2.2-alpha.6", "2.2.2-alpha.9", "2.2.2-alpha.10", "2.2.2-alpha.14", "2.2.2-alpha.17", "2.2.2-cb-test-beta.0", "2.2.2-transformer-without-feature-flags.0", "2.2.2-with-standalone-transformer.0", "2.2.2", "2.2.3-agqlac.0", "2.2.3-alpha.0", "2.2.3-alpha.18", "2.2.3-cb-test-beta-3.0", "2.2.3-cb-test-beta-4.0", "2.2.3-cb-test-beta-5.0", "2.2.3-cb-test-prod-1.0", "2.2.3-cb-test-prod-2.0", "2.3.0-rds.0", "2.3.0-test-tag-1.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "1.14.13-alhotpatchfeb.0 - 2.2.3-cb-test-prod-2.0 || 2.2.6-alpha.7 - 2.3.0-test-tag-1.0", "id": "7v3aF9gLOgfmlJK1X2BT8AjMhU89UFS0Jx5L/dGmmAVEHSD57sPVZE3tN4ixdC8JTcwL7ducLETwEbV7b6iIcw=="}