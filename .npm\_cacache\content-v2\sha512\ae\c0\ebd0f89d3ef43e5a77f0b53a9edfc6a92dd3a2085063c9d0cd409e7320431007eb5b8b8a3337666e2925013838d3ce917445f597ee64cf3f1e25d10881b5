{"_id": "react-countup", "_rev": "84-6f6ad3ece8a9c1045c9456c42358ac87", "name": "react-countup", "dist-tags": {"next": "6.0.0-2", "latest": "6.5.3"}, "versions": {"0.0.0": {"name": "react-countup", "version": "0.0.0", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animated"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@0.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "33ba2db98d22f4687c5a01ae4723ae999427db00", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-0.0.0.tgz", "integrity": "sha512-khEEsR6aSAE/yryZ2Q5M9TrPu6b/22CDqmsfzgymcIluSIPYid4bkZd7rIAqfivBnL9MLBSVIhPyf91hjf6dmA==", "signatures": [{"sig": "MEUCIQCWJBm6srWxWegNhD7SXvgiv+SFQigVOFepfFD+1jZ4vgIgSv+80zeXK0Yr5DqZ+z7fG8D9ilWFiVFNNV5RD26XJSc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "index.js", "_from": ".", "_shasum": "33ba2db98d22f4687c5a01ae4723ae999427db00", "scripts": {"test": "jest"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.1.0", "_npmOperationalInternal": {"tmp": "tmp/react-countup-0.0.0.tgz_1462661378139_0.4459475725889206", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.0": {"name": "react-countup", "version": "1.0.0", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "1f6253fe75fee18c2ecc202abf4dab025eeb718b", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.0.0.tgz", "integrity": "sha512-5+bEuHmAuyEBAP1BRVzhrCn1LL9lAmTVLFKRdfylYp9dZ8KYB9qFFwKgzCdM8GAIzsSq70ol5KhkTall6B5urw==", "signatures": [{"sig": "MEUCIQCkxnFS7x3MIomkTc6L1a310C2tJs4pS5xpSUs3RXnHqwIge8kETUZ5E9ZzTCDlalxeEwkyEnkCHIn7QAIcqQACF/E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1f6253fe75fee18c2ecc202abf4dab025eeb718b", "gitHead": "5755293b23f6479fc43d6c989ed476220f2968f9", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --compilers js:babel-core/register", "build": "NODE_ENV=production babel --out-dir=lib --ignore=test src", "clean": "<PERSON><PERSON><PERSON> lib", "pretest": "npm run lint", "coverage": "cat ./coverage/lcov/lcov.info | coveralls", "prebuild": "npm t && npm run clean", "prepublish": "npm run build", "test:watch": "npm t -- --watch"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "express": "^4.13.4", "babel-cli": "^6.9.0", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "babel-polyfill": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "react-addons-test-utils": "^15.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.0.0.tgz_1464223494720_0.32356524909846485", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.1": {"name": "react-countup", "version": "1.0.1", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.0.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "b91e26807d7cb23d610180b46f4b49cd30bc8ddc", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.0.1.tgz", "integrity": "sha512-hQGGltMYd6BxTvEpTP7FZO4AhcEC/GkzBQRmsekJmRLL1O+6I6ws2sDZmwtUaa6uupCQmQoxm1bLFHstP0U7bQ==", "signatures": [{"sig": "MEYCIQD5QmKCL8wDILkRh62AtPeXkdY0kBridIZhaPGyL8OFMAIhAOCuB5ZftmUxNh62YENt4egiQd2PfGcW+8MniASXAlR2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b91e26807d7cb23d610180b46f4b49cd30bc8ddc", "gitHead": "b0b58335f0875941b5b6cdb707344bd76bf78338", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --compilers js:babel-core/register", "build": "NODE_ENV=production babel --out-dir=lib --ignore=test src", "clean": "<PERSON><PERSON><PERSON> lib", "pretest": "npm run lint", "coverage": "cat ./coverage/lcov/lcov.info | coveralls", "prebuild": "npm t && npm run clean", "prepublish": "npm run build", "test:watch": "npm t -- --watch"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "express": "^4.13.4", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "babel-polyfill": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "react-addons-test-utils": "^15.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.0.1.tgz_1464225568489_0.2932323154527694", "host": "packages-12-west.internal.npmjs.com"}}, "1.0.2": {"name": "react-countup", "version": "1.0.2", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.0.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "478d485df70c4166664cac407162e51331df76aa", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.0.2.tgz", "integrity": "sha512-l5B3diwme8RCwdJofWBLNKNqyhbGY4bWaQ96AIVug72+ctPhs/gVpatxdy9h+WDX5ieWZyQgqsqdeFKVIdFgxg==", "signatures": [{"sig": "MEUCIQCEO4Q/aBnj9/YC5caYppHAuN2CiGYFT67aVjChzlMPvQIgNSWPi24TveeuO6uwJnui//tmuQbE8DkswgIaKqeq2dk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "478d485df70c4166664cac407162e51331df76aa", "gitHead": "a5c003cd4e463b962a02aec7f346a834559e1da9", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "<PERSON><PERSON><PERSON> lib", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "express": "^4.13.4", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "babel-polyfill": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "react-addons-test-utils": "^15.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.0.2.tgz_1464231923526_0.6775909876450896", "host": "packages-16-east.internal.npmjs.com"}}, "1.0.3": {"name": "react-countup", "version": "1.0.3", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.0.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "7eb23861b69d6b956681adb8d11b83007c918868", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.0.3.tgz", "integrity": "sha512-kWq4gWyz5+ShPzrgwAksYgcAnOw83kodcjwYHwUX+T1aFM5r3IZ5x1KwzhcK/KcDf1R3CnSmvGvxEyTOsdNoyQ==", "signatures": [{"sig": "MEYCIQCDPbZ0pAOps0pqXuucjWJVzyF2e6vyCH/3zIqcFFhdzAIhAKJW4bqUJEbBoN1u8+yJe9j05gh5HHrrIXg/k2chLg/q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "7eb23861b69d6b956681adb8d11b83007c918868", "gitHead": "a7c27d546e487a843075434464e6af3d00ae2aee", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "<PERSON><PERSON><PERSON> lib", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "express": "^4.13.4", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "babel-polyfill": "^6.9.0", "babel-preset-react": "^6.5.0", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "react-addons-test-utils": "^15.1.0"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.0.3.tgz_1464263896411_0.5772185926325619", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.1": {"name": "react-countup", "version": "1.1.1", "keywords": ["react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "7a10372e278904d052bbfa8a49fa6ca8c713e599", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.1.tgz", "integrity": "sha512-vSbOrudbRdueuGRcCMphJ1MZ2ucK/OdGXdC81mDTELVxRmYZI+DvRFJNV51mt1pX71NzNrVUgLFfLtrxNkjwrw==", "signatures": [{"sig": "MEQCIErI3/+V0EDU/bcERVbIfyL+us/H3xCsYkoyBWOW4+F3AiAyvu6AwYY3iumDnXO7QYXBf/lgIfgqrW281iFruCbr8A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "7a10372e278904d052bbfa8a49fa6ca8c713e599", "gitHead": "2b50b1ac6415d970e35be927c3efcce1543ae4f3", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+ssh://**************/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.1.tgz_1464280989273_0.5411097607575357", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.2": {"name": "react-countup", "version": "1.1.2", "keywords": ["react", "react.js", "react-component", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "3c2ce82d7153cb89fc9991e09a5f52082bf9acdd", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.2.tgz", "integrity": "sha512-ahh/+sGmEcfG4AkxOEdGsw3doo0Pj0On3Gk49LDFEGr6XWlzmx/CrJaQkbCZR2aQxC3grWBk/hhC/XHj2vp/Zw==", "signatures": [{"sig": "MEUCIGaK92e7ljRYDusoDTxOXlJGVQg2RpbgHQaGqxNcGPkLAiEA1z7Kopb6NIFbQx+FbXY976txd/tpo/s4jAaHB2swEEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "3c2ce82d7153cb89fc9991e09a5f52082bf9acdd", "gitHead": "0773aacb61edb33cebe9b0cecb74202c01a88b26", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.2.tgz_1464282793936_0.2308334328699857", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.3": {"name": "react-countup", "version": "1.1.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://github.com/glennreyes/react-countup#readme", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "b0d599a237cdbac5d19a3378eb7fa21aef0ea210", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.3.tgz", "integrity": "sha512-NBhbMOINwJ7KzjbfqhPcTQMZtzVSXz7rL0zxisN4UUxrEX9+wH9hWcrmYqnaLYZC1jLIn7/ZrrwXGPo3yHwmZw==", "signatures": [{"sig": "MEQCIFxfpWmNE9SzlPZA/OdN9PxQaC+I970EvJso2EFcEszFAiAzKJZ9TZbOPZ+FeCSXJJS+aOLGKg7NAeeV+WpwjEhS/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "b0d599a237cdbac5d19a3378eb7fa21aef0ea210", "gitHead": "cb49139b019e9fd1e354ac33a6648ef7f1924072", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.3.tgz_1464284350872_0.6343456928152591", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.4": {"name": "react-countup", "version": "1.1.4", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.4", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "538aa52169d81ae3cd28be3814369557644fe2e5", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.4.tgz", "integrity": "sha512-LlNEyddp3LH+R1R/GV9C7AST6046GWA26CO6y8qXPBnNQL8TTMOHyevpO5jogLUxwYSwtGmNA3dxnVPWPkpLWw==", "signatures": [{"sig": "MEUCIAuOlgNpCUoOl16+hiOIMOSTmMWHBxTInzzsF/6nz3bwAiEA188gzvZf9dATCtnnWoWLwyLoHtBCeSQs49hx/p5oRgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "538aa52169d81ae3cd28be3814369557644fe2e5", "gitHead": "5a95f65c15ac05b012143b17e41fc50d69f9d764", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.4.tgz_1464285506163_0.00603063334710896", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.5": {"name": "react-countup", "version": "1.1.5", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.5", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "1d6cf1223aff942fcaf6f65dec8443f2ffdf3939", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.5.tgz", "integrity": "sha512-8ia+akvNWFDp9lZ2gn/Go3XfbvwZwMEsQp3h09Q/ZAXBlARa09R7KBvHrhcO/xyybr/n6+vrFvJzvEFx/PN+fQ==", "signatures": [{"sig": "MEQCIBuAOfbtaeZc49M9/7QfgPJvp2W1YAML2CRG4bhkW70qAiBB9RVJAYvphJCLB7GXawZRJrFOYKPKQefZnEMJBPlzPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "1d6cf1223aff942fcaf6f65dec8443f2ffdf3939", "gitHead": "4d8be418107b219fb0acf0f5f30c2550ea3a4b3e", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.5.tgz_1464294714898_0.05409384798258543", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.6": {"name": "react-countup", "version": "1.1.6", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.6", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "610bd3915dfa1cdaf0ff6290d697b48cdeeb8882", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.6.tgz", "integrity": "sha512-GC932QAQ52miAYIkzbcKT1C7q54NkGdQJ1oai5nz/6Dvizkb5Ca1umVsjuA6ywgl1ACmeaYQS2XJAEp5hTgsvg==", "signatures": [{"sig": "MEYCIQDDK70e/ZupND+qSuV3ymag6EDMuLSOrpxY7BUNHUiq3gIhAK43ZrPO2uzSYLnyj0h+zemt+QmXF1frD4/3vsWJ26Tx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "_shasum": "610bd3915dfa1cdaf0ff6290d697b48cdeeb8882", "gitHead": "288e400b712af6888db36d5a8d7c15e793610d5c", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.6.tgz_1464297550964_0.32345536071807146", "host": "packages-16-east.internal.npmjs.com"}}, "1.1.7": {"name": "react-countup", "version": "1.1.7", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.7", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "a1829800b553d2db97865b830713fa436ed5e5c8", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.7.tgz", "integrity": "sha512-/Psa+aAuO9zrtqXAx0l5Gr+RLzGjDzR0AMbzYRrPdRbLq2XGh66ctU7YpOwiy6r0jsVdof6ThuAGAbSM5IA4gQ==", "signatures": [{"sig": "MEYCIQCFuRc/1vBwhi9M/Pkqlldj/aL+wU1NCKzntb+7rkz5mQIhAJVzKZuNv/mbOgnYfNSqdr7rm8ZFY84GY/ZEw7LVIOQg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "src"], "_shasum": "a1829800b553d2db97865b830713fa436ed5e5c8", "gitHead": "2a8ed47d8899513cd436c5c58f3e00020fcac380", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.8.9", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.0", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.0", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.10.2", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.6.2", "express": "^4.13.4", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.9.0", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.9.0", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^3.3.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.5.2", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.1.1", "html-webpack-plugin": "^2.17.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.8.0", "eslint-plugin-jsx-a11y": "^1.2.2", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.7.tgz_1464330125983_0.18696339009329677", "host": "packages-12-west.internal.npmjs.com"}}, "1.1.8": {"name": "react-countup", "version": "1.1.8", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.1.8", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "c36f5b2c83b46f8d0b9988d576afaa485aca0493", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.1.8.tgz", "integrity": "sha512-F1iut+PWsZuAtMqcnlDgHSUzdP3k0t+BZNGd3TDW3r17QyyqbXwJyZK0fKTyFY/SMfJePXSIfjJoYDX11jv90w==", "signatures": [{"sig": "MEQCIAo7Riu4Qhdv70z2GAL5JoEEJ0rFh4LPv3UgMlEABhOqAiACt2hZEeRYN0O6zcauMb5J3BnpBpMtIgOOMI51Gz68XA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib", "src"], "_shasum": "c36f5b2c83b46f8d0b9988d576afaa485aca0493", "gitHead": "6100bef85a05540aa69ecb0a4e002d080069e11d", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"react": "^15.1.0", "react-dom": "^15.1.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.2.1", "mocha": "^2.5.3", "enzyme": "^2.3.0", "eslint": "^2.13.1", "expect": "^1.20.1", "rimraf": "^2.5.2", "cssnano": "^3.7.1", "express": "^4.14.0", "webpack": "^1.13.1", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.10.1", "coveralls": "^2.11.9", "cross-env": "^1.0.8", "babel-core": "^6.10.4", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.4.0", "sanitize.css": "^4.0.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.9.1", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.6.0", "babel-preset-react": "^6.5.0", "react-highlight.js": "^1.0.2", "babel-preset-es2015": "^6.9.0", "eslint-plugin-react": "^5.2.2", "html-webpack-plugin": "^2.21.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^9.0.1", "eslint-plugin-import": "^1.9.2", "eslint-plugin-jsx-a11y": "^1.5.3", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.10.0", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.1.0", "extract-text-webpack-plugin": "^1.0.1"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.1.8.tgz_1466599750699_0.5045111021026969", "host": "packages-12-west.internal.npmjs.com"}}, "1.2.0": {"name": "react-countup", "version": "1.2.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.2.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "3f9c7edbdb624df14470ee4a65516b68bdbe864c", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.2.0.tgz", "integrity": "sha512-bn9skW/SwAoOFeqYDaKTtYIAPlm8mZoyXgFxA+MErzmBUfn9uFXTtlCmlmWVSqeYChWkFsRsKxjMtrInhfZdfA==", "signatures": [{"sig": "MEUCIQCk8nV/4W0oLmtF0NfEg8xdNodQe2UkMCY8PEHZ9f1MPwIgItY9ou5oIIOXpM0hrGAQaLqqFJS6V0B5PZLQ06H1kx8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "3f9c7edbdb624df14470ee4a65516b68bdbe864c", "gitHead": "86aaf739980cb77cc578d3510bfcd0893b6059d0", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "npm run clean:lib && npm run clean:demo", "start": "node server", "deploy": "npm run build:demo && npm run deploy:demo", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "clean:lib": "<PERSON><PERSON><PERSON> lib", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "build:demo": "npm run clean && npm run build && cross-env NODE_ENV=production webpack -p --config webpack.config.prod --progress", "clean:demo": "<PERSON><PERSON><PERSON> build", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "deploy:demo": "gh-pages -d build", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.3.1", "dependencies": {"react": "^15.3.0", "react-dom": "^15.3.0", "countup.js": "^1.7.1"}, "devDependencies": {"jsdom": "^9.4.1", "mocha": "^3.0.2", "enzyme": "^2.4.1", "eslint": "^3.2.2", "expect": "^1.20.2", "rimraf": "^2.5.4", "cssnano": "^3.7.3", "express": "^4.14.0", "webpack": "^2.1.0-beta.20", "gh-pages": "^0.11.0", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.11.4", "coveralls": "^2.11.12", "cross-env": "^2.0.0", "babel-core": "^6.13.2", "css-loader": "^0.23.1", "babel-loader": "^6.2.4", "highlight.js": "^9.6.0", "sanitize.css": "^4.1.0", "style-loader": "^0.13.1", "babel-polyfill": "^6.13.0", "postcss-loader": "^0.9.1", "postcss-cssnext": "^2.7.0", "babel-preset-react": "^6.11.1", "react-highlight.js": "^1.0.4", "babel-preset-es2015": "^6.13.2", "eslint-plugin-react": "^6.0.0", "html-webpack-plugin": "^2.22.0", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-0": "^6.5.0", "eslint-config-airbnb": "^10.0.0", "eslint-plugin-import": "^1.12.0", "eslint-plugin-jsx-a11y": "^2.0.1", "webpack-dev-middleware": "^1.6.1", "webpack-hot-middleware": "^2.12.2", "babel-preset-react-hmre": "^1.1.1", "react-addons-test-utils": "^15.3.0", "extract-text-webpack-plugin": "^2.0.0-beta.3"}, "peerDependencies": {"react": "^0.14.0 || ^15.0.0", "react-dom": "^0.14.0 || ^15.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.2.0.tgz_1470829848113_0.5471609877422452", "host": "packages-12-west.internal.npmjs.com"}}, "1.3.0": {"name": "react-countup", "version": "1.3.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@1.3.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "6c245b3f6b77a8d54c5492e5c15e32adee976ed3", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-1.3.0.tgz", "integrity": "sha512-vFX6aZOgDf9L5LTEixcKfkfsheaGIjPgm/UnzI2FTmUGp5syeFAxprA3ep/nxk2Ull0nHWJ3z0WEeGnfXInFDw==", "signatures": [{"sig": "MEYCIQDn+7U2OHEEJ7gndeoyJirqIn7uD5Pk/4D7rLpO2VlviwIhAOThikbUMCOMkC6CoBkbFVdzu42x8ggUbA5/EgyKj5Kk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "lib/index.js", "_from": ".", "files": ["lib"], "_shasum": "6c245b3f6b77a8d54c5492e5c15e32adee976ed3", "gitHead": "ec1d4d5aadf7b313aa50029f6d5edef307e80929", "scripts": {"lint": "eslint .", "test": "cross-env NODE_ENV=test mocha --opts mocha.opts", "build": "cross-env NODE_ENV=production babel src -d lib", "clean": "<PERSON><PERSON><PERSON> lib", "pretest": "npm run lint", "prebuild": "npm t && npm run clean", "coveralls": "npm run test:coverage && cat ./coverage/lcov.info | coveralls", "prepublish": "npm run build", "test:watch": "npm t -- --watch", "test:coverage": "cross-env NODE_ENV=test istanbul cover _mocha --report lcovonly -- -R spec --opts mocha.opts"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "3.10.8", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "6.9.1", "dependencies": {"react": "^15.3.2", "react-dom": "^15.3.2", "countup.js": "^1.7.1", "react-addons-shallow-compare": "^15.3.2"}, "devDependencies": {"jsdom": "^9.7.1", "mocha": "^3.1.2", "enzyme": "^2.4.1", "eslint": "^3.8.0", "expect": "^1.20.2", "rimraf": "^2.5.4", "istanbul": "^1.0.0-alpha.2", "babel-cli": "^6.16.0", "coveralls": "^2.11.14", "cross-env": "^3.1.3", "babel-core": "^6.17.0", "babel-loader": "^6.2.5", "babel-polyfill": "^6.16.0", "babel-preset-react": "^6.16.0", "babel-preset-es2015": "^6.16.0", "eslint-plugin-react": "^6.4.1", "mocha-lcov-reporter": "^1.2.0", "babel-preset-stage-2": "^6.17.0", "eslint-config-airbnb": "^12.0.0", "eslint-plugin-import": "^2.0.1", "eslint-plugin-jsx-a11y": "^2.2.3", "react-addons-test-utils": "^15.3.2"}, "peerDependencies": {"react": ">=0.14.0", "react-dom": ">=0.14.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-1.3.0.tgz_1476991533068_0.6553967206273228", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.0": {"name": "react-countup", "version": "2.0.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@2.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "d2a873bc41b781ef43e9e514d0accd2a0af8edb2", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.0.0.tgz", "integrity": "sha512-+nw8x8wPfzfBpMxQIClLmia21dKwUJAFWhXRShu+x7KtMkLZBFcobyt69GIMiZipxztJ4DF74MoPRh4mFFSBpw==", "signatures": [{"sig": "MEUCIA6uEun+jTyV7+RHHmOwRStAcCoRpg5TyK4iGPm8eBijAiEA0O1pz1LghN0QOkJQJ2uiFjBC1qOrQYjO+vh6xcSUz2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["index.js", "build"], "scripts": {"flow": "flow", "lint": "eslint index.jsx --ext .js,.jsx --ignore-path .gitignore", "test": "jest --coverage", "build": "babel index.jsx -d build", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"react": "15.4.2", "countup.js": "1.8.2"}, "devDependencies": {"jest": "19.0.2", "babel": "6.23.0", "eslint": "3.17.1", "flow-bin": "0.41.0", "babel-cli": "6.23.0", "react-dom": "^15.4.2", "babel-eslint": "7.1.1", "babel-preset-env": "1.2.1", "babel-preset-react": "6.23.0", "eslint-plugin-react": "6.10.0", "react-test-renderer": "^15.4.2", "babel-preset-stage-2": "6.22.0", "eslint-config-airbnb": "14.1.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-flowtype": "2.30.3", "eslint-plugin-jsx-a11y": "4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.0.0.tgz_1489164208223_0.9995099583175033", "host": "packages-18-east.internal.npmjs.com"}}, "2.0.1": {"name": "react-countup", "version": "2.0.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@2.0.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "af70edb7b06994b0b231a8fac056cec1e5ac661c", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.0.1.tgz", "integrity": "sha512-i3OTt+a/shpjnZzGbmzioCEFbONDXTrHvnvj/qfSbZPKDG/AJib2jVbt5AKcYvEl4GUDoN2p8BWFYeDmuP945Q==", "signatures": [{"sig": "MEUCIGCWs8/ZxXO6b4NXiJ3lBXOOz3AlsEeSY7iHhJyVEyKZAiEAnKWzRM9qsUWAEvEO4JFJlrUVgIoUBW7ZRctHtUNdI88=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["index.jsx", "build"], "scripts": {"flow": "flow", "lint": "eslint index.jsx --ext .js,.jsx --ignore-path .gitignore", "test": "jest --coverage", "build": "babel index.jsx -d build", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"react": "15.4.2", "countup.js": "1.8.2"}, "devDependencies": {"jest": "19.0.2", "babel": "6.23.0", "eslint": "3.17.1", "flow-bin": "0.41.0", "babel-cli": "6.23.0", "react-dom": "^15.4.2", "babel-eslint": "7.1.1", "babel-preset-env": "1.2.1", "babel-preset-react": "6.23.0", "eslint-plugin-react": "6.10.0", "react-test-renderer": "^15.4.2", "babel-preset-stage-2": "6.22.0", "eslint-config-airbnb": "14.1.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-flowtype": "2.30.3", "eslint-plugin-jsx-a11y": "4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.0.1.tgz_1489166105903_0.2541937995702028", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.2": {"name": "react-countup", "version": "2.0.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@2.0.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ef2cb4b65730997ec45f7258ab55168e2ed1769a", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.0.2.tgz", "integrity": "sha512-6QEPpQ7DQgJLJJqDLouZlm0RHlDdbCtPHVl27l7U3CR4xttUnjHj3yBGhC2P+0FP3ZskAe6m6bP5YV+OcdgplQ==", "signatures": [{"sig": "MEQCIDX69N0dMphH6GFSb0b6I6GIOVwqpbyXbJNmRDRSp1OgAiBJkYC5Kyr9ab4UjvPyPgwOl1fMk8d2Qw+emUqmDd+bTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "_from": ".", "files": ["index.jsx", "build"], "_shasum": "ef2cb4b65730997ec45f7258ab55168e2ed1769a", "gitHead": "9e321eadc55f9109dc59efbcc52c54b02c7d28b6", "scripts": {"flow": "flow", "lint": "eslint index.jsx --ext .js,.jsx --ignore-path .gitignore", "test": "jest --coverage", "build": "babel index.jsx -d build", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"react": "15.4.2", "countup.js": "1.8.2"}, "devDependencies": {"jest": "19.0.2", "babel": "6.23.0", "eslint": "3.17.1", "flow-bin": "0.41.0", "babel-cli": "6.23.0", "react-dom": "^15.4.2", "babel-eslint": "7.1.1", "babel-preset-env": "1.2.1", "babel-preset-react": "6.23.0", "eslint-plugin-react": "6.10.0", "react-test-renderer": "^15.4.2", "babel-preset-stage-2": "6.22.0", "eslint-config-airbnb": "14.1.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-flowtype": "2.30.3", "eslint-plugin-jsx-a11y": "4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.0.2.tgz_1489167861709_0.5704993503168225", "host": "packages-12-west.internal.npmjs.com"}}, "2.0.3": {"name": "react-countup", "version": "2.0.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "react-countup@2.0.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "c6c777f90782773ea6c2ec652dfcdbd6bceb1429", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.0.3.tgz", "integrity": "sha512-oaNmvcDBnRRYPkyGTha9+OidOC/EbZXoS8mQqz9CsGcFlHbMSRZE4HUYKUirsBwnQLXp8cWTllD6dEyPz8rW1Q==", "signatures": [{"sig": "MEQCIF0oHALHD19s0yuWqmwXJMrvQMhEzu+F4816NZgjrSPAAiB+svNspBu4xAA4yfACAEA9NQLlOr2KRLXbN2DB7D25uA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "_from": ".", "files": ["index.jsx", "build"], "_shasum": "c6c777f90782773ea6c2ec652dfcdbd6bceb1429", "gitHead": "646fd8e83fe87fe627aba848a3317db631ff1b7a", "scripts": {"flow": "flow", "lint": "eslint index.jsx --ext .js,.jsx --ignore-path .gitignore", "test": "jest --coverage", "build": "babel index.jsx -d build", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "4.1.2", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "7.7.2", "dependencies": {"react": "15.4.2", "countup.js": "1.8.2"}, "devDependencies": {"jest": "19.0.2", "babel": "6.23.0", "eslint": "3.17.1", "flow-bin": "0.41.0", "babel-cli": "6.23.0", "react-dom": "^15.4.2", "babel-eslint": "7.1.1", "babel-preset-env": "1.2.1", "babel-preset-react": "6.23.0", "eslint-plugin-react": "6.10.0", "react-test-renderer": "^15.4.2", "babel-preset-stage-2": "6.22.0", "eslint-config-airbnb": "14.1.0", "eslint-plugin-import": "2.2.0", "eslint-plugin-flowtype": "2.30.3", "eslint-plugin-jsx-a11y": "4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.0.3.tgz_1489168869749_0.5804777997545898", "host": "packages-18-east.internal.npmjs.com"}}, "2.1.0": {"name": "react-countup", "version": "2.1.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@2.1.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "22e7ed78361fae1c842e48d385b60ab20581b435", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.1.0.tgz", "integrity": "sha512-LiofQn4tglH82U79CUFnrmGGadg8QB48GQHt+JAczK4Ws8K3gTIkI5R0kRvsEb4+MxnbXTIJRF2bi3+H1/0JSg==", "signatures": [{"sig": "MEUCIQCH8lqwPifvFXds7Tzcwy4CYjb00hCFRyRypPfpgNzNSQIgKQC7obomDP6ZTSWDExWLsNHppEo4HguWdUWZKvtDmP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["index.jsx", "build"], "scripts": {"flow": "flow check", "lint": "eslint --ext .js,.jsx index.jsx", "test": "jest --coverage", "build": "babel index.jsx -d build", "precommit": "lint-staged", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2017 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"react": "^15.5.4", "countup.js": "^1.8.5"}, "devDependencies": {"jest": "^20.0.0", "babel": "6.23.0", "husky": "^0.13.2", "eslint": "^3.15.0", "flow-bin": "^0.45.0", "babel-cli": "^6.24.1", "react-dom": "^15.5.4", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.4.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^6.9.0", "react-test-renderer": "^15.5.4", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.32.1", "eslint-plugin-jsx-a11y": "^3.0.2 || ^4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.1.0.tgz_1494357612167_0.08711475692689419", "host": "packages-12-west.internal.npmjs.com"}}, "2.1.1": {"name": "react-countup", "version": "2.1.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@2.1.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "0fccacfd6d2d9045d347d07e4c0e18b1c1fcd33f", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.1.1.tgz", "integrity": "sha512-AsPrIy1EbsrKEr4wiRoiCz7RVuTxDtC8obi36DkF7nPOVranLcIfDSvv0hF5rCznlLw/MYNzLEOvsV/XAQgwzQ==", "signatures": [{"sig": "MEUCICZJ+mipfwfIXx135nJoHK6Ix4C0A3ru/fOFMpSAqjFnAiEAnkmjRvLAjBkiFEZd1fFhNHNq6ohMIiACxfURGpPXQsY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "_from": ".", "files": ["index.jsx", "build"], "_shasum": "0fccacfd6d2d9045d347d07e4c0e18b1c1fcd33f", "gitHead": "0a3cbe3c20a4bb9c8f530fb64256cbcf477f722b", "scripts": {"flow": "flow check", "lint": "eslint --ext .js,.jsx index.jsx", "test": "jest --coverage", "build": "babel index.jsx -d build", "precommit": "lint-staged", "prepublish": "yarn run build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "4.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "7.10.0", "dependencies": {"countup.js": "^1.8.5"}, "devDependencies": {"jest": "^20.0.0", "babel": "6.23.0", "husky": "^0.13.2", "react": "^15.5.4", "eslint": "^3.15.0", "flow-bin": "^0.45.0", "babel-cli": "^6.24.1", "react-dom": "^15.5.4", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.4.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^6.9.0", "react-test-renderer": "^15.5.4", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^14.1.0", "eslint-plugin-import": "^2.2.0", "eslint-plugin-flowtype": "^2.32.1", "eslint-plugin-jsx-a11y": "^3.0.2 || ^4.0.0", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.1.1.tgz_1494359552737_0.32070460682734847", "host": "packages-18-east.internal.npmjs.com"}}, "2.2.0": {"name": "react-countup", "version": "2.2.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@2.2.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "a10b740511f55ad0c200cc36a30fcd3d22ec0ab8", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.2.0.tgz", "integrity": "sha512-iVG0UbV0zWkO/L1gJ1gEPC9pZ4wHMfePS5MlfAWlyec5TSQduaHgivgGfujj7EO2EDpFp5ofNT0Df1sgqaOJCg==", "signatures": [{"sig": "MEUCIQDRuFhG+eEfm8YgLtUVHTp0js0vFVupTz9TsoJzGqDpPAIgYkZvu1SiZ4IkOC4bJJOVPo5DTpb090LjeEf9GTn7Ef0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["index.js", "build"], "gitHead": "971ce6987df2ceaa3acda18c6be4ca267cce171b", "scripts": {"flow": "flow check", "lint": "eslint --ext .js,.jsx index.js", "test": "jest --coverage", "build": "babel index.js -d build", "prepare": "yarn run build", "prettier": "prettier --single-quote --trailing-comma all --parser flow --write index.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.2.1", "dependencies": {"countup.js": "^1.8.5"}, "devDependencies": {"jest": "^20.0.4", "husky": "^0.14.3", "react": "^15.6.1", "eslint": "^4.4.1", "flow-bin": "^0.52.0", "prettier": "^1.5.3", "babel-cli": "^6.24.1", "react-dom": "^15.6.1", "lint-staged": "^4.0.3", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^15.6.1", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-flowtype": "^2.35.0", "eslint-plugin-jsx-a11y": "^5.0.3", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": ">=0.15.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.2.0.tgz_1502409533188_0.3681894347537309", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "react-countup", "version": "2.3.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@2.3.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "c9acd9447609d581186240c8654c2ef0e40667ed", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.3.0.tgz", "integrity": "sha512-jnPP1shLIi309jhR0lDmoemTwh94lq0J3IpIDfSLFPQJfjGfmLCV0valWhDmiiWnDBZb4nzWWPAqCxrYlm2zAw==", "signatures": [{"sig": "MEQCIEY6nStaTwhPv7hWS9tVL0ZGe+SVsN4zsBp86XMYurN1AiAVUGDU08JyObitHfLPvEf2BK2GA3RwTg1hJtmziSja6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["build", "src"], "gitHead": "52fc83d7ef758c5737d60d0561b5ade5fcc007f5", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest --coverage", "build": "babel src/index.js -o build/index.js", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.4.2", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.6.0", "dependencies": {"countup.js": "^1.8.5"}, "devDependencies": {"raf": "^3.3.2", "jest": "^20.0.4", "husky": "^0.14.3", "react": "^16.0.0", "eslint": "^4.4.1", "flow-bin": "^0.52.0", "prettier": "^1.5.3", "babel-cli": "^6.24.1", "react-dom": "^16.0.0", "lint-staged": "^4.0.3", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.6.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^16.0.0", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-flowtype": "^2.35.0", "eslint-plugin-jsx-a11y": "^5.0.3", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.3.0.tgz_1506683748863_0.3185385169927031", "host": "s3://npm-registry-packages"}}, "2.4.0": {"name": "react-countup", "version": "2.4.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@2.4.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "efdd5579fc9b38a1178659a9a047b95c2efa8445", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-2.4.0.tgz", "integrity": "sha512-V9Egn7WkbbRZwCUQvJWfZP7ueLy6XFVqWIazdIsZOB7FdSZDe9LMrx9KyVuoiaWDfAYlWPGayM6E7HIzCa/f6g==", "signatures": [{"sig": "MEUCIQD4lQ6Xnlkgj7d4dHrztjNUJvNKiY+9cewh9YHLE7vbXwIgBovR8SW2cPZhfZoGYnY8VlhQrnSJlNVIFihu4do7DyI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["build", "src"], "gitHead": "290f2cfc03289512e3524d549617e5fb33f0d0aa", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest", "build": "babel src -d build --ignore __tests__", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.8.1", "dependencies": {"countup.js": "^1.8.5"}, "devDependencies": {"raf": "^3.4.0", "jest": "^20.0.4", "husky": "^0.14.3", "react": "^16.0.0", "eslint": "^4.10.0", "flow-bin": "^0.52.0", "prettier": "^1.7.4", "babel-cli": "^6.24.1", "react-dom": "^16.0.0", "lint-staged": "^4.3.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^16.0.0", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^15.1.0", "eslint-plugin-import": "^2.7.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-jsx-a11y": "^5.0.3", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-2.4.0.tgz_1509443243276_0.33369345078244805", "host": "s3://npm-registry-packages"}}, "3.0.0": {"name": "react-countup", "version": "3.0.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@3.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "7f69f998349bce614abc4338b08a9e75833d320b", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-3.0.0.tgz", "integrity": "sha512-yfJfDlbQjuWkJRHzr1NHeXFF+BSA7iKFVpIsphNuplEe04lHxU2yYS+AUyLwYdfjMefzOPzPqut3+A90JS9JKg==", "signatures": [{"sig": "MEQCIBmtgqYd2yHlmCxUCgs/Yovt2YAk73yJikGVg1x8cfgiAiBMr96ZwY7wOV/YBFjA884mEhy48QTUyymJiSWSWOxezA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["build", "src"], "gitHead": "0cb26ebcf2671773516121cbb58a751f340da48a", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest", "build": "babel src -d build --ignore __tests__", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"countup.js": "^1.8.5"}, "devDependencies": {"raf": "^3.4.0", "jest": "^21.2.1", "husky": "^0.14.3", "react": "^16.0.0", "eslint": "^4.10.0", "flow-bin": "^0.58.0", "prettier": "^1.7.4", "babel-cli": "^6.24.1", "react-dom": "^16.0.0", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.1", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^16.0.0", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.7.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-jsx-a11y": "^6.0.2", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-3.0.0.tgz_1509657608131_0.6805883238557726", "host": "s3://npm-registry-packages"}}, "3.0.1": {"name": "react-countup", "version": "3.0.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@3.0.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "cbca3589da71d40bccae8cce19773c80ab0e15b6", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-3.0.1.tgz", "integrity": "sha512-cgmn5GP1BMP8/MeEg4A7PlUMcIveaUI+jbmSpEwbCkIZ0TNh/rz9cKITidpthqlyNDEvBjQZcfNB2r/91odO6w==", "signatures": [{"sig": "MEUCIFQOA2O44fSSHWhgPVHL41qvTVgJQ3NiwLBclNqayFRzAiEAzxD6ku51QR8CK0tQNR9KOL5Pavf1yp/WaPBs7Waa9oI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["build", "src"], "gitHead": "0aac569cc09f7f651d2b2253c0c46377f5516ac4", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest", "build": "babel src -d build --ignore __tests__", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"countup.js": "^1.9.3"}, "devDependencies": {"raf": "^3.4.0", "jest": "^21.2.1", "husky": "^0.14.3", "react": "^16.0.0", "eslint": "^4.10.0", "flow-bin": "^0.58.0", "prettier": "^1.8.1", "babel-cli": "^6.24.1", "react-dom": "^16.0.0", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.2", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^16.0.0", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.7.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-jsx-a11y": "^6.0.2", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-3.0.1.tgz_1510078571879_0.657061860896647", "host": "s3://npm-registry-packages"}}, "3.0.2": {"name": "react-countup", "version": "3.0.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@3.0.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "26b7e27a91cdefc8ec744704ec93b1cbd9e638f0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-3.0.2.tgz", "integrity": "sha512-tqZIJrdByp0kvGcQAgbOOKm70i6hp6+S5Eo9wFoYTdmFvwX0v/NiebvOKChBygMHN1XLy6O3jno9J7zSpMY3eQ==", "signatures": [{"sig": "MEUCIFlvgDirK2GUeqw4ylvSS3YWPoTlF+RsOhUptsihnLysAiEAoXISkMw3V6n653XVylxPNEqt6+6sEMXGlnZ7ER3i9fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "build", "files": ["build", "src"], "gitHead": "68992c1aadcd5c51129f5a84533ae1a414009e31", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest", "build": "babel src -d build --ignore __tests__", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.5.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "8.9.0", "dependencies": {"countup.js": "^1.9.3"}, "devDependencies": {"raf": "^3.4.0", "jest": "^21.2.1", "husky": "^0.14.3", "react": "^16.0.0", "eslint": "^4.10.0", "flow-bin": "^0.58.0", "prettier": "^1.8.1", "babel-cli": "^6.24.1", "react-dom": "^16.0.0", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.2", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.2.0", "react-test-renderer": "^16.0.0", "babel-preset-stage-2": "6.24.1", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.7.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-jsx-a11y": "^6.0.2", "babel-plugin-transform-flow-strip-types": "6.22.0"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup-3.0.2.tgz_1510079768133_0.19953022804111242", "host": "s3://npm-registry-packages"}}, "3.0.3": {"name": "react-countup", "version": "3.0.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@3.0.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "e698a4e28964fe0322a925f52929e3b08db714fd", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-3.0.3.tgz", "fileCount": 7, "integrity": "sha512-QXlDzvZiOrx0MJkUqAJyqCj3zqVUFWYBTIPJIyC04LLBOprHAStxIBmB8l20Xbs1S4uuUFaliXucl02fVOpwAw==", "signatures": [{"sig": "MEUCIAEuVdp3AwXOjH6X7JrQlqdiezEMRdRgRuOwGnmukhiqAiEAyIbNZ2wz+OA/qZ7MjRDdltu3SnSdSZHpYtNu9GDkOQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19540}, "main": "build", "files": ["build", "src"], "gitHead": "d0002932dac8a274f951e53b1d9b1f4719176147", "scripts": {"flow": "flow check", "lint": "eslint src", "test": "jest", "build": "babel src -d build --ignore __tests__", "prepare": "yarn run build", "prettier": "prettier --write src/**/*.js", "precommit": "lint-staged"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "5.6.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "9.5.0", "dependencies": {"countup.js": "^1.9.3"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^21.2.1", "husky": "^0.14.3", "react": "^16.1.1", "eslint": "^4.11.0", "flow-bin": "^0.59.0", "prettier": "^1.8.2", "babel-cli": "^6.26.0", "react-dom": "^16.1.1", "lint-staged": "^5.0.0", "babel-eslint": "^8.0.2", "babel-preset-env": "^1.6.1", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.4.0", "react-test-renderer": "^16.1.1", "babel-preset-stage-2": "^6.24.1", "eslint-config-airbnb": "^16.1.0", "eslint-plugin-import": "^2.7.0", "eslint-config-prettier": "^2.7.0", "eslint-plugin-flowtype": "^2.39.1", "eslint-plugin-jsx-a11y": "^6.0.2"}, "peerDependencies": {"react": "^16.0.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_3.0.3_1518521377462_0.7369761747975339", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.0": {"name": "react-countup", "version": "4.0.0-alpha.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://glennreyes.github.io/react-countup", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "4a8d8e6602b57248d0ba3480a1d5eed75e3f858e", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.0.tgz", "fileCount": 8, "integrity": "sha512-I577eyW3h4ZPb+jU53iLLHgJoJ+F8J94yaud096RehUsChWO3/vO3lm4cuS1O+GG55VwpWClMbMdEndoU2d4Yg==", "signatures": [{"sig": "MEQCIAfk3MIrYKi1Zp0+BNuSta8JL9zpQDwmgh66WJMKgROzAiAvbsXGFzoTANdBYlQ/erQFyzA5o4ulBAzHzdVVCSse6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbhGaCRA9TVsSAnZWagAADIcP/0K8FyaBA53sp/g+ML7g\n16TyfMWzcndAXRrGLQ3LzqjSOHP0TsYkKzVcVq81GGyb8sFbgv3mMaGUCQHp\nyUbr6HUOfRtkajJQKXuFeap+xCsrlBDYxyZcu4QD5m6uokpcj6NCEHW91ofP\nBFKRdk+593rSW4K2SegZPDVW5h8rZrqGkFbHbF1agJ12OhdZLtyKZMAeAv4m\nxAhri3WgISrThXXAq8KrMj41WvvrDWStH8taHuxVyr67Xs/qoxSalHmHhNqg\nq0pHW504ry5MljKNbNCNtHP3c3W6bORkKlwKJ9hCP0jFSHF2hhQtQlT14OuI\n+zJuSbrEzJt7MgIeLosxU559UWF0DjJA4aPdJxzHlxUldjz0Q3CoIHl03P8U\nUz7Kt0cjMX4LzFcZdEJbe4X8uSY/2dqA1otdBmp2M2x486TXxOv7/gbRgdiS\nDsNV8ZW7m3b9tgHPnyG7ACPfDPMeioCn8AkJBoukzCGFcB7WQuoa7xrS+ACw\n07ARmavOfu83utGzDGm1cUcv1R6htOd6HkGtZuq9n9HxCisv3uSWGwaBgUvN\nzLZw45B833n2Y9mhUqfhIln+yam4Jng4heDWgtkOfOGycgAlCFMSmAgTzj4K\nhC97b/QCGPRTq8sHZ12io/YmtkUU5n5cvsX0kOyESRIBB8McmP1SLxFUjOIv\n5xVx\r\n=YdKQ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "43b523dd789471a8bc10db4d7a4e9a406ab661fb", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.4.2", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.1", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^4.1.7", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.0_1533940121492_0.7732562628478303", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.1": {"name": "react-countup", "version": "4.0.0-alpha.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "0917c5c9ccfe0ca985796953a7c73fa84d38575c", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.1.tgz", "fileCount": 8, "integrity": "sha512-a1a2nViLAD6n79pdAb4nJZjZbe8SBvt43zLbdg0UFZK2LoWMIyziKD6GvDFjPbEsw8JoiM4mxjNAC6bm59qqPw==", "signatures": [{"sig": "MEUCIHC3R1Gd5f+GbLdekYQ155YO0fzGEmXeAKkab9sljQVBAiEAyTLAQOFBUB1zAZOnk8zGid4AVeMO/CJLl4HYEQCu0E4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbiDTCRA9TVsSAnZWagAAF9sP/jZJqfVi7kvkdEZdIzx8\nsiB8uV8HrQzCin1qxqa0juQUbFCZVmJY8jz5+n/N8V3JjnittvNV+yncTZkV\nkJoNEHZ+sDAvsN8nKGjVA9jA1ThyeOgRHQfXtyWx121K5JtJIJMUcYgLihel\nVYZMDMe+ZpYcow34QZXncuYwKviGctvU1k+dND4njLIOXORXi9Zcl9gd4bYI\nmyZ9ZM7G11CyRoBj/CUPKoDILYKcTgAsUCLGtVxx14UYEpequczYjyzK46zz\nJEISSbIz/LRz47fArzpMCdikSxmnNFgPWLImp5XEzJIy+YEmc0xr41w6vcmE\nuYU3wjH9csPyJSX7LVnyvO4Hex/WW1qC08y21HSor5jnyH6I9wSF5QBopAdq\nh4C+XNsqdjhLN9DbxnmMag5xGmKNXeP6lcTAcLpG2s9k1fZfzPvUPWW7E5xT\nq9cTt5q7/iLsJIzgC/xBf4ZN8O2odl5spY3BCirvbZSTb/mjrkitlg/eez77\nJPyhl0voNRQVjvelhW+bd8k2ha6m9M+YcdCiU5i2YOd14n1uE1+N1TjcMg1S\n5gZI81f2+QngRTRqjRlvYowNOXuE/gWmYaArDQ4jzHcacsJfbks/wIX33Czf\nFMWFMDTOFBSHXVBWplhptFhjdfM1SSeM6oxmaEmDcK5KGY6T/oL6mDLgBUoo\n01XF\r\n=0jVn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "621734d1f49a1c50157a47eeb250b331dc9d07fc", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.4.2", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.1", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^4.1.7", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.1_1533944019028_0.7711742155878845", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.2": {"name": "react-countup", "version": "4.0.0-alpha.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "8203bfdd22e23f4629f9d9daac8058879d34de40", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.2.tgz", "fileCount": 8, "integrity": "sha512-l46M2T9+BnzqopnFFLbM1vpRsKYnK1liMZy1kw6FykR6oY0joIij4ucKE/duhJIj06vi7V24L7M6CGbUA6oijw==", "signatures": [{"sig": "MEUCIFfWrDIpOZ3M81mhxb66Zjk6zU/3pudiDy1QwHGGyw7sAiEA8vsZBnU3AT5Vhp3R4XoguuZ3j03G2tP1+etSg76w7Ok=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28177, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbipzCRA9TVsSAnZWagAAuI4P/A/mvlIopGZVzKuC3S8P\nrJJV2Jl1pIGYsDJ0EjckMnufIfV6el41aK2wSSvKOHis1TA1sL+S9r3/XQjZ\nQ6u7tdA3e/sz/ACdIizGi4P5tzZw4ayCi/A3ALCF3UY9Cq5+FVC7zTSkMWMo\nXi+fLeN5pyCpi9TFkVDhJ6BjoOwJnwMB3BlHl26EHr1AuWNyHnmszlP6/bb7\nA6cdYSJs3noqPsx1uc0BsvMVJc8xdPSAtne416ubxJJWhoTrji6VuwuKybL3\nxuZ4wc8HBZ85TYmFK99bKjFwOSsDTcZ1SNek36VV7u8VJMluS2d0+PXvvL6T\nq+uOnz2jtHy66tIvbpz6tfVwRfDQZcwqFxGXSE+KVtddILm1skkJDysGmC+j\nOEciMP7/JEIYAJklMhQ3fz5tbQVUsaMe6OlBi9LFwI0HNiKHlg6vwIrCEF9A\n7P1JCyQ2ZkBgov5rvOA4IhFIeHOLtBvuAppAzRAKMOe7xJHrVifGdOb/flhd\n8HpeDzL42smvYLgkYJsLG/HiqnwqO9LEp0NWR8t4+xT1s3KrQ7/M0+oVEndw\nSX4le9TNcL2l1q+cT50o5cecLxQdN2JJgLko9kuhaOO1Gxvp/rgDDzWxAuAl\n3RM4XhUAB/zAvXMHLEOmNfiO9mI4PlXqJID3BEvIyX13uqebzxDDBROsrItZ\n5R8L\r\n=84Mk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "a69a4681478dba855b7d845cdf8e40e967d0916c", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.5.0", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.2", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^5.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.2_1533946483310_0.20973995648776245", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.3": {"name": "react-countup", "version": "4.0.0-alpha.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "a79f548ec355b7c4cf7a65e2e98fab078781e4d3", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.3.tgz", "fileCount": 8, "integrity": "sha512-JDne3lWktwbIEvUPUSar6UZIGVjgA8/5ttWcPV8inECSz4UjysveMrR0/vqD1MZe9DwLRIT+B7ToESiDsgpu6Q==", "signatures": [{"sig": "MEUCICc3wfrdmbKR/DOjgZgZUS5q6DZwxFPBf3NWIIwGwORiAiEAlNUfoenEUif1SFVy/j+MUWHZPgrzNdc3htqSgmRQQ2Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28566, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcEYgCRA9TVsSAnZWagAAjS0QAJscYJ08onkQB7Z8f4im\nbXQyWaoSws6mxzhH8go08s/mDbuviWRYrKCkuQizew06VTyaIJzh5zMzPHLq\n7yw/k1hsnrlJRraOy0ouvjwnPZxpgabba3Y/lTX3qJpxQJVdYRfLKGF40Zz9\nqOcrdCLW+GmnGCEuhH0OxyTLB9sPxRsrPD+VPXpUp4lZLNXAev7FSv0ekjcT\nVHHhlXzYW6dJjL4ign3eLl34gCMbBO99X4b67a3zkJ7B+sbsgsUzASv/JVCy\nCM0QMI4qxlDGJfuzhEg6rGL6LIrFOfC3XgsZSQGbKUjnzQBQx3VzSYUzBgLc\nV3Clkg5K/s0lP47A5FzJ45VJx2MP/LKjo0xNVXgFFtvIuPidhZaBh6USXT8U\nYaEfd/OUW0TjDvyYSg1aWH0pzPEfHd5qU4QMJ/N4+6irfaQ2yupA+2OoCLaU\n4slHDtHI4XcyiU0QRmLbFOH3oE7YkV6XQBpzHesT+cT6A4ML+kOESAf/Qm0r\n41TonIq2jYtVMO/3To73a8aoEaDDUZPFPIpP64YFSCd0sWXgERKL67K065PI\n3wF9QxX2DeJPuybmkva8bBLGaLJ0kKU70DiswHP5ied/sfinzA7eSW+HW5Ti\nDGYlapHauY0p4UZvWSoJsREcl8bQUoFdGWLbCQ4oBSVDbbljMcasgfNFubUJ\nHtmj\r\n=peN8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "6df1da7917e0f977bfd445b517d1f4f415276974", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.5.0", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.2", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^5.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.3_1534084639746_0.34743732044060227", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.4": {"name": "react-countup", "version": "4.0.0-alpha.4", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.4", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "257f571cb0e001c6785897333d2b88ffea51e8da", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.4.tgz", "fileCount": 8, "integrity": "sha512-t17SfCL9B5DzuZ2Cmq3a7WbM5BON4zF0wiLjBn45FRDdJVWfGr0ymqpsik7lNMRnHogpcRU4pOcjw4eCVX4c4w==", "signatures": [{"sig": "MEQCIHYKk3AscDSfpETOw5BGPKoiruJZ6UPs6SCOpGWCINvaAiB9c+/+EzPjbG/653LXF7ge8aHLtC/HXUnozlK3qwofyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcFKqCRA9TVsSAnZWagAAMzQP/jIADB0mQCwnrz1I+6zl\nqUT5yXQ+p7wLlHK/QN8V/aVrBJIy2NhJxdQkU0Vw3QXX5m08aEvEkRmi9alx\n/LoUfIBJjgboKTe7pFej+HnWmcv1GdAE/jE6oKb+PLmNwb5CQRQLMdPXqi3A\noLIlQeOiwNmnWfjDJu7mWXadWTsrfBKm++pf2lAasjmvG/SZGI1OTfWlwAZe\n6lGby0AuxYYLicc6220KW4u1vAWUvMKLyMIlpxMHkPYq5jvXaSTo2EaJ4R97\nhSrYPPueMlt0mvNtUZjxDKTWKjZrNTNDvb9X4Es80jGOTSIBMXuUpeC/fmCd\nkjIOUhMKYFYUca83FwWiQPmbqik07fHvKPxBqopS54nBxiwej74js2OtC9vM\nLRjPv+BvekYn55fAZ+nq7ptD9XQqdv3vS2MPvWADnEoCF8oKQRlmvEGvfDsx\naJTUbzeK11Lm/OuENzX5tKQpGH+V5qSd4P1U9ou54CKOmBuzSzaaY0F3DWZN\nRjvQuLPwgKV99GGPKafH5tMiMFxxJRKXr9IVtLmktGITdxlpwYubod/7/Z79\nfADyxiaEfeY1Eoixm3WQUd2NPKhx12DQkzdutU/C+HRkdKsILNykHsXg5fFz\nBS2hs9o4+7wohYKppz1MEskHnk1KnAHrg84fVxlu2gQfsDFzg9xQ7D2ECA9b\nz4Kz\r\n=as0n\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "5420295b2f71c01c56b7e6dad9534d754c22d915", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.5.0", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.2", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^5.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.4_1534087850094_0.9256180291587646", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.5": {"name": "react-countup", "version": "4.0.0-alpha.5", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.5", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "67579e4cbe73b9ca20a159bb864dc657a73abf6c", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.5.tgz", "fileCount": 8, "integrity": "sha512-L3v78PgTSxfxWzIR11K1jnc0kXKS1UeMT0mlXoJIQV7E/P37h35h/a5td9seXrrUXSo6rdQkq5B11Yy1d2LNvQ==", "signatures": [{"sig": "MEUCIQClOTde3+8bg/Ok7kq1E7azjw7185pNIDR2qk3dFp1VgQIgMTMIUZ2kEZIQLYOUWDfhG8IBpOrfp/HKKKCd3S9YwyA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28518, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcVU/CRA9TVsSAnZWagAAra8P/0i5WvN5tI51gwKgBdtg\nFPB3VX56m5lJVLPVz80hZ5ltSTXw5imngGdgtGHrFjyIhxBMR+2pgtOH7gjp\nXgQaagtgLASr5jXRuW0EKBokOVnGsfWs5yTTG/oAt8u8F8h21vf7mJQMiopo\naD8/8HgbhVL/JR8Pj9CVIC99WXZ4Xv1ihBERSqPGiyi8qfnMFkdAVRjWnf8g\npGQA9HatDv4MNGyXWAZDSPjEk3jNBI+Lz8tzTeicpWiZ7OLRPXht5WIy90Sn\nfnIIbgkL3Cpb86uW/WXMrmTqzTbkYlg0Qnw6aQSJBntbWMGMp/NS8myDB9A8\n13kj5ndLvi63HuxWou7wED7YzhDHmqnVGJXvFt0aDnazhxnk+kwPsWDwlEhq\ng6Kq5/QuxQ5mZgpU6dAa9moT2rUvkQOt3u9kAo4QKhDXpTlRpVF/zmKzm6oj\n7YMy3epCAIEkTSdAn3QMxXjq9dOdGCLRXPFDZ/vDsn3CCBKM4Sj/IoiawVOh\nr4z/pMQBojM1aG3QZwMUE2JhKOoyATE2bw7DhbFUsqaPl/4ecz4qknVrtnFA\nT5VBxpH+bV2nT8feMdos/FdaKE2NwnuOmGkyNTJzNly1E4vOPX3QPFQdD7FV\n0ZI9M1qZAtMs0y2svn3fo4+imOAAVQuAX8rAcRs53RvqlxJP4mNO72ip/CPB\nWBoz\r\n=81gA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "4baba14e2fe22a8203d52405babad7833c7d79b7", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.5.0", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.2", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^5.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.5_1534154046925_0.8572849544037755", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.6": {"name": "react-countup", "version": "4.0.0-alpha.6", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.6", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "04d752cbc40ecfd333e9831b84896028cf3a1ef0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.6.tgz", "fileCount": 8, "integrity": "sha512-qb+Q/MU6yl2xbZpxFLBuFqwbbMY/Ay/0+X7Dy2xkj5xOWqYutp5xtZD/sBSTE9OgaNt9CFuuVOxm6kwhfRJ90A==", "signatures": [{"sig": "MEQCIF8UWxXYL/raC2TcRJ6wItRptbHJNTT+d7rJ/xmuTuzMAiAoImwi0yqrbkMV2V4JK5cDcoyhyIMloX/t6dOxkXxxoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcVYHCRA9TVsSAnZWagAA89oP+wWBUw2Ofe7z8pxCj5Ui\n9gCiBCdyg5JGd9HGboTP4JGtdnfvNzdoKIeG2L1GZ5Nb7NR3BeKINKC6fxOS\nTbiRcRK0PK5HSaZ+NHggEcV5LiTdNwsFtvZTJiPODQTyTrIyysA7SFFsuEWB\n1qn23dsT5et8MRwYY/oiEQPh+hxQp1zCpttql1J7aZDVUmVFED0LRFbcaRW4\nPVQPq2GKktHPHM7BE4pSQzCgXyxe+9GD/qYk7ghqhHWrkftXU6auGZYVjBvL\noiFMZqMQO9l7Jr++36fHOi2L0f0gvBwRUHCFRTtQfSJp8W+7dIwrG3y2qZxy\nKlvmEIqyE5rzfxWovchoy4pyufiNwKBUhVpM80fG6mEVlzZmKYCq5VsFl7JG\nhmNiFTchxy+lOJN7JB8ZsNCc7VFf25rSMulNBmGLG+A96J3k2mQlaEqHK45M\nujNCwHXfT9msmvILTgAJ+sjl0I82/yc0i/1AWVP3HiYJMVUBChm1rqSrJNrJ\nSzlRS52UvsSRiHcAu1CR1xu5R9RTM47thOqckL+buFfNO6xGjhaaLs0OEjD/\nt/dKCWK4LRMF8w8C6CSnZeZvaPbSz4kqSMjoDomXqoGgHWPKW0wHgspA5p90\nuNLbsarFHXQps4D4c7eRh0s5KRwenFXcnv80PoMBamBWfVOiXo+sFizJDkPb\nDoL4\r\n=B2FN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "files": ["build", "src"], "gitHead": "dec120ef479b32b10dea08d17e6bcc731de4f35c", "scripts": {"test": "jest", "build": "rollup -c", "precommit": "lint-staged", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.8.0", "dependencies": {"warning": "^4.0.1", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.5.0", "husky": "^0.14.3", "react": "^16.4.1", "eslint": "^4.19.1", "rollup": "^0.64.1", "prettier": "^1.14.2", "react-dom": "^16.4.1", "babel-core": "^6.26.3", "lint-staged": "^7.2.0", "babel-eslint": "^7.2.3", "babel-preset-env": "^1.7.0", "babel-preset-react": "^6.24.1", "eslint-plugin-react": "^7.10.0", "react-test-renderer": "^16.4.2", "rollup-plugin-babel": "^3.0.7", "eslint-plugin-import": "^2.13.0", "react-testing-library": "^5.0.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-flowtype": "^2.50.0", "eslint-plugin-jsx-a11y": "^5.1.1", "eslint-plugin-prettier": "^2.6.2", "eslint-config-react-app": "^2.1.0", "babel-plugin-transform-class-properties": "^6.24.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.6_1534154246816_0.32239517841358234", "host": "s3://npm-registry-packages"}}, "4.0.0": {"name": "react-countup", "version": "4.0.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "cbce95c8f4d03fccc431bc52707dba23654621a8", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0.tgz", "fileCount": 8, "integrity": "sha512-l2GZY274KJir6Lvxahr+ZKT4xl9zzpSXO9dTvyKylm6jQcaSzk8TVECYlznTxWw4ctte3PBk+W1o3PeCXRyijg==", "signatures": [{"sig": "MEUCIQCb7SF1zGhEXvt0s/+iyyAeIbKq/X9FMfREB2FeCJkh3AIgKwFTx/vG93qKtsptfGHtdJ1B9g7w4un8DAnNjIikrJE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0uW6CRA9TVsSAnZWagAA5TwP/jR59Zcq6VLEE2g86F13\nxiFMfHdasOEJj/dJkKT9PIbPlGfrZQMePFyfW57aeMdJN4OiOtvuytbQWr+a\nUdhJl9fxr5yvOAWCJCN5zNOdkPzeqOZMEORcC06l6B3Nh7C2nr++o9iWk6T9\nQM/RxpNe6HfO7wr1l9OcOIBtMb6pNNifeH4LcewBFkj/l1UD02sB6RPJ2zIL\nVS8PnLfC4XKHqFQzaPuv1xCrdLcw2i9GoamvPfCu8s3CzkbMeo8ZKtWdtB5o\n67UUp9LoOtegekYSbcNNKD1v2QHZbgOWeiWq1rYAagmN3C3Fl1GRS2ruNgTT\nuGIPdGPyC8BvjDhV+GACbzvtE3YCz2gLdU/tUY1nay7DZ2QldHvlEvPD9i41\nOkpzGTTyT/cwnkOj4wZjv9ynxFWoV7Nd6yppfRLciYVy7DHvGa0xz8HZ3CEi\npPuIklhygJS/3nW54PZgyvdmIVyfCB+EElD7QXsidiEp6F6bVbWe2o499mAD\n2aB6O3LGFpOkFOq6085VZqPpYwNDZ5wfv2jnBDAeJCPU95OMd5zLhyEbxyha\nxBIoILPPhrn9X+/OnhM1KKmclkJ7LEEUKvuA0CH/nsjAWpRVNQ0duIm6T270\nVNpqRNxW8zIYMid5jbmMz1221OjCnj1ciAFHdt7RZrhZ+8xSQmsNwfDRSqfd\npqHX\r\n=td95\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "609f8892d43666f3208e3fc9314534ebf0a14388", "scripts": {"test": "jest", "build": "rollup -c", "prepublish": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "11.0.0", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.6.0", "husky": "^1.1.2", "react": "^16.6.0", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.6.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.2.3", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0_1540548026124_0.9056141733963035", "host": "s3://npm-registry-packages"}}, "4.0.0-alpha.7": {"name": "react-countup", "version": "4.0.0-alpha.7", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.0.0-alpha.7", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "469b186a7d81731b6ca51fe94e475ee76f925d40", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.0.0-alpha.7.tgz", "fileCount": 4, "integrity": "sha512-uMv7bCmIwuzT14Ld1aAQrLUVNX7f2XNsGOO+/4JK6x6hEy4fYi2LmJHv+XU9fOx5QeFZburem77iAzrPEt3bug==", "signatures": [{"sig": "MEQCIGy6mlq0rKR1nU/7xXEUigBIcx0ikGjhHh+8j4KrrcYbAiBy+CFYAZZpq+Fbcubhau2FHDn31hdt41rsm/aL13iLKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25075, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcVBwZCRA9TVsSAnZWagAAKe8QAID4BIGOhgCy/Zpsp5p8\n2+8PZX0YAu3Xn1GL8iZN6FzPUJwnpI+NJantyUWipGwNK+8yUnhIaqZlixRD\nmmBkwyA7VQO7ny9AB+F9gTh1tqUIHIHjrNoQ/RK6WkZnWsSrlYPP3qxaA+t7\nXzBTQMYHGsbVkRe9vC75QtSmQJXz/Nfm/JQBQuI+DESRojG0AxPP6zNhn2wq\nLTbN9Tm9Bm5/cnPmsoC8hYomfWGxXyammG/0n3jvBe5kFeKX2KRTG2uMQ4+l\nNfSButDxfQNyWWLYL/j+rNIBFD9nuUsLg1qlV5F0g4uyn3/9mnQSkFp42oCb\n6WW/3FpryOObH/rELn9Y+c/IPlVt24wr2CTqwmrQs84CdSogpgIpHY+FSdXT\nsbtUh8EzQCfGLtWQxW93isbERel/atoZnUnpD/A/A0Z/GIJjukWpfQQkbrom\nnm2RDq6XyP3Ulgh0ECIpbYNgl8ke8enonBF6Fou9v34kID9GDSK1is6qlNXc\nQuRI2QgkslrdYUl6dLNiLhkdOZxnenUn0pMgy5ifWmUGiy3fIj9saZ8Wbcpg\nncJj905q8IEUUzfCt8G+ES2wcKM8uvhfXzhi2c7pRvVtgiLljopsfjEJwT93\nhsiC0RwS6E54fDWxeQoKRCUAdcHG+xZkY16pLLsy3Fmr7IaHIOMiK97Wu2Wg\nfhH0\r\n=dZSA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "c96662f104c578ccfec487fdf4054546e54148d6", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "11.8.0", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.1", "jest": "^24.0.0", "husky": "^1.3.1", "rollup": "^1.1.2", "prettier": "^1.16.3", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^24.0.0", "@babel/core": "^7.2.2", "pretty-quick": "^1.10.0", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.3.2", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.3.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.0.0-alpha.7_1549016088697_0.4959059757914812", "host": "s3://npm-registry-packages"}}, "4.1.0": {"name": "react-countup", "version": "4.1.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.1.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "2f8d174cd2fbe25d893378eebe42b1169e3359e0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.1.0.tgz", "fileCount": 4, "integrity": "sha512-Hpu9NrPNp+WyqNHIzfH4W4n3Arix32AX1UVuiRF08d7DlIAtvQasO5c5ZexXXTh+tiWHzFx+rnwy+CmPl2PPTw==", "signatures": [{"sig": "MEYCIQCvGkJuu1GS28rSVSFeBcJVNUPMVHu3bwihUcBiQOM9wQIhALfnqDhOjl3bhJClVSNWKNtkO3VHQtxHmIlsnqoy+767", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYdA2CRA9TVsSAnZWagAA0rEP/AoUAsSSk/jiBhdEssDb\ni4Ncvv+uanzCn7Eqj+//DOYjOa1Ig8csgPvGzTqisYI4OYp5138UJ1zAglnE\n/RiXZWlQsrrZzBTn4x9WCqyqc4b4cdLdv4Xq04DVkkhs5R7qh7wmO9G4BhFl\n0qH12VOINrK+bmisOHXpgI/+J9Drogfu1rQtxdxS6sAn0zTeXl9ywF5UQZo5\ns5FdqXZh35PDPPdk78Qc32JktfyprZAVn0EG3zhcqf5I8sG6UiN7oa9Z/i8D\nOWhv2y0KDtus63MibwbCeSYobBZdz1MqLtoxb2AbbCuE0WEM0tesNWYhrrdu\niIOhGIlvB8A5TXBfqleapVBEFvRuq7KrWzYHaKUZs2w56W5DN6QQjZTqNmTo\nBsZ5d0UIAP09qIFEL1BRL50CT2cwRvHMidhntSG5K+yl+DYwIZ91UTbuaoU4\n43MPIJFam2CRMNx8+iEUNYqqItizPHGVvTGbvKgOeM/mlXx8CKqFFu1v0j3x\nFy2MoG9+/DUYyKxStO8Q7I6LJgz49wySD2Vk43XdjV30U+5QO6GzvqbQxZgs\n135/CXJCLu/epnjUD46DL9wHqSResJFRo5Xd5RJztMc6fpj4Bx5eX2R2mv0S\njIPSCL4g9BfX0+MmXvgzPde7rWz03s+gYJZzdB28RzyvOJVrO3w2g+5k3aBN\nBNAm\r\n=T82Z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "cb78f8ad45f5814ca86faebce7927b1013a1e27a", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "11.9.0", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.6.0", "husky": "^1.1.2", "react": "^16.8.1", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.8.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.1.0_1549914163284_0.4912066697337454", "host": "s3://npm-registry-packages"}}, "4.1.1": {"name": "react-countup", "version": "4.1.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.1.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "4c8e70187ae07840cfd75bee29a0892e97d17c49", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.1.1.tgz", "fileCount": 4, "integrity": "sha512-j9YFcCTFiJ1lDqn9JARk6pLfhV5bgEnVF0Ltmlf+j8z/uuOLoxHpsj9masgpfA72/8FHwLYssMFKUE4fngyTzQ==", "signatures": [{"sig": "MEUCIQCXz81RI40G7GzG62ZB/W6A8iMxS3ML9AGEUlzjwRGgBgIgfowJ5h5dmOMHFe3UWm6V1UQhugKjBdCYnfTHeoJmS+g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbYlMCRA9TVsSAnZWagAA2PMP/RpdgnyTUvOWRi1KhHt+\nZWl/UgRraHH5Mic2w4KnTeokKZZWcuy+XGPEdS8MkVzqWjSe3MLfXSO9sHYb\nlGtw3e454Bhn1EDt5NplJSD9OsMCVnBhPrvEMKPwaUapM/gvtW6madImz/Kt\nnoi7yi1+tGLhKNZ3s7XVY33oQ5DIRIzTcyJXSgC95dDw7YDhPvkCPv0kCMvO\no9/EXu3uBdBTCVLkDzV+VYqxRFyIQNFbJsgMfVC1vqWxYhvghZK71KsTpzBi\n97AuOv/v4PrsURtWvFhn+4KAaEjxHi1cYcwTm7Wy/aI2CPOllnLjhse4eqyl\nSB7JMQDGUpgyio69yweKOihH4Dznu2WwkON3E1Wtv1whKYWZzJQlezqFN7FF\n4MRujtoUwjxXLOACDseZqO3fvf4IsrFAsmRAoBLWk3cGWMn2NbcuxTPXC5u+\nE9o+OzAhEnAKcLki8xpkP1JRlzuyO/ZSoXG9X/t+/trMEUc8v4+POB6ghCJ8\nZACFXGDBdy9UpVfkrifUGSt0EU9isBqNM9/c70E9+PmB2wiK+6ZzVa+eVkZg\nZnef2NFK5Wie4NC3OUQpqrTrvLC6k37ZrGWBDjbKpTPSSlfms4sCOQ4dlwo+\nzFHEG5pIW2cVT0pXzAM0iKDZd4gZvQFKqSmBvEKmewmSnm9rKgvo20Hqtq9z\nIpUk\r\n=gnzM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "3d76282a1daaab29e62e9fb7e7967d150ca60e14", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "reakaleek", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.6.0", "husky": "^1.1.2", "react": "^16.8.1", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.8.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.1.1_1550682444309_0.8184429718397044", "host": "s3://npm-registry-packages"}}, "4.1.2": {"name": "react-countup", "version": "4.1.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.1.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "a3e2c9d83b63524e89d519b42fe8ad23b3990ce0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.1.2.tgz", "fileCount": 4, "integrity": "sha512-U3AGHrn+BV2LNI0gCzVWELgw1Q1gILDjiOdsaNKfOTnCekx7SAqNZ3otk9MDy4t5VNVdmVLRWDEqdxtfuEcFhg==", "signatures": [{"sig": "MEYCIQC3qJC0GcCl26fHeQb+Wh6yiUmGybbAWD2S3NXImuyDvAIhAPod9vm4dzzFkuJazwkeyjslm2edWUumMP9kvy4SU4u7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25681, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcml9KCRA9TVsSAnZWagAAOhQP/1JhmxFfaMYRB26DvqSN\nVmf32sko7nxWKWqEZKHy2bk1NApkIRkoBB7wQJGhdeHbkYsAoJ9M4e+6vzzO\nk8YZ+pibFLgPRdMaTRY3ONmlTaohEBqfVywsQNRcm+pJX3wnL8hOlKG+A1/3\nAgDCnBOv6dC1Czsv98o0bykDg7rjCTLsafzWb5iUNpIP+1qL/Z3LkTf7TIky\nEiQPe1sljw99DjGXtTdrgLEOg66rwfKRyhPMzE3HjmOiWXARVl4x3S8Wn5Oh\nQjaS0hCFD9KmHWzGUS09nV9N3hd/leSeSE04sJS87NbiowhxElUjzToRkMHw\nA2FZVIViDbFayU8cE9Op2rcYxsjUTpdaTUnaTMuRK2jUmI8l+AtGG0jEqg59\nhtw5c7NBkrYFUbKzd5i+PATvgQQt5WUWIjgQ94bccF1k6qj57YAO1vSNXVJI\nhbUwpVCkeQ30ieEy/8+c4p+YMGzAGmDwveoZGdEkGrsExiRaWPTjI+aYNTlZ\nKehJzd/3e/RVTCutVyAit6LTp9GEw0kKzNxF9vJytVE/3JQhE2kY1ntT3S3w\nxCHnRQMJJTC8UQIHoHK07ZDnX9c4FkFqZfHohHBZC7Bx5yiZLB3Ou+xfm9OX\ne31HJTIuGObicgRhtx2pE6L1OCMoiG3RFa+HBDdys21OuYVXZKmDEDrGcqdo\nelxb\r\n=uaWY\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "9240891c8a86ebaa0819f2941b1411ee50fef80c", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "reakaleek", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.4.1", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "10.15.1", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.6.0", "husky": "^1.1.2", "react": "^16.8.1", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.8.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.1.2_1553620808997_0.27280824084988153", "host": "s3://npm-registry-packages"}}, "4.1.3": {"name": "react-countup", "version": "4.1.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.1.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "57e9be04a77c0b55a501aa74e35e536b9523b826", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.1.3.tgz", "fileCount": 4, "integrity": "sha512-lbipT26PoPAM8kUTcDn8NEnWSXFSDcXaFNzKHLrxyL+8g74TlYV2dg7JdcgGJJWm04UKgQDLEjJxc8judfctbg==", "signatures": [{"sig": "MEYCIQCl28FlqsurxlYtW1JSIPhQdMjCvctj/qaqiR1LftakJAIhAPH4JJuH4RbXpfjN2Cbnkd0Fs/7Sc4S44tA0zVgG6ANI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25739, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcq1m5CRA9TVsSAnZWagAABj8QAJ7pYChnBwVOAAXXnc6b\nUYzvrFbSmjEPCp999uCQEgz4OIzgEsG65DnVCe8iOAHGZkj8hCvaPf8O5zxw\nJccxfzTcHUtZseXp6PQVFkAHylty6TeXeTSdG6pQ49gsPBgtQeWW+xLd8rqf\nyoVnUlpNh6r6Q7SKse2t3KFXYsv23dcotszjAflh2k644mBcGxUkAlaLtRuZ\negxiauVCu8IEo4zPQemwKYRkHs80DFZ1jI3IckM3Rz3J+rCl/KIunSH+Y9bq\np9p8YCO83ZMtmMTUCMhsyICkBtaIUxc/3VT5kif9RyewuadEjk8HbXWdSZUT\nO8yGWlDMYZJK1g5GbOpi6LZJWk1U3quW4jzW2ZNLnlGpxd4GrdE4J9P2Bhks\n+JMLpU2d2RsdS9vtxwZhvS5LUG7IxNnmlvnKfpVi/ET0Xk5Yq8PkJPg4xd7v\nJlFCyA2sT6V+mMjipp9ce9ycl/b9rjzCI8YGzztamrTO/tSdJEl2s12ge1M1\nkVQxlKONMThb6ZpO1k2uFTVaxVNgR28Icz9BlOx0HKm+GahBkfCVsJwcCEUM\nymCadaa7SXG28jN+9pZI9BfsgAnoXHOtG2a5avEZhT4qI1H3pHA1QhiUsQrr\nH3jV6sNBm4IKZSBtaDKfdab6GervpEwGYxHw2mzM5VKzH0qd/bfh9pXqRqOK\n4BHR\r\n=4j8O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "619ce4f86d87bfeae190d79e246412b3fde62fa6", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.7.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "11.13.0", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^23.6.0", "husky": "^1.1.2", "react": "^16.8.1", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.8.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.1.3_1554733496605_0.9741440190840329", "host": "s3://npm-registry-packages"}}, "4.2.0": {"name": "react-countup", "version": "4.2.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ecbcd85182ceb27cea5a9227219f8fad8443cd52", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.0.tgz", "fileCount": 4, "integrity": "sha512-CkmtvYOZV0iW8byfrZ96OKPDWwmvpS7rcfTKU9Ngn+oYRVoZrZ5quoKC2gbUS66Iona6vAoko6f8YxXJJuP2FQ==", "signatures": [{"sig": "MEUCIG0fLuxwI3fYmG5aLUJes+fzgv8uchT5ZW3qwobc10bxAiEAmQXwYCl1NTVkugDp82VDhBvciNtt7LXiKd7nPT1om3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25739, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdJcfSCRA9TVsSAnZWagAAaDkP/3A7YSDU71/tyJy0hNt3\nSqhMxgr/viNkLK6bQjRziLkA4k7TPnjfncHFOZC73RAuLAfwAADAu5NXVkqs\n35Jm8wektCje1aft6DJFHHRWhQXOVnQI9yjJhnAl1UgbsElNrji1oc2zODYR\n7zQOmrLz195YLiyeB2A+VnB+qattpfMI5v9KYa0RzKTCRS5gvQEw4gIBjwd3\nF9pUULdpvMCd/NBc221OBD0gqOR1YxLkhTCtyCofMV3E96/KYRl2uNKjpEnl\nCDBOuoIDB4OHPy/zETgU4WcFqUi9+WCmV0qJGItQdTc4Kl6vEOduKA5Ij9wV\nBhKJ9MZtYPdvj6b6BocjHQeH50/biS++p83D6A4keNgtyUHcTxdKON1hc6Eh\nMYwcsuzzarq6E1JkH+Lwegrk4/ATdIAarEMoIpuBCd5C9UX4kpSbfyiBsB3R\nFB5MTZbINyC1OzpyLYQYdEsgVex5Gof254aJ9oxcRqv3oFvP+Hwf/D9LFUEF\nHt2baWvvtpp9x5UxjrFxMFD9/PyB1DEo7+ZZM6RSBvYZ4lY8X+A5OPFyc4ec\nSkm3a3dSzs2c8rM9p94wTXCFUld7s7JuQFN40Ev3NylsbCC3SCGNS1n2VYcd\n2m3wQTvn5Ntqp4lFAj7foGOcko6JeJ5eogd53nvApJPTZ0MMDbASpqEdGtIs\nwaIw\r\n=nbsq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "gitHead": "2a5103e5a196125e95930b41c547adc39c9c76a1", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/glennreyes/react-countup.git", "type": "git"}, "_npmVersion": "6.9.0", "description": "A React component wrapper around CountUp.js", "directories": {}, "_nodeVersion": "12.5.0", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "^3.4.0", "jest": "^24.8.0", "husky": "^3.0.0", "react": "^16.8.1", "rollup": "^0.66.6", "prettier": "^1.14.3", "react-dom": "^16.8.1", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^23.6.0", "@babel/core": "^7.1.2", "pretty-quick": "^1.8.0", "@babel/preset-env": "^7.1.0", "@babel/preset-react": "^7.0.0", "rollup-plugin-babel": "^4.0.3", "react-testing-library": "^5.4.4", "@babel/plugin-proposal-class-properties": "^7.1.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.0_1562757073968_0.2901435036320381", "host": "s3://npm-registry-packages"}}, "4.2.1": {"name": "react-countup", "version": "4.2.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "3e9c9ec1476b3df7e8ec5f268707474ed0f7a4da", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.1.tgz", "fileCount": 6, "integrity": "sha512-fpNAgrPh0wgLrwM7SmZA8COGWyu8HUSFYJCjW9vx+eER+PNk6HItEGNCqYa4JWTDl3yUu1Jq/3jXF1Mhd4Azqg==", "signatures": [{"sig": "MEYCIQCcfyz3ovEgWFBh6gY85PY9UJGlFkCcAGGdEsC2CMoDmgIhAI6KL9eedHWbgux9Omala6TSGDH4XAuHCIEMsLZ8+jzg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdRJrsCRA9TVsSAnZWagAAY6UP/2gscLK5aIHPGM5djDY4\nDV/QuuPRbQ2yGwVrYaRB1ASASh5vnxw1MXXKuhBgXpKEG+YO8sPeYtBuRlWN\nUiLQjzmoJHMKi1v4RsC8f0zSdFtzQMndQUCZsDm2GLTja7keoYSuv+vZAPPd\nc2I/QEu1ZS2VjGHfySQwqHNw1kfIGTzG4zT5ZdzCuSzPsruWMdqKZEyFOOZ9\nKPSU6IepGFcTYhHMCsLCi6Etie8bL2UXh5x638OCWKkJENTVedcJk+2cf5BT\nI0hurBDj/99QxySJXWSubNn9SVe366xKsrSpgKLin/kee49Rx37TYmtuSzOy\nxGx2nJkzbSryw4y7AJusnrnMiP8TWsjM5yzuRm7dqxnGmzXVhvMvX+yETDBU\n+nTNrDoBJ0J1I7S9FIY56gHBzIDM5SOxBcuWmK7dxkx22Y0/QPQQCTDQO7xM\nmRCdqIFP8eSk2OyiIwWS5mMuzVPBLGh0WLy09BiUu+s6bW5PoO4cwvybgYLs\nCQgZ3i0w/D6Fit34m+A+XLcewEtCdVg95Nrom90q1yxNJV5KIM3wCqo+O6Y6\ncjsrkk3lrBB5rGPpzdyynl/LofhL1klmoeuRGhZpEDGDWkcrvmR84I7bTJOM\nJQsEZSvqty0705+QgAh69ud3zQafhmmEa2+q60BXI0iiW86A1t/2RMrehHtg\n4i5w\r\n=ASEi\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.8.0", "husky": "3.0.2", "react": "16.8.6", "rollup": "0.68.2", "prettier": "1.18.2", "react-dom": "16.8.6", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "7.5.5", "pretty-quick": "1.11.1", "@babel/preset-env": "7.5.5", "@babel/preset-react": "7.0.0", "rollup-plugin-babel": "4.3.3", "react-testing-library": "5.4.4", "@babel/plugin-proposal-class-properties": "7.5.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.1_1564777195810_0.8344858590043049", "host": "s3://npm-registry-packages"}}, "4.2.2": {"name": "react-countup", "version": "4.2.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "b98c1bf4a311411548000373b15f5c9b8d3cd6ac", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.2.tgz", "fileCount": 6, "integrity": "sha512-6mTJiXDt/x/K5odlKL2mAbQLYQLT9PyqmLcmLO6iCxeGy8yfcaRZTKw9BuzCR6qSHNsR6uzLAyJh6kjf0HvhcQ==", "signatures": [{"sig": "MEYCIQCQUQe/r6QvrZPqcVsGtS0eibppGQXsPbEh7oYdbMAw3QIhANJHSAgSLg+f/qHBvbLGzZGKph2KGklWETDta3/BtKTa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdVwhBCRA9TVsSAnZWagAAqcIP/1VzSgVNxMNp8aJ2TnMx\npoY9AyEvq6MMvgn6Z4E6e1zFpkoNRcJamBST35w0EM20mhOgtrrMLckIC8rf\navBNcM5DXVmRV0MVkrBCA8pFFxh0FPiJewT1qlgFnIyEJe8hn016yTSQJdbl\nG53zOVHNtw7ERj7ZVXaULCqMBnShosgcdtiGoFvkHVMYBcC6pR8xoVmAAqNF\nJiyjrSWd/ikvJVef7IHZpW9b+3CeZKtWv6XHMqAlrLepp3sngPqEGlKef9WA\nngLnGCAPYgxhpMaUPeEeht2tN2Cy2XvDvqyoLONbpjMoZ7gAPxSRBLgb575C\noc8NQ14SjxTuWb0oeIOalHfyChzmHDirm+jehfxlGAe2IdenRVnamXYtbydV\ncBTKrB8QsogbaWpHnjp+TR7RhnCCNTWTU2TUmHxScysSkg1e3t0Gzd5t6ABW\nqOV/y0EvspKDkJSCncQ/wBuFRPAGt2zti3ZjE+1ndxHgy+YKi5sMnJSfEZaV\nnreKhU64b2KWakeNzeyi+SGrkNTMeFNF+xRktsd9Ci5vSmoS8YDm+uuUF7FQ\nCUESgot5QZfnBPzidJB1pqg3LeWFIgNkTfQUaVOSB4X4mRLIhCeOuW0oDdRs\nwv/FXXpzX6J4TskS73+hYRQhKLOvekvw0d2SnCkAwGLFpzygLjrMR3F9NC/w\ng4UT\r\n=yOyy\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.2", "countup.js": "^1.9.3", "prop-types": "^15.6.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.0.3", "react": "16.9.0", "rollup": "0.68.2", "prettier": "1.18.2", "react-dom": "16.9.0", "babel-core": "7.0.0-bridge.0", "babel-jest": "23.6.0", "@babel/core": "7.5.5", "pretty-quick": "1.11.1", "@babel/preset-env": "7.5.5", "@babel/preset-react": "7.0.0", "rollup-plugin-babel": "4.3.3", "react-testing-library": "5.4.4", "@babel/plugin-proposal-class-properties": "7.5.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.2_1565984832647_0.6259172885444968", "host": "s3://npm-registry-packages"}}, "4.2.3": {"name": "react-countup", "version": "4.2.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "22581e68861b2b93be0cf0d5c4c6dff349e7b3b0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.3.tgz", "fileCount": 6, "integrity": "sha512-4re0CdtymFC0z5YivMnnBg2SC5ugqt4is/Y/RyTRTY/4QePawUhi2+Y23cQPUjXsdm/FpvicD9ZkQ4H+zVBsZg==", "signatures": [{"sig": "MEUCIQCzfjNce+buqlpwOc8PcQVP7IVJ1Od5Hit72Ldh13nOOQIgOBja051DtMOrnmmhINfGXNBw9dUkeq7Jy904WvGgvNw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27412, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvGYoCRA9TVsSAnZWagAAZ1QP/iEIedO6FL3MPBdj9qKP\n1nohHU/ANJysVhz91F1UESpwBMxpM0qbsfpBjEDPgYMoD1HBGNz1hdenk7IR\npVCmRivNC3/w+W9wvY0CKqHKpD6C6uP7YcVe1Rf0FW3N5HCtovMdmq2lSmlM\nSqixYuzJa98/KZSOlovyjKMZI3d9N/TUsJdCCkCUyn62IPOmATkw02IYT14g\nVYVCH7LCA2IgMQbcCCp0YTVIey9/IFDu77vqRQGhfPeYAIzFc2p5/kmV3eUu\n1G43II/apbixRx4cQAJ8s0y1qMStr6fq6xEg4tsbxTR6DSOr80FqcohQFtuU\nmPvGdvgGoCenF3PF+2CYCur3iM9QSnfyjpeoaHJ4yQe673DkqdD56zq0sicn\nshc0S/+PkyOASvNgTNfpDmxrn+CYOkmWeawsEhzEdlBygGcHKDCtCVt/kzcy\nsZgxleSnrsCalSCKAouJGPBvUV+wURIh7ZrcXr90u8xSaO3BGyWjAejyTpwe\n5u3UN/psnN/vRSwTeyAseRUIBzDYdXGRCIVoHNhxWtK65f9F2n5N53jo9G3B\nPTzbGEF7NniBxKx+pjCKuRzr0/JefrqPvrfurNUoSTaginDAFpYDZW53pU5x\nMEvXNaSlDnlG5Ie7saxsooiSzW6W4/ldOzpBu5TNgVOEm4Qhej0/t2hFWANF\n48Hy\r\n=7opq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.0.9", "react": "16.11.0", "rollup": "1.26.2", "prettier": "1.18.2", "react-dom": "16.11.0", "babel-jest": "24.9.0", "@babel/core": "7.6.4", "pretty-quick": "2.0.0", "@babel/preset-env": "7.6.3", "@babel/preset-react": "7.6.3", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.3.1", "@babel/plugin-proposal-class-properties": "7.5.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.3_1572628007681_0.9825592477392067", "host": "s3://npm-registry-packages"}}, "4.2.4": {"name": "react-countup", "version": "4.2.4", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.4", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "b53893216497559d95f3a6c9044b001121977423", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.4.tgz", "fileCount": 6, "integrity": "sha512-fiC0Mq9/bzPT/9j/vhWJ0TG4H2Meyw2ODZoDNW6tZLTue40ncpN3rv71Ggv9T0ZiGGZudzlOjVZRRWRW3bRkQw==", "signatures": [{"sig": "MEQCIDTkriEtO8Nl3jYKt21y2tVA67S0zBjQ2cNh3rk7+C+XAiB+QpNTuS70Gp9mlXm5bW2vRnZm/4eH4NLdvhVgRwLi7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7nh1CRA9TVsSAnZWagAAq9MP/14wQicRIXh0pyQPMWCh\ntii+YuvMaQQwxAajh7RmHjlt7U+Gt3YJboopTeENJo/b/1iZhSXTIOsvibIt\nwV842sBbopN6lPQ90UY6/whOhyLdnZNQFR8HgJH7mLiSjP8lgiSQttQnJIFx\ncDUknXiQa1SoEMn8D0yyxYRHEv3sWkmrQZaEZW9/+t4cOzuLJl2PpHINPEej\nRLYSDrBdt08GQoLwqcvcEvjbV2hZ1oKndu8pvPS2g+nxCoOD/m2Y+wGSemJm\nlcrhJlRkhmcQ6gymHQOKfUDVwxnLijZ3tlLeDkxMr1Px4ANPFwQM9TUUypK6\ngwy8hB/t/cjX9W2zXOOxGbSkhq5LcImvgQGrhuuWPgJkKCd7WWFvwPcsy+L2\nvRyyYOZDiF2RW4gjmRfKaWhwdWeKGvSrR4qmd7UGZ/JNPOwbfsG183FKVOm1\n8l39F729C5rJhRWjcd33/QBf+FSzUyoQmp0DmdZJDIQyWLhYD0BlRWCxF2oz\n/qQH/oZKCmvD7R6k/MmMqW/UtepKiCRmonlq7NkRxM+9fx/Qj+AfCS4pyD2a\nMbBV1Po+QqubYWfC7XNUMszyoa3tFZBNYJCwTv7rYLBT5oGnKIGOjtcINvc0\naOA33jZc/2OddplvPFFQYuFcoRMMuFUwVhbVP8d48rYhQFKn4LlDApE2whaa\nXhvY\r\n=ZQzI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.9", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.5", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.6", "@babel/preset-react": "7.7.4", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.3.2", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.4_1575909492810_0.4730733815035548", "host": "s3://npm-registry-packages"}}, "4.2.5": {"name": "react-countup", "version": "4.2.5", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.5", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "23a83e2f5c698db6e1c842f9a43ab2afeadfe380", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.5.tgz", "fileCount": 7, "integrity": "sha512-SNrqnUglz9M/L8Nml4/2vuI/j8Mmd4KaHT/Bmsh3Yag09ENfRvBMWvptJn1HMTAwTI1FfRPE2AxOdZ6G4WA0bw==", "signatures": [{"sig": "MEUCIB4vLsh1YyFq1GbhzwKZcqqw/quLqp+G/qWfNWkKXJg/AiEAhwS8yKQV8Lh6Yuor3N+GJ7CvstZq5/nZzNPpShC5M5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd7ntYCRA9TVsSAnZWagAAt4MP/AmoPVQetih+q3vYuc/M\nG/8JMuQKJyESZugG+/7t0chKEQQcFqG68+1+c/TIpSW1bMVrT9FBu6m+0Fw4\n6UA0+DqLe7pZ5JfY4WOBIUYmjjl8egi/vKhP/GrQacQzGl7G1rLk0/3qNWCS\nDEYXcTRO+jOZ2uwiGONF2IEaGrXZnz7yrUbGzBiz7/8uZvVM6ulJVOaOmIgP\nJOAR6OHdKTFIEOp0sl1Rqt06uCvSO/i6pSsAPnRdIDH0ulkNLjscJ8gxnqP+\nQG1APl0LD4rhdheM6oe5rX+KznqI7SRFqacGgoQluDrTUrdhqmOTkeFdRaFZ\nbAPMZUYe1gjaBHkZE1PV6xeo4NiaxNdSZ1FvBSQCC27I0Lllz94srzgNVnqB\nH4CDJNKT6GpYBwn6frjPjAUtxyV3tShjfuaELUMx9GmCHj1FAuEPtiifZ4C1\n7SEfS6Ng74ZGoofn6W4Jlvix49WJm4cRprP8slDN7za9XZHp2PbZsTaMSeYx\nGPqOlnV1X7/+CUVklDaqvzX8C6geL6+XlFMZ5DGafjF0MGF9clEynqKWRwd7\n0D/nwKFibLyP72LjI7tLuH8gss0bawhkEf7o5WGtdfmY4ezDwT8vMOpsLjaH\n9VghsMNSemcpEGXH7AanWHO4kHlRyd8CfOzXbnQ2/TJ2rdeu92xNucrhzCPM\nZH9k\r\n=xN7v\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.9", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.5", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.6", "@babel/preset-react": "7.7.4", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.3.2", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.5_1575910232005_0.5005038351750797", "host": "s3://npm-registry-packages"}}, "4.2.6": {"name": "react-countup", "version": "4.2.6", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.2.6", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "f28a28715b5bf14048c0d7882d4fcd5d29f1d151", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.2.6.tgz", "fileCount": 7, "integrity": "sha512-eHHE03V6QbFbnDGIva9uh6MtTtNk8R9/CipXIrhc3moe0w1ikPTsxUov5taPmwYsAsQmpDyDQEXYrwnGAX8FvQ==", "signatures": [{"sig": "MEYCIQD24RXWgHJYABxk3ZOI68LegGon9MotL8aoJ4Pp7V0GuAIhAORYoLOwTalYq9HDHwlhT84McN9AcT6lpb9PvuCnmady", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd70PkCRA9TVsSAnZWagAAhOYP/igX2z46vXvUM1ItoACB\nI7A7c2RUp3x4B2UX1mClwPKrpFBgiTFPX1bJJDcIz4uMosWv93MzsylkqlAt\nFP9q6IaX7IFaJ/oKtdTzK65RG7rFzAYbBXgqie2Kx3Wlgtd2pVwUkvsqFqcI\nlYO5lIy9yEo5MoU9FUtDTsL9P6W4ET5dgc85r+rm52BHIxJ3Fi1pLoas3UGb\ndIeNBNvsko83Pfj11yXKtMOxV2KYpvyURSbboxxzQ5mnNF1jpWuJw1CLZlpM\nLNjEfSE2HH2Ppy7U3mA6AF4JXASRTzgPcEh0Vk8HFtt1GYsMlZbi+rmkUlSL\n46iVZDGawsgwKx+t36Izv/of2ZmeMFd2WyEr6MOVLz6GebCR/PkIw7F6N+5q\n2isTiI7ItJ6EFMDFaXBYg8VCGDR1+qD5l6KjSgAIRFfrwvG9KjZ4SYjcZaZq\nTtOkCxXl5t7GxZaFLMzFDOAK4xk8JXL+apHZVYfJj81/IhHuai2wuwm9e2y3\niPx3gAIn6JLU62Bsx0n4h2cDdmVJYAeLL9oglYUqNabxJLehlBvmEoI8Hoir\n7xprxx8lNUpeMtjTkEMOdXEpkspuAkQ/WT4/Dig8/2gEOl6RfmGQjUHk8O48\nFVdajY1L9EF6boTxikuOQ7tFGaMAoWfnXzCae1F/bHcXPTpxfcbbzxnjkPlz\nHbOv\r\n=F3Md\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.9", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.5", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.6", "@babel/preset-react": "7.7.4", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.3.2", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.2.6_1575961571617_0.6083832407660108", "host": "s3://npm-registry-packages"}}, "4.3.0": {"name": "react-countup", "version": "4.3.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.3.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "e809866e3869d82c7f35431f253bf549e4b45c15", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.3.0.tgz", "fileCount": 7, "integrity": "sha512-HTHZtv2WtCs8SNUDI9iDLs3LCt+T7oBN/qliFzYkXVpklLYLD7Qcrm4fQrgL27CVddONZDxrozagpNhEf0pBqA==", "signatures": [{"sig": "MEYCIQCiOUxDtHJIMC/mdIy/SUQM6wl2SmFDGBXPVgWbG6sodgIhAOeHLTyJXNT3XWg2O3LNBNRyavpUuKMdaAYxe3e4K4TZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29576, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd9QjPCRA9TVsSAnZWagAApKoP/3nTTiiHm5GIBwmlTWuC\nJZ0hTzo2BwrjPf+ms5Z4KzF3j3+Oupn2hbeqOAmuEb9LzrMviteB5xn7Zuoh\ni1Y4oQ3YPjXAcNGasC9vSfvm+mjnuVpt/AJVHtZnaYgdCOVa2swlQoyun3s/\nMuyCOK5pj/oY7GQ2/KSpTDnIitUbF0xIVC2SebH+UJS5Gjqfz0SCjL3Qn+RU\nJq+39tbuTDXGuJ+Qu5T4URqCJ/1+jwxgbmEDNBWqAbjWNAeXaf9aPZL++J9+\nT1yQzRZ6LyLNU7MjyWhS8TyxMuJEu/HTYmgnKdj5E+7df5visXeL+z/MipMI\n/7Vrs5x/EuvKn+z669EJeW27E+FXkeIXA6sl0/n/X9mMONViJmPSojQMaty6\n6M9ccFN/SklMkkbGeeo76wCdutnCZY5BhyQlIOS2JPKeEGcwSz6LoK6E7w2R\nEK0IhytsxcrWMbFI1yx/teCvj9zBO0+J2SVjhtK5bpd83OQU1XPXrJ4vppZn\nFF+gHsb+ZyYEYKSthvr0jvk41ACs9JoBF1Yg/UZ+45mAnoCvFOk2A2S8r7M9\nDutlZN1ZJbFhlOvDufG25Irwjc244B04W9q4Xr+USUdRQDoVys6osYMvOONw\nCzswiCkScKdcZ8+/lkw19SsgZOaBzF5dkru6qQC313cgiFskogQSxAB3xVtQ\nK9y9\r\n=UUUX\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.12", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.5", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.6", "@babel/preset-react": "7.7.4", "react-test-renderer": "^16.12.0", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.4.0", "@testing-library/react-hooks": "^3.2.1", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.3.0_1576339662411_0.37447664244492485", "host": "s3://npm-registry-packages"}}, "4.3.1": {"name": "react-countup", "version": "4.3.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.3.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "04a654b049f76db4d293acc81159dbd6043ffeea", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.3.1.tgz", "fileCount": 7, "integrity": "sha512-/rNpazn1Ca4n571OTr0LCGdY8oWdjCPXQR9bcWEiAkS6ZXqKoga1xtTKuOss4ydDzqmNyJHJZ5ErE72/MlvA/A==", "signatures": [{"sig": "MEYCIQCd4B4AjX2EGNwZU83JZZIwwJIliaicwYd05OPif2pRMQIhALAKORkSAL14HoDLqi/sU929g2sr/nfEkOBfsxEdZHst", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29695, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd/P63CRA9TVsSAnZWagAACZQP/0ClApC8i4NfBijmaJ4w\nAqpE91H6lxxDSgjWeF8lm/30+y+p4FzW55FIZpQ/njWLXlRsUXasvEjtNF9q\ndIJOYaJdUwYQtQjYkcAMR4geAWnBVzBKi35mMNM2LDJcM1HuzOUK4xL77C0K\n5y2ZoDSdxsi3blC7c8pRQNY/F3+ol1BM5tpBm/6fIs0qYWefbEZ4wzABFD/3\nBbTuqXIGNWtjTvSAznH3XPiE+CAG/Ba9pzz5UtS5vGwlD1pTU+HGGVYScMvA\nfCbFKS2FOJjahGwg9tvtv9gD/2eSGw+IjqO/uxBrbKjiLCp+vbE6bmAznQZC\nLoVczGxdoEZP88xrdXaMEfq//7U5RO9CfGsBjYXm+A2UolLKGBFENMrHTbl8\nxjxYilSLJevfTzrqfvr4Q9PpI1igHcOF6zLSySOoBKYK/IkGL/sHOXi7RHS0\nN5WBTrfpX+jL3JypGyHZY6nv27+zmb80lVCfDnOgsXiJeco2tdFYXWIROu47\nY3aqM/XTfH9V63jjXMTte9JokeLMv5B4UGLDSJaBUFphU9mZZCf3OJww47t0\nF7pQSZd5O/VjG0UQalitDSo6gaAh0E9mJvJCvrenI1yyjyRNZmatxDeXgasy\nfoyqHma0WvV5i55xswUB6+sTbSMN9SDRHmmvy/JK0HPKQ4EgDRVkoUyRjHsz\nnua/\r\n=+L8z\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.13", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.7", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.7", "@babel/preset-react": "7.7.4", "react-test-renderer": "16.12.0", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.4.0", "@testing-library/react-hooks": "3.2.1", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.3.1_1576861366689_0.8829669842078318", "host": "s3://npm-registry-packages"}}, "4.3.2": {"name": "react-countup", "version": "4.3.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.3.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "57f1424538863484848da25f2ce59a36b0da0d1b", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.3.2.tgz", "fileCount": 7, "integrity": "sha512-3zkN1XXQgRC1b2L97e47m7D2BKQoYEw0ZiBsbrfqp4w7YA/GtOXwJWgA/2XcYh0TQyHqp47ZCCjradCK8iSGEw==", "signatures": [{"sig": "MEUCICEAli+3CpHv42qo68z0FBwsbKsXrLE1d8zXuup4yg3tAiEAxJnNj1S/k7zys5edpOVja0NRp2qM2/QF9ghTpoUMdiA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29772, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeEKHGCRA9TVsSAnZWagAABP8P/jNmW8c0+oIxeLl6Tp8Q\ngsqZAZ4I5Fa+rIYY/CBIYTI+kYcq4YxBm5cJ2gyE21Cy/+zAmoH79/DWQAPw\nDf6JE03SGsa7bn1RKjM5dD+jl8NIQ9JRtdhhUgD68NK6WnA6faepk22pE08h\nMwom4hTiwUa0XdQFO5qFayJu/swdxKUeus+iIR/p7Z6XsA+wZ2WLIVXSQORL\nsph9dDYRhg1ta8w49mSl31ES8mAcZF636Zh4rctQ96drBqhxyRSCd0GWjodt\nu0cMy3YDeOFxYpUlpydfUzc53rSl2xvc2NScp9pEJPv7Qe/Pjq+ziXxlvs1+\n4C1SgQYaSI5NW/Tx04LkVA7NPlwx4qEVxv+oPgL9+J95tMkxIyZoWynTZw0d\nf9VnCQZ/UyQlTHCrKS7TLCTAOVLpzP119jJ2IX6S1MzJD+4S8+PhaVItg5Ii\nNRu88XkEFYn3xrkH/Nv79YosybcymKYwCftIOa9LWzMR6OXPf0zya9I508pH\nYS4SSY4bix2j/7QiLCE/sLRGCJP4JPIAWdFlwSE4nvHqBARKq0G8xVnoYJbZ\nf2juGQK9ishL6fYYJ4PG1+CRtTPrbat68MjclOnOK9G8JDwkJr3Q/AWcI3ed\nuwWg/aFd1SCEkAd4WALVpJe0MYcwTufE7RKb1QiIkrRJOcT6YJI0fAoMKzmU\nm7sx\r\n=E3Pc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "24.9.0", "husky": "3.1.0", "react": "16.12.0", "rollup": "1.27.14", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "24.9.0", "@babel/core": "7.7.7", "pretty-quick": "2.0.1", "@babel/preset-env": "7.7.7", "@babel/preset-react": "7.7.4", "react-test-renderer": "16.12.0", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.4.0", "@testing-library/react-hooks": "3.2.1", "@babel/plugin-proposal-class-properties": "7.7.4"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.3.2_1578148293858_0.38228044733380573", "host": "s3://npm-registry-packages"}}, "4.3.3": {"name": "react-countup", "version": "4.3.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.3.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "143a8d854d47290b73d6915eac20a12d839153c2", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.3.3.tgz", "fileCount": 7, "integrity": "sha512-pWnxpwdPNRyJFha/YKKbyc4RLAw8PzmULdgCziGIgw6vxhT1VdccrvQgj38HBSoM2qF/MoLmn4M2klvDWVIdaw==", "signatures": [{"sig": "MEYCIQCNHaK9CTiQB8s1QqmDtyYNiN83DAVmnr4emAOA5UfwPgIhANiQ8BYgZ4y3AapI9k9RHl9yhRA7VxqITQrIMLpOKPUQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeLEtLCRA9TVsSAnZWagAAqZIQAJ8Ni3Y4NFw4uSTCTapp\n2X8YOBUjtY63uUjruUqnKP/kLCnqvJ0Y3xLq+WJMtqFukyescV5erLbJkHgh\nc8x+Qv+mhoaWW4F5YND2JoiB08sE0xED1krA28quu1fuldSvbjw4oWwX6XTE\nFjDdYxj+bpClkrf51f4f4dTWFnk8TTkjPDd0DN5X15KJXmpz+C8lNQV4hsBg\n0BLifXhBKTdGzVxrPIJRcPU08HOk4maK6LhYXDzQK/JPMVcdec2oLnGJKqJy\n1bJ1LouHvnKh3I+o6PCu0pF2JkpLFtxek09URMRG1rDDv5slJG9DS3YvhDRh\nV0RFnqKKrIYpN2xxIrbWIaEoyIVDLb5GBAM7me5P40KGrwUDonhRZL+7JQSC\nxO+ivUKlxRWzBGXaCapioNFN21S8Xchslp8tn29Dms5xEjl9v5ADVKzoytxL\nMfWadPpw5gNJ0lNyZgPHNY5haRsxuAumz9mKnRDNror70RvZ1rbwMCLuawUC\n7MhG1UZqVJu83clikjvBly2fFAtayjo/bfFpyD9c50xg9qL86bMw1Dzxnq0Z\no3Qi60naspftCZF/99c9+/jrhgvNBceoybmEonVi/d0FnQ1wBa9boEU2UnXN\nvBGzTLbs4S6AYOV0GUbOlxgmtqNniVwfzU4mJjhNYu6njOQxw/VzYwq1kfcF\nw3CL\r\n=Quzn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "25.1.0", "husky": "4.2.1", "react": "16.12.0", "rollup": "1.29.1", "prettier": "1.19.1", "react-dom": "16.12.0", "babel-jest": "25.1.0", "@babel/core": "7.8.3", "pretty-quick": "2.0.1", "@babel/preset-env": "7.8.3", "@babel/preset-react": "7.8.3", "react-test-renderer": "16.12.0", "rollup-plugin-babel": "4.3.3", "@testing-library/react": "9.4.0", "@testing-library/react-hooks": "3.2.1", "@babel/plugin-proposal-class-properties": "7.8.3"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.3.3_1579961162878_0.28973338230281076", "host": "s3://npm-registry-packages"}}, "4.4.0": {"name": "react-countup", "version": "4.4.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@4.4.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ca5c0a971c442a06a7bc52f3921ad2e66ba65990", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-4.4.0.tgz", "fileCount": 7, "integrity": "sha512-PSSpvRT5FCjCVh3NzPpz/SgeG8b9LnpfCSO1TEMK8K/GhmxI+s8D/KbxGCFsmmeq+Y0cnf2KepUHssPfH/7iyw==", "signatures": [{"sig": "MEYCIQCAIBrwE7wVe8U/Ug1FuB5UDIESFhiriPLf2XsMkQU/IAIhALysH25HFmGtKdpFO6qUxM1xglSZdXZI4yTZBfAG5XJy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31842, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg2yziCRA9TVsSAnZWagAA49IP+QCcciBb5YQprXCKg9Mn\nvozj8DGtRoUblzIPz2IxPj5MXXBPBbuEM1boJ5NLg8GffbDjoceX3tYgGn3P\nUP0QrlvisvJmWcPuzaxef7GceFMn4ifPsBOxh4L+j/xXkBE/Ei2CMRUxl+DS\nVfOYvEEl6wLIYT7S3tnFoxRZeRh7O/a9X9TzHaXQT3Gs5NNNuk2ZN5vH9GtF\nYk2+6tJjrfnGSx6Z5YK/O8aPPeRzmOScN/WkaqJobgbcpORqWzmy4ikAB3Qn\nPs2biL++e9+uakRPhde5RY/WEtjOy/R2Q4ngQrdhyhMihU3YSYWFRDEeT/D4\nF3bPDuT/W23cXwjrn7BVQP9WIYPBYdb6pARZhVL7ms6Fj9MqdN9nSaRnLUqB\niGT0+ApKUyuZlspkvTEM488pjzU89jfgnrOhycmPzi31w09Rm72W23tPhB6/\nlm0utn66ODypAb+vqmbx2cQtl9v8AikcXpFk0/SFHHwh4F4tM20biVkZXANd\n+YgpxrISLRahjWyhKuuWLImke94JpH63Uizp6MVJD/Bei69Jh4LuH81WnzCX\nF/Mnf+986lTBV5JDDTpumXxE81TwLiqN3kZx+QCz+s4rc3F6p0sHjc7zYpAi\nugGBozTKhCgiruY58hVvo3Jeve5LVeadFIAc4j0/HKBohW4R5JITJ9EyIyYg\nP+li\r\n=HdCd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^1.9.3", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "rollup": "2.52.3", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "26.6.3", "@babel/core": "7.14.6", "pretty-quick": "3.1.1", "@babel/preset-env": "7.14.7", "@babel/preset-react": "7.14.5", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "@testing-library/react-hooks": "7.0.0", "@babel/plugin-proposal-class-properties": "7.14.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_4.4.0_1624976609863_0.9553064151281043", "host": "s3://npm-registry-packages"}}, "5.0.0-0": {"name": "react-countup", "version": "5.0.0-0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@5.0.0-0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "9aff055f2244f857e65cb124d38e51fde8731ec8", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-5.0.0-0.tgz", "fileCount": 7, "integrity": "sha512-wbnO6AilYOEGu9nagFwKZRdh40/MGMc344TMVpTrmdO7s9BEYa//NUnuNrAT2Mr3XhmXWJNGiEVS22SJI9r5Ug==", "signatures": [{"sig": "MEQCIGjdWsT5/oi9iqx6f/G21rHOBo8meRxhkF3M69QK4zTFAiBBZWr+T1joDQgNAkFlaznwxOc6SHMVxz0/Gjh0N93LKw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5hXoCRA9TVsSAnZWagAA2tkP/Rt3mjarCazgH1dy9DwT\nSz+HGi4htgYhHc7/Qro1pfO2ivPe8jgzBYhNDhJ5xgweEaGp2kniiN+H5eT4\nMsQC3E+AlGZFdRauK4mzxJPVBxDruDKQESNSEEZiqx0s87/hKbYXo7DK9883\ncTHKiWUBXnVytvVY45NHLzi2dh4szsPdCzYw1ww8vuoydqeQnbaBvRnNNQrD\nvaYkC8vFEpc0vcRoGt+t5FpC5iWsI789zd6DgQxEPGQlhFXe8zffThPt3nmZ\nd4yh6ILM5UUGGvBrw24SGS2Xzsb+vUvYKBQoMeJ5KnR+1CyHCo0Ys+LVT0pA\ndzE3woNFYtigx2y0hst82NmGgX9gTBjxUCzL7puS7vw7CE7Mwjnq2FrvGF0V\n20s8kYMIpaYW93VSj3G9Jf/i8k6CP8U14ns66N1HqXhYqKQjhtdsX2anuvP5\nCoYCZQ0H7H2UtWU0I8Tr9Iz4nwuqWs8aNkrd2IrOBMOBqlTDuaAX+NSrUW45\nQ8e8UTMNWJy3NbQMIobUV0Ibn1rpGRYWiWAlIm9/jXa0Wfmpu+z8hh+9SP8D\neqHFZUp/k538ifO/IO/x1mpftNdQe02gsN1gaEjLIZXQ9eGQZr4tsExxyJtL\nW5zy8aLF0c5Ea7iMaLA1W8DQO4ltgZ+EPudZ6tAsIT8oClAlHiG5palWsgat\nWSKT\r\n=/9CO\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "readme": "# [React CountUp](https://tr8tk.csb.app/)\n\n[![GitHub license](https://img.shields.io/npm/l/react-countup.svg?style=flat-square)](https://github.com/glennreyes/react-countup/blob/master/LICENSE)\n[![Build Status](https://img.shields.io/travis/glennreyes/react-countup.svg?style=flat-square)](https://travis-ci.org/glennreyes/react-countup)\n[![Coverage Status](https://img.shields.io/coveralls/glennreyes/react-countup.svg?style=flat-square)](https://coveralls.io/github/glennreyes/react-countup)\n[![Version](https://img.shields.io/npm/v/react-countup.svg?style=flat-square)](https://www.npmjs.com/package/react-countup)\n[![Downloads](https://img.shields.io/npm/dm/react-countup.svg?style=flat-square)](http://www.npmtrends.com/react-countup)\n[![Gzip size](https://img.badgesize.io/https://unpkg.com/react-countup?style=flat-square&compression=gzip)](https://img.badgesize.io/https://unpkg.com/react-countup)\n\nA configurable React component wrapper around [CountUp.js](https://inorganik.github.io/countUp.js/).\n\nClick [here](https://codesandbox.io/s/github/glennreyes/react-countup/tree/master/demo?fontsize=14&hidenavigation=1&theme=dark&view=preview) to view on CodeSandbox.\n\n### Looking for v3.x docs?\n\nClick [here](https://github.com/glennreyes/react-countup/tree/d0002932dac8a274f951e53b1d9b1f4719176147) to get to the previous docs.\n\n![react-countup](https://user-images.githubusercontent.com/5080854/43985960-0a7fb776-9d0c-11e8-8082-975b1e8bf51c.gif)\n\n## Table of Contents\n\n- [Installation](#installation)\n  - [Usage](#usage)\n    - [Simple example](#simple-example)\n    - [Render prop example](#render-prop-example)\n    - [More examples](#more-examples)\n      - [Manually start with render prop](#manually-start-with-render-prop)\n      - [Autostart with render prop](#autostart-with-render-prop)\n      - [Delay start](#delay-start)\n      - [Hook](#hook)\n  - [API](#api)\n    - [Props](#props)\n      - [`className: string`](#classname-string)\n      - [`decimal: string`](#decimal-string)\n      - [`decimals: number`](#decimals-number)\n      - [`delay: ?number`](#delay-number)\n      - [`duration: number`](#duration-number)\n      - [`end: number`](#end-number)\n      - [`prefix: string`](#prefix-string)\n      - [`redraw: boolean`](#redraw-boolean)\n      - [`preserveValue: boolean`](#preservevalue-boolean)\n      - [`separator: string`](#separator-string)\n      - [`start: number`](#start-number)\n      - [`startOnMount: boolean`](#startonmount-boolean)\n      - [`suffix: string`](#suffix-string)\n      - [`useEasing: boolean`](#useeasing-boolean)\n      - [`easingFn: (t: number, b: number, c: number, d: number) => number`](#easingfn-t-number-b-number-c-number-d-number--number)\n      - [`formattingFn: (value: number) => string`](#formattingfn-value-number--string)\n      - [`onEnd: ({ pauseResume, reset, start, update }) => void`](#onend--pauseresume-reset-start-update---void)\n      - [`onStart: ({ pauseResume, reset, update }) => void`](#onstart--pauseresume-reset-update---void)\n      - [`onPauseResume: ({ reset, start, update }) => void`](#onpauseresume--reset-start-update---void)\n      - [`onReset: ({ pauseResume, start, update }) => void`](#onreset--pauseresume-start-update---void)\n      - [`onUpdate: ({ pauseResume, reset, start }) => void`](#onupdate--pauseresume-reset-start---void)\n    - [Render props](#render-props)\n      - [`countUpRef: () => void`](#countupref---void)\n      - [`pauseResume: () => void`](#pauseresume---void)\n      - [`reset: () => void`](#reset---void)\n      - [`start: () => void`](#start---void)\n      - [`update: (newEnd: number?) => void`](#update-newend-number--void)\n  - [Protips](#protips)\n  - [License](#license)\n\n## Installation\n\n```bash\nyarn add react-countup\n```\n\n## Usage\n\n```js\nimport CountUp from 'react-countup';\n```\n\n### Simple example\n\n```js\n<CountUp end={100} />\n```\n\nThis will start a count up transition from `0` to `100` on render.\n\n### Render prop example\n\n```js\n<CountUp\n  start={-875.039}\n  end={160527.012}\n  duration={2.75}\n  separator=\" \"\n  decimals={4}\n  decimal=\",\"\n  prefix=\"EUR \"\n  suffix=\" left\"\n  onEnd={() => console.log('Ended! 👏')}\n  onStart={() => console.log('Started! 💨')}\n>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\nThe transition won't start on initial render as it needs to be triggered manually here.\n\n> Tip: If you need to start the render prop component immediately, you can set delay={0}.\n\n### More examples\n\n#### Manually start with render prop\n\n```js\n<CountUp start={0} end={100}>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\n#### Autostart with render prop\n\nRender start value but start transition on first render:\n\n```js\n<CountUp start={0} end={100} delay={0}>\n  {({ countUpRef }) => (\n    <div>\n      <span ref={countUpRef} />\n    </div>\n  )}\n</CountUp>\n```\n\nNote that `delay={0}` will automatically start the count up.\n\n#### Delay start\n\n```js\n<CountUp delay={2} end={100} />\n```\n\n### Hook\n\n#### Simple example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst SimpleHook = () => {\n  useCountUp({ end: 1234567 });\n  return <span id=\"counter\"/>;\n};\n```\n\n#### Complete example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst CompleteHook = () => {\n  const countUpRef = React.useRef(null);\n  const { start, pauseResume, reset, update } = useCountUp({\n    ref: countUpRef,\n    start: 0,\n    end: 1234567,\n    delay: 1000,\n    duration: 5,\n    onReset: () => console.log('Resetted!'),\n    onUpdate: () => console.log('Updated!'),\n    onPauseResume: () => console.log('Paused or resumed!'),\n    onStart: ({ pauseResume }) => console.log(pauseResume),\n    onEnd: ({ pauseResume }) => console.log(pauseResume),\n  });\n  return (\n    <div>\n      <div ref={countUpRef}/>\n      <button onClick={start}>Start</button>\n      <button onClick={reset}>Reset</button>\n      <button onClick={pauseResume}>Pause/Resume</button>\n      <button onClick={() => update(2000)}>Update to 2000</button>\n    </div>\n  );\n};\n```\n\n## API\n\n### Props\n\n#### `className: string`\n\nCSS class name of the span element.\n\n> Note: This won't be applied when using CountUp with render props.\n\n#### `decimal: string`\n\nSpecifies decimal character.\n\nDefault: `.`\n\n#### `decimals: number`\n\nAmount of decimals to display.\n\nDefault: `0`\n\n#### `delay: ?number`\n\nDelay in seconds before starting the transition.\n\nDefault: `null`\n\n> Note: `delay={0}` will automatically start the count up.\n\n#### `duration: number`\n\nDuration in seconds.\n\nDefault: `2`\n\n#### `end: number`\n\nTarget value.\n\n#### `prefix: string`\n\nStatic text before the transitioning value.\n\n#### `redraw: boolean`\n\nForces count up transition on every component update.\n\nDefault: `false`\n\n#### `preserveValue: boolean`\n\nSave previously ended number to start every new animation from it.\n\nDefault: `false`\n\n#### `separator: string`\n\nSpecifies character of thousands separator.\n\n#### `start: number`\n\nInitial value.\n\nDefault: `0`\n\n#### `startOnMount: boolean`\n\nUse for start counter on mount for hook usage.\n\nDefault: `true`\n\n#### `suffix: string`\n\nStatic text after the transitioning value.\n\n#### `useEasing: boolean`\n\nEnables easing. Set to `false` for a linear transition.\n\nDefault: `true`\n\n#### `easingFn: (t: number, b: number, c: number, d: number) => number`\n\nEasing function. Click [here](http://robertpenner.com/easing) for more details.\n\nDefault: [`easeInExpo`](https://github.com/inorganik/countUp.js/blob/master/countUp.js#L103-L106)\n\n#### `formattingFn: (value: number) => string`\n\nFunction to customize the formatting of the number\n\n#### `onEnd: ({ pauseResume, reset, start, update }) => void`\n\nCallback function on transition end.\n\n#### `onStart: ({ pauseResume, reset, update }) => void`\n\nCallback function on transition start.\n\n#### `onPauseResume: ({ reset, start, update }) => void`\n\nCallback function on pause or resume.\n\n#### `onReset: ({ pauseResume, start, update }) => void`\n\nCallback function on reset.\n\n#### `onUpdate: ({ pauseResume, reset, start }) => void`\n\nCallback function on update.\n\n### Render props\n\n#### `countUpRef: () => void`\n\nRef to hook the countUp instance to\n\n#### `pauseResume: () => void`\n\nPauses or resumes the transition\n\n#### `reset: () => void`\n\nResets to initial value\n\n#### `start: () => void`\n\nStarts or restarts the transition\n\n#### `update: (newEnd: number?) => void`\n\nUpdates transition to the new end value (if given)\n\n## Protips\n\nBy default, the animation is triggered if any of the following props has changed:\n\n- `duration`\n- `end`\n- `start`\n\nIf `redraw` is set to `true` your component will start the transition on every component update.\n\n## License\n\nMIT\n", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^2.0.7", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "rollup": "2.52.4", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.0.6", "@babel/core": "7.14.6", "pretty-quick": "3.1.1", "@babel/preset-env": "7.14.7", "@babel/preset-react": "7.14.5", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "@testing-library/react-hooks": "7.0.1", "@babel/plugin-proposal-class-properties": "7.14.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_5.0.0-0_1625691623651_0.7594616492023412", "host": "s3://npm-registry-packages"}}, "5.0.0": {"name": "react-countup", "version": "5.0.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@5.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "6c0767375a500a9ec98de5c4a88ef8b8853a8adb", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-5.0.0.tgz", "fileCount": 7, "integrity": "sha512-nfskEVNHELkQ5famxdE13Xl/zODG3g67hNY4y6yhiIInonOo/b0P2yHHi7wYFaEKLixDPIQfcIzssg/jW3pYdg==", "signatures": [{"sig": "MEQCIAq3DL9JzCgt0SkEZ+8MmHAeN2B6PBWfWAqvqd6rzLHrAiBi+tn9IciVMffTegLikw0elHDy9X5RH3i6h1iWkiZVJQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30628, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg/uGdCRA9TVsSAnZWagAA8lcP+waeBGHEyVN+/hAmCiaI\nRkysMs3w6MT3H8oP9fVPCC0gw1cFshPCdy/JD9yWVYZwHQkucyzUv+RKaziG\nxNIVODyrlQN/m25LZQr/DlHIo4Rj7Zz5f+mJ29JaGGNaYMB6gAS9epwbADtO\nm/BXNVOfGblyElWcoskhVyB1N8ShBm8YECztme80Pzo84hbZlL6F/oWRVtJB\nJGlzI6pEQfzzVyL/fzVk5A6m2mfL4dY27QNA6g+7Wi7i/8kBJ8OgIUh4jPw2\ngqxG0ddrZCfTwZnuBVdRcyL+zL27LV+2QWtKWoViJhlpG5V01DVGLAfz59x8\nyFaWIYrDjDlXkWWXEaCtSLPjwF1xTvZX3u1FcEVdLYGKgKnedrbQudQIrRoh\n8NyWfX24njHi4ZH53sNY7KqKK1rNd7ocveYCYFl/lKcaOYTWhDs0yDlPBEJh\n6vUshb58iwn5T2vZYa/LQbbW9xF8RgSNzR8mgsoo+9AyK0PG4vMfI1M2wob8\n+cPtq3vQkKMcmg/eRNjQPWNNHcraKIPlS6oyG5BKT751+Par6z9oEmdJ2+my\nYUFGhxY5VutbNYNTLeBvL2q2gxxvqRC6ipJ3m4Lnz8HPoC2A/E2AYLZej/Yb\nNUpKLvhzSg1dVw6SKz5JA/C77N6WF66tNSxQlkehs3VDNgp8hTAzPpiVrzYt\nHSPw\r\n=Tt7K\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c", "prepublishOnly": "yarn build"}, "typings": "index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^2.0.7", "prop-types": "^15.7.2"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "rollup": "2.52.4", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.0.6", "@babel/core": "7.14.6", "pretty-quick": "3.1.1", "@babel/preset-env": "7.14.7", "@babel/preset-react": "7.14.5", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "@testing-library/react-hooks": "7.0.1", "@babel/plugin-proposal-class-properties": "7.14.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_5.0.0_1627316637249_0.2771434539395119", "host": "s3://npm-registry-packages"}}, "5.1.0": {"name": "react-countup", "version": "5.1.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@5.1.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "23454c397b723f6c9c5bfbe959610ba0d34334ae", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-5.1.0.tgz", "fileCount": 11, "integrity": "sha512-aDVXrnef5ByOqJfUOS0Gm0R8BQSfZagFoqczSbuPXdidTZh52OYZ8JN+cX58oouxvBOYwRvkhwPr0qUVrIpryw==", "signatures": [{"sig": "MEQCIFrLzADdIPWxj2Xc1akbgqikO6m0t0rME979ASqXw8W4AiAAsPNDm3BENxdgU8h2Eu9sCKkGZ+m11Vjy/VFHHcvU+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33473, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhABUVCRA9TVsSAnZWagAAdAwP/0K2IISib5INJdWTE43B\nROK44+xUYXNV8Un+9gNZT3WFVt8hWI8KaQTQa7nwQ0yi2YZ66gU/Hf+F/UW3\n5By2vC5oAkuh9Fcotys1RLKyDJDLKSYwLiABW3pZS9/AwUqGmTBB7VwPEP1u\na2vIyYh6ImQNvwXS1msa7Kiv77QRBmGTBez/YMtOcPrQSQOcBVk10UBGShza\n4+Pbph8LAPkSNi8YxMKSrDC3MutRw48FT+BJrtef+gIAwEgJa6aC4bBT3A9x\nqKeX6CxgUXWgLmT+xL4sd8LREbaE5i/OEBvp/SGRIBApev97CxLbtrxWC312\nDtUwXnLRToDM+kEsUbRVQZYQpRsUwtEKg8s7PcO4ssC1HCytLFXK9QqqPiHf\nlISExNHzkGJ6tiZD9GlcvvtUCuI5kqZKwg95+vziNTbRefODch37RcF3I2OF\nwYOt9nBofaAzwYVeCXiljKkA+vhX+sQTtsyCEiJU7z8QBXFhQ5jo2gbvJBg/\nNSekf+0vWjdgyOwt+52FbGJYnxHel5wU4nEL8ReaTRpzT3WyBKKanXKaPvzF\nV+HQt+ctmi/j0qDlQBDGL84nzZPuennrcz8C1oG1OTLq933Zl1cgR5rn4yv6\n4HSPA1CmUseElGUmerW1/uL9hmoJcPWe3Pf1ctmw2j/GTSRHOJcx0KFV09A3\n5dyn\r\n=67ZS\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"warning": "^4.0.3", "countup.js": "^2.0.7"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "rollup": "2.52.4", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.0.6", "typescript": "^4.3.5", "@babel/core": "7.14.6", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.14.7", "@babel/preset-react": "7.14.5", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "@babel/preset-typescript": "^7.14.5", "@rollup/plugin-node-resolve": "^13.0.2", "@testing-library/react-hooks": "7.0.1", "@babel/plugin-proposal-class-properties": "7.14.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_5.1.0_1627395348463_0.7773442579389713", "host": "s3://npm-registry-packages"}}, "5.2.0": {"name": "react-countup", "version": "5.2.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@5.2.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "58be5d97acebf767d5c61df5c3a3417b95c45a16", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-5.2.0.tgz", "fileCount": 11, "integrity": "sha512-R6+FIrW8ypwoAe0Q0CZ16OhrgAntnnnch7HrnRAy9miXFKk8jQzVADjNtGSoNUuJeq/ZFiiXCGJCJIAmRJ5fLg==", "signatures": [{"sig": "MEYCIQCk4n0ve9VmwOzC40GmpyNHQUmkxcFbXDsLOgn6QuQtBgIhAKeW1vCI1aeIQWoqYlN6XB+sc0Pj2I64Jjlho2i7OjQW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33383, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAvdoCRA9TVsSAnZWagAAhnwP/0x6N1gsYUG2aqI9CwrA\nrEyZ/PtJvb0Rh+GhhbuESPyJrhCz9mgpQIOeHVtW8i+LAHN71kwV2Wv7mo33\nuwIK3SboF2SZuqlymYbDloIllucQvhHrzf/mFOzFGnO8ZmhbmXPivNOk6o2S\n6mK7k7czi1ZRqvhygKY+LKs11JibWeX6GOP7VUi4jYt0ArIn3NMD//JU3iya\nM7px+KnuBPYtMn5NbiL6jbeQgNrZTnOh3j0Un7i/xCd0XXtGAhGQKCfwT46r\n20XSdykRNRGePFGol3pNqG1k/rlG6bKOMIwfX0K+VGF+MCT1fWpGen7eKFqu\n//lCHB39VLn6zXpIENnMOtqFistovBOLjdZ/kdpN6xMZS4+vrZINTJNes6e2\nSa3TOPlrWgv8ujPMUTfXdkWwNuQ9iEbphgQ/PBkJFSzMHmz+ToIqAhhN6k+5\nk3hO9We+ShYDdPddgeQcANCcDjHzsR/6knMyRm6JoMHNWLlW8q2sWaozq/2G\nWsr3cEAOWVLlQVYqlkcSBT0cJVNeF9u7ZwgYCQWtox7yg2tU4BfVO70VzgsT\nQ4nori6rqwQDMxFlrYri5DiZ5FgBmknvUA20YSqK6jYOCb1VAieHXl7EOC6M\nOoa0K5mAsVY2kzSWiLDpgGsP8SYs/k+znIop0+DxOIcxn+rEuwTveagE+MsJ\ngjyy\r\n=PR6g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "rollup": "2.55.0", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.0.6", "typescript": "^4.3.5", "@babel/core": "7.14.8", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.14.8", "@babel/preset-react": "7.14.5", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "@babel/preset-typescript": "^7.14.5", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.1", "@babel/plugin-proposal-class-properties": "7.14.5"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_5.2.0_1627584360152_0.9543003990413632", "host": "s3://npm-registry-packages"}}, "6.0.0-0": {"name": "react-countup", "version": "6.0.0-0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.0.0-0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "9e833db41490e5c634a6ff63741f1c883f6da9b0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.0.0-0.tgz", "fileCount": 14, "integrity": "sha512-MwiRn5IBvpwQUUaZOvMS8ep6ag5LO/fDFlGsqfYiKPexAVoqimP3PXzXGmdWYlXsGV2K4d/bJY5Hi9Bv+xkTTw==", "signatures": [{"sig": "MEQCIG5h7XKldsdEczTCkOP9M+djzwSMXWrLEJmhHhVKQHbgAiAke+8EhIVE+y/66gQ8Mo/o+o6e2g50qdfqlNruDxsrow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27348, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhKRTQCRA9TVsSAnZWagAAoBUP/jKXiAZmbHjAUesu4bXF\ngrqKCwFWV1j2rfP6bytxTDFUzq0raBSgDNGTTBfGouJUlmAsAEoP4zx6YBY0\nDTGqw9OaTEbsfaeqkQv8myTYMl6gHC7Op1Ar7LOasysoBtSGVsp/FWFFkD3b\nlUCf4iR1H43+tAy4B0Jgc1CZ6WvBoNG6WwyzDReKjKGegitr58HDx8CQhStv\nUBcX9JZtkG8OOWNaRgG9sgVhYJiCBw9dKdNnao+yJr0SwNDp5ANcgb8Mlrqv\nJBkD6gkDzFnPUFjJvM+Tk4pwJJDkM+Z4yxpsQ0G2eyLOkFfMtb0pE7zfY87f\nINSsomj0a7Ni24Kiv18SAAYq7vW4MuqsnwcaA28/HIPqL2ClorIMreX6T5Aq\nWwzKex44xzLkG0H8apkXMstOAvbKSgQvf5nt1XtgHVH91HKm8wz8U7xaiz7n\naWNTCKQ/nJCq44hHnvrkhFfiiUyt6Y4yorQ4VhIYT+McN/DRMHYiqsOyHV97\n92iuv8wT1/HLTa06MRsbUg6veI5bYpoUYye1DGGaHBG7Hg7SQwbf72sSipkJ\nHQOCHzpU236toJsxy5/nVooYwNIby1aeNuriNtMG/XHVCvEouWFv1Ip1KE8i\nd/7sneOF2VnxiNSe5aFK9zfRjyXKFm/k1I3rqT+kOC4qjxjx/L3SGd7JmzDs\nMmnh\r\n=TlLn\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "readme": "# [React CountUp](https://tr8tk.csb.app/)\n\n[![GitHub license](https://img.shields.io/npm/l/react-countup.svg?style=flat-square)](https://github.com/glennreyes/react-countup/blob/master/LICENSE)\n[![Build Status](https://img.shields.io/travis/glennreyes/react-countup.svg?style=flat-square)](https://travis-ci.org/glennreyes/react-countup)\n[![Coverage Status](https://img.shields.io/coveralls/glennreyes/react-countup.svg?style=flat-square)](https://coveralls.io/github/glennreyes/react-countup)\n[![Version](https://img.shields.io/npm/v/react-countup.svg?style=flat-square)](https://www.npmjs.com/package/react-countup)\n[![Downloads](https://img.shields.io/npm/dm/react-countup.svg?style=flat-square)](http://www.npmtrends.com/react-countup)\n[![Gzip size](https://img.badgesize.io/https://unpkg.com/react-countup?style=flat-square&compression=gzip)](https://img.badgesize.io/https://unpkg.com/react-countup)\n\nA configurable React component wrapper around [CountUp.js](https://inorganik.github.io/countUp.js/).\n\nClick [here](https://codesandbox.io/s/github/glennreyes/react-countup/tree/master/demo?fontsize=14&hidenavigation=1&theme=dark&view=preview) to view on CodeSandbox.\n\n### Looking for v3.x docs?\n\nClick [here](https://github.com/glennreyes/react-countup/tree/d0002932dac8a274f951e53b1d9b1f4719176147) to get to the previous docs.\n\n![react-countup](https://user-images.githubusercontent.com/5080854/43985960-0a7fb776-9d0c-11e8-8082-975b1e8bf51c.gif)\n\n## Table of Contents\n\n- [Installation](#installation)\n  - [Usage](#usage)\n    - [Simple example](#simple-example)\n    - [Render prop example](#render-prop-example)\n    - [More examples](#more-examples)\n      - [Manually start with render prop](#manually-start-with-render-prop)\n      - [Autostart with render prop](#autostart-with-render-prop)\n      - [Delay start](#delay-start)\n      - [Hook](#hook)\n  - [API](#api)\n    - [Props](#props)\n      - [`className: string`](#classname-string)\n      - [`decimal: string`](#decimal-string)\n      - [`decimals: number`](#decimals-number)\n      - [`delay: ?number`](#delay-number)\n      - [`duration: number`](#duration-number)\n      - [`end: number`](#end-number)\n      - [`prefix: string`](#prefix-string)\n      - [`redraw: boolean`](#redraw-boolean)\n      - [`preserveValue: boolean`](#preservevalue-boolean)\n      - [`separator: string`](#separator-string)\n      - [`start: number`](#start-number)\n      - [`startOnMount: boolean`](#startonmount-boolean)\n      - [`suffix: string`](#suffix-string)\n      - [`useEasing: boolean`](#useeasing-boolean)\n      - [`easingFn: (t: number, b: number, c: number, d: number) => number`](#easingfn-t-number-b-number-c-number-d-number--number)\n      - [`formattingFn: (value: number) => string`](#formattingfn-value-number--string)\n      - [`onEnd: ({ pauseResume, reset, start, update }) => void`](#onend--pauseresume-reset-start-update---void)\n      - [`onStart: ({ pauseResume, reset, update }) => void`](#onstart--pauseresume-reset-update---void)\n      - [`onPauseResume: ({ reset, start, update }) => void`](#onpauseresume--reset-start-update---void)\n      - [`onReset: ({ pauseResume, start, update }) => void`](#onreset--pauseresume-start-update---void)\n      - [`onUpdate: ({ pauseResume, reset, start }) => void`](#onupdate--pauseresume-reset-start---void)\n    - [Render props](#render-props)\n      - [`countUpRef: () => void`](#countupref---void)\n      - [`pauseResume: () => void`](#pauseresume---void)\n      - [`reset: () => void`](#reset---void)\n      - [`start: () => void`](#start---void)\n      - [`update: (newEnd: number?) => void`](#update-newend-number--void)\n  - [Protips](#protips)\n  - [License](#license)\n\n## Installation\n\n```bash\nyarn add react-countup\n```\n\n## Usage\n\n```js\nimport CountUp from 'react-countup';\n```\n\n### Simple example\n\n```js\n<CountUp end={100} />\n```\n\nThis will start a count up transition from `0` to `100` on render.\n\n### Render prop example\n\n```js\n<CountUp\n  start={-875.039}\n  end={160527.012}\n  duration={2.75}\n  separator=\" \"\n  decimals={4}\n  decimal=\",\"\n  prefix=\"EUR \"\n  suffix=\" left\"\n  onEnd={() => console.log('Ended! 👏')}\n  onStart={() => console.log('Started! 💨')}\n>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\nThe transition won't start on initial render as it needs to be triggered manually here.\n\n> Tip: If you need to start the render prop component immediately, you can set delay={0}.\n\n### More examples\n\n#### Manually start with render prop\n\n```js\n<CountUp start={0} end={100}>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\n#### Autostart with render prop\n\nRender start value but start transition on first render:\n\n```js\n<CountUp start={0} end={100} delay={0}>\n  {({ countUpRef }) => (\n    <div>\n      <span ref={countUpRef} />\n    </div>\n  )}\n</CountUp>\n```\n\nNote that `delay={0}` will automatically start the count up.\n\n#### Delay start\n\n```js\n<CountUp delay={2} end={100} />\n```\n\n### Hook\n\n#### Simple example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst SimpleHook = () => {\n  useCountUp({ ref: 'counter', end: 1234567 });\n  return <span id=\"counter\" />;\n};\n```\n\n#### Complete example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst CompleteHook = () => {\n  const countUpRef = React.useRef(null);\n  const { start, pauseResume, reset, update } = useCountUp({\n    ref: countUpRef,\n    start: 0,\n    end: 1234567,\n    delay: 1000,\n    duration: 5,\n    onReset: () => console.log('Resetted!'),\n    onUpdate: () => console.log('Updated!'),\n    onPauseResume: () => console.log('Paused or resumed!'),\n    onStart: ({ pauseResume }) => console.log(pauseResume),\n    onEnd: ({ pauseResume }) => console.log(pauseResume),\n  });\n  return (\n    <div>\n      <div ref={countUpRef} />\n      <button onClick={start}>Start</button>\n      <button onClick={reset}>Reset</button>\n      <button onClick={pauseResume}>Pause/Resume</button>\n      <button onClick={() => update(2000)}>Update to 2000</button>\n    </div>\n  );\n};\n```\n\n## API\n\n### Props\n\n#### `className: string`\n\nCSS class name of the span element.\n\n> Note: This won't be applied when using CountUp with render props.\n\n#### `decimal: string`\n\nSpecifies decimal character.\n\nDefault: `.`\n\n#### `decimals: number`\n\nAmount of decimals to display.\n\nDefault: `0`\n\n#### `delay: ?number`\n\nDelay in seconds before starting the transition.\n\nDefault: `null`\n\n> Note: `delay={0}` will automatically start the count up.\n\n#### `duration: number`\n\nDuration in seconds.\n\nDefault: `2`\n\n#### `end: number`\n\nTarget value.\n\n#### `prefix: string`\n\nStatic text before the transitioning value.\n\n#### `redraw: boolean`\n\nForces count up transition on every component update.\n\nDefault: `false`\n\n#### `preserveValue: boolean`\n\nSave previously ended number to start every new animation from it.\n\nDefault: `false`\n\n#### `separator: string`\n\nSpecifies character of thousands separator.\n\n#### `start: number`\n\nInitial value.\n\nDefault: `0`\n\n#### `startOnMount: boolean`\n\nUse for start counter on mount for hook usage.\n\nDefault: `true`\n\n#### `suffix: string`\n\nStatic text after the transitioning value.\n\n#### `useEasing: boolean`\n\nEnables easing. Set to `false` for a linear transition.\n\nDefault: `true`\n\n#### `easingFn: (t: number, b: number, c: number, d: number) => number`\n\nEasing function. Click [here](http://robertpenner.com/easing) for more details.\n\nDefault: [`easeInExpo`](https://github.com/inorganik/countUp.js/blob/master/countUp.js#L103-L106)\n\n#### `formattingFn: (value: number) => string`\n\nFunction to customize the formatting of the number\n\n#### `onEnd: ({ pauseResume, reset, start, update }) => void`\n\nCallback function on transition end.\n\n#### `onStart: ({ pauseResume, reset, update }) => void`\n\nCallback function on transition start.\n\n#### `onPauseResume: ({ reset, start, update }) => void`\n\nCallback function on pause or resume.\n\n#### `onReset: ({ pauseResume, start, update }) => void`\n\nCallback function on reset.\n\n#### `onUpdate: ({ pauseResume, reset, start }) => void`\n\nCallback function on update.\n\n### Render props\n\n#### `countUpRef: () => void`\n\nRef to hook the countUp instance to\n\n#### `pauseResume: () => void`\n\nPauses or resumes the transition\n\n#### `reset: () => void`\n\nResets to initial value\n\n#### `start: () => void`\n\nStarts or restarts the transition\n\n#### `update: (newEnd: number?) => void`\n\nUpdates transition to the new end value (if given)\n\n## Protips\n\n### Trigger of transition\n\nBy default, the animation is triggered if any of the following props has changed:\n\n- `duration`\n- `end`\n- `start`\n\nIf `redraw` is set to `true` your component will start the transition on every component update.\n\n### Run if in focus\n\nYou need to check if your counter in viewport, [react-visibility-sensor](https://github.com/joshwnj/react-visibility-sensor) can be used for this purpose.\n\n```js\nimport React from 'react';\nimport CountUp from 'react-countup';\nimport VisibilitySensor from 'react-visibility-sensor';\nimport './styles.css';\n\nexport default function App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello CodeSandbox</h1>\n      <div className=\"content\" />\n      <VisibilitySensor partialVisibility offset={{ bottom: 200 }}>\n        {({ isVisible }) => (\n          <div style={{ height: 100 }}>\n            {isVisible ? <CountUp end={1000} /> : null}\n          </div>\n        )}\n      </VisibilitySensor>\n    </div>\n  );\n}\n```\n\n## License\n\nMIT\n", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"raf": "3.4.1", "jest": "27.0.6", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.55.1", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.0.6", "typescript": "^4.3.5", "@babel/core": "7.14.8", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.14.8", "@babel/preset-react": "7.14.5", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.1", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.0.0-0_1630082255996_0.4589144677577699", "host": "s3://npm-registry-packages"}}, "6.0.0-1": {"name": "react-countup", "version": "6.0.0-1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.0.0-1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "e423689798a2a891d054080b2813c863453a4801", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.0.0-1.tgz", "fileCount": 14, "integrity": "sha512-FsE4INBsQzWahqE43KxLc2I3qDsXbP1kvTgQUkK528HGwxmHOx1W0lXmu0R+daEkiFKAj2lOOTI3zwDa6hxMWg==", "signatures": [{"sig": "MEYCIQCJU01+8a50TDiNEUu+Xlp4J0E2A3y5H3Oa8jjdjJeqMQIhANrYPLixwwrUgC7w6B1H66qzit86jfNyOAi9YmB01Dag", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27375, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhOlolCRA9TVsSAnZWagAAjDMP/1RDNHxItXBYNJ4ADNCe\nNeiKKDxC8NDw1xi4cyXU3Xjihm60noxBJOSufhI5nzwSuj+HM/KtHDlI+iac\nysuvCo0CZ3hYa10ATvqC9WGX/7X+kWseNXughtIJzjMf+N6H+vrvY+iBdUB2\nkiTjSyBpN4lTSqVuvEBZXk36cytZ6y0dJUFnBK1ednCP7mari0Dx205ENd2r\n/FJm2Z2Edoj1DKzs3K7ypLQmZkwlFOc6NItJ+wMY3FcsYkA1+Z2u1h8V53qT\n5aU42imIgapC8L3tMWRKWvtMjTwmeEfe+6ZLlGG46JXHETIkJ5e7vvOVyJJe\nkXBMnjVpLg2d8AH26KsakmHitX81DahXxLxNuA+jpC6Hsz6elL0Ne/3ynYRG\neqEm+kyzV5D5nm3HV9RCvrSzEeX2I7qUoBwTkWlKoM6YMhaBrW6wk33eRIL4\nXDY8DnMd1+RrHID46S2kqedicbsBgvva3yLKhOkPFcVRr/DuxF0FG0gZaOtP\n0V/x8ZhlviGlSaKvzDetFEgQSlU/hHvbnKeN57xDJSLu7nKynfgs2/wjNWja\n7cCXPRaIiZmIuTRp0Iqt7PGp8KhHSEUGyn5w8WLN2yc3pKKd4lSMEiXX4n9O\n5KXjUWfsKnHJmss/2kSJxsGSSC4lwOgLSfPiTj2IkVv637U3M+A8HlKS9H4O\nO1nz\r\n=yXRB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "readme": "# [React CountUp](https://tr8tk.csb.app/)\n\n[![GitHub license](https://img.shields.io/npm/l/react-countup.svg?style=flat-square)](https://github.com/glennreyes/react-countup/blob/master/LICENSE)\n[![Build Status](https://img.shields.io/travis/glennreyes/react-countup.svg?style=flat-square)](https://travis-ci.org/glennreyes/react-countup)\n[![Coverage Status](https://img.shields.io/coveralls/glennreyes/react-countup.svg?style=flat-square)](https://coveralls.io/github/glennreyes/react-countup)\n[![Version](https://img.shields.io/npm/v/react-countup.svg?style=flat-square)](https://www.npmjs.com/package/react-countup)\n[![Downloads](https://img.shields.io/npm/dm/react-countup.svg?style=flat-square)](http://www.npmtrends.com/react-countup)\n[![Gzip size](https://img.badgesize.io/https://unpkg.com/react-countup?style=flat-square&compression=gzip)](https://img.badgesize.io/https://unpkg.com/react-countup)\n\nA configurable React component wrapper around [CountUp.js](https://inorganik.github.io/countUp.js/).\n\nClick [here](https://codesandbox.io/s/github/glennreyes/react-countup/tree/master/demo?fontsize=14&hidenavigation=1&theme=dark&view=preview) to view on CodeSandbox.\n\n### Looking for v3.x docs?\n\nClick [here](https://github.com/glennreyes/react-countup/tree/d0002932dac8a274f951e53b1d9b1f4719176147) to get to the previous docs.\n\n![react-countup](https://user-images.githubusercontent.com/5080854/43985960-0a7fb776-9d0c-11e8-8082-975b1e8bf51c.gif)\n\n## Table of Contents\n\n- [Installation](#installation)\n  - [Usage](#usage)\n    - [Simple example](#simple-example)\n    - [Render prop example](#render-prop-example)\n    - [More examples](#more-examples)\n      - [Manually start with render prop](#manually-start-with-render-prop)\n      - [Autostart with render prop](#autostart-with-render-prop)\n      - [Delay start](#delay-start)\n      - [Hook](#hook)\n  - [API](#api)\n    - [Props](#props)\n      - [`className: string`](#classname-string)\n      - [`decimal: string`](#decimal-string)\n      - [`decimals: number`](#decimals-number)\n      - [`delay: ?number`](#delay-number)\n      - [`duration: number`](#duration-number)\n      - [`end: number`](#end-number)\n      - [`prefix: string`](#prefix-string)\n      - [`redraw: boolean`](#redraw-boolean)\n      - [`preserveValue: boolean`](#preservevalue-boolean)\n      - [`separator: string`](#separator-string)\n      - [`start: number`](#start-number)\n      - [`startOnMount: boolean`](#startonmount-boolean)\n      - [`suffix: string`](#suffix-string)\n      - [`useEasing: boolean`](#useeasing-boolean)\n      - [`easingFn: (t: number, b: number, c: number, d: number) => number`](#easingfn-t-number-b-number-c-number-d-number--number)\n      - [`formattingFn: (value: number) => string`](#formattingfn-value-number--string)\n      - [`onEnd: ({ pauseResume, reset, start, update }) => void`](#onend--pauseresume-reset-start-update---void)\n      - [`onStart: ({ pauseResume, reset, update }) => void`](#onstart--pauseresume-reset-update---void)\n      - [`onPauseResume: ({ reset, start, update }) => void`](#onpauseresume--reset-start-update---void)\n      - [`onReset: ({ pauseResume, start, update }) => void`](#onreset--pauseresume-start-update---void)\n      - [`onUpdate: ({ pauseResume, reset, start }) => void`](#onupdate--pauseresume-reset-start---void)\n    - [Render props](#render-props)\n      - [`countUpRef: () => void`](#countupref---void)\n      - [`pauseResume: () => void`](#pauseresume---void)\n      - [`reset: () => void`](#reset---void)\n      - [`start: () => void`](#start---void)\n      - [`update: (newEnd: number?) => void`](#update-newend-number--void)\n  - [Protips](#protips)\n  - [License](#license)\n\n## Installation\n\n```bash\nyarn add react-countup\n```\n\n## Usage\n\n```js\nimport CountUp from 'react-countup';\n```\n\n### Simple example\n\n```js\n<CountUp end={100} />\n```\n\nThis will start a count up transition from `0` to `100` on render.\n\n### Render prop example\n\n```js\n<CountUp\n  start={-875.039}\n  end={160527.012}\n  duration={2.75}\n  separator=\" \"\n  decimals={4}\n  decimal=\",\"\n  prefix=\"EUR \"\n  suffix=\" left\"\n  onEnd={() => console.log('Ended! 👏')}\n  onStart={() => console.log('Started! 💨')}\n>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\nThe transition won't start on initial render as it needs to be triggered manually here.\n\n> Tip: If you need to start the render prop component immediately, you can set delay={0}.\n\n### More examples\n\n#### Manually start with render prop\n\n```js\n<CountUp start={0} end={100}>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\n#### Autostart with render prop\n\nRender start value but start transition on first render:\n\n```js\n<CountUp start={0} end={100} delay={0}>\n  {({ countUpRef }) => (\n    <div>\n      <span ref={countUpRef} />\n    </div>\n  )}\n</CountUp>\n```\n\nNote that `delay={0}` will automatically start the count up.\n\n#### Delay start\n\n```js\n<CountUp delay={2} end={100} />\n```\n\n### Hook\n\n#### Simple example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst SimpleHook = () => {\n  useCountUp({ ref: 'counter', end: 1234567 });\n  return <span id=\"counter\" />;\n};\n```\n\n#### Complete example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst CompleteHook = () => {\n  const countUpRef = React.useRef(null);\n  const { start, pauseResume, reset, update } = useCountUp({\n    ref: countUpRef,\n    start: 0,\n    end: 1234567,\n    delay: 1000,\n    duration: 5,\n    onReset: () => console.log('Resetted!'),\n    onUpdate: () => console.log('Updated!'),\n    onPauseResume: () => console.log('Paused or resumed!'),\n    onStart: ({ pauseResume }) => console.log(pauseResume),\n    onEnd: ({ pauseResume }) => console.log(pauseResume),\n  });\n  return (\n    <div>\n      <div ref={countUpRef} />\n      <button onClick={start}>Start</button>\n      <button onClick={reset}>Reset</button>\n      <button onClick={pauseResume}>Pause/Resume</button>\n      <button onClick={() => update(2000)}>Update to 2000</button>\n    </div>\n  );\n};\n```\n\n## API\n\n### Props\n\n#### `className: string`\n\nCSS class name of the span element.\n\n> Note: This won't be applied when using CountUp with render props.\n\n#### `decimal: string`\n\nSpecifies decimal character.\n\nDefault: `.`\n\n#### `decimals: number`\n\nAmount of decimals to display.\n\nDefault: `0`\n\n#### `delay: ?number`\n\nDelay in seconds before starting the transition.\n\nDefault: `null`\n\n> Note: `delay={0}` will automatically start the count up.\n\n#### `duration: number`\n\nDuration in seconds.\n\nDefault: `2`\n\n#### `end: number`\n\nTarget value.\n\n#### `prefix: string`\n\nStatic text before the transitioning value.\n\n#### `redraw: boolean`\n\nForces count up transition on every component update.\n\nDefault: `false`\n\n#### `preserveValue: boolean`\n\nSave previously ended number to start every new animation from it.\n\nDefault: `false`\n\n#### `separator: string`\n\nSpecifies character of thousands separator.\n\n#### `start: number`\n\nInitial value.\n\nDefault: `0`\n\n#### `startOnMount: boolean`\n\nUse for start counter on mount for hook usage.\n\nDefault: `true`\n\n#### `suffix: string`\n\nStatic text after the transitioning value.\n\n#### `useEasing: boolean`\n\nEnables easing. Set to `false` for a linear transition.\n\nDefault: `true`\n\n#### `easingFn: (t: number, b: number, c: number, d: number) => number`\n\nEasing function. Click [here](http://robertpenner.com/easing) for more details.\n\nDefault: [`easeInExpo`](https://github.com/inorganik/countUp.js/blob/master/countUp.js#L103-L106)\n\n#### `formattingFn: (value: number) => string`\n\nFunction to customize the formatting of the number\n\n#### `onEnd: ({ pauseResume, reset, start, update }) => void`\n\nCallback function on transition end.\n\n#### `onStart: ({ pauseResume, reset, update }) => void`\n\nCallback function on transition start.\n\n#### `onPauseResume: ({ reset, start, update }) => void`\n\nCallback function on pause or resume.\n\n#### `onReset: ({ pauseResume, start, update }) => void`\n\nCallback function on reset.\n\n#### `onUpdate: ({ pauseResume, reset, start }) => void`\n\nCallback function on update.\n\n### Render props\n\n#### `countUpRef: () => void`\n\nRef to hook the countUp instance to\n\n#### `pauseResume: () => void`\n\nPauses or resumes the transition\n\n#### `reset: () => void`\n\nResets to initial value\n\n#### `start: () => void`\n\nStarts or restarts the transition\n\n#### `update: (newEnd: number?) => void`\n\nUpdates transition to the new end value (if given)\n\n## Protips\n\n### Trigger of transition\n\nBy default, the animation is triggered if any of the following props has changed:\n\n- `duration`\n- `end`\n- `start`\n\nIf `redraw` is set to `true` your component will start the transition on every component update.\n\n### Run if in focus\n\nYou need to check if your counter in viewport, [react-visibility-sensor](https://github.com/joshwnj/react-visibility-sensor) can be used for this purpose.\n\n```js\nimport React from 'react';\nimport CountUp from 'react-countup';\nimport VisibilitySensor from 'react-visibility-sensor';\nimport './styles.css';\n\nexport default function App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello CodeSandbox</h1>\n      <div className=\"content\" />\n      <VisibilitySensor partialVisibility offset={{ bottom: 200 }}>\n        {({ isVisible }) => (\n          <div style={{ height: 100 }}>\n            {isVisible ? <CountUp end={1000} /> : null}\n          </div>\n        )}\n      </VisibilitySensor>\n    </div>\n  );\n}\n```\n\n## License\n\nMIT\n", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"raf": "3.4.1", "jest": "27.1.0", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.56.3", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.1.0", "typescript": "^4.3.5", "@babel/core": "7.15.0", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.15.0", "@babel/preset-react": "7.14.5", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.0.0-1_1631214117059_0.7589025892637131", "host": "s3://npm-registry-packages"}}, "6.0.0-2": {"name": "react-countup", "version": "6.0.0-2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.0.0-2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "20d5bfda2c1941a25041660bff81620a0e9bb209", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.0.0-2.tgz", "fileCount": 14, "integrity": "sha512-5aUXh3pxD8oSrv/YU3H/QxkpxGCi906ugi58sXpVD7G8ZWeAkOaOHNXzATcZKvScO964m8OaXVvhzf5S24HKUA==", "signatures": [{"sig": "MEUCIEOBUQ9kQKq7DsHCKp4N2eaS5d95yVWrYvvfZ2EYntpVAiEAuJYYcdqaTmMXNFPIbONOGlVtFEXa+P/RaQVN9wtBaC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ2ypCRA9TVsSAnZWagAAwAcP/0+KOYBOfN+bhOFZ3afm\n/ZxGbWp0kEQcJEW2j6jfGIAE7FC0LfzL1JSArqHVKnLEKMyBbYzuT1Cj+kRV\nfO2sn7YxVePZhoThPiEetuWWAmfRBDyzNMboAT9eEqtqBVdr3/6Ap2KkjyLz\naEpXMSz0FloahfX4D3RknW3mvdwJYv+PQH7Bzz4fbJhnIz7SgdRGxpAzQYhu\nn/8DsNiwA0DXDx/D/DK0VeE641rZUBoG00y/Cqkl4Dbl6qDwvCzHULMtxLzy\nhG+aziUbo8hfj+9d+pGprBDTtuRvN0zFbeQPO3mau/orcpu6U6IR+lP9CQ9g\ntVnsZSVPlaRMFAy9fnp1Jnm4V2YiEyMWIIXgibuk5q/szPcZxGGdeRKJFz1f\n+gx2bloDiB0zy+ll9N1MpATQSZ1AowGjrg8qfMK7IcryOGK1WO7ioEY//QwI\nLwSzC8/5lZoEfnauHRFN6uhVHll1ZsdsRHWlchfmjhszXCoFxaOjjvWwyStX\n9wWb6qwP3CX3A+EyIcmHL7QAVMipfnfh1XDei2YyE/elothpilMcNm+qV0SL\nLXS5kehd51yNo6Bm8QUy+sada8FYgd6i6NlMXTj9+sr/VxBaT2cJYcuW4oqh\ncnCvzEEVr9368YOmL4Wkh9EYUArrHBEbh/DsaoHdDXPB1X6+iqV+xPZNoR1+\nRbqV\r\n=+VMP\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "readme": "# [React CountUp](https://tr8tk.csb.app/)\n\n[![GitHub license](https://img.shields.io/npm/l/react-countup.svg?style=flat-square)](https://github.com/glennreyes/react-countup/blob/master/LICENSE)\n[![Build Status](https://img.shields.io/travis/glennreyes/react-countup.svg?style=flat-square)](https://travis-ci.org/glennreyes/react-countup)\n[![Coverage Status](https://img.shields.io/coveralls/glennreyes/react-countup.svg?style=flat-square)](https://coveralls.io/github/glennreyes/react-countup)\n[![Version](https://img.shields.io/npm/v/react-countup.svg?style=flat-square)](https://www.npmjs.com/package/react-countup)\n[![Downloads](https://img.shields.io/npm/dm/react-countup.svg?style=flat-square)](http://www.npmtrends.com/react-countup)\n[![Gzip size](https://img.badgesize.io/https://unpkg.com/react-countup?style=flat-square&compression=gzip)](https://img.badgesize.io/https://unpkg.com/react-countup)\n\nA configurable React component wrapper around [CountUp.js](https://inorganik.github.io/countUp.js/).\n\nClick [here](https://codesandbox.io/s/github/glennreyes/react-countup/tree/master/demo?fontsize=14&hidenavigation=1&theme=dark&view=preview) to view on CodeSandbox.\n\n### Looking for v3.x docs?\n\nClick [here](https://github.com/glennreyes/react-countup/tree/d0002932dac8a274f951e53b1d9b1f4719176147) to get to the previous docs.\n\n![react-countup](https://user-images.githubusercontent.com/5080854/43985960-0a7fb776-9d0c-11e8-8082-975b1e8bf51c.gif)\n\n## Table of Contents\n\n- [Installation](#installation)\n  - [Usage](#usage)\n    - [Simple example](#simple-example)\n    - [Render prop example](#render-prop-example)\n    - [More examples](#more-examples)\n      - [Manually start with render prop](#manually-start-with-render-prop)\n      - [Autostart with render prop](#autostart-with-render-prop)\n      - [Delay start](#delay-start)\n      - [Hook](#hook)\n  - [API](#api)\n    - [Props](#props)\n      - [`className: string`](#classname-string)\n      - [`decimal: string`](#decimal-string)\n      - [`decimals: number`](#decimals-number)\n      - [`delay: ?number`](#delay-number)\n      - [`duration: number`](#duration-number)\n      - [`end: number`](#end-number)\n      - [`prefix: string`](#prefix-string)\n      - [`redraw: boolean`](#redraw-boolean)\n      - [`preserveValue: boolean`](#preservevalue-boolean)\n      - [`separator: string`](#separator-string)\n      - [`start: number`](#start-number)\n      - [`startOnMount: boolean`](#startonmount-boolean)\n      - [`suffix: string`](#suffix-string)\n      - [`useEasing: boolean`](#useeasing-boolean)\n      - [`easingFn: (t: number, b: number, c: number, d: number) => number`](#easingfn-t-number-b-number-c-number-d-number--number)\n      - [`formattingFn: (value: number) => string`](#formattingfn-value-number--string)\n      - [`onEnd: ({ pauseResume, reset, start, update }) => void`](#onend--pauseresume-reset-start-update---void)\n      - [`onStart: ({ pauseResume, reset, update }) => void`](#onstart--pauseresume-reset-update---void)\n      - [`onPauseResume: ({ reset, start, update }) => void`](#onpauseresume--reset-start-update---void)\n      - [`onReset: ({ pauseResume, start, update }) => void`](#onreset--pauseresume-start-update---void)\n      - [`onUpdate: ({ pauseResume, reset, start }) => void`](#onupdate--pauseresume-reset-start---void)\n    - [Render props](#render-props)\n      - [`countUpRef: () => void`](#countupref---void)\n      - [`pauseResume: () => void`](#pauseresume---void)\n      - [`reset: () => void`](#reset---void)\n      - [`start: () => void`](#start---void)\n      - [`update: (newEnd: number?) => void`](#update-newend-number--void)\n  - [Protips](#protips)\n  - [License](#license)\n\n## Installation\n\n```bash\nyarn add react-countup\n```\n\n## Usage\n\n```js\nimport CountUp from 'react-countup';\n```\n\n### Simple example\n\n```js\n<CountUp end={100} />\n```\n\nThis will start a count up transition from `0` to `100` on render.\n\n### Render prop example\n\n```js\n<CountUp\n  start={-875.039}\n  end={160527.012}\n  duration={2.75}\n  separator=\" \"\n  decimals={4}\n  decimal=\",\"\n  prefix=\"EUR \"\n  suffix=\" left\"\n  onEnd={() => console.log('Ended! 👏')}\n  onStart={() => console.log('Started! 💨')}\n>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\nThe transition won't start on initial render as it needs to be triggered manually here.\n\n> Tip: If you need to start the render prop component immediately, you can set delay={0}.\n\n### More examples\n\n#### Manually start with render prop\n\n```js\n<CountUp start={0} end={100}>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\n#### Autostart with render prop\n\nRender start value but start transition on first render:\n\n```js\n<CountUp start={0} end={100} delay={0}>\n  {({ countUpRef }) => (\n    <div>\n      <span ref={countUpRef} />\n    </div>\n  )}\n</CountUp>\n```\n\nNote that `delay={0}` will automatically start the count up.\n\n#### Delay start\n\n```js\n<CountUp delay={2} end={100} />\n```\n\n### Hook\n\n#### Simple example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst SimpleHook = () => {\n  useCountUp({ ref: 'counter', end: 1234567 });\n  return <span id=\"counter\" />;\n};\n```\n\n#### Complete example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst CompleteHook = () => {\n  const countUpRef = React.useRef(null);\n  const { start, pauseResume, reset, update } = useCountUp({\n    ref: countUpRef,\n    start: 0,\n    end: 1234567,\n    delay: 1000,\n    duration: 5,\n    onReset: () => console.log('Resetted!'),\n    onUpdate: () => console.log('Updated!'),\n    onPauseResume: () => console.log('Paused or resumed!'),\n    onStart: ({ pauseResume }) => console.log(pauseResume),\n    onEnd: ({ pauseResume }) => console.log(pauseResume),\n  });\n  return (\n    <div>\n      <div ref={countUpRef} />\n      <button onClick={start}>Start</button>\n      <button onClick={reset}>Reset</button>\n      <button onClick={pauseResume}>Pause/Resume</button>\n      <button onClick={() => update(2000)}>Update to 2000</button>\n    </div>\n  );\n};\n```\n\n## API\n\n### Props\n\n#### `className: string`\n\nCSS class name of the span element.\n\n> Note: This won't be applied when using CountUp with render props.\n\n#### `decimal: string`\n\nSpecifies decimal character.\n\nDefault: `.`\n\n#### `decimals: number`\n\nAmount of decimals to display.\n\nDefault: `0`\n\n#### `delay: ?number`\n\nDelay in seconds before starting the transition.\n\nDefault: `null`\n\n> Note: `delay={0}` will automatically start the count up.\n\n#### `duration: number`\n\nDuration in seconds.\n\nDefault: `2`\n\n#### `end: number`\n\nTarget value.\n\n#### `prefix: string`\n\nStatic text before the transitioning value.\n\n#### `redraw: boolean`\n\nForces count up transition on every component update.\n\nDefault: `false`\n\n#### `preserveValue: boolean`\n\nSave previously ended number to start every new animation from it.\n\nDefault: `false`\n\n#### `separator: string`\n\nSpecifies character of thousands separator.\n\n#### `start: number`\n\nInitial value.\n\nDefault: `0`\n\n#### `startOnMount: boolean`\n\nUse for start counter on mount for hook usage.\n\nDefault: `true`\n\n#### `suffix: string`\n\nStatic text after the transitioning value.\n\n#### `useEasing: boolean`\n\nEnables easing. Set to `false` for a linear transition.\n\nDefault: `true`\n\n#### `easingFn: (t: number, b: number, c: number, d: number) => number`\n\nEasing function. Click [here](http://robertpenner.com/easing) for more details.\n\nDefault: [`easeInExpo`](https://github.com/inorganik/countUp.js/blob/master/countUp.js#L103-L106)\n\n#### `formattingFn: (value: number) => string`\n\nFunction to customize the formatting of the number\n\n#### `onEnd: ({ pauseResume, reset, start, update }) => void`\n\nCallback function on transition end.\n\n#### `onStart: ({ pauseResume, reset, update }) => void`\n\nCallback function on transition start.\n\n#### `onPauseResume: ({ reset, start, update }) => void`\n\nCallback function on pause or resume.\n\n#### `onReset: ({ pauseResume, start, update }) => void`\n\nCallback function on reset.\n\n#### `onUpdate: ({ pauseResume, reset, start }) => void`\n\nCallback function on update.\n\n### Render props\n\n#### `countUpRef: () => void`\n\nRef to hook the countUp instance to\n\n#### `pauseResume: () => void`\n\nPauses or resumes the transition\n\n#### `reset: () => void`\n\nResets to initial value\n\n#### `start: () => void`\n\nStarts or restarts the transition\n\n#### `update: (newEnd: number?) => void`\n\nUpdates transition to the new end value (if given)\n\n## Protips\n\n### Trigger of transition\n\nBy default, the animation is triggered if any of the following props has changed:\n\n- `duration`\n- `end`\n- `start`\n\nIf `redraw` is set to `true` your component will start the transition on every component update.\n\n### Run if in focus\n\nYou need to check if your counter in viewport, [react-visibility-sensor](https://github.com/joshwnj/react-visibility-sensor) can be used for this purpose.\n\n```js\nimport React from 'react';\nimport CountUp from 'react-countup';\nimport VisibilitySensor from 'react-visibility-sensor';\nimport './styles.css';\n\nexport default function App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello CodeSandbox</h1>\n      <div className=\"content\" />\n      <VisibilitySensor partialVisibility offset={{ bottom: 200 }}>\n        {({ isVisible }) => (\n          <div style={{ height: 100 }}>\n            {isVisible ? <CountUp end={1000} /> : null}\n          </div>\n        )}\n      </VisibilitySensor>\n    </div>\n  );\n}\n```\n\n## License\n\nMIT\n", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "readmeFilename": "README.md", "devDependencies": {"raf": "3.4.1", "jest": "27.1.0", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.56.3", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.1.0", "typescript": "^4.3.5", "@babel/core": "7.15.0", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.15.0", "@babel/preset-react": "7.14.5", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.0.0-2_1631808681738_0.9645328741476067", "host": "s3://npm-registry-packages"}}, "6.0.0": {"name": "react-countup", "version": "6.0.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.0.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "463f174781db4bec25de8853335c18ff0a305131", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.0.0.tgz", "fileCount": 14, "integrity": "sha512-Q5M8TbtdeSrnK6UaMgZBaV13GZHKsWh5FedzFM9HSWcI48x1SzJ33fCJcX23juLzguwRU3ZtumqlLdCUJdRPfA==", "signatures": [{"sig": "MEUCIHfcu1rutvIQeppqM+u52BS0XYiCsY3V/uboMlmgoMXBAiEAh2e3J7FX6MLUvAwOaocMxvHTcgC0THpS9oUQc6pCFgg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28885}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.1.0", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.56.3", "prettier": "2.3.2", "react-dom": "17.0.2", "babel-jest": "27.1.0", "typescript": "^4.3.5", "@babel/core": "7.15.0", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.15.0", "@babel/preset-react": "7.14.5", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.0.0_1632065641302_0.9325363353385239", "host": "s3://npm-registry-packages"}}, "6.1.0": {"name": "react-countup", "version": "6.1.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.1.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ac9b923ecf1351a0c3281209ba55952345814cd8", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.1.0.tgz", "fileCount": 14, "integrity": "sha512-0tN65l4ksaNa4rm8ZKshpGxbIHQ4RAh8TGaKYp06EZ7nZw+haXpW3dQTVDhTey9+10jDkJgdzyXKDmC96c1M8g==", "signatures": [{"sig": "MEYCIQCFJwOAXH80D0pm5a1CcMChKciltBjQYSUHX6Xr1SVnmAIhAL4yipIwSaDag9z4P1lSFDGFcu8c5qUzPJ6CAHFfncxg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29319, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh24MCCRA9TVsSAnZWagAACvAP/3whgaaFvIIgt8X3TLL5\nfCDEGvsjb8OTNh7LsQZHMO0Zi3IySCsZLOZo5bd2bLH1INyFyL6O4qAyswPs\nboWAXvcLLtLotjEEZtTQ3eb2Cb9XeHkM1UqEugBHJkKR0hCCjA+UK1orcMuC\n/D3D9saCk2nTyqTwbXz0ghJ5xiaFDoHjOaWPGp+mw5WEvPYGU2QPAAUPz94p\nizxFe4/OC8BhBaqRCcPDNzNU95dr/wYQ3gXPuNVUb7pl9IBrA+CDW31rA00g\nHt8s9L1qqpj3l6QBrxdrfNc7gTjOYgOGHpzv+VrbxvLUc2GRRgCm3qNx18kI\n4EmoIaOveDjxOIXmZK+6tmnZTbCpIYNtc2bVVkPrQbruO3bXP7WRL2OxeTNi\naNctUFVWmUtD8kCOpvqGxeCRRpaeJVDxB2d4WSlHkAJw1/wuX0/8yWME6tWP\n2tC91c48+QDQLX/A3RyySMtyC/72lwWPJbB9v5SraF3934/0noEIbDFZsvH9\nH9LAoHXD60398YvRK/gndG1kVNPt+Q99K2mgu4IPGdmYnACglGghuqMbggW4\n/crF1Pq/VJCA9r2IUONbDimaVRk7PviYd7RX5UA8gGV/LVpDoK+nWuTp6juN\nopXD53PGDz906YmeExP9XVQ97WjdsMyeON0hEhZr0RvNdzqSPVTWIQi+cA1M\nWs+H\r\n=u0MZ\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.3.1", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.59.0", "prettier": "2.4.1", "react-dom": "17.0.2", "babel-jest": "27.3.1", "typescript": "^4.3.5", "@babel/core": "7.15.5", "pretty-quick": "3.1.1", "@types/warning": "^3.0.0", "@babel/preset-env": "7.16.0", "@babel/preset-react": "7.16.0", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.1.2", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.1.0_1636308496477_0.1291216253542955", "host": "s3://npm-registry-packages"}}, "6.1.1": {"name": "react-countup", "version": "6.1.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.1.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "2dd5a82dd1059afd4e269a33de5cd1fa5a7910d7", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.1.1.tgz", "fileCount": 14, "integrity": "sha512-9mYlnk/yCFg3g2/65pCTex6mr9h0eYdDMIL4T5nfLj/RzoiqY03wlPxZglzbJ6OzXvU37OmYApwQ4WMZNFr8Aw==", "signatures": [{"sig": "MEYCIQDpZvIE8yRnkWmXNyinsC8U8W6IAkcgG0D3kMGVgu/1EAIhAJhCwcukb7S1+4GFGmDuRUX+lfO+n3nfqgUR1yalIogd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29243, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5Z3iCRA9TVsSAnZWagAAgWwP/1gwIO7VrYBQhJgw54Vn\nAzHdBdXQ7IRjMr+xw04Xsz3t4b48d+QXDsEqSVTAUzA3a93hT7Tt8kxrh/L7\n34Hm7wmNyM2UtOZApk1tE0VGrl17u1qeYDFZnJZvwPkVpcd4354GoAI2Qkwq\nk74p8WCv7VFqGMOf5PJTk93xbvZnSPPnSp8JUmSdTjbZ94trRqc3H9htCCcy\nwBMY2wSib6d/KfVnOFiN1Vj0HDK3Jdn+Et5Ws/dIXrB++CV3mqORB9Eky4hh\nNsMDtEkhkRzZt0ocU/6EEfq8QA8UCOKyT+piKGQOvih7m4tM6dvg2dtp64Ll\nW+1tfw4fnEBocBVt5QVK6x87NMYRLBRzGioMF/diNUCuwVB0U0an1ZE3/fGB\nW38b2mf6+FaY4YD09DwAsp0YMb2tXLKoa1AOM5YSkuCWVvu9VOpjnnsruFI9\nz9Ex3gyBjsmdW6eGfPnIQ2aGQmKFW4OShZgNJEynk+4g2UzyPbLe4qi8Zeyv\nsFtC/svktZBtyToQPfkYYoui4EqPNWCY8NDxXI1VmRtItxSXUsLXF9T56So2\novYkSGwRKgte/r58J7NPQW1RSat7OPUrMg4fLBIQuWBDoMTp0K6Lg38alvkl\nvZEvA1zXs65iMwxabVFYWWUZ1BYEHIk473O536VaNxGy6Ukkb6nNBaNBzQf3\nMILM\r\n=Fg80\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.0.8"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.4.5", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.62.0", "prettier": "2.5.1", "react-dom": "17.0.2", "babel-jest": "27.4.5", "typescript": "^4.3.5", "@babel/core": "7.16.7", "pretty-quick": "3.1.3", "@types/warning": "^3.0.0", "@babel/preset-env": "7.16.7", "@babel/preset-react": "7.16.7", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.1.2", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.1.1_1642438114824_0.2620743785064652", "host": "s3://npm-registry-packages"}}, "6.2.0": {"name": "react-countup", "version": "6.2.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.2.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "091ef28874c5bd11fb713b98c56dd2093a80a932", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.2.0.tgz", "fileCount": 14, "integrity": "sha512-3WOKAQpWgjyFoH231SHEpIpHhDGb5g5EkTppM6T7vLa3X+8WMdw6750vVcY0wxysKiY00gTFhDwSB5qLU+xPZA==", "signatures": [{"sig": "MEYCIQDthh3HyiWHYKrfYaFbgX/LtvGP8n0Eu7GKuf6UpVZ7JwIhAKEre7OrcFlPn+XS2V1sksi6jw1/cc6Z0a86LV7yi/TG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29141, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiSKGMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO+w/5AReIfqUofAlH7V6ZpI6Xy3B/kagi9ti3i8QE1EI7kS0qjyTe\r\n53HYEK5edw+MqhrZAcFwvQYm5S98yWZVQ8fHQMSyxt2NKCni2LtBp7mFdcX8\r\n3wv+nAOgTz9W0kqY9OyERW48hh8DfPOXUe6NbkNF+40y7DMp18wJY7/gJkBM\r\nneD1pNYgRDZsdHL967Vky7gJQSiVIzWqhK29uAmdsXxFGSang3+HJjc2v2sM\r\nafRptI/87iy/tc/qFsNxQaO44JVkCL16XHeAKdQOewGimQ3zeFmNtSiUkz71\r\nNZ1sQP4JM7T9HezZpR5x/DSpKJl9bVYsctTeLMgmbAWLoJO0tLoBZkq+QwdW\r\nhJ8LhSPmGFRTPkvJREnGUdatqvY/d6k8NeGvyH6bDrc5/3DMmUfWaiNPsLq5\r\nifxbI80ixqk2Kj3em8DfNlLn6lxRRZBGmz8hP3/ACssDQ7qqhWvbIJNDNlm1\r\nUdLwzokIzeYat9yHKVaROErGpEHBmod47gCMi31aZsI3SxXGobIRaRqcRvLd\r\nE+DlmEI0+R5bHvxyYEFqXoPDvV9FOutRkskAOMLav97xn58LCa3pT7JQy4Dv\r\nueJJZ/7XqVRBlcv0YwEYgJWHZyx1NQP6PiRlxY3jr3br+X0aKMl31rUyRNg7\r\nNmTJbJ15aDFya5TxoS3gT8p0nLz1pFSQ2nk=\r\n=Ajx5\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.1.0"}, "_hasShrinkwrap": false, "devDependencies": {"raf": "3.4.1", "jest": "27.5.1", "react": "17.0.2", "eslint": "^7.32.0", "rollup": "2.70.1", "prettier": "2.6.1", "react-dom": "17.0.2", "babel-jest": "27.5.1", "typescript": "^4.3.5", "@babel/core": "7.17.8", "pretty-quick": "3.1.3", "@types/warning": "^3.0.0", "@babel/preset-env": "7.16.11", "@babel/preset-react": "7.16.7", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "17.0.2", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "12.1.4", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@testing-library/react-hooks": "7.0.2", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.2.0_1648927116024_0.7809061542894351", "host": "s3://npm-registry-packages"}}, "6.3.0": {"name": "react-countup", "version": "6.3.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.3.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "9a345674d571f76c1046a0ded89fc86dbe52a2f0", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.3.0.tgz", "fileCount": 14, "integrity": "sha512-uZsE+kuisXp/3OZeAuq2lzp6sXKmzw331Et5CRy4ueR9VcOp7z7T6/O1F04BC2Pa2frKmIITsghi3Hh933qWXQ==", "signatures": [{"sig": "MEUCIQCBA4AVa1J1lUBDAdEWuGihSX4NLvOenCn7wvv7gV2igwIgGqAVfD1b4wWtWRkCHemKWSVKS4Cm+JFpL95l/Ry5xpI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30123, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisLS3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoMgw//XNPzT9gjgjvCjkXKffepm9aMx4PygiQfHJX9GOMQw5BT5rMK\r\n8ggBKat8o50/yDOO11Sw9qnTRSWL8rfHrFnMz4uc9L6KoOaSjhU4X6WWrBNx\r\nD4eIZymPuPWse0s7EvxNi6ntPIYgUr93DjZx+Wu1yHX5gL9H6hFwBDm8KRQC\r\nfkRdgcm9coLrygQOkFLGj05n39pmNP4iJsOSFnJ/CZbH60nWVJcyaHRCMfoq\r\nBaVyQvWsaOiQJYQJDdAtxGYfHXm/o2flMTuXqRgfWudUC7dhQX7a/QxNzTJD\r\n32tvdbnW0G0HqUNcRsDC3RLc+fl1nIMpU9j3ZLSwp5ts3jkgfKhHYqvLuWKv\r\ny6+G4iEwYL4KiNGbe5PIMkOL6CgnjJGDz6+0WAOB1dH7MJnHgd1WUm5+JVYi\r\no+8t4rrnis+todQw8Acoa/3+vmsamHF3miZEoar3UTm3RocY8jYcLJHXK42c\r\nBIkOhY9YHkW+BZ8Vt1pGvcySnfIjv2LAqvHg28E2GH6VbPUBbG7sBDsCv9Jm\r\nrpYVSdqcoeToqBSqtNAk6PedCPUXGrIUVgACdcuLe5U3QPGSaDjpyCpe+HDY\r\nJrdYj1XieT7x8SsrQBZB44f3fIY3xvsTJhLwSKhcTstd0DM/WNUG4u6hYqPZ\r\nVz3m3+wih5jjrT0c2GI3ZqYRwuU4kVBRwNQ=\r\n=29RU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.1", "react": "18.2.0", "eslint": "^7.32.0", "rollup": "2.75.7", "prettier": "2.7.1", "react-dom": "18.2.0", "babel-jest": "28.1.1", "typescript": "^4.3.5", "@babel/core": "7.18.5", "pretty-quick": "3.1.3", "@babel/preset-env": "7.18.2", "@babel/preset-react": "7.17.12", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "13.3.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^28.1.1", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.3.0_1655747767596_0.6852089119674438", "host": "s3://npm-registry-packages"}}, "6.3.1": {"name": "react-countup", "version": "6.3.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.3.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ae4e8d33a7ed86c2cfc27e20b1fe76704f61d9fb", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.3.1.tgz", "fileCount": 14, "integrity": "sha512-+Bf1caAZHtmVQ5Jmhe2MZuN1cw1CEP5OdeMph9CBwM5K8DEDGmg00AzcN6fO9XolUhqUfiR0pOEiSyngSrHwig==", "signatures": [{"sig": "MEYCIQDeOPmjydOcH9c6ZOOisqDozaaSLGhoxLC20LNQlzFhwQIhAK3B2Zyes+qrkTHq4FrbMA2YLeLquSXhwRaEpylAcumj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30258, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjCnTjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJqA//TcoroNbEFMF128kHdGniHTUE3GJktcI6Fv3lxy18yYQ9cHOr\r\n5NMl7sKy2MyEwJXEpuJDa1LjGbjsUHIK/OV8EtbpID6hJLchmgH8sIa2sXuT\r\nIjwC1+Y2GzDhV/8VN8s+Jt2833hYiBwvhQ9f0bXcwBvQVfLoffLK1u6M+Lr9\r\nYhRy3KxqKdjqiyhZipBPPTRc6QStMoI+0mc7y84cvaEYdWvn5nHdEdV7UtMU\r\nDvk+YxWkV6qJl6GakVmPm4e1lPoXt3RPE03rYhTtmZvEdJxRyXdPg8WD8Hbf\r\nUUL343FRGi5WkTnpe7UhNGraPQdERoKFwlhuObSkfdaaTffLlEUypX/1scYI\r\nNmv6lYgFklGf5Mbubf35/vyLRPJr5tFConoyGCz4cTHgeCEKunO+pIJZvh2o\r\nvQdIqdtHmO3azo6gbC9Q8Kq+nhdkjYR4HNl6jCPpCgdMyP8Z77eGpyAtW+Sg\r\nQWDDVmDUJtY1wYOn4d1mJ4ITmLv4Heu9pCBTj/AA5wmnodW3Y3G/4jzAPHqi\r\nR23ZQboQUTKYmvtByy+EuQLBXWMCm2v80jivnqxNrYdA7aHJcXWkA1dqSifq\r\n/8tX1gIl+21RQIZZkvVQhSPlZhHfeg4Pb/cDkQuziWGa/VDrkb9zeMJU9ns4\r\nxTVMH7tzU5lBuB+8TLQvvx9W1VbBqUWx+SY=\r\n=xjBp\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.2", "react": "18.2.0", "eslint": "^7.32.0", "rollup": "2.77.2", "prettier": "2.7.1", "react-dom": "18.2.0", "babel-jest": "28.1.3", "typescript": "^4.3.5", "@babel/core": "7.18.13", "pretty-quick": "3.1.3", "@babel/preset-env": "7.18.9", "@babel/preset-react": "7.17.12", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "13.3.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^28.1.1", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.3.1_1661629666930_0.6969540391915869", "host": "s3://npm-registry-packages"}}, "6.3.2": {"name": "react-countup", "version": "6.3.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.3.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "37359bb4262c2af52ae1e0ad36d2d0feff01d278", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.3.2.tgz", "fileCount": 14, "integrity": "sha512-ID3344D1Yo0X5CWQiJYXBpiPCYLwMOw1VvNP9geJE+PqpdDmx6iuoMhxXAqjpdaRWiD6zUnLRK6rrnGgDg09pg==", "signatures": [{"sig": "MEYCIQDauZgVlHSDxpjPhtJjpNPlyu3m6qF0XzL4Rs+PvBKckgIhAI+A6CaDzBDqGL6oDDuVQvGNGIn4qv6xS+mlzaeTTuIf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30279, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTCZkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjGw/5AegP5MwZUf90T2rLdezaScDzGtt3CkESOMt6F6lNAnJ6bHZP\r\n+BssXyS2ljzIkKIPmQk8l4S287UEry2AeUR+Yf6FTF/Tker+yrDqrGQDucJH\r\ntaEE6ls4Kx82uFsZaUV7MAoigMI1z2T+jwBha4ImmPtajvuQjvvrWiNsY1Ma\r\nbLM9XZMb4c0a8ojDtxgj9moE0//uyctlAuksslA6yd+rsGeCdLds2nKc6C2W\r\n8sWLyJ/C52OHmUrxVICWRTmdg7mWJoy2opn6bGTyMPR4W2jqKs1HTisYigfC\r\n9Gs30JQr08lBzGljRy98Pr5eCp+VgaoE9RaOSfevP0hzfHLEhIyGn0d74yRO\r\n/4tHfe5cXs+VQeNV7mReAAUk1S9yD8gtLwsqzoVijEPe28TovRqhJD9mK0dG\r\niwNHYGWhTDQGePjD1oPnOR4tO8qk4g6VJPrEaGsvOmyaBET7C3N+7UM8g8Za\r\n/bn306hD/ajVBWfHzXzesZyZYbBUSWaIEJLlNt1+Cb3k6XlAiirRW3XweOne\r\nZsNih3FtI2nHZAGMcdlOshlFEuWCPAbkF8ZjYBHtV+vf+lQCvUZk+3IgFpfM\r\nKq7BI+K/E+6yoyRXh4W6nUz+EJqM4Q62lPiSFXFku/4tCTvPHq5H0glwqxpt\r\nI9115cSdrQLDLE5PyiO2WZ/EwIuLGuMaxXQ=\r\n=ggEK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.2.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.2", "react": "18.2.0", "eslint": "^7.32.0", "rollup": "2.79.1", "prettier": "2.7.1", "react-dom": "18.2.0", "babel-jest": "29.2.0", "typescript": "^4.3.5", "@babel/core": "7.19.3", "pretty-quick": "3.1.3", "@babel/preset-env": "7.18.9", "@babel/preset-react": "7.18.6", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "rollup-plugin-babel": "4.4.0", "@testing-library/react": "13.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^28.1.1", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^4.29.1", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^13.0.4", "@typescript-eslint/eslint-plugin": "^4.29.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.3.2_1665934948262_0.07538715041273347", "host": "s3://npm-registry-packages"}}, "6.4.0": {"name": "react-countup", "version": "6.4.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.4.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "f18d24e40c37c465b29353172cc8b3b5e82f3241", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.4.0.tgz", "fileCount": 14, "integrity": "sha512-7vvz0IIcDwWdd1JDzDB3z+9wSWL5Cf7pqwMPUXnIe66M3UXZyMWrvTp1p96wpPgg8/v4Gvf8YLvJoO+34G0GMA==", "signatures": [{"sig": "MEYCIQDlu+0sSBHykog27F07vOD14LbcQHMepvi60FVqPKEVxgIhANbhpNv6yVgq4F1rSFg8ZxlFH9A9rS22SJ6W9BI9t0N3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjgDMnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9wxAAgfWMwz79WdcWyN+Vo/tkwexOKfgvsnnbZD+NJ/0Ng9UKgCzp\r\nVyztzf5mEwI/VrvY+GuZdGIohCT4ZUacPxzX07HQ/srTnM/lOKXlJzKZzUDq\r\n4cLEX+daquIKJGNQ8KCIVUAru5zeMezsbJRulz/ZJg3iBF/ubZtLfzyNIbYO\r\nPYaYCguQmGS3gi4sUMNlCWcwa4bAaAns2/7BxxDTGbF9ZXkxz/WJfmCL7mcX\r\nNyEZhC36DAfdc1HAsZ9W+ciPJwKHJatrFQWv6fOEHoi1yHBGfTYsjHc7HGbW\r\ne6zuACQ4w3ibMEoYh0ODEtfBl4SXb26uUTiA5h3KW2yZnolWNaMcuiQdms2i\r\nSP17SX7CE9za/AYSC/mKSvTFxAaJntRSmwUyBxTLE6RKvhDw0aw1U5hVgMlJ\r\nBR635w8IsaAbvC7LcGeUSRFJzBc267frJvuMN9o3x6VW147wjagX7jW09GAA\r\n8TbTh0JYGqW5MbSXeQPFtRSOYY5KENWGle+fWBj2RCQVcquyawwLRWl7d5ba\r\nHN0XZTNT2pR7Z/fUEJQe/jQ0HlVCcSfeSPtzKjibOe+Ni1wZYDayR3P2f1Nx\r\nPYR04ITrx78JII1JkktyffY2WgoYVImxRG5m++73+7z4kA221vCvOqmfWUTd\r\nDgzNGlIgczIDICg90PpQUzqyvsetEckMnpI=\r\n=da6g\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.3.2", "@rollup/plugin-babel": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.2", "react": "18.2.0", "eslint": "^8.28.0", "rollup": "3.4.0", "prettier": "2.8.0", "react-dom": "18.2.0", "babel-jest": "29.3.1", "typescript": "^4.9.3", "@babel/core": "7.20.2", "pretty-quick": "3.1.3", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "@testing-library/react": "13.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^28.1.1", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^5.44.0", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.44.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.4.0_1669346087015_0.29905610211413824", "host": "s3://npm-registry-packages"}}, "6.4.1": {"name": "react-countup", "version": "6.4.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.4.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "cbbda64a64065cb12376eff23e5aa084665e6c4d", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.4.1.tgz", "fileCount": 14, "integrity": "sha512-FxQ7qaNnnbA+Px9RebOZY4Tbawa1bhgpVPlUqRemZb90mh3f3OqyHVgr1SZqnJgaRxR3uEYqgkszgkDgFVCeEw==", "signatures": [{"sig": "MEUCIQCsrGhwnouxCgrMnezLCGvwndTvIUq9hOTHo+dbuOO/UQIgVFMh0xTDEkB+fXisCXNwnNxB46s2H6j54uoHtNT/3Xw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30705, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0pYzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZxxAAiwq3DXmwLAtswFwrk/h0XUIDC3UEqd9TJcQFM+c832a1GnZZ\r\noqyt9EhFEcQAZJ1zi146uFV0mROpcvbr6y90pklg88h2gUALXakqImpnkr1p\r\nGZX3keMiXaNo/cLI14fkqCRlVPQRO2MGr2QjXub5K6VU08KrXHpFEYVM8YH5\r\nmtbaalt/zY01veejfDFbxHy6RWFb21CyTUodI3TwsA35brZNmuhe/8AUV3El\r\ngk2Pw6Sdonc3iV/fU6x8Xv+BbuPyix3IRFEesptiQUi5dp6Oe53tLhjczUjL\r\n8rxqNwvjTOB2x7TGOpnRMuCKHN9nHo+gOoqqnLvOk1RIANVPtr6UJPNYHYN4\r\njpmxrFv4PjOlUHE/6wbtTZkuki/8iCn2HN0EFU8JBFXvU2C/8lzyeVOMwAE1\r\nTkNx3+0L9dPvuLbyzvogKAmgejH1DEOeibXNAhsIFBP1+P3MaHNU4hZDQDE1\r\neA1t6bwKPcOhA+NRcZwI4kdJmHBme0zC4jB04PXYznfWQ1CrRXIdInEHdTdy\r\n8/0AIdEKTxyLP9FxouSBe3rhvkEI8c1RJu8cSBOiavuVhtEHZz5D68Z7HtQ3\r\n6cs91FFYXSUKumh5GScnunTVgpFlpnomVACrULWSve6e0HkJkn5+WKrYHiRN\r\nY0DRBa/0lENc/TnPque0ooNgAP3Ige/Ncz4=\r\n=rBIW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.4.1", "@rollup/plugin-babel": "^6.0.2"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "28.1.2", "react": "18.2.0", "eslint": "^8.28.0", "rollup": "3.11.0", "prettier": "2.8.3", "react-dom": "18.2.0", "babel-jest": "29.4.0", "typescript": "^4.9.3", "@babel/core": "7.20.12", "pretty-quick": "3.1.3", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "@testing-library/react": "13.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^28.1.1", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^5.44.0", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.44.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.4.1_1674745395676_0.08151187399663207", "host": "s3://npm-registry-packages"}}, "6.4.2": {"name": "react-countup", "version": "6.4.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.4.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "cf8564c9381958a36c7c25f7c0769f7a472e4c99", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.4.2.tgz", "fileCount": 14, "integrity": "sha512-wdDrNb2lPFGbLb+i0FTgswPbWziubS6KZRII8NRpXmUCoZsi15PFbIHgBz60Dyxd4KPuRvwsK5aawIU4OPP3jA==", "signatures": [{"sig": "MEYCIQC4+ZPNI4uBUIEno1zOdjB8FjiSK3SL1XfFLWiAPB/0rgIhAKhLNOUPkZetXsPNajDkAtAFiaTIz+cuPE5gZBLNuH3o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkDKa8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmosxw/9G0GPrFSx83Ao4O9jvpf8n6iBJt2yYT+3HE64XCWSf0/R5dwu\r\nPAon1U22PAfD9VtV4/9hJ93/AmEimTskbdTU5WDaORfAPrOdYdnbk2vbZX1L\r\n2+aNHJ9i4Tozpkq6vOB1ixoGc/p7YSwnF58cAYeZsxJEscVTObarNCqtQuDJ\r\nO59S3Xs6TVXC8uHTfid7PYl2YhHIrM38P2d2wy38o2JskivkNXFUNbt+ezAE\r\nUpZcTH8WLyTSxKcnZlaHEYsH/qEdpapUg6BsNskxEGfKvAXbX6Wb1vZnD9UE\r\nCe3r/Vkgi2jEU+ErzmfvNvrr9iIxyHn961oqv6eUeRcoYuYAV5OG+RENyfsv\r\nfjDnn78IoJTUDCz/alC/VR/wsOfFeeBhdxGXjbpAhdQ6zJwO/IZ916xEN2Tz\r\nsx+hCXyXhuharT+AxHa3AsnYyz+YatBmAqagtpeg2kDx6dG3Fht0mldnP1hx\r\n4K8M7+dDWI34mc3Ws46OweABktPjcYAq1UuaH3Qxrus+763N+DG0BXgRcQej\r\nN+7K+ExSJn5quKwTsqbt2piAvbJxcaQJtSt2D/l3bKqraiSzjPKj/P5VFZXY\r\nig9becUXTKljOS6cCgLyhCLUTzqIpzWQbzmyDjhoI1rxCFVWpLU9cDlsRmEQ\r\nabvvXojFW0Bfl2PQw5VrGvWCFVvIeP6gIog=\r\n=XfBc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "build", "scripts": {"test": "jest", "build": "rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.5.0", "@rollup/plugin-babel": "^6.0.3"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "29.5.0", "react": "18.2.0", "eslint": "^8.28.0", "rollup": "3.19.1", "prettier": "2.8.4", "react-dom": "18.2.0", "babel-jest": "29.5.0", "typescript": "^4.9.3", "@babel/core": "7.21.0", "pretty-quick": "3.1.3", "@babel/preset-env": "7.20.2", "@babel/preset-react": "7.18.6", "eslint-plugin-react": "^7.24.0", "react-test-renderer": "18.2.0", "@testing-library/react": "13.4.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.14.5", "@typescript-eslint/parser": "^5.44.0", "eslint-plugin-react-hooks": "^4.2.0", "@rollup/plugin-node-resolve": "^15.0.1", "@typescript-eslint/eslint-plugin": "^5.44.0"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.4.2_1678550716609_0.4893137825117062", "host": "s3://npm-registry-packages"}}, "6.5.0": {"name": "react-countup", "version": "6.5.0", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.5.0", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "2671035ec5012ed8db097a79d08fa6f193e139d3", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.5.0.tgz", "fileCount": 14, "integrity": "sha512-26JFHbUHsHxu8SetkJwWVIUEkaNnrj4P9msxNGC8tS4hGr1bngRzbwtJYOgXD2G/ItjaKJ3JfYKd85sw7qRVeA==", "signatures": [{"sig": "MEUCIBACX6iU0XhN5TzVizeAMVWyP7cMv2L0kjRwe60Ge8g+AiEAoyU2KbNZj0AGoQZkJbDCgZLhB7QZgCAT3+EOMaCqhVA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33571}, "main": "build", "scripts": {"test": "jest", "build": "rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepublishOnly": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "29.7.0", "react": "18.2.0", "eslint": "^8.52.0", "rollup": "4.2.0", "prettier": "3.0.3", "react-dom": "18.2.0", "babel-jest": "29.7.0", "typescript": "^5.2.2", "@babel/core": "7.23.2", "pretty-quick": "3.1.3", "@babel/preset-env": "7.23.2", "@babel/preset-react": "7.22.15", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.2.0", "@rollup/plugin-babel": "^6.0.4", "@testing-library/react": "14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest-environment-jsdom": "^29.7.0", "@babel/preset-typescript": "^7.23.2", "@typescript-eslint/parser": "^6.9.1", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^6.9.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.5.0_1698763171473_0.9698574035979206", "host": "s3://npm-registry-packages"}}, "6.5.1": {"name": "react-countup", "version": "6.5.1", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.5.1", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "7ec2a014dd422e8a33f29004b00024f22ca34fce", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.5.1.tgz", "fileCount": 15, "integrity": "sha512-G+7zS3tzFn8azsj8M9FEnWKvB1s0euoGX7GOP8cFVZPXpP5MVh7zOun4/F/TM2RNOBEl2ApJrPuQOhyZ78s0AA==", "signatures": [{"sig": "MEQCIGYgPI/XaUKy22ZwF0OZhX/YCEYsR3zTq5nk91KcyJgeAiAteRne4iayL3T7Ny9qNePPd3xVJ+aM/WDx6tTIsAKzzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47825}, "main": "build/index.js", "exports": {"types": "./build/index.d.ts", "import": "./build/index.mjs", "require": "./build/index.js"}, "scripts": {"test": "jest", "build": "rm -rf build && rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepack": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "29.7.0", "react": "18.2.0", "eslint": "^8.52.0", "rollup": "4.2.0", "prettier": "3.0.3", "react-dom": "18.2.0", "babel-jest": "29.7.0", "typescript": "^5.2.2", "@babel/core": "7.23.2", "pretty-quick": "3.1.3", "@babel/preset-env": "7.23.2", "@babel/preset-react": "7.22.15", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.2.0", "@rollup/plugin-babel": "^6.0.4", "@testing-library/react": "14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest-environment-jsdom": "^29.7.0", "@babel/preset-typescript": "^7.23.2", "@typescript-eslint/parser": "^6.9.1", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^6.9.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.5.1_1710712343563_0.552654492803645", "host": "s3://npm-registry-packages"}}, "6.5.2": {"name": "react-countup", "version": "6.5.2", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.5.2", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "ac1d8ddd95fb5ed8c8579722f0f4b0b10decdd2f", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.5.2.tgz", "fileCount": 15, "integrity": "sha512-5QXlUnDqBaMRbiAFGMs942EaJGGpYibER6IKBxKIedGZRza0ImACOUO0FdBrSWv8+RcjV2oKNT8lLvZ3vnjkVQ==", "signatures": [{"sig": "MEYCIQDlkLPpd6VWb+AjoypZkmGaVmNeQFtDk6ThibLH5cXvWQIhAOBbmPzEm4ab0VN8nGiGWegf2rO20Mn5iM6+57TNdlhu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 47568}, "main": "build", "scripts": {"test": "jest", "build": "rm -rf build && rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepack": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "29.7.0", "react": "18.2.0", "eslint": "^8.52.0", "rollup": "4.9.6", "prettier": "3.2.4", "react-dom": "18.2.0", "babel-jest": "29.7.0", "typescript": "^5.2.2", "@babel/core": "7.23.9", "pretty-quick": "3.1.3", "@babel/preset-env": "7.24.0", "@babel/preset-react": "7.23.3", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.2.0", "@rollup/plugin-babel": "^6.0.4", "@testing-library/react": "14.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest-environment-jsdom": "^29.7.0", "@babel/preset-typescript": "^7.23.2", "@typescript-eslint/parser": "^6.9.1", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^6.9.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.5.2_1710772036402_0.4639805786940796", "host": "s3://npm-registry-packages"}}, "6.5.3": {"name": "react-countup", "version": "6.5.3", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "_id": "react-countup@6.5.3", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "homepage": "https://react-countup.now.sh/", "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "dist": {"shasum": "e892aa3eab2d6ba9c3cdba30bf4ed6764826d848", "tarball": "https://registry.npmjs.org/react-countup/-/react-countup-6.5.3.tgz", "fileCount": 14, "integrity": "sha512-udnqVQitxC7QWADSPDOxVWULkLvKUWrDapn5i53HE4DPRVgs+Y5rr4bo25qEl8jSh+0l2cToJgGMx+clxPM3+w==", "signatures": [{"sig": "MEUCIQDuvmWC5NP5xlFbCyX6duc6UACUTZWGthKmc7j1A1OBTQIgRHCRoQJ5FNfv5cZxrPXFGcmiacbx3WQ/t3aiGSEb33k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33501}, "main": "build", "scripts": {"test": "jest", "build": "rm -rf build && rollup --bundleConfigAsCjs -c && tsc --emitDeclarationOnly --noEmit false --project src/tsconfig.json --outDir build", "format": "prettier --write \"*.md\" \"src/**/*.ts\"  \"src/**/*.tsx\"", "prepack": "yarn build"}, "typings": "build/index.d.ts", "_npmUser": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "directories": {}, "licenseText": "The MIT License (MIT)\n\nCopyright (c) 2018 <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR <PERSON><PERSON>Y<PERSON>GHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n", "dependencies": {"countup.js": "^2.8.0"}, "_hasShrinkwrap": false, "devDependencies": {"jest": "29.7.0", "react": "18.2.0", "eslint": "^8.52.0", "rollup": "4.9.6", "prettier": "3.2.4", "react-dom": "18.2.0", "babel-jest": "29.7.0", "typescript": "^5.2.2", "@babel/core": "7.23.9", "pretty-quick": "3.1.3", "@babel/preset-env": "7.24.0", "@babel/preset-react": "7.23.3", "eslint-plugin-react": "^7.33.2", "react-test-renderer": "18.2.0", "@rollup/plugin-babel": "^6.0.4", "@testing-library/react": "14.2.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest-environment-jsdom": "^29.7.0", "@babel/preset-typescript": "^7.23.2", "@typescript-eslint/parser": "^6.9.1", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^15.2.3", "@typescript-eslint/eslint-plugin": "^6.9.1"}, "peerDependencies": {"react": ">= 16.3.0"}, "_npmOperationalInternal": {"tmp": "tmp/react-countup_6.5.3_1711115274252_0.07746774745583096", "host": "s3://npm-registry-packages"}}}, "time": {"created": "2016-05-07T22:49:40.782Z", "modified": "2024-11-17T06:18:11.802Z", "0.0.0": "2016-05-07T22:49:40.782Z", "1.0.0": "2016-05-26T00:44:56.088Z", "1.0.1": "2016-05-26T01:19:30.917Z", "1.0.2": "2016-05-26T03:05:24.984Z", "1.0.3": "2016-05-26T11:58:18.129Z", "1.1.1": "2016-05-26T16:43:11.600Z", "1.1.2": "2016-05-26T17:13:16.011Z", "1.1.3": "2016-05-26T17:39:12.622Z", "1.1.4": "2016-05-26T17:58:27.984Z", "1.1.5": "2016-05-26T20:31:56.798Z", "1.1.6": "2016-05-26T21:19:12.847Z", "1.1.7": "2016-05-27T06:22:08.538Z", "1.1.8": "2016-06-22T12:49:13.034Z", "1.2.0": "2016-08-10T11:50:49.868Z", "1.3.0": "2016-10-20T19:25:34.964Z", "1.4.0": "2017-03-10T15:24:03.688Z", "2.0.0": "2017-03-10T16:43:28.792Z", "2.0.1": "2017-03-10T17:15:07.810Z", "2.0.2": "2017-03-10T17:44:23.736Z", "2.0.3": "2017-03-10T18:01:10.319Z", "2.1.0": "2017-05-09T19:20:14.149Z", "2.1.1": "2017-05-09T19:52:33.465Z", "2.2.0": "2017-08-10T23:58:54.102Z", "2.3.0": "2017-09-29T11:15:50.361Z", "2.4.0": "2017-10-31T09:47:24.418Z", "3.0.0": "2017-11-02T21:20:09.077Z", "3.0.1": "2017-11-07T18:16:12.408Z", "3.0.2": "2017-11-07T18:36:09.087Z", "3.0.3": "2018-02-13T11:29:38.149Z", "4.0.0-alpha.0": "2018-08-10T22:28:41.595Z", "4.0.0-alpha.1": "2018-08-10T23:33:39.090Z", "4.0.0-alpha.2": "2018-08-11T00:14:43.446Z", "4.0.0-alpha.3": "2018-08-12T14:37:19.831Z", "4.0.0-alpha.4": "2018-08-12T15:30:50.172Z", "4.0.0-alpha.5": "2018-08-13T09:54:06.977Z", "4.0.0-alpha.6": "2018-08-13T09:57:26.876Z", "4.0.0": "2018-10-26T10:00:26.240Z", "4.0.0-alpha.7": "2019-02-01T10:14:48.838Z", "4.1.0": "2019-02-11T19:42:43.423Z", "4.1.1": "2019-02-20T17:07:24.450Z", "4.1.2": "2019-03-26T17:20:09.150Z", "4.1.3": "2019-04-08T14:24:56.751Z", "4.2.0": "2019-07-10T11:11:14.161Z", "4.2.1": "2019-08-02T20:19:55.942Z", "4.2.2": "2019-08-16T19:47:12.749Z", "4.2.3": "2019-11-01T17:06:47.841Z", "4.2.4": "2019-12-09T16:38:12.966Z", "4.2.5": "2019-12-09T16:50:32.225Z", "4.2.6": "2019-12-10T07:06:11.713Z", "4.3.0": "2019-12-14T16:07:42.553Z", "4.3.1": "2019-12-20T17:02:46.863Z", "4.3.2": "2020-01-04T14:31:33.978Z", "4.3.3": "2020-01-25T14:06:03.024Z", "4.4.0": "2021-06-29T14:23:30.042Z", "5.0.0-0": "2021-07-07T21:00:23.779Z", "5.0.0": "2021-07-26T16:23:57.403Z", "5.1.0": "2021-07-27T14:15:49.181Z", "5.2.0": "2021-07-29T18:46:00.307Z", "6.0.0-0": "2021-08-27T16:37:36.210Z", "6.0.0-1": "2021-09-09T19:01:57.224Z", "6.0.0-2": "2021-09-16T16:11:21.910Z", "6.0.0": "2021-09-19T15:34:01.474Z", "6.1.0": "2021-11-07T18:08:16.610Z", "6.1.1": "2022-01-17T16:48:34.976Z", "6.2.0": "2022-04-02T19:18:36.177Z", "6.3.0": "2022-06-20T17:56:07.777Z", "6.3.1": "2022-08-27T19:47:47.210Z", "6.3.2": "2022-10-16T15:42:28.448Z", "6.4.0": "2022-11-25T03:14:47.199Z", "6.4.1": "2023-01-26T15:03:15.898Z", "6.4.2": "2023-03-11T16:05:16.789Z", "6.5.0": "2023-10-31T14:39:31.640Z", "6.5.1": "2024-03-17T21:52:23.816Z", "6.5.2": "2024-03-18T14:27:16.562Z", "6.5.3": "2024-03-22T13:47:54.463Z"}, "bugs": {"url": "https://github.com/glennreyes/react-countup/issues"}, "author": {"url": "https://twitter.com/glnnrys", "name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://react-countup.now.sh/", "keywords": ["react-component", "react", "react.js", "countup", "countup.js", "counter", "animation"], "repository": {"url": "https://github.com/glennreyes/react-countup.git", "type": "git"}, "description": "A React component wrapper around CountUp.js", "maintainers": [{"name": "glen<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "reakaleek", "email": "<EMAIL>"}], "readme": "# [React CountUp](https://tr8tk.csb.app/)\n\n[![GitHub license](https://img.shields.io/npm/l/react-countup.svg?style=flat-square)](https://github.com/glennreyes/react-countup/blob/master/LICENSE)\n[![Build Status](https://img.shields.io/travis/glennreyes/react-countup.svg?style=flat-square)](https://travis-ci.org/glennreyes/react-countup)\n[![Coverage Status](https://img.shields.io/coveralls/glennreyes/react-countup.svg?style=flat-square)](https://coveralls.io/github/glennreyes/react-countup)\n[![Version](https://img.shields.io/npm/v/react-countup.svg?style=flat-square)](https://www.npmjs.com/package/react-countup)\n[![Downloads](https://img.shields.io/npm/dm/react-countup.svg?style=flat-square)](http://www.npmtrends.com/react-countup)\n[![Gzip size](https://img.badgesize.io/https://unpkg.com/react-countup?style=flat-square&compression=gzip)](https://img.badgesize.io/https://unpkg.com/react-countup)\n\nA configurable React component wrapper around [CountUp.js](https://inorganik.github.io/countUp.js/).\n\nClick [here](https://codesandbox.io/s/github/glennreyes/react-countup/tree/master/demo?fontsize=14&hidenavigation=1&theme=dark&view=preview) to view on CodeSandbox.\n\n### Previous docs\n\n- [v3.x](https://github.com/glennreyes/react-countup/tree/d0002932dac8a274f951e53b1d9b1f4719176147)\n- [v4.x](https://github.com/glennreyes/react-countup/tree/afd39ca66a317271ad3135b0a924b86e2982f207)\n- [v5.x](https://github.com/glennreyes/react-countup/tree/ae4586c9f502fba726ff2d24d215c88d8f4879d7)\n\n![react-countup](https://user-images.githubusercontent.com/5080854/43985960-0a7fb776-9d0c-11e8-8082-975b1e8bf51c.gif)\n\n## Table of Contents\n\n- [Installation](#installation)\n  - [Usage](#usage)\n    - [Simple example](#simple-example)\n    - [Render prop example](#render-prop-example)\n    - [More examples](#more-examples)\n      - [Manually start with render prop](#manually-start-with-render-prop)\n      - [Autostart with render prop](#autostart-with-render-prop)\n      - [Delay start](#delay-start)\n      - [Hook](#hook)\n  - [API](#api)\n    - [Props](#props)\n      - [`className: string`](#classname-string)\n      - [`decimal: string`](#decimal-string)\n      - [`decimals: number`](#decimals-number)\n      - [`delay: ?number`](#delay-number)\n      - [`duration: number`](#duration-number)\n      - [`end: number`](#end-number)\n      - [`prefix: string`](#prefix-string)\n      - [`redraw: boolean`](#redraw-boolean)\n      - [`preserveValue: boolean`](#preservevalue-boolean)\n      - [`separator: string`](#separator-string)\n      - [`start: number`](#start-number)\n      - [`plugin: CountUpPlugin`](#plugin-countupplugin)\n      - [`startOnMount: boolean`](#startonmount-boolean)\n      - [`suffix: string`](#suffix-string)\n      - [`useEasing: boolean`](#useeasing-boolean)\n      - [`useGrouping: boolean`](#usegrouping-boolean)\n      - [`useIndianSeparators: boolean`](#useindianseparators-boolean)\n      - [`easingFn: (t: number, b: number, c: number, d: number) => number`](#easingfn-t-number-b-number-c-number-d-number--number)\n      - [`formattingFn: (value: number) => string`](#formattingfn-value-number--string)\n      - [`enableScrollSpy: boolean`](#enablescrollspy-boolean)\n      - [`scrollSpyDelay: number`](#scrollspydelay-number)\n      - [`scrollSpyOnce: boolean`](#scrollspyonce-boolean)\n      - [`onEnd: ({ pauseResume, reset, start, update }) => void`](#onend--pauseresume-reset-start-update---void)\n      - [`onStart: ({ pauseResume, reset, update }) => void`](#onstart--pauseresume-reset-update---void)\n      - [`onPauseResume: ({ reset, start, update }) => void`](#onpauseresume--reset-start-update---void)\n      - [`onReset: ({ pauseResume, start, update }) => void`](#onreset--pauseresume-start-update---void)\n      - [`onUpdate: ({ pauseResume, reset, start }) => void`](#onupdate--pauseresume-reset-start---void)\n    - [Render props](#render-props)\n      - [`countUpRef: () => void`](#countupref---void)\n      - [`pauseResume: () => void`](#pauseresume---void)\n      - [`reset: () => void`](#reset---void)\n      - [`start: () => void`](#start---void)\n      - [`update: (newEnd: number?) => void`](#update-newend-number--void)\n  - [Protips](#protips)\n    - [Trigger of transition](#trigger-of-transition)\n    - [Run if in focus](#run-if-in-focus)\n    - [Set accessibility properties for occupation period](#set-accessibility-properties-for-occupation-period)\n  - [License](#license)\n\n## Installation\n\n```bash\nyarn add react-countup\n```\n\n## Usage\n\n```js\nimport CountUp from 'react-countup';\n```\n\n### Simple example\n\n```js\n<CountUp end={100} />\n```\n\nThis will start a count up transition from `0` to `100` on render.\n\n### Render prop example\n\n```js\n<CountUp\n  start={-875.039}\n  end={160527.012}\n  duration={2.75}\n  separator=\" \"\n  decimals={4}\n  decimal=\",\"\n  prefix=\"EUR \"\n  suffix=\" left\"\n  onEnd={() => console.log('Ended! 👏')}\n  onStart={() => console.log('Started! 💨')}\n>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\nThe transition won't start on initial render as it needs to be triggered manually here.\n\n> Tip: If you need to start the render prop component immediately, you can set delay={0}.\n\n### More examples\n\n#### Manually start with render prop\n\n```js\n<CountUp start={0} end={100}>\n  {({ countUpRef, start }) => (\n    <div>\n      <span ref={countUpRef} />\n      <button onClick={start}>Start</button>\n    </div>\n  )}\n</CountUp>\n```\n\n#### Autostart with render prop\n\nRender start value but start transition on first render:\n\n```js\n<CountUp start={0} end={100} delay={0}>\n  {({ countUpRef }) => (\n    <div>\n      <span ref={countUpRef} />\n    </div>\n  )}\n</CountUp>\n```\n\nNote that `delay={0}` will automatically start the count up.\n\n#### Delay start\n\n```js\n<CountUp delay={2} end={100} />\n```\n\n### Hook\n\n#### Simple example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst SimpleHook = () => {\n  useCountUp({ ref: 'counter', end: 1234567 });\n  return <span id=\"counter\" />;\n};\n```\n\n#### Complete example\n\n```js\nimport { useCountUp } from 'react-countup';\n\nconst CompleteHook = () => {\n  const countUpRef = React.useRef(null);\n  const { start, pauseResume, reset, update } = useCountUp({\n    ref: countUpRef,\n    start: 0,\n    end: 1234567,\n    delay: 1000,\n    duration: 5,\n    onReset: () => console.log('Resetted!'),\n    onUpdate: () => console.log('Updated!'),\n    onPauseResume: () => console.log('Paused or resumed!'),\n    onStart: ({ pauseResume }) => console.log(pauseResume),\n    onEnd: ({ pauseResume }) => console.log(pauseResume),\n  });\n  return (\n    <div>\n      <div ref={countUpRef} />\n      <button onClick={start}>Start</button>\n      <button onClick={reset}>Reset</button>\n      <button onClick={pauseResume}>Pause/Resume</button>\n      <button onClick={() => update(2000)}>Update to 2000</button>\n    </div>\n  );\n};\n```\n\n## API\n\n### Props\n\n#### `className: string`\n\nCSS class name of the span element.\n\n> Note: This won't be applied when using CountUp with render props.\n\n#### `decimal: string`\n\nSpecifies decimal character.\n\nDefault: `.`\n\n#### `decimals: number`\n\nAmount of decimals to display.\n\nDefault: `0`\n\n#### `delay: number`\n\nDelay in seconds before starting the transition.\n\nDefault: `null`\n\n> Note: `delay={0}` will automatically start the count up.\n\n#### `duration: number`\n\nDuration in seconds.\n\nDefault: `2`\n\n#### `end: number`\n\nTarget value.\n\n#### `prefix: string`\n\nStatic text before the transitioning value.\n\n#### `redraw: boolean`\n\nForces count up transition on every component update.\n\nDefault: `false`\n\n#### `preserveValue: boolean`\n\nSave previously ended number to start every new animation from it.\n\nDefault: `false`\n\n#### `separator: string`\n\nSpecifies character of thousands separator.\n\n#### `start: number`\n\nInitial value.\n\nDefault: `0`\n\n#### `plugin: CountUpPlugin`\n\nDefine plugin for alternate animations\n\n#### `startOnMount: boolean`\n\nUse for start counter on mount for hook usage.\n\nDefault: `true`\n\n#### `suffix: string`\n\nStatic text after the transitioning value.\n\n#### `useEasing: boolean`\n\nEnables easing. Set to `false` for a linear transition.\n\nDefault: `true`\n\n#### `useGrouping: boolean`\n\nEnables grouping with [separator](#separator-string).\n\nDefault: `true`\n\n#### `useIndianSeparators: boolean`\n\nEnables grouping using indian separation, f.e. 1,00,000 vs 100,000\n\nDefault: `false`\n\n#### `easingFn: (t: number, b: number, c: number, d: number) => number`\n\nEasing function. Click [here](http://robertpenner.com/easing) for more details.\n\nDefault: [`easeInExpo`](https://github.com/inorganik/countUp.js/blob/master/countUp.js#L103-L106)\n\n#### `formattingFn: (value: number) => string`\n\nFunction to customize the formatting of the number.\n\nTo prevent component from unnecessary updates this function should be memoized with [useCallback](https://reactjs.org/docs/hooks-reference.html#usecallback)\n\n#### `enableScrollSpy: boolean`\n\nEnables start animation when target is in view.\n\n#### `scrollSpyDelay: number`\n\nDelay (ms) after target comes into view\n\n#### `scrollSpyOnce: boolean`\n\nRun scroll spy only once\n\n#### `onEnd: ({ pauseResume, reset, start, update }) => void`\n\nCallback function on transition end.\n\n#### `onStart: ({ pauseResume, reset, update }) => void`\n\nCallback function on transition start.\n\n#### `onPauseResume: ({ reset, start, update }) => void`\n\nCallback function on pause or resume.\n\n#### `onReset: ({ pauseResume, start, update }) => void`\n\nCallback function on reset.\n\n#### `onUpdate: ({ pauseResume, reset, start }) => void`\n\nCallback function on update.\n\n### Render props\n\n#### `countUpRef: () => void`\n\nRef to hook the countUp instance to\n\n#### `pauseResume: () => void`\n\nPauses or resumes the transition\n\n#### `reset: () => void`\n\nResets to initial value\n\n#### `start: () => void`\n\nStarts or restarts the transition\n\n#### `update: (newEnd: number?) => void`\n\nUpdates transition to the new end value (if given)\n\n## Protips\n\n### Trigger of transition\n\nBy default, the animation is triggered if any of the following props has changed:\n\n- `duration`\n- `end`\n- `start`\n\nIf `redraw` is set to `true` your component will start the transition on every component update.\n\n### Run if in focus\n\nYou need to check if your counter in viewport, [react-visibility-sensor](https://github.com/joshwnj/react-visibility-sensor) can be used for this purpose.\n\n```js\nimport React from 'react';\nimport CountUp from 'react-countup';\nimport VisibilitySensor from 'react-visibility-sensor';\nimport './styles.css';\n\nexport default function App() {\n  return (\n    <div className=\"App\">\n      <div className=\"content\" />\n      <VisibilitySensor partialVisibility offset={{ bottom: 200 }}>\n        {({ isVisible }) => (\n          <div style={{ height: 100 }}>\n            {isVisible ? <CountUp end={1000} /> : null}\n          </div>\n        )}\n      </VisibilitySensor>\n    </div>\n  );\n}\n```\n\n> Note: For latest **react-countup** releases there are new options [`enableScrollSpy`](#enablescrollspy-boolean) and [`scrollSpyDelay`](#scrollspydelay-number) which enable scroll spy, so that as user scrolls to the target element, it begins counting animation automatically once it has scrolled into view.\n\n```js\nimport './styles.css';\nimport CountUp, { useCountUp } from 'react-countup';\n\nexport default function App() {\n  useCountUp({\n    ref: 'counter',\n    end: 1234567,\n    enableScrollSpy: true,\n    scrollSpyDelay: 1000,\n  });\n\n  return (\n    <div className=\"App\">\n      <div className=\"content\" />\n      <CountUp end={100} enableScrollSpy />\n      <br />\n      <span id=\"counter\" />\n    </div>\n  );\n}\n```\n\n### Set accessibility properties for occupation period\n\nYou can use callback properties to control accessibility:\n\n```js\nimport React from 'react';\nimport CountUp, { useCountUp } from 'react-countup';\n\nexport default function App() {\n  useCountUp({ ref: 'counter', end: 10, duration: 2 });\n  const [loading, setLoading] = React.useState(false);\n\n  const onStart = () => {\n    setLoading(true);\n  };\n\n  const onEnd = () => {\n    setLoading(false);\n  };\n\n  const containerProps = {\n    'aria-busy': loading,\n  };\n\n  return (\n    <>\n      <CountUp\n        end={123457}\n        duration=\"3\"\n        onStart={onStart}\n        onEnd={onEnd}\n        containerProps={containerProps}\n      />\n      <div id=\"counter\" aria-busy={loading} />\n    </>\n  );\n}\n```\n\n### Plugin usage\n\n```js\nimport { CountUp } from 'countup.js';\nimport { Odometer } from 'odometer_countup';\n\nexport default function App() {\n  useCountUp({\n    ref: 'counter',\n    end: 1234567,\n    enableScrollSpy: true,\n    scrollSpyDelay: 1000,\n    plugin: Odometer,\n  });\n\n  return (\n    <div className=\"App\">\n      <span id=\"counter\" />\n    </div>\n  );\n}\n```\n\n## License\n\nMIT\n", "readmeFilename": "README.md", "users": {"jireve999": true, "kadir.acikgoz": true}}