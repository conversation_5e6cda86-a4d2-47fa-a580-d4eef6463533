{"name": "uncontrollable", "dist-tags": {"latest": "9.0.0"}, "versions": {"1.0.0": {"name": "uncontrollable", "version": "1.0.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "~1.9.10", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": "^0.12.2"}, "dist": {"shasum": "f2b2a9caafdcf2a46590078db09048fade4195b4", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.0.0.tgz", "integrity": "sha512-kZRee9857LD70xdZyfruXZiBjdhGVlXmBUk5hs8oi8uzr3FOII/ZGJjpxtP92cxrl3Svygg/f73Gzbu0Faxtew==", "signatures": [{"sig": "MEUCIQDcJl+GqgV+0chmy+JfljcZ3IYsSU1OtrLLN1Ds88y8ywIgUVdfD/MmFOI9M+nTZB1YFuvKcJTPoUCnoJbZ0ErddIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "uncontrollable", "version": "1.1.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "~1.9.10", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "5fddf6b97a95d3e64e3a04b145eb09d7c7ff60d3", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.1.0.tgz", "integrity": "sha512-e2mUi5jw/xFA9L3zcwG+7xUX5RFrdjRjD5i2e5/Y9BuT7k/vgfFF4dj8Sgf0jp8/1frVwklYDGOA1R+ryVZjkA==", "signatures": [{"sig": "MEUCIQDRHQoim5vqibfq3Y3Q/jpn/8kFfo0qh7wdF6hE9KWJDQIgUGl8hokx5biyFOd95Mlof06whlkobYt8EdWfLY2EVQg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.1": {"name": "uncontrollable", "version": "1.1.1", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "~1.9.10", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "ff163223a3653cce19d8bb4e183fca750277b2b4", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.1.1.tgz", "integrity": "sha512-6nZGeZg4WDQqEdJjUHS18HrdMPZnn7BxwACl4KS9o54qA8tEJuAKVGRvn78LJKHZ9oTKOehNoYFe5b+3IirO1g==", "signatures": [{"sig": "MEUCIQC9dey/Phtgj1vEeiPdCQZVb3CX9ClQVAoUEwzoPvwO4AIgYgX+ie1uy7WlWDbrotaDxfPlky9OFoCkCwiHGs55DGc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.2": {"name": "uncontrollable", "version": "1.1.2", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "1bef5db6af766a4b3b660f761b4f5f727a4cb29a", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.1.2.tgz", "integrity": "sha512-i4+7uQukVJ4Z7zfRwhgtr3zqpaevPr6P6bxLOjqgxZs+Ku2mifD2dlxw5fPoNCautJwpsq3TgNtEN5eHNnxarQ==", "signatures": [{"sig": "MEYCIQCnTrkYXM/4wzjsSPCPXFGX6pDLC1mi85m7ik/Jj9NCDAIhAJpCvcVwvRHk9PEQFKE2k2QvoGj9kEpz7wiIncXlRxiH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.3": {"name": "uncontrollable", "version": "1.1.3", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "1ef3492cfe1ac6555d80c25d47a5d199232f5374", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.1.3.tgz", "integrity": "sha512-TCovfsircX8oFNfS9G+b0KtO+19jNqR9tCu+f0gU7oPlLFeldsbKnt63lHiGpGrZ573FweErV4re/5akehQKpA==", "signatures": [{"sig": "MEUCIQDzmDzYJw9Q/vHWF3ubaHfQTFwMGpRagYJ9curqvGxJ2AIgSNk0j2mpLIykIc6QGbNPPocce+diDSejqo5yJsLzy3s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.4": {"name": "uncontrollable", "version": "1.1.4", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "3e60ab27ef718c0a8e6e8e6dea8e25ae577e270c", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.1.4.tgz", "integrity": "sha512-/BRyfTeOUfSAdjIOtmOgLi4MsG9YgHhEk7qam6oyVhBgXfwit+pUkc45JXSiVrs7IaiSi5naFPTUmLgpLQDMyg==", "signatures": [{"sig": "MEQCICubLsvcxiUPv1u+JyxBWddVaXiptjMCYRTWUIcfQEjfAiAZhx3jgYZRiZzH9aHFhEqjUd9sHE0RkQYUUIA1l8kjgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "uncontrollable", "version": "1.2.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "f5b5470532f05b13035ecb8aa31c588ca3986ed0", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.2.0.tgz", "integrity": "sha512-L7lWiYBGDrrxJT005HruHnRJG/sLT6fwz7D/kwhj2RVgQoO5GljbuYsFcLdqb6YqnebIczJ6fYgeQ0hReR/y6g==", "signatures": [{"sig": "MEQCIDrBpwK4ns5hucUJnGFzLB9OqrugqaFuJPDX2h9owHVgAiAOdHxc2kiYalLY3bM3TuolB0hLI/EtYWnh+SSOVJXgLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "uncontrollable", "version": "1.3.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "react-layer": "^1.1.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "404d453c238ca53078fb7d9e8560194ef42ffbe8", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.3.0.tgz", "integrity": "sha512-O+bW9rS7ly24zwp5agjGfZ/zC6dP9Son4yyay/ylurjFDkq7CTsb92Ju9nRKoFSfVeoxOygF680LYGOJqGewHQ==", "signatures": [{"sig": "MEUCIQCrnRebq05uukdrZDnOwT3VYOgyM/OdEHlc4Xk868eCmQIgGWhdfV94zPvNckylJzoalhk0izG0STa3yboT2XADlkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.1": {"name": "uncontrollable", "version": "1.3.1", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "react-layer": "^1.1.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "07c9c04a13d6204f47f012114e533bc87dec67df", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.3.1.tgz", "integrity": "sha512-ycz2g7ZFMLKbHcbFU8/tzLmAgmw4BZ0gTaZBPCbjlxYfRXEylzyAho510jhUXLKfRWlPgiEgxi5oV0Kwio4/Hw==", "signatures": [{"sig": "MEQCICz+GZLPpzpbAg/hnUFoxzPFWjKY6SE7codOOJlb+iv6AiB54oqMz7b3KgQumahMCZn4visyyreWVoYvIcz/SPvTwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.2": {"name": "uncontrollable", "version": "1.3.2", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "react-layer": "^1.1.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "2a8ee7acfc99cc839d7937c48f7f42cdd86663e8", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.3.2.tgz", "integrity": "sha512-U62MSfrbFh3kErIizjoCm5TYzOKapxQ+xo3POHywaiCVkb4QXB0N3yE4/wNtsdWcPOnCouomCrztPw4/g7BJfw==", "signatures": [{"sig": "MEUCIHhkP4yG4Y6sXMwv7jLotiNFO3A1bx2uY5Vq/ZRspfBQAiEAmNlZxdS7Aj3oTQnt5mCVgsfKmIsXaC+vVyrAR0sebtg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.4.0": {"name": "uncontrollable", "version": "1.4.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "react-layer": "^1.1.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "8a80126d8d55beba037ee2b87f25afce3d5b38cc", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-1.4.0.tgz", "integrity": "sha512-kZ5IeDR7GOepycm3e7CFjmTX54Rtq3VeRHAq06pSuYAhq9ohQMYhSPz1YyTTWiTh9A6vYOhpJ0MyZumykNdupg==", "signatures": [{"sig": "MEUCIQDTpEF3hc09+9ZGrmsqB+NVZ99Mjjcp8HXGVm678wV2vAIgRi+EPqAUtFmJsgsvIHJfQdSHwcozrh1AhBih2EIQFG0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "uncontrollable", "version": "2.0.0", "devDependencies": {"chai": "^1.10.0", "gulp": "^3.8.10", "babel": "^4.7.2", "karma": "~0.12.23", "mocha": "~1.21.4", "react": "^0.12.2", "rimraf": "^2.2.8", "webpack": "^1.4.15", "gulp-less": "^2.0.1", "phantomjs": "^1.9.16", "css-loader": "^0.9.1", "jq-release": "^0.6.1", "sinon-chai": "^2.6.0", "gulp-rename": "^1.2.0", "karma-mocha": "~0.1.9", "less-loader": "^2.0.0", "react-layer": "^1.1.0", "babel-loader": "^4.0.0", "gulp-plumber": "^0.6.6", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "react-hot-loader": "^1.1.7", "gulp-babel-helpers": "^1.1.2", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "4316bcf9ba0e026d6ccdeb3cb5bd683d0c202a57", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-2.0.0.tgz", "integrity": "sha512-wLSa8wjw5XyiMQumopVf1pfGQYr+l5DSwNUIvGpcorhcWoCLxPW1x8bOc9AsM2bb+nsmTedl5SlsPiAohTbMhA==", "signatures": [{"sig": "MEUCIQD1VBCAHybVRxQ/3ihiGRyJmpYK/vJr5RMaQ+YU2PzbFQIgPTFDKge3vMZ1w8D+u4O9xCcLc1PbB9tcM3PokGkAJ2A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "uncontrollable", "version": "3.0.0", "dist": {"shasum": "48c191b3c5a6977389909f51fc0747a7ffd8a8f9", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.0.0.tgz", "integrity": "sha512-HPOenrb7rXKYt0XomLYdfjuBa5aIM9RHQyXRMR85RKbIW1HJ9OnN+xRXtbqq3ktzO8ebjerKALcldtNqK6DKWA==", "signatures": [{"sig": "MEUCIDDwvmNchGgQYZD7BmWjkknWQLUGHU85gM7nz5NksZwRAiEAz486Tqgnjd/TDN2H+nCYAaJs+iZbekE7RRtDfJ8HXno=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.1": {"name": "uncontrollable", "version": "3.0.1", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "646da7449bb2abfeb9043b6eca1473a11bc86089", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.0.1.tgz", "integrity": "sha512-wsJPJYURWwp6p9hbmL2+vxi2zGdhdA9w6EdjsQUU7dZeOwe4/PI2bzZizHMbc8lVV//e3wiJA8VWbSzfj5Jkug==", "signatures": [{"sig": "MEYCIQDlgLH4jP5f8YqHKM1XeExz0v1Nt//kDt2otw0cWuClmQIhAKiL2ePXOCOGkXAFMpJadeokMn9GH+7YgSzEnotWEmg7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.2": {"name": "uncontrollable", "version": "3.0.2", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0 || 0.14.0-beta3"}, "dist": {"shasum": "d334080269db2fc28f48cd3df8a9771ab887b6e4", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.0.2.tgz", "integrity": "sha512-AgZOyt2/P+b+OkJbdQqAQxyoLVyD8RQEaFbTLaUezrJ8aIIfL2doLUGpIIh7k4Osc8xzQA0yyb3SHcfArZKpfQ==", "signatures": [{"sig": "MEYCIQC4CTA3ExBjlRj3stVnrr5OqyyIkp5JyIT7/EHbmKxQIgIhAKexBKnEmuLAgY8NLz+TZQex68L1wMPjTMeLqpXIvSCm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.0": {"name": "uncontrollable", "version": "3.1.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0 || 0.14.0-beta3"}, "dist": {"shasum": "5fb24f8e5fc23e27e497bf1a1b2933e6fc65df40", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.1.0.tgz", "integrity": "sha512-JUTH6P+jarLEh34aa6cjPWJpdwH9k6xMsEx4I8suOvm2KGdA+DQwUWRM6sXLIO4nAiazBbO8A7L5M7qHRV4QLw==", "signatures": [{"sig": "MEQCICcc/z0StsQ5qwmWSV8ghSya+SfRmoOPffK6TGOzL1cyAiAyjE3+JZZ8+8bXR9pM4YSneM0YOy4D7jWNYZyOpBFE+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.3": {"name": "uncontrollable", "version": "3.0.3", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0 || ^0.14.0-beta3"}, "dist": {"shasum": "126f5f85dd2da9e2c06b6d6b84401a5c908d2131", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.0.3.tgz", "integrity": "sha512-p<PERSON><PERSON><PERSON>r6LLapt64p/cowsoWFKXvaoIaVplx+2hq9hI3pKagGvfUwPyfw/A2g7JbO72HUGboVjyJC0eHNyiqae1Q==", "signatures": [{"sig": "MEUCIDtqpCvPiVduREKrYZXxpNFQr5z9PuwDdL2zaWbVgy0eAiEAu2R/RGI3CROJA+wYDDuL4z0qishi+Wnfgrpdidtmgug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.1": {"name": "uncontrollable", "version": "3.1.1", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0 || ^0.14.0-beta3"}, "dist": {"shasum": "3ab5ba9a77e537fb5431a4d48964758a9bf8aada", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.1.1.tgz", "integrity": "sha512-rayGmc/3L5nlsn8Em/uDQ491oOykCYIPEI+mdljkek6jkTAIpSmeRg/dQVrktBBzfveIn38sYSZltflef0BEAQ==", "signatures": [{"sig": "MEMCIBYe1lv1TN2C8FqvY2dpvCPzsJZysGvHjo0hIEjsBg2gAh9BKaDnqV8BGyfvtfQBWpEctXlrMGcH5CkxSmp3oRtq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.2": {"name": "uncontrollable", "version": "3.1.2", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0 || ^0.14.0-beta3"}, "dist": {"shasum": "1272f3647d85e85fe359a677835b99d9dcab4719", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.1.2.tgz", "integrity": "sha512-i15NKGq0ch0q1jz4uEfxXPDjK8AfwZ8ITQmTUn/mUj06Dj19wda3RIBGEPeqdhJ+3R2w/2N4v3E2aKwMmZrYCQ==", "signatures": [{"sig": "MEUCIQD9si4eRRqTYNTs0Cw4Ne0ovJO6oPXLCI2lyuTvpNXx7QIgXY0UXH23Jy8aprOVBbSedT43zEnCjhZl2Qi1A/hOgHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.3": {"name": "uncontrollable", "version": "3.1.3", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "4fdddfba42ffcd83e6186d9c12f18b9102b2514e", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.1.3.tgz", "integrity": "sha512-6wIIrCP7P4iqOtIWdwTXGJIHMbGrjfokCWsOUS/WghCJSHo9pgJ4LdHNW3MVl6pAagoFF3AVVGamabU9Bn+D5g==", "signatures": [{"sig": "MEUCIQDJgN/qauRXl8SauCetpD9JBu6xSJvRULLMM5IBDqIHpQIgP3eRo7UEFbfky/jnuk9HZmIbx63sIgkM31U5YeS+xE0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.4": {"name": "uncontrollable", "version": "3.1.4", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "7646fae6bfc86ac033e0fe1ada305e4c51098064", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.1.4.tgz", "integrity": "sha512-Ofm3MOjqNZ4Ggkph1WlwoofigZFklPuo61dh5lvubqZpMMlP16hjYgA6lLt3nLGg7tRgbo2RKRWofyA2giKTIA==", "signatures": [{"sig": "MEUCIQDtzrC6kRYLr6jj0bt+BIlTNgYlB5+yjz3MCCJ5dbYxKgIgdjsMg6XQaCS5x+0j4n0vdXffJjOgyvKJoogXW3bsuE4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.0": {"name": "uncontrollable", "version": "3.2.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "ed99a1daadfafc6e042815da6c8a75fbb1f2dc28", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.2.0.tgz", "integrity": "sha512-GD6uE0euglQtkO/8MCKvzJEBVP/22149G52vGDZwyEUnOLDwoVJ7ZjsSCs8UaevbK6cz2DWc4F+qoJrCrzMhtw==", "signatures": [{"sig": "MEYCIQDtf7FE9bVgv6zKl+piMKKQEHh/kBtbA1/nWecHMO058gIhAMrJvKz6IXLVKyW8IQUVnxlXapvrdQuL3iknJtXhy+w1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.1": {"name": "uncontrollable", "version": "3.2.1", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "91548ca99d5c414b6c72bfb485952c6816519562", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.2.1.tgz", "integrity": "sha512-bKgiB/k+/Y0TnrUqzPxGEAFh3dEf2s8skYMhY31X59EQhCE98mBOeQ0whUbNIaJmb8W9ukh1UGY9+L4ZKpFFTg==", "signatures": [{"sig": "MEUCIHGPYdGPhvan2O8MQSBsK7BH46j9TwmhItehIi1aPChVAiEA2tlgtaxkaPcmQdcO6n3f5e2bO54QhKlnRArxMGYnSAY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.3": {"name": "uncontrollable", "version": "3.2.3", "dependencies": {"invariant": "^2.1.0"}, "devDependencies": {"cpy": "^3.4.0", "chai": "^1.10.0", "babel": "^5.8.21", "karma": "~0.12.23", "mocha": "~1.21.4", "react": ">=0.11.0", "eslint": "^0.24.1", "rimraf": "^2.2.8", "webpack": "^1.4.15", "phantomjs": "^1.9.16", "sinon-chai": "^2.6.0", "karma-mocha": "~0.1.9", "babel-eslint": "^4.0.5", "babel-loader": "^5.3.2", "style-loader": "^0.8.3", "karma-webpack": "~1.2.2", "imports-loader": "^0.6.3", "chai-as-promised": "^4.1.1", "webpack-dev-server": "^1.7.0", "karma-mocha-reporter": "^0.3.1", "karma-sourcemap-loader": "^0.3.2", "karma-phantomjs-launcher": "~0.1.4"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "8e210c6f80f63085f7705897a2788e9bffbbe2ce", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.2.3.tgz", "integrity": "sha512-WqW6ktxku78n5fazN2hydONcOr5OWmA90qPak3cO2Re/uhqiaIK4wda7qjPSli6OrQ4zzr0mANkMjGNS7zQdPQ==", "signatures": [{"sig": "MEQCIF/RWjUVOvfUbtbfiyD3UiE71t7XRKKj+eSI22hI/03SAiBdHgh5hZTdKmX5IGfGXSyktZcp4BfdsMquACpKJXC7WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.0": {"name": "uncontrollable", "version": "3.3.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "c4fc680ae73e8fd1e0386b4ca22f4da29a686d98", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.3.0.tgz", "integrity": "sha512-4JKzQHHrg8T5xK3ooCtS1j5vYbr0OBEuKeT4w3t7qN5VfBFle0vG45bf48lkwZWgglUAn6OsMn7RYBiOovhWIg==", "signatures": [{"sig": "MEYCIQDBXmecubhw/m52NPc0Cxiylz+IABtbSrvxUGeCvNsn7gIhAMNIkHTACdLV2i2lMhdwIecDbsVGGDZP/zgCQX21VFCc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.3.1": {"name": "uncontrollable", "version": "3.3.1", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "e23b402e7a4c69b1853fb4b43ce34b6480c65b6f", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.3.1.tgz", "integrity": "sha512-JozeCshb5DgYJkk1HPGqGRfloVv5OVw3g/HjAxDlizSxbZza7LQXWigODDrit3w90wrGl+71Q+ehtuRkh2dkjg==", "signatures": [{"sig": "MEYCIQDbKmg1s6lEfaIUYb92j6+JIlRkzHftDKryDPgjCvXkqgIhAPw8voNeKIe1HPD4mX/l1xb0EbF06DzTUvEB8vqPDuII", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.2.4": {"name": "uncontrollable", "version": "3.2.4", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "9ee00172f98c668c04ca260f73b6b0eacd4f65df", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-3.2.4.tgz", "integrity": "sha512-/6Cg0VAkPcAXlKzFQ4Qq7LUY78Ojqy7NZViRHu/JzlPeBl0lmKBLgHkvzBWJUBVIiR92uvZZZys8obw4qEbr2A==", "signatures": [{"sig": "MEYCIQCYsscxYC6kKnQjpyg0EDoV48egDvHryhXhfucohJMJKwIhAO89vwefjU0xjjQxPmmYGgeUz+sDbPBPlN8HuQ7B3GMS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "uncontrollable", "version": "4.0.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "a919bd01683c23a27c632d2e236a7e8c4da858fe", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-4.0.0.tgz", "integrity": "sha512-cUVOPgRXbFs2ldpenFsOAbEPf6XQpTwJnI9q6FaB9xt9FmCGhZ02PRAjHi98rQKzSyWGAKCGjtFCWp/Z+05mcQ==", "signatures": [{"sig": "MEUCIQCsZXwV7qROfxNTML5LAXRIZQ/Ie45hPgoXIpYv7zo5rAIgU0J62zR7wO+O1IC0lMcJANQKvKxCP0hKP5O5gT8e2as=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.1": {"name": "uncontrollable", "version": "4.0.1", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "e1f69b7eb97d39b6bee502db179ba2b9fee8d8a2", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-4.0.1.tgz", "integrity": "sha512-aK+JWNVmo+FABI4xZY/LK7STR4C48XoxquVChoEMPx2ixQ0WVWcsng3fUx6YhcV2oD49n1HmHA/8+n0Jzk3X1Q==", "signatures": [{"sig": "MEUCIQCk4555hkMrUk5pLEkOyrtwTxFieqsDhEgpMPu5Qt4yzQIgAwnO8Mj5NYRjiKZO26E8zRSYitmVexfLR6Y/j+Kncf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.2": {"name": "uncontrollable", "version": "4.0.2", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "bc184191b26a3bb61c66519e0381a91fa284fe8a", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-4.0.2.tgz", "integrity": "sha512-Wk+BcqZ9GnpDvUj1q/MuPKgz0GHskcFzvqEzIyL/koI1gI+JSFyZ5FUqOiryZOczYZrXdgqG6kk+76xZflO2hA==", "signatures": [{"sig": "MEUCICtGuqAyJ+16URHvoLvGxrDNFGOKvfmyeiF0q8Jx5DOxAiEAsHszMCDzqW52weW1wQ40i7TyjjG9ptxgFpNafAPyyqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.3": {"name": "uncontrollable", "version": "4.0.3", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "06ec76cb9e02914756085d9cea0354fc746b09b4", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-4.0.3.tgz", "integrity": "sha512-lTJoCZbNmJzGn6N4yUJMwElygLNLaQNnnZf08m+Fj/+3pETVtb2TapqDgB1Ubsm8XAdy32D7GcqtnkuHoELafQ==", "signatures": [{"sig": "MEUCIB0terVUhRiNMElvAjk0xsIImHxiPiokTPMM//WwzxwOAiEAgcTgw1fiPqKy3z5d7n9gC3newq/Ao85Dtq3Rttxmc5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.1.0": {"name": "uncontrollable", "version": "4.1.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=0.11.0"}, "dist": {"shasum": "e0358291252e1865222d90939b19f2f49f81c1a9", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-4.1.0.tgz", "integrity": "sha512-YN1vmvC+UkttgPcFaal2UaNVODu6Rf1FU2x1guyiQRHOzSKkfTJLb0dzhJAEfRsAtjog4PF9UyNWUM2crqDyvg==", "signatures": [{"sig": "MEYCIQC66MFVQOZXxiMauDohlFaliNeblHFi3IVocRoLVFCZ7wIhAIMpPSPyWEMnRRXCJNIJDJ5wbxtDq7Vk6eTNrjbbG+Y/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "uncontrollable", "version": "5.0.0", "dependencies": {"invariant": "^2.1.0"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "ab713baa04df3fcb6ac65bc18f1792fee1729119", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-5.0.0.tgz", "fileCount": 4, "integrity": "sha512-U6EhDHqTTrU5Tk+WVNi2EPFetNsWwoH8o0J0MdP0wlD4ilcWqCsd8aIvlPUFLfRke6TLXCRMj7htfS1tqH2y2A==", "signatures": [{"sig": "MEQCIB6ps7RsvU6gnPJPTu58pSIgraZL2O8srWFKY/RZ72mXAiAFnNYHIMDVnWxU37HSNvpw3Lv8+eILj6zc5mf5qFn0kw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16269}}, "5.1.0": {"name": "uncontrollable", "version": "5.1.0", "dependencies": {"invariant": "^2.2.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "7e9a1c50ea24e3c78b625e52d21ff3f758c7bd59", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-5.1.0.tgz", "fileCount": 5, "integrity": "sha512-5FXYaFANKaafg4IVZXUNtGyzsnYEvqlr9wQ3WpZxFpEUxl29A3H6Q4G1Dnnorvq9TGOGATBApWR4YpLAh+F5hw==", "signatures": [{"sig": "MEUCIQDPY8CWMYaGOvd4fU41xOqPP1sTOeYYG9AvydPjL0pXqwIgOhg/QbWcnG0O/tvEyMQsj+wtlfWbRtIMDN9LmAdNer0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16491}}, "6.0.0": {"name": "uncontrollable", "version": "6.0.0", "dependencies": {"invariant": "^2.2.4"}, "devDependencies": {"cpy": "^6.0.0", "jest": "^22.4.3", "react": "^16.3.2", "enzyme": "^3.3.0", "eslint": "^4.19.1", "rimraf": "^2.6.2", "react-dom": "^16.3.2", "@babel/cli": "^7.0.0-beta.46", "babel-core": "^7.0.0-0", "babel-jest": "^22.4.3", "prop-types": "^15.6.1", "@babel/core": "^7.0.0-beta.46", "babel-eslint": "^8.2.3", "release-script": "^1.0.2", "semantic-release": "^15.1.11", "babel-preset-jason": "^4.1.0", "travis-deploy-once": "^5.0.0", "eslint-config-jason": "^4.0.0", "eslint-plugin-react": "^7.7.0", "eslint-plugin-import": "^2.11.0", "@semantic-release/git": "^4.0.3", "@semantic-release/npm": "^3.2.5", "@semantic-release/github": "^4.2.14", "@semantic-release/changelog": "^2.0.2", "semantic-release-alt-publish-dir": "^2.1.1", "@monastic.panic/enzyme-adapter-react-16": "^1.1.1"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "d39a3d5334802a862a0ef4a6e4ba0d19b8ddec4d", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-6.0.0.tgz", "fileCount": 13, "integrity": "sha512-gmy2ESW40LGbijSbW5piBGiPv55IgyDbjQcMr7LkDR5icpw/06UgMqULAGDBAcFn2a9d/SRPgcb3oo8hdEUfIw==", "signatures": [{"sig": "MEUCICW2pRYfOc/C+cAGOynlwuwcMcF+9BFJUXpUTUGpN3zGAiEAyddxhkwb4Yu6aeUoL/yAf6IT0YQNOxfrKTk97RHrMMc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 286268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa6gXDCRA9TVsSAnZWagAAo1MQAJwa/jiW4sNGJyMK8xWT\nUtfhVuzGw8KtolA0qiDL5PXIA5rc/fb9OXSm1mLW54RjkeIaz4znN2JHV3oa\nLItK6OZLnHvY+zcvM28Hrc5a5KQIONIEjPlic2f2QZJsnOO3w9npBLLo86Tr\n01ufvXNEE+7h+9ppPLoiCy40MMYEwooXuFoOeA2zc56OfXGde10mvTgIU12E\nFsTBGtn2RX78kmEKzrIUMn7UtlWHbGRMq/crDQxZ4q9IT3aDSBRWIeeg5+CT\n+dA8LK5YqEpRsoiCiMe1s4oRFYzwdw2ZUpXUg67LgCn3QOwoKo+Rg1NgZQ9u\nzN9DSuThqHvDXgf1ulFha5MvDFP8nLzGvJ/inE6o1UV38UUaYq84MbNTMKrh\nsD65DAeSIv8zR2cEMNJMaO3cUbo7NAJUyO0/EUMV6++MTkTuv8d7bkTsZ66F\nWu01MNXUm2x6WiicvIJfIDhRVIr31sTHuvpEz9MKFCeCtEe9yxUulDbiaIth\n0vgbdGEWP4K25GunW8ctRcVJSDD+2eDlYGD4tVoMWo/k2HQ7x/xBZfN4Ced8\nx3A+wdi7XCft00drBBzFzsi79zsgL9caS8y5DWEUk3dcGwmjf04aZnIPLlcw\nzg2aZhE2XOSHLTJBTEuRnyDAId1ciLYSWPkYg4p+jRyeHrSqHBfCLEg/bcEp\nFFM+\r\n=v4mh\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.1.0": {"name": "uncontrollable", "version": "6.1.0", "dependencies": {"invariant": "^2.2.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "45dcf54b76bf07e0ddf7c1a669caf935d2e101d5", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-6.1.0.tgz", "fileCount": 6, "integrity": "sha512-2TzEm0pLKauMBZfAZXsgQvLpZHEp95891frCZdGDrSG7dWYaIQhedwLAzi0X8pR8KHNqlmuYEb2cEgbQzr050A==", "signatures": [{"sig": "MEUCIQCk6w5kH7doP1SvtvVf9uSSIolo4SCZVTexv3yvf5D32QIgXb22qYk1C2iephs0ksX4II6bnMkOg7n/cYrma3kk8fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22169, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYxdQCRA9TVsSAnZWagAAs4cP/2G4zQl+jmzXLPliBfmk\n0y/CHHRdU38efiPmVwGdAZWkjAp7Ci7J25JC2F0hMXpSRWqh0W6sBM+Phayu\nvshcd4kr8wLp4g2GxabiA7nT/jrMeDsDHyMH/11i82ZAQgvo3ODW+vLBSkkJ\nqTl1UKg5nXiAGRYuP2m2Y0tfwcPMFbI/rCOM7pVIOm90RGKB+9A0Dzx9Sqm9\nT/cKe3dxwJcWoot110TwfAM9VNVXkLRbIBve3jOnpnyNv5PlRus3f7dvOWN7\nmrRaPLsD+oa5uDxjWq5UKF/nyMVwbppfWvPs9k9AdpJZZfHf5UKsn99BgZiN\ncj0tYRSoQqW/8YTg7Uhg128fqr3j7dKJg397tyZGYkKm8j9iprbCN0KaoNee\nbrkLbTFay4h6ZyMBji6+CLhD7AZ4llSzrsYobtLvXDROH8DOO5GmeM71sLKD\nXiuw0tnEoV40KyOoKN46+qTUSd4J+Lmn5A2I6OBSxYq417zD1MAUQQck0Fqs\n5uYLR8Vjeio+Be+hwKyhH/9CfOehb1BWk/Pkk1oNeVqsRoLxywqLmt6r6cX0\n7StqMp1WitRBSsBhXKTiIq56FYD0eHiqf1g6Cr3tyzZY+s89B6wCl/mWMEb6\nAb5z8IBTN1e7Xb+4dXMBflpYJI9Ljv1zdBVyaFqvCIHup8F5oE7h0d3uaxhH\nV4Ay\r\n=x5ZC\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.2.2": {"name": "uncontrollable", "version": "6.2.2", "dependencies": {"invariant": "^2.2.4", "@babel/runtime": "^7.4.5"}, "devDependencies": {"cpy": "^7.3.0", "jest": "^24.8.0", "react": "^16.8.6", "enzyme": "^3.10.0", "eslint": "^5.16.0", "rimraf": "^2.6.3", "react-dom": "^16.8.6", "@babel/cli": "^7.4.4", "babel-core": "^7.0.0-0", "babel-jest": "^24.8.0", "prop-types": "^15.7.2", "@babel/core": "^7.4.5", "babel-eslint": "^10.0.2", "release-script": "^1.0.2", "semantic-release": "^15.13.16", "babel-preset-jason": "^6.0.1", "travis-deploy-once": "^5.0.11", "eslint-config-jason": "^4.1.0", "eslint-plugin-react": "^7.13.0", "eslint-plugin-import": "^2.17.3", "@semantic-release/git": "^7.0.12", "@semantic-release/npm": "^5.1.9", "enzyme-adapter-react-16": "^1.14.0", "@semantic-release/github": "^5.4.0", "@4c/semantic-release-config": "^1.0.6", "@semantic-release/changelog": "^3.0.4", "semantic-release-alt-publish-dir": "^3.0.0"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "5c2a298a8490da1eba1aaa7bac4adf190855eeaa", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-6.2.2.tgz", "fileCount": 18, "integrity": "sha512-Ago6C/WeqqT91EGM7sZdKkIixfr9eNSGC/sVTJ35rQd9TRJOh321kLzLmHTHh9yWpyzZ+lxBmhNj9MbZoTyWGA==", "signatures": [{"sig": "MEQCIDt+uMmlGqPEmNTEr9vXvVGgMZjpUyyUy+iHm5RRRwQOAiAkUYaw9QnXrO6D4zEtGhvBDALt0I1oKAfbhWgfJHia5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 430981, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdB7aHCRA9TVsSAnZWagAApD0P/094Uq6Ghk0vx2lkkLyl\n0kJIXV+64SqclAIE+wlYihqJ+/KwXakfphrfz4s3KbyxwYdyE1HeIMEV3aWs\nLPTFCgDlVevpsLC3+mIqHVPYb+qM5oyWiVuVNZt1euKCX8d4RzxdtjPxJQUL\n+l9QDoqnR5Zh176LytUsrCL2T8ERe9gFbMMYcLVXQbwCa4kOk/apEhIY4q59\nw9l8Ta6xv1VRoYNI1SfRUBb6qpsjqd4OiMCAChjioYIdICpIC5Tk3gV8N/T5\n6jSEHXL/gpiANOuS/1OOkIGISzvqglG5NYtkl513ydm7Z08iZBgllldyiQjb\nfCe4bIU2Gl/7elfZ23YPdTLi7+KTgovecfItn4te2Oyj2ogclD/gcnROhWUg\nodJaYqkJ4ZlOOW/0XT8C3w/dEZ+H4FlLKnz4ljuPMBOQZejaO6NbsNbXZ3Qt\nOKU3C3nmW3I/CtCJ6zL5pBFEr5/7wYpzAz4a3UEoTzTI5pXunx28yVzKGDnW\n2s4ReMzGj3qW/GmQvSquzjjV567jCqlW9LTstUF2oWzbhd2e1nbJGPH32Grp\n/z5ad5NjRaxrAdE7puqzsyTe/xjQgC8Afta+eT8P6pPvzJ4R8rn3+puLKgxV\n8IU595WPnB0QfsbsEUwfowFpElMgp41u/cEwwB6tfcn6VancyXwO19ECpj31\nGAEP\r\n=qdEf\r\n-----END PGP SIGNATURE-----\r\n"}}, "6.2.3": {"name": "uncontrollable", "version": "6.2.3", "dependencies": {"invariant": "^2.2.4", "@babel/runtime": "^7.4.5"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "e7dba0d746e075122ed178f27ad2354d343196c7", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-6.2.3.tgz", "fileCount": 10, "integrity": "sha512-VgOAoBU2ptCL2bfTG2Mra0I8i1u6Aq84AFonD5tmCAYSfs3hWvr2Rlw0q2ntoxXTHjcQOmZOh3FKaN+UZVyREQ==", "signatures": [{"sig": "MEUCIEMLSTw63pUBQr2iLtLB+sjNfL/jaNfDopr9vCN202yOAiEA+f32ahaM6DxPZaVW8xvOYD4HKKGtOEYL//kTejGmGjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdB7fBCRA9TVsSAnZWagAAbXcP/i3MEf7UHdx9LU0YiUmu\n45FBm0eYjD1PwonUpoDNjK11d6/5vGvLXsH16lclw4kNxBN/KEObI3LqCsNb\nbmYLbRNxkRtTFv3smAYEprxrQQFhiFNcrJTXLzPZuvMQzP74w6J08agBbZPL\n3xXvWJgfuKo1Hj7GE0zo5c0d3VBGumuj+YdKVfxWQus3L4ExLWLZlJG79hnp\nlZINdLt7me5TTz3spD4NU7dH02ZlNeZxSTZkKDsLHrwFNuKbaQ1Kgf/kqV9B\n+/R8J/s9Ku/ylivLUjBC1q9wncsFkypN1dMKIT7XJtji7epiw9pfD0fjrmlb\nSAP74bWQwAbdTdk/Z7K542lBwphAV1cuthy4Y0GO/uMv9mMJ5kEvZL+3MJrl\nC1ohLcZ7+gWSVdkst8gsDwqHY2OEyZzi04tFLYzK54hX0kZCSglZo5YgUbuj\nre0BE0pPK5pDz68K4VS1tJGMIXX9AgUFLD9h0Js0It4Dr5WMbliKOMyj+XN5\nk33NswZ2NN2HKefp1ppFxUrvUphFiixnUo1OLDuN41vJjg8LDolr7wo4Y8oQ\nDQJNyTrf2fFiTeCqQltEXWLaEceMiIz1KfDJ52HmBayRVDteucCk5VtlfI3u\nKMI2A2mcOmnvcWRe+4IpUZkA5mqt06vqS7IEW6DDh29PutK3LridqIHd/aO4\n71SQ\r\n=Q0o2\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "uncontrollable", "version": "7.0.0", "dependencies": {"invariant": "^2.2.4", "@babel/runtime": "^7.4.5"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "6aa0b972e3aa6b7ea100e67e20c1ee1c7fbe0803", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.0.0.tgz", "fileCount": 11, "integrity": "sha512-HFhKHDACiAsTYoV3el/LP4PqcLzqyWrNRHE6nMdr0h8f7qbvTPXIN2S4q+tdfc64PHEXaSFBs/fKVB2+UwSYOA==", "signatures": [{"sig": "MEQCIBPEJcnye3108GeEiikkV3o/vz/j6uYirkPRKXKTm4QFAiAk0x6H6AOP4LpQzpBK+zGgYJCU64TahRf1pgauvIxBBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30819, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdCP12CRA9TVsSAnZWagAAEagQAJezbnDr97ZoKUQ8lM84\njcADbLxQNDeJH1gLIRSNQxUII/v6ivfq0Ymm2p1hppv2rETOLydGvGAERUeD\nurfqiHzYPE13YKAw7v2vfVtCYNQ4TvgtlJKhor1Qae1Y8JVJrk0Mta1Cxxoc\npzYzH5tSslT69c7hy4U6HI4Xdx8WVWCXR2vD+cTjsdeAbO0Xr/jU4i2I62pk\nCCd/GrIw6wk557hGlGvrfF90prusvW/g5nNWO81+Tf8WL4vx8PD7YniAz5p0\nRlbtjHqlSlhexKyKeioPVoJr0Lsmgg5k5tlrFvGtCbJle7PIoVgRR4w3ZgYx\nGlwpcyfUxHUeYjsjckNKlGJE0RKE7fWJrskJqzneYgVXnKQXVhBP2zqhqAfW\nG4hXBBgRwGfgn8SO8GQHKvgYy0uzyl+uvPX0XQZdAgY5286lOeTWaPoHZSgJ\nNrkopxhVHHCswAkPMSfBBZpigHtKAYE8AE/OG/kId07aH52htWWA7yiaV+mN\npsht19CVgYDTrHWZbBM271oe6tAmz5Tx+WiLarXcuLd54ZfYTCqBSVP0tF/c\nuJSCCDq0PIwJv80yeSpeS/uFioesk7Hu+wZYdfnyr/Wmr3Lg18os7vrLZsBu\nzLZFvMbmTaSSAiB+WazTjXSZdaKd+d84n6deQuAe8zdWuwJ57OTZ1wqWAZgf\nb5T0\r\n=/YEg\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.1": {"name": "uncontrollable", "version": "7.0.1", "dependencies": {"invariant": "^2.2.4", "@babel/runtime": "^7.4.5"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "9451ecd13ed480c3e8318c2ee5f0f7522b13b33c", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.0.1.tgz", "fileCount": 11, "integrity": "sha512-MGlbii7jczJYfY2GbmZi4j1JmB/6giM0Xc/WcKfxEN5W86KS8NPH/Fq/AD1nKjiFEMq7/MRwTCtzKWCeYgiWMA==", "signatures": [{"sig": "MEUCIAfEofwhWgc0EsWlc47tor7CwpHNAEHWXuTOIB0+sH2IAiEA07ibHXdY7nbfWg76l4/PsP11eDMVSted43QYYWUhFmk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdco/FCRA9TVsSAnZWagAA+gUQAJPi0XMElqBEWhtPykjg\noAuJE9xbuVRQYj4bhtJmjAVHBCt4iHxL9Kd3DsyEdS3rMZtzvQFZuMRPI+BX\nvYz3S+gZ+5PGRbHp1z2smIMRR9Ffi/2EOotvrOWZgBINWDuKWaMYuS5rh5Ja\n6P0w+9hnOUp6Lxz/jNYnu2zROYbDYp8skd2U2KwLaUisOk81Z7Q6dwmJBg55\nysqncbBo/X9CrZtk0qqqBsnV0YECJ0zp3oRmBiaqkftqd9opx/nfj58feVCC\nz/QXLl+0vqqQ0oNeHaKCJFtWc+K/N3oWq6Uw9MOFfOOPLFD9a/0m2ReLHuc7\nA+kBUREz3nER9bL5pxqZ381Qst9JUBNrzpdpvCecp1erPO4/Vt+9lr1JuMfp\n9CmjC5JnFjwfpkvc4W1oTqQpnpi7KNVfEwPSV+7ZucSnn6HwF2f3oEmAYfmH\nDCXjyPnmr7SJS6JFDYx4CiMdF2G2KUC12Hs7mpye8JrE3dIbNPhVE+A/12Vj\n/exQ5GterDQSPkhfDdjiNKP6qdR+rCT3dGWgL7jeVcYWW+LhP64vxLeYjDkJ\nuN09jtv7oGayzn3IJtRNExGdwqDdgWEW3N2VTOJjTf3v0Sku/rmtGfbxOtu4\nstVUyWoaT4/yG0OfrATVIrDgtPzq3mdfOZ03BO7YByfcAEEypOu/clM2h7hi\nfSpM\r\n=9w0E\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.2": {"name": "uncontrollable", "version": "7.0.2", "dependencies": {"invariant": "^2.2.4", "@babel/runtime": "^7.4.5", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "8c8fc4d04c2c13a8cb463991838ca3016945475f", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.0.2.tgz", "fileCount": 11, "integrity": "sha512-7fa8OBQ5+X4VAcp0os6BD74bCeUPQSHmr4Rqy75Me98NnlD5kNShCqqx4xWo4OmlAMiT2/YSMklLFC4FCuoGYg==", "signatures": [{"sig": "MEUCIGjnWbKcc1SYAdhIm98pO7tWAKMisiXcl9Zhb+idrnmRAiEAqfIyVPpt4je91mfF4n02GF/g63M/x73mCFyMSRz1LCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31821, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdlj1MCRA9TVsSAnZWagAA+jwQAKMlxfe36MKZFW1bOtHq\ncqcfBDKkRULauEseF9B4wFgrWg3pXkfDpIzbGM5F1CFOm3C0Tgv+waxk5kJT\n7uX9QMQk+2cawO4m4zBw6QGmWfPxAW4/TE6CtWn+dhHt8lBbGl9Fsdvz9wIz\nS0Q1eJ7b8Jqdep3XLpULGktHs+ySOFEJwLL7UIbMkfe0QmQiLjIpUI23iHvB\noHwRz4AVv7mBNjES0/ujZQXtS82/UywVQ/qZKnkplY011CNccxDk2Ub/V5dj\nzxbILJoThlzUeAbFgEpygpgoRF4kX+owUZhKOJCuY2d+zUj3+DlSG8oQVcyC\nB/p/mrGYe9bh4XUv8YvbTi2znu2ovH0g7K6+fPh95MnLcPD24QnPUpDgvtMf\nokqddUzQV+pY4Pm8Hv3sGHeDjPiQarb6hIGJGB8ACPdDb7Qfhmsc+TtGEdFl\nQdmxFBL9NuyZbUCF9HROYNwhb4Z7OPFoOv0NOlV5ucfVn2e4vSi/acF679NO\nvbaGxVL66Z2/z3dtilbb63xGWBzpiPK+sbDGMCMOooVK2JIAnLoiRj0Ujlua\nQWikbdqk4Hf3qnubzBfaTaAXa2O1zXR93yrGZjt5mEe4+TziohoP4lr+Xfga\nbzlH2aUoffcCKloh0dKxjWhwzKpoxTtKf8kQGI0anAGzwv7Z/XthaIMzw6WT\nYgKJ\r\n=lpyw\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.0": {"name": "uncontrollable", "version": "7.1.0", "dependencies": {"invariant": "^2.2.4", "@types/react": "^16.9.11", "@babel/runtime": "^7.6.3", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "de2a4e8a2edca06024c81228a54503d23bb83e51", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.1.0.tgz", "fileCount": 17, "integrity": "sha512-ie1drnxEKHREeGehl+26HvnCkwhTxxwFGt4FpoBvjmv8qyljIkCHpYVYAwYroTQyXFgEbDc4zoZKtr/EAN9eJw==", "signatures": [{"sig": "MEUCICrpWiGQbV7ZWmBjq/I4A5Tztvqp1ij/oxZD4kvfXRE2AiEAqRg1+N3Sv3jCYdMUM2t0ONJbx5486pKyGgAO3+mFQrg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35714, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduPbvCRA9TVsSAnZWagAAHpEP/RPyUPUIaYoMf6STcw4h\nRHjSlX8CkOSz5JrEwrT+rwkwnC2+Vzt6odSos6kaghudZS00cOeUHPVzbGGD\nYwa7Bgm77iE/BLoVR50MhlJVfdYNnBtH9em1LVIvhTbC7ExVwTUmPWT8SA3o\n306eEEPVbtb7oUfBx6Lyq2ufT3rGDe8CB5foA7ba4JePPg0mduDNPlbtoCAV\nhaznscqFdc/pq6eIQCqYff9m9N7W0S2Z5qF6Y6raWgA6LHJKg+BwDuZP8siR\nMGopHOsuluDLzQiMwXIlqydTAlsQDSvw9UHPF43UEKLRue3xXhNXS2uQbqFs\nBEbkmTOumumC7ZAxxM0Wmnk2y/x0QvATMq3DwtQ5qyzmkX07euHekTLQUSHj\nudkyKp9Gn748+p2Su2J1hFaY1Q4zPJKnD0AZBI6A0kIfHE8asdWcWxTq31Pd\n4cMbXlf38rVHl6mJ0JuAsQD1a9bz7tvV7jyVPaE2mi0YftKvSqUEVdVk8Z18\n3BpkbmPpGhyY0vvt/+6PLbjh+198Fqal1OZ4zkeb1KkeoIRrsKGtJ83yMm5V\nwvAX0sCAuIcnWwk2QawG33LoJINSdy6sR9RZzQZZBhCR/ccgsJh0BWt86htQ\n9bwQuJPiJFKjQupGT0WbfJ5pFC+r1vSJLQIKzOicgE7AymVgn4nca8hQU1zo\ncM9t\r\n=TXXB\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.1.1": {"name": "uncontrollable", "version": "7.1.1", "dependencies": {"invariant": "^2.2.4", "@types/react": "^16.9.11", "@babel/runtime": "^7.6.3", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "f67fed3ef93637126571809746323a9db815d556", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.1.1.tgz", "fileCount": 17, "integrity": "sha512-EcPYhot3uWTS3w00R32R2+vS8Vr53tttrvMj/yA1uYRhf8hbTG2GyugGqWDY0qIskxn0uTTojVd6wPYW9ZEf8Q==", "signatures": [{"sig": "MEUCIQC242G9wHugaRm/S4uK3c8ku5mIsJx1Kh7HTysDoPuW2QIgHhUFEtoaxTBHjAwf/xoesPDuQ3y6iZdJsmlrmfO2Vzw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36067, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduzLXCRA9TVsSAnZWagAAvNIQAJyhW9rW89DjNwkfEeRT\nxikGUmI+K0xSbHu/Vj9rtqh501zJdiJeJJlz3ltdthj7qoJLhlkQdRIeWH+w\nxRoZ3aAmKio3gZnWGdu5KrCPE+APEDnZtaAfUj36GQPkpeTQpM4BZrnf9KP8\nvKCJaVpc+JIJ7jRiZH1KF0X62glNsNjij7ezQsIC0Nc7mpCmURKtcxfJMdUE\njGke5euSt95yRqpRX1DcEUn8ELD0n+b9HuU/jyUReIklF90WYb2lGeiMjQyd\nRLC+GwSUiaKhOeWl/26XOXHyDcWtaS+0/VsOGePvogjsJ6iVFBr/dZoJkZiq\nhkHZOvuj7hnrP6AbrD8VXHCnifgv0QLj8P5dMfS3jttYXJ0wyXvbP1YyW1gC\n80R7T0BF4d8GyCD2m2xYXeI/AL7almrb969Sc2vRRHLS163/X/Ipo3fL+7dc\nMoKAVtNbGw0UfPDEbNU6AC9IEFcBlALX8vhgShUos+nfRss1RtJvMWSrRYIn\nZKKJ5S7FWrCyPjZt195IOrM3mx4/XTDmdyImzpqfKVww9U1UvS2ffKK4cU1o\nSIIxUmV0S/VKQNyLoqADjiNafRFbT4CfPy2ywKYzEOL2JNhkuAKz79lhhYn+\nWpWUCBkbz98tZ2nZ0xKm/v1cLYMqsKZytYoFHQlJ+e77PfA5lf1pxN/SJ9ja\nxvHT\r\n=3LIh\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.0": {"name": "uncontrollable", "version": "7.2.0", "dependencies": {"invariant": "^2.2.4", "@types/react": ">=16.9.11", "@babel/runtime": "^7.6.3", "react-lifecycles-compat": "^3.0.4"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "89459b3cda01489bba43f00d295009440221d787", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.0.tgz", "fileCount": 17, "integrity": "sha512-PX0BS7opRjIDoYRfe89vMa6OziCpBBQhTxLjIeyebwQTiIslUqL2FuckNqIpsYY4qs4dSFT1gyC+TLTnQSQPpw==", "signatures": [{"sig": "MEQCIBX0J/C6LNuNoXvPbC7xVBZPovShy8lcg+5+LzPIneboAiBrS+jUwgxIVHKxvx+U40QMSEturTxIcfDgJce2371Sqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34304, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgDyJiCRA9TVsSAnZWagAA0BAP/0XAqT7AXm8iLtqK1Jqm\nrqg5Qg19beQfL0Mkf5ApKEkKhOoD6MaBnj0I2ps5obiB20EZBwmGxo73Y++2\nIXKaDKxX9AvDl/CXyKvEFxc4x2OxGByGnRfOVYkSghBsI73gc+4GItls1XLJ\nIK/SH36pxCQ70QzMJLM0ew1RmA3+X6EA0ezq32eLyolio3l2lTZedWYuz80s\nNzaQdATO5G5pbFqHJefaBWt/ItA+XcRA0yeTPCjgYRRTJSWVHolvNrIEuPJo\nvyLB8HFJcnJiNpKzPtew7auFh8JFc4cxsLTQSCxxZwXUVKcAN382dwlcD259\nBup5CIOVkvRfwgfDFeWFlifZDfwn3694M5djMXsi1RXNaR5EvNCOqL20mvOS\nOkBJHnmVAYRorNSnNfnWCEAcI3/d0EIn9iFCWvi1HNwWwBZ1/npghzdoDXgQ\nJuKdErw3NiNnGJTMOzmY/dbCA4PrCYPEVK/PNRXNXl0bAEnbci1C7YTuplgr\nS0DKgSFwkrPK1x5ohfa7eE3BCnuA2EdTYbt39BzXWzwcH39aS4ya5WpWcMfd\nPrQ9umPYjo+Yu0wFHMPQokOxZdHBpdqLBurrZeg2O6WAXAkgYNVwipW6kFXG\nQRzBvrzh5SwOvBws6iC5pFBn7TxljpwFo0K4hqgS+S45CvAjsENAeEJABrDA\nlkX+\r\n=Fo4g\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.1": {"name": "uncontrollable", "version": "7.2.1", "dependencies": {"invariant": "^2.2.4", "@types/react": ">=16.9.11", "@babel/runtime": "^7.6.3", "react-lifecycles-compat": "^3.0.4"}, "devDependencies": {"cpy": "^7.3.0", "jest": "^24.9.0", "react": "^16.11.0", "enzyme": "^3.10.0", "eslint": "^6.6.0", "rimraf": "^3.0.0", "@4c/build": "^2.0.1", "react-dom": "^16.11.0", "@babel/cli": "^7.6.4", "babel-core": "^7.0.0-0", "babel-jest": "^24.9.0", "prop-types": "^15.7.2", "typescript": "^3.6.4", "@babel/core": "^7.6.4", "@4c/tsconfig": "^0.3.0", "release-script": "^1.0.2", "babel-preset-jason": "^6.0.1", "eslint-config-jason": "^6.1.0", "eslint-plugin-react": "^7.16.0", "eslint-plugin-import": "^2.18.2", "eslint-config-prettier": "^6.5.0", "enzyme-adapter-react-16": "^1.15.1", "@babel/preset-typescript": "^7.6.0", "@typescript-eslint/parser": "^2.5.0", "@4c/semantic-release-config": "^2.0.3", "@typescript-eslint/eslint-plugin": "^2.5.0", "eslint-config-4catalyzer-typescript": "^1.1.0"}, "peerDependencies": {"react": ">=15.0.0"}, "dist": {"shasum": "1fa70ba0c57a14d5f78905d533cf63916dc75738", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-7.2.1.tgz", "fileCount": 31, "integrity": "sha512-svtcfoTADIB0nT9nltgjujTi7BzVmwjZClOmskKu/E8FW9BXzg9os8OLr4f8Dlnk0rYWJIWr4wv9eKUXiQvQwQ==", "signatures": [{"sig": "MEYCIQDMYxnv5OgqjIObHvl2jw1zOcT5UM2S0HoTWo4eA1UotQIhAPPs9CvNQlUKUdN2KAqus7zj5cdwYnXsbPItnL+LZmds", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 71224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgEI5jCRA9TVsSAnZWagAApjgP/jyd5UwTTfchzbC68pof\nvAtx2UfeOfAjVc1bLAJiA4Lwu8G8Hts2oeObp8DKJlhU7lqI7lMtZsjMKWQ2\n2XjtpvTTl6nkphFl/NpB5juwqcmriztBmXW6hkaJUC9n/kjCV45S/QKpyNqE\nzrwmSXaIbXUSr4pDyu9CDBJwuUsQSNw3saURXphnjWiHHz/bPpct46WxBFqG\nz9Oc2vZgRD5c5SOqJSXQIPP1c7m43vwfmCslzzgZjejhDO3Xo+PQCquka6vs\nUP6FjquCYIDt1OueOfYRC93islLZv+aOyvRitj9mWGi4X77zFIHHuIOoPQI4\ngTwjcwhKcW3gfNl1dRVmSlcqVistD/TTiDATm0voTNaoDP5nXJn++mnQqieD\nBUJdkMBTQe0MAG+LYZyoKLK1JsXPbiIlhnPN0aRdF+7vbb4seh5de2HMGisz\ng+LtvvbIPhWroI0PAqIJQ/Ul8Y2xHAlkoSVBGGZXv21NsMbS1oPYkLROcuLB\niSoMEmCMUUWhr80gjogQOR55ss/1oyEJtFa8aK5cngKv4MEQjyrFjZ2DlvRo\nFjI6rBlnup8J0VI5B1SAyamwrrezamPnJXxWvkbS1jvMvoY905ibiws9T2un\nuSb9VUQON/8iYayhQhoucSrT0nhbVkEjnEtLrlKTdQAOeMGL7dP+FHyHQ9f8\ngd1k\r\n=n+vd\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.0": {"name": "uncontrollable", "version": "8.0.0", "dependencies": {"@types/react": ">=18.0.28"}, "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "@4c/build": "^4.0.0", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "babel-jest": "^29.5.0", "typescript": "^4.9.5", "@4c/rollout": "^4.0.2", "@babel/core": "^7.21.0", "@4c/tsconfig": "^0.4.1", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^14.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.21.0", "babel-preset-env-modules": "^1.0.1"}, "peerDependencies": {"react": ">=17.0.0"}, "dist": {"shasum": "01e1f09e1ed3cad1d4685233c71a6900b3b3c1a9", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.0.tgz", "fileCount": 12, "integrity": "sha512-a954G/0JyXoZdpt0YIzTfoQyWtRS1VvygOBsHttCtZL8jDTKd6vQlUo811y46XnWoXIqQ36QKi3cSEdPuFADkA==", "signatures": [{"sig": "MEYCIQC0NHLHwalGqPvb215X9A3f<PERSON>ly<PERSON>Z7cLse5a8tYJqfrF8QIhAIZyPdTsRCPPg6QBFP9SG4z4nRCOIMmIGR6aTMI4sEcw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCOjnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjEQ/+JG+3SEF3j1YkCmCqBxpD+23inKmIKQpRZfdw6uo7josrxmYs\r\nO4ceWlogb7emlRe4bS3Kwo+DcHaM/Z1Q5ozW4zVwP875QdfPLACDXeWDeuNE\r\nUMpn398ERmMH4tp30jjZkItIimJV1uzAJ/KOe4467Do9IQZd1TWyXu6f47tY\r\nOSFmm/guF0ylIV34xj8hearzeZ3BpZ0qmlquW4vDh6NYFvmPbWbjtoWUs8K/\r\nriE2Nj+L6yVC08A5KvrQ7DD6HGb4/U/3tr7AQ30HD+XCyPRVCqyD3bFAj7K1\r\n4H5cEQ7pkUkL1zewPyjerQvywui0/Zpq+EdUThpOSOfRg0A0rd39gEkJ8k7X\r\nRDvT8HNsNAaiDgw3aK378rr67tWL/FjHswhWDVDs8jT+t/yFWTOXX4OsTotI\r\nWya3o3tZ+uLp1Dw34X/gz7EXR8I0NcuZ/H2h0xoPU13xUe8UvzRv5yzio9oI\r\nuZssBZEYe/e6pnfxnyIb6nBiRToTME5Oi4RZtvzywO5B5xyg50l1QjU/kGY1\r\n+Qu1eRpFl4N+p3nwxhqJ/qIDhTqLMaFKEi3aHO6gmuT8cdyzmZxfv3Rz6/td\r\nEtfKaGg18qHRB6Rq71Y7oMv21iSvZhPrOiMfCHqEVe/xCiukqQtUZcHJI7x4\r\neZx/IrLSrTccNpp8FEFXE0RkWtZ+A6fyM2A=\r\n=bQkE\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.0.1": {"name": "uncontrollable", "version": "8.0.1", "dependencies": {"@types/react": ">=18.0.28"}, "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "@4c/build": "^4.0.0", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "babel-jest": "^29.5.0", "typescript": "^4.9.5", "@4c/rollout": "^4.0.2", "@babel/core": "^7.21.0", "@4c/tsconfig": "^0.4.1", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^14.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.21.0", "babel-preset-env-modules": "^1.0.1"}, "peerDependencies": {"react": ">=16.14.0"}, "dist": {"shasum": "437c0d7ada7a8d366966fe167f43ef4f53051605", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.1.tgz", "fileCount": 12, "integrity": "sha512-OGzmijNEn84T6+TffPCy6Vb4caBOI1mxXtpZXFi63eL/Il1+smSGv+i0AxAYvfKUJFq0SiXQk2KOWEWsvoIuJg==", "signatures": [{"sig": "MEQCIA/Dc8TCRqNMCzENNLalZbcwIPG/z/T2h7jNelvcw4xWAiBUZu+cFcYimxonLdMt3TA6LjsC3vWDeoD5vcdQ9OraRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24225}}, "8.0.2": {"name": "uncontrollable", "version": "8.0.2", "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "@4c/build": "^4.0.0", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "babel-jest": "^29.5.0", "typescript": "^4.9.5", "@4c/rollout": "^4.0.2", "@babel/core": "^7.21.0", "@4c/tsconfig": "^0.4.1", "@types/react": ">=18.0.28", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^14.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.21.0", "babel-preset-env-modules": "^1.0.1"}, "peerDependencies": {"react": ">=16.14.0"}, "dist": {"shasum": "ed11b9b6c785b6a471365bcd9a6789cebc4c7849", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.2.tgz", "fileCount": 12, "integrity": "sha512-/GDx+K1STGtpgTsj5Dj3J51YaKxZDblbCQHTH1zHLuoBEWodj6MjtRVv3TUijj1JYLRLSFsFzN8NV4M3QV4d9w==", "signatures": [{"sig": "MEQCIEnduVmOXQ6dT475HDQBUVCU1lUn2To3p4yW0C3EWMydAiBEy0+Az3URp9HH/D7ZnlNUZhuNpuN58SR2+tjjX6J7hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24464}}, "8.0.3": {"name": "uncontrollable", "version": "8.0.3", "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "@4c/build": "^4.0.0", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "babel-jest": "^29.5.0", "typescript": "^4.9.5", "@4c/rollout": "^4.0.2", "@babel/core": "^7.21.0", "@4c/tsconfig": "^0.4.1", "@types/react": ">=18.0.28", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^14.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.21.0", "babel-preset-env-modules": "^1.0.1"}, "peerDependencies": {"react": ">=16.14.0"}, "dist": {"shasum": "475b79613d1213743ddc1e5d656763347c266762", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.3.tgz", "fileCount": 12, "integrity": "sha512-30DU9xjtubz6mPVTZI1ICyPjqppuLlG/DCLJhvRVvsgF9brwsQzoTOJA7Gp9esU2LYR+ss2NI2lyl16FCqk3mg==", "signatures": [{"sig": "MEUCIQDsOJB3wgMvSLCnOWcHdDI8Gk0mwqmRKQCCIDSyIJqXKwIgJcviQL9SWmCS2Zqrtu1xISI5Yo9iBB0BMMwOjpSbA9g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25643}}, "8.0.4": {"name": "uncontrollable", "version": "8.0.4", "devDependencies": {"jest": "^29.5.0", "react": "^18.2.0", "@4c/build": "^4.0.0", "react-dom": "^18.2.0", "@babel/cli": "^7.21.0", "babel-jest": "^29.5.0", "typescript": "^4.9.5", "@4c/rollout": "^4.0.2", "@babel/core": "^7.21.0", "@4c/tsconfig": "^0.4.1", "@types/react": ">=18.0.28", "@babel/preset-react": "^7.18.6", "@testing-library/react": "^14.0.0", "jest-environment-jsdom": "^29.5.0", "@babel/preset-typescript": "^7.21.0", "babel-preset-env-modules": "^1.0.1"}, "peerDependencies": {"react": ">=16.14.0"}, "dist": {"shasum": "a0a8307f638795162fafd0550f4a1efa0f8c5eb6", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-8.0.4.tgz", "fileCount": 12, "integrity": "sha512-ulRWYWHvscPFc0QQXvyJjY6LIXU56f0h8pQFvhxiKk5V1fcI8gp9Ht9leVAhrVjzqMw0BgjspBINx9r6oyJUvQ==", "signatures": [{"sig": "MEUCIQCeXQCnj1SkQ42DwkcMu5Mw4uqZvwcsKYyaUnNoV+l+egIgJswOEhmZXXKTvqrmYEKAeQ13mMLejyXXNKWJRXgPE4I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25597}}, "9.0.0": {"name": "uncontrollable", "version": "9.0.0", "devDependencies": {"@4c/rollout": "^4.0.2", "@babel/cli": "^7.21.0", "@babel/core": "^7.21.0", "@babel/plugin-transform-modules-commonjs": "^7.26.3", "@babel/preset-typescript": "^7.21.0", "@testing-library/react": "^14.0.0", "@types/react": ">=18.0.28", "jsdom": "^25.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.6.0", "vitest": "^2.1.8"}, "peerDependencies": {"react": ">=16.14.0"}, "dist": {"integrity": "sha512-wPScOFmvRkHdvebf7qNPn9Wog9B1TL8Dqcx0Vecz6HYTKFKMfGKP6MJHTjiKL8kwd8KTW3YfIUke7pmRDtkcRQ==", "shasum": "a20495552b1c23640edd530733ed8c57bf66c103", "tarball": "https://registry.npmjs.org/uncontrollable/-/uncontrollable-9.0.0.tgz", "fileCount": 8, "unpackedSize": 15676, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCLfwbnEiOFHVLJGt7OKPu0RvRsOb1JnNjdz12R5Y0gRQIgY4/W+TTTmaIcEMi5SRflKEW41407svwnkpS3//Nn3c0="}]}}}, "modified": "2025-01-03T16:23:49.578Z"}