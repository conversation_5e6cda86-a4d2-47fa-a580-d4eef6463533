{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-function-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.2.0-beta.1", "0.3.0", "0.3.1-alpha.35", "0.3.1-beta.0", "0.3.1", "0.3.2-authHeadlessImportTest.0", "0.3.2-beta.0", "0.3.2", "0.3.3-authHeadlessImportTest.0", "0.3.3-beta.0", "0.3.3-flutter-preview.26", "0.3.3-flutter-preview.27", "0.3.3", "0.3.4-beta.0", "0.3.4", "0.3.5-beta.0", "0.3.5-ext.0", "0.3.5", "0.3.6-beta.0", "0.3.6-geo.0", "0.3.6", "0.3.7-beta.0", "0.3.7-geo.0", "0.3.7", "0.4.0-beta.0", "0.4.0", "0.4.1-beta.0", "0.4.1-runtime-hooks.0", "0.4.1-siwa-update.0", "0.4.1-siwa-update.1", "0.4.1-siwa-update.2", "0.4.1", "0.4.2-beta.0", "0.4.2-beta.1", "0.4.2-ext.0", "0.4.2", "0.4.3-beta.0", "0.4.3-beta.1", "0.4.3-custom-iam-policies.0", "0.4.3", "0.4.4-auth-dir-v-next.0", "0.4.4-auth-dir-v-next.1", "0.4.4-auth-dir-v-next.2", "0.4.4-auth-dir-v-next.3", "0.4.4-beta.0", "0.4.4-headless-s3-not-for-production.0", "0.4.4", "0.4.5-beta.0", "0.4.5", "0.4.6-amplify-export2.0", "0.4.6-beta.0", "0.4.6-ext1.0", "0.4.6-ext10.0", "0.4.6-ext11.0", "0.4.6-ext12.0", "0.4.6-ext14.0", "0.4.6-ext15.0", "0.4.6-ext16.0", "0.4.6-ext17.0", "0.4.6-ext18.0", "0.4.6-ext19.0", "0.4.6-ext2.0", "0.4.6-ext20.0", "0.4.6-ext21.0", "0.4.6-ext3.0", "0.4.6-ext4.0", "0.4.6-ext5.0", "0.4.6-ext6.0", "0.4.6-ext7.0", "0.4.6-ext8.0", "0.4.6-ext9.0", "0.4.6-graphql-vnext-dev-preview.4", "0.4.6-graphql-vnext-dev-preview.5", "0.4.6-graphql-vnext-dev-preview.7", "0.4.6-graphql-vnext-dev-preview.8", "0.4.6", "0.4.7-graphql-vnext-dev-preview.0", "0.4.7-graphql-vnext-dev-preview.9", "0.4.7-graphql-vnext-dev-preview.10", "0.4.7-graphql-vnext-dev-preview.11", "0.5.0-graphql-vnext-dev-preview.0", "0.5.0-graphql-vnext-dev-preview.1", "0.5.0-graphql-vnext-dev-preview.2", "0.5.1-beta.0", "0.5.1-beta.1", "0.5.1-beta.2", "0.6.1-graphql-vnext-dev-preview.12", "0.6.1", "0.6.2-geo.0", "0.6.2", "0.6.3-beta.0", "0.6.3", "0.6.4-beta.0", "0.6.4", "0.6.5-apiext5.0", "0.6.5-apiext6.0", "0.6.6-beta.0", "0.7.0-GqlExtensibility.0", "0.7.0-apiext1.0", "0.7.0-apiext2.0", "0.7.0-apiext3.0", "0.7.0-apiext4.0", "0.7.0-gql-ext1.0", "0.7.0-gql-ext2.0", "0.7.0", "0.7.1-beta.1", "0.7.1", "0.7.2-beta.2", "0.7.2", "0.7.3-beta.1", "0.7.3", "0.7.4-beta.5", "0.7.4-beta.6", "0.7.4", "0.7.5", "0.7.6-beta.7", "0.7.6-geo.0", "0.7.6", "0.7.7-beta.1", "0.7.7-mapsto.0", "0.7.7-mapsto2.0", "0.7.7", "0.7.8-beta.0", "0.7.8-codegen-ui-q1-release.0", "0.7.8-mapsto3.0", "0.7.8", "0.7.9-beta.0", "0.7.9", "0.7.10-beta.0", "0.7.10-codegen-ui-q1-release.0", "0.7.10", "0.7.11-beta.0", "0.7.11", "0.7.12-beta.1", "0.7.12-npm-pkg-cli.0", "0.7.12", "0.7.13-alpha.11", "0.7.13-beta.1.0", "0.7.13-beta.2.0", "0.7.13-npm-pkg-cli.0", "0.7.13-pkg-npm-install.0", "0.7.13", "0.7.14-alpha.35", "0.7.14-alpha.39", "0.7.14-alpha.41", "0.7.14-beta.1", "0.7.14-beta.2", "0.7.14-beta.3", "0.7.14-beta.4", "0.7.14-beta.6", "0.7.14-ic-changes.1", "0.7.14", "0.7.15-alpha.18", "0.7.15-alpha.26", "0.7.15-alpha.27", "0.7.15", "0.7.16-alpha.0", "0.7.16-alpha.1", "0.7.16-alpha.2", "0.7.16-beta.2", "0.7.16-beta.3", "0.7.16", "0.7.17-alpha.38", "0.7.17-alpha.40", "0.7.17-alpha.5135", "0.7.17", "0.7.18-test-api-package-migration.0", "0.7.18", "0.7.19-sub-username-identity-claim.1", "0.7.19", "0.7.20-sub-username-identity-claim.2", "0.7.20", "0.7.21-alpha.18", "0.7.21-alpha.22", "0.7.21-alpha.26", "0.7.21-alpha.29", "0.7.21-alpha.31", "0.7.21-alpha.32", "0.7.21-alpha.33", "0.7.21", "0.7.22", "0.7.23", "0.7.24-alpha.19", "0.7.24", "0.7.25", "0.7.26-alpha.0", "0.7.26-alpha.3", "0.7.26-alpha.4", "0.7.26-alpha.5", "0.7.26-alpha.23", "0.7.26-alpha.27", "0.7.26", "0.7.27-alpha.0", "0.7.27-alpha.1", "0.7.27-alpha.2", "0.7.27", "0.7.28-alpha.7", "0.7.28-alpha.20", "0.7.28", "0.7.29-alpha.1", "0.7.29-alpha.27", "0.7.29", "0.7.30-alpha.1", "0.7.30-alpha.7", "0.7.30", "0.7.31-alpha.0", "0.7.31-alpha.12", "0.7.31-delta-table-improvements.0", "0.7.31", "0.7.32-4.0.9-function-mapping-patch.0", "0.7.32-rds-support.0", "0.7.32", "0.7.33-alpha.1", "0.7.33-alpha.3", "0.7.33-alpha.13", "0.7.33-alpha.14", "0.7.33-alpha.23", "0.7.33-alpha.27", "0.7.33-alpha.38", "0.7.33-circular-dep-fix.0", "0.7.33-circular-dep-fix.1", "0.7.33-circular-dep-fix.2", "0.7.33-upgrade-graphql15.0", "0.7.33", "0.7.34", "0.7.35-rds-support.0", "0.7.35-rds-support-preview1.0.0", "0.7.35-upgrade-graphql15-2.0", "0.7.35-upgrade-graphql15-2.1", "0.7.35", "0.7.36-rdsv2preview.0", "0.7.36", "0.7.37-alhotpatchfeb.0", "0.7.37-alpha.34", "0.7.37-alpha.35", "0.7.37", "0.7.38-alpha.0", "0.7.38-alpha.74", "0.7.38-alpha.75", "0.8.0-beta.0", "0.8.0-category-split-test.0", "0.8.0-category-split-test.2", "0.8.0-category-split-test.3", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.2.2-sync-fix.0", "1.2.2", "1.2.3", "1.2.4-alpha.1", "1.2.4-alpha.2", "1.2.4-alpha.3", "1.2.4-alpha.6", "1.2.4-alpha.7", "1.2.4-alpha.9", "1.2.4-alpha.11", "1.2.4", "1.2.5-agqlac.0", "1.2.5-agqlac.1", "1.2.5-alpha.2", "1.2.5-alpha.5", "1.2.5-alpha.6", "1.2.5-alpha.9", "1.2.5-alpha.10", "1.2.5-alpha.14", "1.2.5-alpha.17", "1.2.5-cb-test-beta.0", "1.2.5-transformer-without-feature-flags.0", "1.2.5-with-standalone-transformer.0", "1.2.5", "1.2.6-agqlac.0", "1.2.6-agqlac.1", "1.2.6-alpha.0", "1.2.6-alpha.18", "1.2.6-cb-test-beta-3.0", "1.2.6-cb-test-beta-4.0", "1.2.6-cb-test-beta-5.0", "1.2.6-cb-test-prod-1.0", "1.2.6-cb-test-prod-2.0", "1.2.6-rds.0", "1.2.6", "1.2.7-agqlac.0", "1.2.7", "1.2.8", "1.2.9", "1.2.10", "1.2.11-alpha.7", "1.2.11-test-tag-1.0", "1.2.11", "1.2.12-rds.0", "1.3.0-no-internal-synth.0", "2.1.0", "2.1.1-rds-1.0", "2.1.1", "2.1.2", "2.1.3-rds-2.0", "2.1.3", "2.1.4", "2.1.5-rds-3.0", "2.1.5", "2.1.6", "2.1.7-amplify-table-preview.0", "2.1.7-rds-4.0", "2.1.7-rds-5.0", "2.1.7", "2.1.8-construct-publish-test.0", "2.1.8-nov-14-cut.0", "2.1.8-nov-14-cut-1.0", "2.1.8", "2.1.9", "2.1.10", "2.1.11", "2.1.12", "2.1.13-alpha.1", "2.1.13", "2.1.14-ecs-tagging-permissions.0", "2.1.14", "2.1.15", "2.1.16", "2.1.17-secrets-manager.0", "2.1.17", "2.1.18-implicit-fields.0", "2.1.18-rds-5.0", "2.1.18", "2.1.19-cors-rule.0", "2.1.19-fix-publish-tag.0", "2.1.19-gen2-release.0", "2.1.19-gen2-release.1", "2.1.19-iam-auth.0", "2.1.19-iam-auth-with-identityPool-provider-1.0", "2.1.19", "2.1.20-data-schema-generator.0", "2.1.20-gen2-release.0", "2.1.20-gen2-release.1", "2.1.20-gen2-release-0410.0", "2.1.20-sql-gen2.0", "2.1.20-sql-gen2-1.0", "2.1.20-test-binary-size.0", "2.1.20-z-data-schema-generator.0", "2.1.20-zz-0411-gen2.0", "2.1.20", "2.1.21-0411-gen2.0", "2.1.21-gen2-release-0416.0", "2.1.21-gen2-release-0418.0", "2.1.21-gen2-release-0418-2.0", "2.1.21-gen2-release-0423.0", "2.1.21", "2.1.22-acdk-upgrade-2-129.0", "2.1.22-cdk-upgrade-2.129.0.0", "2.1.22", "2.1.23-fix-sub-owner.0", "2.1.23", "2.1.24", "2.1.25", "2.1.26-gen2-migration.0", "2.1.26", "2.1.27-api-stable-tag-2.0", "2.1.27-gen1-migration-0924.0", "2.1.27-gen1-migration-0924-2.0", "2.1.27-gen2-migration-0809.0", "2.1.27-raven.0", "2.1.27-raven.1", "2.1.27-raven.2", "2.1.27-raven.3", "2.1.27-raven.4", "2.1.27-sandbox-hotswap.0", "2.1.27", "2.1.28-gen1-migration-1218.0", "2.1.28-gen1-migration-1218-2.0", "2.1.28-gen1-type-ext.0", "2.1.28", "2.1.29-gen1-migration-0211.0", "2.1.29-gen1-migration-0214.0", "2.1.29", "2.1.30", "2.1.31-gen1-migrations-0304.0", "2.1.31", "2.1.32", "3.0.0", "3.0.1", "3.0.2", "3.1.0-async-lambda.0", "3.1.0", "3.1.1-ai.0", "3.1.1-ai.1", "3.1.1-gen2-migration-0930.0", "3.1.1", "3.1.2", "3.1.3-gen2-migration-1015.0", "3.1.3", "3.1.4-ai-streaming.0", "3.1.4", "3.1.5-ai-next.0", "3.1.5-ai-streaming.0", "3.1.5", "3.1.6-ai-next.0", "3.1.6", "3.1.7-ai-next.0", "3.1.7", "3.1.8", "3.1.9", "3.1.10", "3.1.11", "3.1.12", "3.1.13", "3.1.14-grant-stream-read.0", "3.1.14", "3.1.15"], "vulnerableVersions": ["0.7.38-alpha.0", "0.7.38-alpha.74", "0.7.38-alpha.75", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.2.2-sync-fix.0", "1.2.2", "1.2.3", "1.2.4-alpha.1", "1.2.4-alpha.2", "1.2.4-alpha.3", "1.2.4-alpha.6", "1.2.4-alpha.7", "1.2.4-alpha.9", "1.2.4-alpha.11", "1.2.4", "1.2.5-agqlac.0", "1.2.5-agqlac.1", "1.2.5-alpha.2", "1.2.5-alpha.5", "1.2.5-alpha.6", "1.2.5-alpha.9", "1.2.5-alpha.10", "1.2.5-alpha.14", "1.2.5-alpha.17", "1.2.5-cb-test-beta.0", "1.2.5-transformer-without-feature-flags.0", "1.2.5-with-standalone-transformer.0", "1.2.5", "1.2.6-agqlac.0", "1.2.6-agqlac.1", "1.2.6-alpha.0", "1.2.6-alpha.18", "1.2.6-cb-test-beta-3.0", "1.2.6-cb-test-beta-4.0", "1.2.6-cb-test-beta-5.0", "1.2.6-cb-test-prod-1.0", "1.2.6-cb-test-prod-2.0", "1.2.6-rds.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "0.7.38-alpha.0 - 1.2.6-rds.0", "id": "gy9KWEzsYAd4HRCad/At+PhVAkQ0P5OSoUQ7eo3juoMYWRGV0yfknPnlOiXsUP19FJaElCPteW40GJXv/eTpIw=="}