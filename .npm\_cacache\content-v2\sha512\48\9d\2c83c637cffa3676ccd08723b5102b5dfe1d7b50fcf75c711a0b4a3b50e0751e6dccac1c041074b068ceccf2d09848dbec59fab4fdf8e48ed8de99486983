{"_id": "countup.js", "_rev": "55-59382dab0efafa996abcaf1f13b50df0", "name": "countup.js", "dist-tags": {"latest": "2.8.2"}, "versions": {"0.1.0": {"name": "countup.js", "version": "0.1.0", "author": {"name": "<PERSON><PERSON>"}, "_id": "countup.js@0.1.0", "maintainers": [{"name": "hamedb89", "email": "<EMAIL>"}], "homepage": "https://github.com/hamedb89/countUp.js", "bugs": {"url": "https://github.com/hamedb89/countUp.js/issues"}, "dist": {"shasum": "82b67bcce2c3e52f543448ed8d5ffe28d46d7a93", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-0.1.0.tgz", "integrity": "sha512-SQjFdmy4FLSij/faRxoDsx7bY8c3sWMY1qRbDukymXhrox32kxQhiLwzxmSEVxpIKCZju075tky/tTTVTemjNQ==", "signatures": [{"sig": "MEUCIQDILKhkA0wXc3RmEOV2XErVmjkEedcMJUwHCEw5BKeYNgIgLz+po3rc/4CCLWcXgJn7Pig5XyoziO9ERwKmL/wHopg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./countUp", "_from": ".", "_shasum": "82b67bcce2c3e52f543448ed8d5ffe28d46d7a93", "gitHead": "fde075a0185ed9ae6715ae60d3c6a66ed5a7e7c8", "scripts": {}, "_npmUser": {"name": "hamedb89", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hamedb89/countUp.js"}, "_npmVersion": "2.1.9", "description": "countUp.js (fork) ==========", "directories": {}, "_nodeVersion": "0.10.30", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "0.1.1": {"name": "countup.js", "version": "0.1.1", "author": {"name": "<PERSON><PERSON>"}, "_id": "countup.js@0.1.1", "maintainers": [{"name": "hamedb89", "email": "<EMAIL>"}], "homepage": "https://github.com/hamedb89/countUp.js", "bugs": {"url": "https://github.com/hamedb89/countUp.js/issues"}, "dist": {"shasum": "460e5f44211a48b7a00527c2e64f739217dd7a46", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-0.1.1.tgz", "integrity": "sha512-ntt/46HE7SCTUG1WBknVbUPvo6+PV9w+/SSeKan/lomumb0CA4a8NIUUhUuhctRHXNvi+vrBd4jhnD4lqP6XZQ==", "signatures": [{"sig": "MEYCIQCDKX6tPrL37qEXA0/ubh/LfNgG3EbjyO82oPfAK+zPqAIhAPFg4jgb1ZRdwn2YMMB4i4WkWCwbPtV+jdLhCXIieLT4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./countUp", "_from": ".", "_shasum": "460e5f44211a48b7a00527c2e64f739217dd7a46", "gitHead": "d0953dac03e57bef1790c50eeb6ffdb70ac0181b", "scripts": {}, "_npmUser": {"name": "hamedb89", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/hamedb89/countUp.js"}, "_npmVersion": "2.1.9", "description": "countUp.js (fork) ==========", "directories": {}, "_nodeVersion": "0.10.30", "dependencies": {}, "devDependencies": {}, "optionalDependencies": {}}, "1.7.0": {"name": "countup.js", "version": "1.7.0", "_id": "countup.js@1.7.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "6704b7e207de01ea4c76d7bd95c3234987f77991", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.7.0.tgz", "integrity": "sha512-NtMwlfnJIV9mgywi7VKKvvxUzwC01yawVXmGKp2QFnOPeAa9AnDvHUCECqVtqEkl3AEb0bhaD+1nGOhMFXU6PA==", "signatures": [{"sig": "MEQCIASeH188yikPMQqqFz0n5KI+eGPT4H8PXH3FMCDVskgzAiBczeCrlEA3N+pCvzYB8UG3qdfYpVYd9djhrrSW0X0WTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "6704b7e207de01ea4c76d7bd95c3234987f77991", "gitHead": "e0afc481b195845a6a0b409383c85a20560cda86", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "licenses": "MIT", "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.7.0.tgz_1460069966249_0.7944538751617074", "host": "packages-12-west.internal.npmjs.com"}}, "1.7.1": {"name": "countup.js", "version": "1.7.1", "_id": "countup.js@1.7.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "918954f9192e72ed9ec03bae63af8cd577c4362e", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.7.1.tgz", "integrity": "sha512-SAH8kNuXzwGdrIit7qIM71O9bDTXISpne8XYt5QkMABvczwOEObUeGyVrKHLqnO9B3OB9R60typVDyHZzkErAQ==", "signatures": [{"sig": "MEUCIQCZtSfvJPDoF/9eqrv4i0TC17QPwB+HOS82hXQScpQBIwIgE7Ll2s2bAz42fc2CH6R/ThKR9kwerdetilCxHNh6n3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "918954f9192e72ed9ec03bae63af8cd577c4362e", "gitHead": "f28c20ffd6e76048b6d299a10ce9b3d06ef60554", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "licenses": "MIT", "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.7.1.tgz_1461369803196_0.513547848444432", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.1": {"name": "countup.js", "version": "1.8.1", "_id": "countup.js@1.8.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "f723bef2a8023182980e7e36f0fb0c8f63ce7146", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.8.1.tgz", "integrity": "sha512-agAfJ37+hUakFU0IhfsCHvO91QdNMNYba4eQ0Am0sXD4ZATRHZWMeewysoXCPKUeaz1EK7fueJc4bnUpNmV7iw==", "signatures": [{"sig": "MEUCIHvl5Y+G6vucVG7SYBJ5Zen+5bChuV8Ds6HRwfMcw2kbAiEAuiskF3UN2lJHlifeRWry0FWg9r7mhQ10NntwDKv6dQw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "f723bef2a8023182980e7e36f0fb0c8f63ce7146", "gitHead": "28bb32ed8b1a47817237ff8cc62593f4004f33ed", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "licenses": "MIT", "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "rxjs": "^5.0.1", "zone.js": "^0.7.2", "typescript": "^2.0.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "@angular/core": "^2.2.3", "gulp-wrap-umd": "~0.2.1", "@types/core-js": "^0.9.34", "gulp-typescript": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.8.1.tgz_1482960582488_0.8667538850568235", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.2": {"name": "countup.js", "version": "1.8.2", "license": "MIT", "_id": "countup.js@1.8.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "24504b4d60721e40c3b97d599202bd3c8919cff8", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.8.2.tgz", "integrity": "sha512-WTyv+unn0b0ZUUm0D0rwSX9PaX2H+mASosSb6EJJjhzexb/tm/hDkEmv/TI4nSV+E33EoVuSCuNvFhwS4yjMEw==", "signatures": [{"sig": "MEUCIQDTGdHvVesyyL0cq+0eveME32lEV6sX5BJ1nY3KW+EJmwIgCsAAqMWuUcqJiBYp+Pi31qm+mz+CTxJmlcnwo8h2cT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "24504b4d60721e40c3b97d599202bd3c8919cff8", "gitHead": "d2759f953c534c03d9194e5c3a31f1be1bfca18f", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "rxjs": "^5.0.1", "zone.js": "^0.7.2", "typescript": "^2.0.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "@angular/core": "^2.2.3", "gulp-wrap-umd": "~0.2.1", "@types/core-js": "^0.9.34", "gulp-typescript": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.8.2.tgz_1488470344927_0.9828880878631026", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.3": {"name": "countup.js", "version": "1.8.3", "license": "MIT", "_id": "countup.js@1.8.3", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "d4fa30f468c056f004daeca17dfa6596ac1d32cf", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.8.3.tgz", "integrity": "sha512-6Yh9uxIyyBoqKr1FP6kHYet1HFPyGTAA60rtmnrvdDLqyzX7UPb/8SaRXno/LOQRhoc+1zS4ISNUN/TvCp9FQg==", "signatures": [{"sig": "MEUCIBTzBoEtb2H8ddTvwOW3Nqk+PB/lWL/FfJCgjFXG8Wn5AiEAgF+kBwpZRvjN95s0cvTPnOw61VUC5MI/qQO9VfnEIoI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "d4fa30f468c056f004daeca17dfa6596ac1d32cf", "gitHead": "2f51d3694d8e894141441e58c0dc93758f0b425a", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "rxjs": "^5.0.1", "zone.js": "^0.7.2", "typescript": "^2.0.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "@angular/core": "^2.2.3", "gulp-wrap-umd": "~0.2.1", "@types/core-js": "^0.9.34", "gulp-typescript": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.8.3.tgz_1491320846500_0.19048150070011616", "host": "packages-12-west.internal.npmjs.com"}}, "1.8.4": {"name": "countup.js", "version": "1.8.4", "license": "MIT", "_id": "countup.js@1.8.4", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "9a86707416606a5ab1c7e1dcb9c2803f273ca6ab", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.8.4.tgz", "integrity": "sha512-n/bih/yIxp7vKwTcOvu/dqhUI8di9qB+kVO+R2rfAmxB2tjEYKghBQ4ustU0mtru3c5DPf38i5LLVrQDzfkqhg==", "signatures": [{"sig": "MEYCIQDLuoo+4iXyHbJVoEBKHODHs6y+OO04dp3u5octGx5NrQIhAPUvd+BAEKlPKR1nj9iAFlfdzmP0T4p21NLLaLLzQlb9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "9a86707416606a5ab1c7e1dcb9c2803f273ca6ab", "gitHead": "f79e9c137953884b8f669deeb81d6258a333d95c", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "rxjs": "^5.0.1", "zone.js": "^0.7.2", "typescript": "^2.0.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "@angular/core": "^2.2.3", "gulp-wrap-umd": "~0.2.1", "@types/core-js": "^0.9.34", "gulp-typescript": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.8.4.tgz_1492526666352_0.9507055513095111", "host": "packages-18-east.internal.npmjs.com"}}, "1.8.5": {"name": "countup.js", "version": "1.8.5", "license": "MIT", "_id": "countup.js@1.8.5", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "0941f62c605835c601478b45e8c096b16d7e9881", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.8.5.tgz", "integrity": "sha512-/UO4SWXvYTU2S4fDrr6GiUsKZCa2hglV9ZPc8AQHLUCYeCqN/OHQxFVKoPfRH+cFFU9Y/Z3lBV/fScQxIIsidw==", "signatures": [{"sig": "MEUCIAdnDvi+SYuFAZMMt6pnyGJRovhKoGMndQtW5J5DvMsDAiEAzkv4It+P9MoF/tt0wlfQpzcoEBrLI751xlQjy6GrUDA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "0941f62c605835c601478b45e8c096b16d7e9881", "gitHead": "e42f0b5721b309d1a4a40563148eeca99ec2e2b2", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.5.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "0.10.22", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "rxjs": "^5.0.1", "zone.js": "^0.7.2", "typescript": "^2.0.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "@angular/core": "^2.2.3", "gulp-wrap-umd": "~0.2.1", "@types/core-js": "^0.9.34", "gulp-typescript": "^3.1.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.8.5.tgz_1493162403810_0.06518958369269967", "host": "packages-18-east.internal.npmjs.com"}}, "1.9.0": {"name": "countup.js", "version": "1.9.0", "license": "MIT", "_id": "countup.js@1.9.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "54896d663a799522d8dd33cb122650fc5d9c243e", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.9.0.tgz", "integrity": "sha512-cRCC5s0fSqsl3RasZz6FEc8rTwIbeZ7HF7EzNTOdYMtTT7gzOqVMs/mEZtBCALY2iZ12Wa9/DFrDRPGd/v1yOw==", "signatures": [{"sig": "MEYCIQDWSn61bzS8ovqFEn+ci+TqlLWv4NMAAP211aC0yoskLwIhAIuJWheIUtzJ06w70yV+3LiMUYyFUVVX1E/hYH2He13o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "gitHead": "0f3c867c676d36f05c7a8cc7e6a24d6c7aaa3410", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.9.0.tgz_1503529699889_0.17916076257824898", "host": "s3://npm-registry-packages"}}, "1.9.1": {"name": "countup.js", "version": "1.9.1", "license": "MIT", "_id": "countup.js@1.9.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "0ab10eed91fd06eb751443afc1403982452c26f0", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.9.1.tgz", "integrity": "sha512-yRMuJCVgm/Mr4VK8DyNWFWCCqRHjiHsr+rwgo01m/4Ne5Pl0fezQAJuPHDbFW+BO+BNuFqkjzfw8ydUXlIA//w==", "signatures": [{"sig": "MEYCIQCesTjZng7ePsarutxVQyXMVeqgQap+oRQNs+GuS7ME4gIhALFm8yAFtj8SG8mwoSeTC2ttc7yj8wbN6oxGyfk3tYKo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "gitHead": "6fd5d4ecb99944364ccf663124ebf77c9cfe6fa8", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.9.1.tgz_1503599856615_0.7720447543542832", "host": "s3://npm-registry-packages"}}, "1.9.2": {"name": "countup.js", "version": "1.9.2", "license": "MIT", "_id": "countup.js@1.9.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "a9cd7eb39e96d381ddc19943d3f5b6613f139640", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.9.2.tgz", "integrity": "sha512-DrM2m5hqFSZEatJM9EhEw+lXk2xEfruTjOxpl5PcEv8zxI5UjjRF8BBhJt/d+5R1+pDOs2cbdlg/0Q3BW3XL7g==", "signatures": [{"sig": "MEYCIQCECMY6ZFkFSQViq4tDkrw54bwCiqdY97C/0afc9wKN0wIhAMJxIOtIg2peL7XCeYaWgpWh/KOxveAKo5kzyxf/CsS5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "gitHead": "5d4dfae775688dff3be2de55012dca36392249f1", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "5.3.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "8.4.0", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.9.2.tgz_1505398071326_0.6451266647782177", "host": "s3://npm-registry-packages"}}, "1.9.3": {"name": "countup.js", "version": "1.9.3", "license": "MIT", "_id": "countup.js@1.9.3", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "ce3e50cd7160441e478f07da31895edcc0f1c9dd", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-1.9.3.tgz", "integrity": "sha512-UHf2P/mFKaESqdPq+UdBJm/1y8lYdlcDd0nTZHNC8cxWoJwZr1Eldm1PpWui446vDl5Pd8PtRYkr3q6K4+Qa5A==", "signatures": [{"sig": "MEUCIAVqBgakoy3PyEC7MUo5h2kFxXSPtkKrxNoxfgM9Bu1hAiEAs3imniYnZixjrDmd1dJIRci6OKxadcFrDsIcSh4SOHU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "./dist/countUp.min.js", "_from": ".", "_shasum": "ce3e50cd7160441e478f07da31895edcc0f1c9dd", "gitHead": "497c3cd44a7c29480e22b1d51ff773f20a0d03b4", "scripts": {"build": "gulp", "clean": "gulp clean"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "3.10.10", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "6.10.3", "dependencies": {}, "devDependencies": {"del": "~0.1.3", "gulp": "~3.8.10", "gulp-rename": "~1.2.0", "gulp-uglify": "^1.4.2", "gulp-wrap-umd": "~0.2.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js-1.9.3.tgz_1510075692206_0.9328174111433327", "host": "s3://npm-registry-packages"}}, "2.0.0": {"name": "countup.js", "version": "2.0.0", "license": "MIT", "_id": "countup.js@2.0.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "d1b2bd6e7afc4e20903bb075ddeb76834831a240", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.0.tgz", "fileCount": 22, "integrity": "sha512-xIAAJGSDfsx71kE+fhKzcF6NtEJqqmxONzfuCDsLIF3zciBkOE/x+8hcBZkHG/pkS1GaTFgL5959RoFF+XmSDQ==", "signatures": [{"sig": "MEUCIAHBUzYsKVkuB1XaMRU3zZFO592bhi58cFdaAqb9j/wrAiEA7LTB9dFBuMFUtY7RiPkixdB3CvcZB/WsDnUwH4vE12A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75902, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbwtwCRA9TVsSAnZWagAA750P/33nOBb47ijYt/FPi4H2\n64AwR6IKwaNVLNUfdDQ6xczWPIZ6hJ/ywdB3tGhsESjoyYcuz/fxqwBtlxua\njXeQsFDb4lOihhj9E72fPw/7UQzPwGR4epHwHWmLONy6VKZe1KB+6FuWHtTB\nispgUkpMicA9+JTzVoqtWuKkMznTN24Tmf/26Hql3peeHHzKMDvj1bDhyoVj\nZvaSehB4wc3/nWL395eUr/ts8PP7Of3SUg6vzgL3cTlbauTT1adNM87wbznl\nwKPUWaRtxJm5TxEFvXtGc5s5jSLNzzUPYLuVIsPjR/SPvLjsdTm+Odxeu4ho\nXwYOlzBaBaa3tbshuGNqIgwRDstY9qTZWnqPI0sc+XrFFkpYv0KO1us/dBuO\nebRP5zGZ1QGpRQH6Hfw3swC2gzHyiiXFAxgeLERItDmbx6qE4VVU7KK6nP7W\njipQRe39QM5jcKhOCyCRQna871JRnonAGtu4X+HR5Sl5J0si627KCtZDk4In\n+SUSk1yq93oYFmxjCy04ZugxWZv2I94MXQNXEJlABoD9A8JMvPyakW1hKSHw\nSE8GxeyacZbVijaBR1UNM26Kk5yGenwmlHAk2qKDDkqiXZS3Pjk6qgDyKMEJ\nJlN/SBSSgOJ296DDpNQKGN1Iwm9CvveHm47EUdGvWPDGNJhxy4rfs8XQDwdO\nHPcs\r\n=xFcd\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "gitHead": "7ab45a3b9c5559e1c22dc8b40e5051917564c55e", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp", "clean": "gulp clean", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.1.0", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "typescript": "^3.3.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.0_1550781295460_0.8194284460070602", "host": "s3://npm-registry-packages"}}, "2.0.1": {"name": "countup.js", "version": "2.0.1", "license": "MIT", "_id": "countup.js@2.0.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "9537a655d36590ec275575d5fd0cc21e993b9d0e", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.1.tgz", "fileCount": 22, "integrity": "sha512-w1ERdwIFwP4grbAGf77vDjacw0HqvkXIa+0Qdew1OV+XXJpu40eqUDcBZcCjKlHFvt0/dHBa8c+tCCASi8LKJg==", "signatures": [{"sig": "MEYCIQDfvtyn4kc1Pbaegv9Eu7p2gewiQiKd/KS4LNG2iz2BsAIhALlcu3BPqGNKif3JTjYceqSxe8UKNzxNYf0tteCZqmir", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 74948, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcdV2pCRA9TVsSAnZWagAAOFYP/22Bgm7+us/z2LrGROAV\n6ys3pf4vGIktWBB4AgG0k/GL2KwoEQkuZfjC2bPTdOc4rZm2uXOSG9YujYbf\nJWm3Lm17wlj/RuOcbFWunBVwlEkjLV41nhfbQK3bse2H+GASHniPfdHxeh39\nxHpoSZCxaGEcyS+fv7Ev+4uDuQUT9l8h5qfrWdT7VAzbbUaJiyEsfjOKXjPz\nUwbDNb355MXVwh296RYiJ92auoiqB0u2aOi5QnPtpUsDs5tR6yCIhcJmcCIq\neq6fEeVY3Y3nw0qC1nTHgGiQmXjH+xLYIILdxfxUc4LM17BH19BPNQKuDO6h\ndYZJkcuOvknoREeZjuO7yNkNKbE5+0U6clfAa8DXqG03feGtPNUNJxJufkdL\nF082dJHvMsS+t4uiopy83N+a/Va/jphlu0I/H9gyKKIaI2QFkpE78/w6uaqg\n2zOk0Snz3bfc6SufXxIfObvqIPmGMu7zRBl9v35dsmECY80YcQUpZGQjckDB\nSDJs5LkjjozWBEzMAORbKmzRCNykiASnIHTvEmj3d9yLtY7plmCFGP9rOIdg\nqdeRVewXxg7xxTlyD4Fbn+WXuOBsc2MwSfFT/M3ZgR6AzCUUDkCiLgxw+dEh\njdbIw86QUX+Dha3zpkmiJMRs/BEiM76FLNVojpJaCJGeDdKcd+xJJLOzSBcL\nKUnC\r\n=SSL0\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "gitHead": "df104c75e5938d897a52d3479e01214e4858fff0", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp", "clean": "gulp clean", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.5.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "9.4.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.1.0", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "typescript": "^3.3.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.1_1551195560532_0.48035977561496734", "host": "s3://npm-registry-packages"}}, "2.0.2": {"name": "countup.js", "version": "2.0.2", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "52d5ae6bb7bd84c86e6d073d6a98677b47df704a", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.2.tgz", "fileCount": 21, "integrity": "sha512-O/H1MP7qfmJxOLnJLBoezqatoeJ4/lBIVzG/8ZJInYMSeGOAalcpti8c4eB9Z/hc6kT0clIKRZ7D8ikaGVr5KQ==", "signatures": [{"sig": "MEUCIQDXZTAJN6GVz5kN45cj45Hcr8wJdhwF4sDyRC845NUgOgIgbLyQangL9lMl9XIagLi3dViZm3Nnz/P3zGz0uDJ8d5A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceV9WCRA9TVsSAnZWagAA0BsP+gOGbTlu3JDsHF1VEjZY\nFQD8rMJ1wBp0DvYq7yORGB5uvt7Fcve+HwUSfvFkDyghRrS5ModSADl6SIZ1\nN4iiupvK6c4RAHZTF3KCa0Fk3dJRX5O93yMZ5lSeP0Yh15Wv6vIYoYPyfTRQ\nvlMNpcSNdwIIzTh+sNNqJ3jYopPv4RQjx+Qc/Tqfmq9DO1GAZ/f64fSluQQx\n/ZRyCSf7L3cm9s9hK5VjtasPg2Lmqbmb9l5thdCfQ3j7mpSYlekTfuN97dbe\nCtmjm7GuafCeYktibGTBrgf7suwpHgSdGH2PayQbDyB7bGIH+iy+nfINK5Pf\nfw/lG5tfuHhGLN241Jy4ttMkcVdAdGuXzWUfT1+/qBiqlZMheXgT28NYdZlP\nQKbO9nE+MoIZADK/afHjIS/QnZzoqG1mr4dAFvi59OMsJjKT0HM7Iwpu4Bhq\n0nQ0CO081fjg/T7a0Srtp42r+5CaJ52Ixx9EgB93L79BI+jR9TlqXXuuZmIk\nRBYrkN++PEcKT5EdrknWCrL+kgZ3SUkK13h5YbCrQClniAUmtvB/WR0ldUPl\nq1IsUDcetwrw1++ST9Btx+U1Vgld5LUkHAjj9QAQ11C1dTKV5msEx5RM775D\nSxduJN5+qY/r38bY/4Wa8Hjq8p1gdaIt+XTWFRy1oOXwRBGQSVqciZif+2iE\nOc0i\r\n=S0cH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "gitHead": "df104c75e5938d897a52d3479e01214e4858fff0", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp", "clean": "gulp clean", "build:demo": "browserify demo.js -o ./dist/demo-bundle.js -u './dist/countUp'", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.1.0", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "browserify": "^16.2.3", "typescript": "^3.3.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.2_1551458133676_0.7535256632363796", "host": "s3://npm-registry-packages"}}, "2.0.3": {"name": "countup.js", "version": "2.0.3", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.3", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "8b5fc824c20ad38e7dd0298e5647bf468d104519", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.3.tgz", "fileCount": 21, "integrity": "sha512-EO2l2tTn6Wizms0KDtdH2SMoIgCrn/ueWY6HIsQT8ia2y6lOR4r6diTD3/G5NaIad9AFmwF1cedgZV/8MocFPA==", "signatures": [{"sig": "MEUCIQDvpkUkuiDr6jXnCFlOA3pr/8m5K76lLF8bRXwniWiBSQIgbn+LNg45XeFFd7WeAVnBOyFOPqapi/8VyzNc18oVbOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84368, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfnyJCRA9TVsSAnZWagAA68EP/03O1MPxHO3BrgP4jFkD\ndoP99ip2RF9S8HtO4qXvI4CHcVtXOFBkzI9J3HzwgXQS6/BC4ky06NvPSfom\nmrwVyv54wRrd0SnxqxD5SWIY/26Xx4vhuNSc6i5EKN77/zwYMBhBF4tWDcql\nnrrueD0ity3qiCW6dkIeXO9vprytDFQddTD5ubXydgrLNsrUC9TU8CYKECYW\nziJc8Z50RseCYUqlg6dGwYEFUzRZupFtNBeqLhDHH7zXJkW+zVE8mR7iutX4\n5Qunm0WOTAYZslfvWvA7UimMfw/I/TcSAj0X04w3+24kQ/hZ8oekM5n64hTB\nwMtD+ovT+9pBDVi81Oko1npBSNyB5KI06PphrTYJV5H0MsL4qvVRjZpAD9Kj\nX34pmhCGlanD4oLOzVqwW3BvGBeMD8wupMuTSdg91wjKVfMAHz+MBoaryrro\nJaqMlV6AD6H/LEg4g56acAKBji8fJo43IShMk/PN0cW+y8fIiP3wkf5W2yvg\nAtEx/Pfotv7gYuZmHb+lrTM0bmrPZOfM8SqG4DqsTgnStL/BxlFwAUH0CieE\nEePCuDBiajPZGI6G7sPzlIF1EPsVQGYzQTZZelKTE3aOuxbansAgBgl1EYex\nlduiKWls1ShAD4ZpgNsPS04TNUIKHNIZqqm8ifbc6Gq/zFiHNuD/cmrIcHDa\nz7lJ\r\n=06iA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "gitHead": "7ca8b69c95ee7252d4944fa1d2f6866f41a86ebe", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp", "clean": "gulp clean", "build:demo": "browserify demo.js -o ./dist/demo-bundle.js -u './dist/countUp'", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.1.0", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "browserify": "^16.2.3", "typescript": "^3.3.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.3_1551793288789_0.578831558931618", "host": "s3://npm-registry-packages"}}, "2.0.4": {"name": "countup.js", "version": "2.0.4", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.4", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "8c845452e499b16187c0d1beb1e5d717bda90005", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.4.tgz", "fileCount": 20, "integrity": "sha512-fb691q1Zo0UqVCsBJU1Q38hGkTHEz3GibaugY5mi67ATYdPQymMmeq9XM0HIT4ow0hLSvUNh6xEEwauLxJ85Aw==", "signatures": [{"sig": "MEUCIQCYNB//RbbJzvAUiemMmTmJ4WM/xCWit8ABccLBv0F83AIgVKXpEX+ADrjc5dI6o6eIqFywJ+NdH2quG9gnV0nmppU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 66935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJciq6oCRA9TVsSAnZWagAAvNoP/08iM8hz/rdoqAemGiPc\n9ts7Sy5rfVRKKXFjlBPS7vnEarh0bEH8VHizxtADJ5pdHDieBfkl42GZ/89O\nGWdzkX5rrzkBp2VcmcPbgjyAsNxIFDwNRwkr6nbOaK/p4AYzZeIwQ6fCwqXA\nnrRhLpyCVovS7KK+TKxfK2p8QCzQWJeqY6AgGakqra2X1gHtxN7clvamuF8H\ns/1ZjNTFUMUwSnOSWVURAe+WGyMTT9sNIgNCHDktbw7N5laijqcqSUosICF+\niiVPtkpX1kMvzgb5fUW/vUIlCExLJWMHKTTRVR3jV1YQrIVeWVzqJzSIB/Wb\nuCAIQXXrqNW3viYsmkSDrOuPaZxdUJBsGqZrUDp1mFYDtt7UtgO7TrXyujut\nBXDjzgXh/Bo8p4siRUWB1pa0tuNd93sbyqEQS+w1RAjksdPUDa/Gz/HcEoKA\n0ruYHD5A+jAjdMHPgsMIMNUgIP0WT3Ww3MuIj/x9rf7LQHKtFK1+w36Dacj/\nTwY1BN3Ku+t6oc4tXc1S4w+UodgX8uVuqH7IyNTeazn+bgxdD9lnKlCm9EU+\nxtiDX+kHClULcl0hfYWtvKn+T8KZQsLNa5fMIAkj7PsTVy5tHNdaJSJ1Oq7o\nb/qlBuOhhKLNo+xPj6rOwh6Ccun5bqcCVpnxXYW5DaGBziXV6vsPoyU0PYh4\nIMtW\r\n=6O6N\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "gitHead": "a346c097ff7f346e8fac8a87c297dbb1ee45c537", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp", "clean": "gulp clean", "serve": "http-server ./", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.1.0", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "uglify-es": "^3.3.9", "browserify": "^16.2.3", "typescript": "^3.3.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.11.1"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.4_1552592552203_0.8488925752163112", "host": "s3://npm-registry-packages"}}, "2.0.5": {"name": "countup.js", "version": "2.0.5", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.5", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "7f6b5a329c1bedea9bdb702e628e2bf7bef3c75c", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.5.tgz", "fileCount": 21, "integrity": "sha512-cyEnorbu6PyvbCnPBKoQh1SG7RGzAGFkIWeB+XErjRSelqRQaSKX1OQbPZGddBYiaNja89536NqbnbAySKap/A==", "signatures": [{"sig": "MEUCIFiz6w2ZkB6dPzl1Hiu7LG2U/HtPg6EYt0TBQT0uhQvoAiEAtvG7or34sVSCJmLISqgAa9VbstXP29cx4sLJRGieJGM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 77513, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeqF5fCRA9TVsSAnZWagAAzEAQAIBLQ8HfcON5AwXCjV8V\nES0hA9lKLCKN+CukUO8ELOXS2Dy8Rv1DEAKWdp1vqOeNVXjLmf2HuLhtPnkX\nZ4eDVC9/iOpH/rUYYjRX+g5GhcLmC/kh1Aj3DCxcmb701Qx5FvQCKg6GDyW6\nTEcxjwrn1FgraomrkQzFLkYNjx2K85VpC9u72lr3S6l6qy6hJzL/frOnZ3Xk\nqUcvzmiFVQxvPdLAF2ZiaAvQDfqxQXdgHZx7HqyDry6S7LTdxiC9zz2bgRnw\nZfPhzSwhUsbznuJNVwo68dqWFRng8vjLfHag9tvTUs3znD6E8AzBL5Deex8L\ng8TXCfMFdXZGxiRBkjVozvxq1eild4IqZYNupWg3VV63Jt5oXeRuTkP6Bh58\nUXPRYP75X9qL1Cwmd497g+gsO6xwJpiJui4YabMS7Nag8xLOn0aWWyk9o4F4\nIBk6iQi85Jo7ndUsBNszT6JjNKOQuIGSS7nyAkb+OMTkAeWYwlht5Y1lMO9Y\nlpJF39yKscQ9qMgFq2lEEGsC0sZaIlt786MV9gnXdLAaSpLWxiFgMnKH1xD9\nfhrvYZWlsE40hwcajgRPf2gLV1Dxms9peUNJWynxcmaVHuZFoU7XX7S6FI5y\n2R312kErAm57gx3UyuqGpODtMPaTkD5/x/MDyWQ2qhTThFMqhFHdd80Mg3uX\nRvVx\r\n=vpM8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "gitHead": "0bca2a413d8732411b518be198520e886073493d", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.0", "jest": "^24.9.0", "rollup": "^2.6.1", "tslint": "^5.12.1", "ts-jest": "^24.0.0", "uglify-es": "^3.3.9", "browserify": "^16.2.3", "typescript": "^3.8.3", "@types/jest": "^24.0.6", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.12.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.5_1588092511244_0.47201098587540513", "host": "s3://npm-registry-packages"}}, "2.0.6": {"name": "countup.js", "version": "2.0.6", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.6", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "3696046e787c08b16cee811710deb030576a3aa0", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.6.tgz", "fileCount": 22, "integrity": "sha512-pcOaDNjW8dz3V5hCZTIwQpRDGm6tdxz0BRbcKEgCAr+j6jbJcdDdnumr4DpCSjhih7s1m/cWYZNC5MuFeSflFA==", "signatures": [{"sig": "MEQCIC1DbXOxPVFOeU5GZYM3o2zLS2RN35VFXQbR8dxUGgtLAiA4eGJ4u6Kfg3rlz3tkV/fs5i3IAtac8o7ZL30ro2QOfw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHtuCCRA9TVsSAnZWagAASNYP/j2h+50Ak78TXKVwiYPp\nZrURaoo3nQaPHHgViB3TsyxhwzvSpnbNawxDzlXHX0cH/HRnrYpNdfq/eVgb\nFjRQIyw/al6ZEr4S39H7GiG8OjvJdCTYdaMCMfkEfrJZxv2ObAcV4qBM3A6V\nXrswfE9U2YNkl0ZEOIoTPXh8dwGxlUlvlGpUTaI3JgA2srcrI6S5OfONLUWX\nxjIYdhNFntnUx4U0oNMAfLd0PN572M2DC2viT2qkCchlc08o8bJhQvPwDMYu\nZGInWW/WwnX3ye1s52SyOo9RiW0hehuzItb4j5pGfxvJJs8P68wAqOpV4XbD\nIH7Uq2HJgLRpKqURFGZKVI6jUUEwDYNpjTMG9IvaJHZygaLyjRIWUnAxheLY\nNjLJsGRbwq6M7Z45hb1Bu4Yyl3xy0+q+7mfJk3aI5hP6LzjmQmJwp1Y6HT66\nH+WpMreg9jEW2/K2BmpPmQXwH/bDDoWO6hyfuhray810GnGs7nydq+61p2fk\n7eyqNN02pCF9HeGw0BP5yh6a95F0EDM/pG4b7Qj4y9t6zSKJwsZ/uMUma/1F\ny+3doWDDyJGvBsWR7UIcYB6gScu5Z3yIfxG2pdzcETiWolyjZZYafMxmimG5\n5UN+slDIgfoKWHx5IG8UdlIsoc44Ul4w/1saTLrSTT8Xee7xNYs5YWcT3xp6\ncb8r\r\n=s61O\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "gitHead": "739dfce057650450ce1511e69573066611a45870", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.2.0", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "10.9.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.2", "jest": "^24.9.0", "rollup": "^2.18.1", "tslint": "^5.20.1", "ts-jest": "^24.3.0", "uglify-es": "^3.3.9", "browserify": "^16.5.1", "typescript": "^3.9.5", "@types/jest": "^24.9.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.12.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.6_1595857794486_0.1780335033863394", "host": "s3://npm-registry-packages"}}, "2.0.7": {"name": "countup.js", "version": "2.0.7", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.7", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "56b72a87fc0ee3cadb38356c246ccac88fb0a8cc", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.7.tgz", "fileCount": 22, "integrity": "sha512-FO0nQdvG1iQwHp28wdvkErxnNUSbdkzztqZ6YNHKLHydngD2tdiKEW8dFrqpahF3tj+Ma70h0vyYrCBzxlVWdg==", "signatures": [{"sig": "MEQCIH6/FNPafeBNiauMTZboQQ+z7XvgEGv6nSUfZAWh7aohAiA3lYTZC2mRXFbaDMcIVACT//qf07YzxkgHkQV76yB+dg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 83953, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfRQkRCRA9TVsSAnZWagAAqJwP/33Pwa+7TJW1p2DueuLa\n2XgMZAsQ5/S0WomOZauTcG5N+p4xzKmm1N0uUf/tOIFDKftvhoKBI0e39sJK\nKtkSNs1MLEun1QcL+wEdWzWgmInPYDG9EBiwmWBo+JVironr8FwYwNL2ul8k\nLCc0uZymAadmeu1oaki8H3s4I9TaT62sWEBITLDONflBjR8odO04rfz+Bil1\nEPJVNQr0BNqCNS3zYUfe1QYldW/XjBepE+IOYrm8KAVCl40DVNZllnKtTdNV\nOwYdLMxkLyW2NEwwLMQkUB/E8VCjiSnQbnASJvUwLcNwOPTNVGhNJzx2Pydk\nqIDIjRfDDrlbSs3kK1hG0a0a/1QyxaKwI5ob2R9lFSmMLyZstC5/zvEBYxaE\npGhoNd1KdHih5kxzLbwj1DrqiO1fVfwLBorHSD5UvZm4/qbIfAAOhjHrcspc\nBk8XFW5lZy35ZgKvvt9eN05UzQgYdPuObHS/R+i+r4rlHCDM/ZUPpMc3hl3i\nwx5qNEdpScXyo5OzT1Ba47iWlGQDqvzRoJYbstCuTpJmiqlMuCbZHwxSRJLr\nIZgSy6zvEmnXQZu23lvyjZ47TIPc83+eO5lAjtbIujj1lhureuSlmerQu0BR\nvt9eyPu9rYoNY2UddEMjUJZwTVfsJEaVUAx7Po2KjC8PDnEbW+7NiBtSqhFh\n6x1C\r\n=p9R2\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "gitHead": "c229fbb1b2de25fb99aa130914e8c34343539b9b", "scripts": {"lint": "tslint --project tsconfig.json", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.7", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.7.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.2", "jest": "^24.9.0", "rollup": "^2.18.1", "tslint": "^5.20.1", "ts-jest": "^24.3.0", "uglify-es": "^3.3.9", "browserify": "^16.5.1", "typescript": "^3.9.5", "@types/jest": "^24.9.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.12.3"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.7_1598359825081_0.48541262606643154", "host": "s3://npm-registry-packages"}}, "2.0.8": {"name": "countup.js", "version": "2.0.8", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.0.8", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "eca0c31c9db3f7769cba494d9315cd52dbaaf1b9", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.0.8.tgz", "fileCount": 22, "integrity": "sha512-pW3xwwD+hB+xmtI16xFcuLS0D5hSQqPQWkZOdgpKQyzxCquDNo2VCFPkRw12vmvdpnicXVTcjmYiakG6biwINg==", "signatures": [{"sig": "MEYCIQCIKD7owEiZuabWqRk9irhc+X1fU/hz2rgSLGWr3yovNwIhANquivKZkGLal5tKY8ZGGQnnX0g7eLLo8Xsnp0ylzz07", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82910, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAGe1CRA9TVsSAnZWagAApmkQAJ/DLJ60JMp/KGYPHwYt\nMOQYtSwlBUZS6u2Ti5ytKoJiI7VUta8ZCBk7RL9nh6ZSrz/UyVkfRdT14hKN\nUFcf3we12cxagFKuu20RQ+1kkpFxVChD2IsS9Sp9xyYtKaBZwyT2U+NjOiKn\nzSK1Sm4tlz69fzlCPp5pM2L4PatViBSFohejL98uqT2AzduYUT7BzT4Odnto\nPrjrjwCouZnga57xR3oIBaDOtp2iXdqiUMEY/+3G5Zbs1C6dZyxxWDyK7cbm\nWyERHrL9uV1PRMx12nTXQ5izqbgYj6E4Px7oWaPQIeZ9Iubtz9CCVY2uX2Co\nW61RvlrGa7BPPPDKmvvziliOVzfMx6w1wuK2UQJcT+XxVmJKHYMXdZP1wK7s\nqG9o9PBMoLqnsVGVAAaOAr80yMadJZ94+p1wuUDpcAjTS92SzVpK9Z90JGyD\ngmrDZIx+1xaMoCWJazp3wJgRyYMVcYPc7OZ+GlZwDLi5TQpudYrV2At9J/PJ\neCjmoH9XdC4x00zfC1aIryzslLhVgl19x4Wctfao+u2TXFc7eYIawfGO8Axm\n0yDtqn9wo3+fiw94UyaobF5w0NIshQndE70G20o4PQACQz+a6n+Nj53aJ3Qx\nQg49CMUxReNoHNgsAuzL299a3Tnqe+tFS9mgpex91ymnax4R1m7GIRA6xqpX\nGf1f\r\n=o6CK\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "162bdc9909855070652d735f6623c00078f3f274", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.13", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.17.0", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.2", "jest": "^24.9.0", "eslint": "^7.31.0", "rollup": "^2.54.0", "ts-jest": "^24.3.0", "uglify-es": "^3.3.9", "browserify": "^16.5.2", "typescript": "^3.9.10", "@types/jest": "^24.9.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.12.3", "eslint-plugin-import": "^2.23.4", "@typescript-eslint/parser": "^4.28.5", "@typescript-eslint/eslint-plugin": "^4.28.5", "@typescript-eslint/eslint-plugin-tslint": "^4.28.5"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.0.8_1627416501396_0.7165365548314424", "host": "s3://npm-registry-packages"}}, "2.1.0": {"name": "countup.js", "version": "2.1.0", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.1.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "86c1c9075a2eed138262b0ea1344ebe71b41e579", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.1.0.tgz", "fileCount": 22, "integrity": "sha512-VanMzLEjkt3Hp/ty5BXikM8s4wE3OH4m1AnFro7THR86nYGRvGfGCoV+zrRJcqTbZi7X1egkLSIeUKDz7+4XLA==", "signatures": [{"sig": "MEYCIQCWc/QgMcMGXgsy1KgonZNuTcHlBhwoe9hWnLLFaVc5GwIhAN7uKPuuuS0krtNGpCxJUcjGJqsEuEyQGWiHDWeRQW79", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88405, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiH8sWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofRg/7BJEB8H6HOz7JhokpxeorErqzGu5z68o9tJPOPYS+/G9ddhyF\r\nZhAgqCNDdUlg22/p81kf1OtG8YCa2nJe3pcfwpB0gxfye10PLJnowKJPPKJ/\r\nxY04RNJ3gfE6YhsXC9syhzoFK65LqcREw9vzCVBZeD7+/D1068Guwp0QM9rA\r\nctHAJ8VCaCV8c/T2SaIkSMWX1quiSG55fiBEVw/nUccvx0NQXoCXpR+J7uU8\r\nq32MvHFzWZqeeM9HQpN5jUkjFgz5PP4sVlpdHFABFvAAPVXCvMhFVBzA1HnP\r\nMw2XLFbovUu0f32+gkJJBJeGrFD4ZXM97d7/ECO/aUWiLzyhZYZpG57hI3qr\r\nVTsc7DtZ5F7futTpJ9XzlzuS37oLVnNIbTHUt+6RRIkuJg1U9qbJ2rix+KF8\r\nQ9ccAzzDdIG9BfaAsNlcqPddxg3yEDdYfRQnkcBjgMIFBWyGN55HA0xI3QmG\r\n8Gy3psHUBFa8bgR9p6U8g3AaaG+MRMjmvvBiGPABItvL8QaikVY1cSowJMpk\r\nR3VPhoqE5KLzw2tPWzj6HlWrY2NiSBPpUcmb9bvPJyGl6h1F/Mg4rWi+3GHn\r\nXnoi/bSrcqs0OBCUSRfcfNlCvld2TUyl4j0ZOQYKI5jNm+MkIx4ivn9NRGZN\r\niHINNENZmIaHtEncuHglympoy4FjlrEbfMw=\r\n=DYxU\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "a39283ef69b3a80f5fab7f52efceabf89729f28e", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.18.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^3.0.0", "gulp": "^4.0.2", "jest": "^24.9.0", "eslint": "^7.32.0", "rollup": "^2.60.2", "ts-jest": "^24.3.0", "uglify-es": "^3.3.9", "browserify": "^16.5.2", "typescript": "^3.9.10", "@types/jest": "^24.9.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^0.12.3", "eslint-plugin-import": "^2.25.3", "@typescript-eslint/parser": "^4.33.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/eslint-plugin-tslint": "^4.33.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.1.0_1646250773942_0.16395412227152195", "host": "s3://npm-registry-packages"}}, "2.2.0": {"name": "countup.js", "version": "2.2.0", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.2.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "e20e247abf801190056c5eeed51ceb13cef6ea0c", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.2.0.tgz", "fileCount": 22, "integrity": "sha512-m0TvFNXm9/eFqJm+QiKVI8e0wRUHzlQSewz9dqVjlhl2DFoZtceLbomwzxHz0hJ1+r4zBC7wSpR/TpthG49h6g==", "signatures": [{"sig": "MEUCICqir3+uCdvLnhGPUf8CYsGsnJuPbF56eaNvF0Zk41IPAiEApaW4dagpxCPPDEBuN73MSs2yDo2AtWioNHz0tLrYEDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihW29ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXOQ//dl4EwOmTdT1Wx3AsNzSGiNofkvglz3auHYpX5FZCz1HCsMzV\r\nP+0XgQqLSuG5qwL25p+jKphgggsLl0hLcvLY3ZHOvzhNXhVWGzU+/HQUzth2\r\nlgCkZ9i4VM2GEr/ROjidIpxdmAfyL1iqQGaVSUA8wKKnDAfRtMpI1DRl1HNc\r\nk7tcO3LigySdd9OcfMDpeM/KQ8XbAjbv/FFlL5c2Sf/cWihqWCLGgz39HPY3\r\nszoF/ZU30OMNV9minpkovZ6jQmSAdCDBtMO96f2BrTHhpRk1+YCOT5P1of/V\r\nYh+aez/2lA4MerWBqkGjryMgF2V0wYueE5Zy9DB3TYGsvLt66dDaQplRxw96\r\n2pV/fkGCTh8c935aHBNvWfhqzNwg1B+DJv5pMKpE1g5PnqftX4YMFYniymTp\r\nJC1oXqd7fxO0cbwrJsKKP7gOg2lw3qCTf4EbXSW/XnTEw6v1kYtCcD/F8MJe\r\n8Diw7ne7AXErnuMW564l7BglsPcCpucvM4FhssbkwdBI2PZvCAByOIarAPJG\r\njx0H2uFzD1HRhtr6HIG8Z7NjF08BP0dupLaaonOYH5GMyCLvOeM5nL5e/IZJ\r\nKRRVu+41zOw/ugsemO3K7sa0GzYbjxASc/eosYLiVDzRAIZurm+Lnxyt38kh\r\nH7KgRpN2kYywIsa1v/8uhwoXen4FfqUmdHw=\r\n=5+E8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "3da7e04f8ab8a9552055fee1766218e48caef939", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.18.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.0", "gulp": "^4.0.2", "jest": "^28.1.0", "eslint": "^8.15.0", "rollup": "^2.73.0", "ts-jest": "^28.0.2", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.6.4", "@types/jest": "^27.5.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.0", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.0", "@typescript-eslint/parser": "^5.24.0", "@typescript-eslint/eslint-plugin": "^5.24.0", "@typescript-eslint/eslint-plugin-tslint": "^5.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.2.0_1652911549218_0.8902849061381475", "host": "s3://npm-registry-packages"}}, "2.3.0": {"name": "countup.js", "version": "2.3.0", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.3.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "368df6f0f82cb623139f9a1adcace705e5e4bfee", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.3.0.tgz", "fileCount": 22, "integrity": "sha512-O341AtF3vBcnXOpMWAT3X+wokiRa4KXNSmfEw7fFR9OdD8zTGTzRDD8vg4AQzhpLIaZoKGH5w1O3cTeZERVy6Q==", "signatures": [{"sig": "MEQCIHYTgMAP3Z6Xsd0UYWScl0pQHbeL85R1lxt9T2S8TrRDAiABFVet9ko93kjEeccMjxsRWLUEf1zdSFBvuJvRd90AxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89836, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuc8wACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrr1g/+P4OYpx44YVk/IXJRDM/uJT5Hq35YN7TqJmlsfYXtWD7E6e+/\r\nEtZmzSHva7RUnj3SsL68O+UD8fyNooFLccMmD7X6M7CcQzQWKrzDSJj69jA9\r\n1bI3QXDeUH1Kjg/aIiVM/F6w1P8lFd9yjEM5ecuIHy+liXkRnQYqxsUEFqhd\r\nes7oOm4gFkET6vFB+GijdTjOTax2/xYWXUIvCnJjjUVdNwA6/W3O+5PZWjV1\r\ngebTqvcjfHbsa1wE1BpFek6W0aenU/Vt+Z62KocnEwARqOHdUr6dbmmTAhNH\r\nU7yPYjgQtreG9I5w41sFUc/IAY9MQqarvr5fZ4ND2abFZtn/NfOUHBKPsLVO\r\nfWd9lKonpGHOkZzkFtYLetIbjMVBvzWm4dOrrNLDIBnolepYEq+ISJn9GDkh\r\nttfBlkvaBTTg8VLuavMUCf7m1iSoJ2p3IUYMpUWX9UIBOTh61n3KFgjoTROv\r\n0ZgiphNnu/iXuTDXmecDP1yhJJF6f+aHKT58x/7kRsS9pq/mZh6Fdtw15AKq\r\nkXcCRbjd/1rP9cpyA+SmDsN47EpXaTlrrqIYIEFP2ioODBZi2zgFYyDCyY3l\r\nvOLe+fFQUUaOgb0DoGQuP25og2Ne6ELrAnchQnNX1Kkr0dvaG/9q5EcHSTXH\r\nKQqMHBE3qDxHHeLQrzetBqZ8r7rLREe41HM=\r\n=9Hwq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "c623142ac788cf4fd8444b4f68bccd7e8cdbaa03", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.18.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.0", "gulp": "^4.0.2", "jest": "^28.1.0", "eslint": "^8.15.0", "rollup": "^2.73.0", "ts-jest": "^28.0.2", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.6.4", "@types/jest": "^27.5.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.0", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.0", "@typescript-eslint/parser": "^5.24.0", "@typescript-eslint/eslint-plugin": "^5.24.0", "@typescript-eslint/eslint-plugin-tslint": "^5.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.3.0_1656344368047_0.3776566100456733", "host": "s3://npm-registry-packages"}}, "2.3.1": {"name": "countup.js", "version": "2.3.1", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.3.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "764b386136ac5d532cacb9134f4fc18d538c486a", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.3.1.tgz", "fileCount": 22, "integrity": "sha512-kZ0OIYQo1Ni5VIyZD1BapS1yBaHGCeV/NfgKAunmKsm6EvU84Vs90LQNbeBWzejivJ13FXgq3m4OxOMCnb6aZA==", "signatures": [{"sig": "MEUCIQDJkJDqxlLQ5yBPlQKoGWUI6i8C21m4Nk015p3aW8kx7wIgYR3tAyfVjhVLQ+WRAGvdi4DfpDWUsq6eIM6wM12iZK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89885, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJivKzuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQjA/+JMXDRhMCvf7Pq+fRpGs3Kb3f7k5Hie/1ZRFpl6qPSNMjbTkp\r\nXHcFAsrWIlSJ/0pZ5GeSUamdES/d1f8PcK11gA0Po6Ml76rL5tk7GaRtvYk/\r\nActxurQ7PqzDjohmiJw/YVaa3EpVRLAmcJs2Von3hh2HcyY1U6PRMBW6AzfY\r\nZMlQ7MOd9Tx7d2UdQ6/dU9Wk8YzXsxszmTzWNC/fjE/Guja1OH6rcICYzk7i\r\nnQG1opy06rNq5xEwIG7QDxy4SJXt53/DAb6pPAilJsCtyYeQNYQfNDyhhhmm\r\nJkIDRDfFbs34d+EckGi5ARBoYuxpyL87yiBg3NAyBZFzxvFwZShfuqzS90cK\r\nUFHckE2Os+6liPBb21DbjRC/ev7KVi9pQjnX3gTkKk6P+OYL9WXiizrfTRgj\r\nSKwHBB6eL/X5MC2RFYjqxMUPnZSRnQ8/9wXm/LwltT647amP1zaklC9kSdBp\r\n3I/cI7duALSFPdTus1MUXoASPyyivbuFw470CScJPro8P1jznLRaVvBnmiGt\r\nWrK57LJSzOMFAzqcgOF+f+o7HjChd03d+vERvnebnf3R1/1A7NXDPD9VdcsM\r\nbINDWrOuKFszv87wZCYvCVeq4taVjjMeORVn+nWRMPew8jpVwAMLF6wHpXgZ\r\nE7DOPwTfOJbLjRps7E7G9vDHu0YY/GR+m9E=\r\n=230b\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "5ec1dfb65f88cd838a0ec755643f10118f4615d7", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.15", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.18.2", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.0", "gulp": "^4.0.2", "jest": "^28.1.0", "eslint": "^8.15.0", "rollup": "^2.73.0", "ts-jest": "^28.0.2", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.6.4", "@types/jest": "^27.5.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.0", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.0", "@typescript-eslint/parser": "^5.24.0", "@typescript-eslint/eslint-plugin": "^5.24.0", "@typescript-eslint/eslint-plugin-tslint": "^5.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.3.1_1656532205792_0.2462954788712992", "host": "s3://npm-registry-packages"}}, "2.3.2": {"name": "countup.js", "version": "2.3.2", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.3.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "9a91d95780be1c908d1e6feb548625f353f57988", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.3.2.tgz", "fileCount": 22, "integrity": "sha512-dQ7F/CmKGjaO6cDfhtEXwsKVlXIpJ89dFs8PvkaZH9jBVJ2Z8GU4iwG/qP7MgY8qwr+1skbwR6qecWWQLUzB8Q==", "signatures": [{"sig": "MEQCIGo6V2BIXMY7m7IqDTSw1z+lw2FQBt+J6hl1p0vlGDq+AiBWKITlSSkD6fJOplcyf9v34OuahFX0/VxvqWYRvby5gw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 91238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiyCXuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjlQ/+JYjAqJ/SZHWPXzvojaPVOeWw1Sddwgxtj+S2N0Mjn+Wxw90Z\r\n5wYcnR7l2vYr5hoKMrzOSkhaBDedocs822uenFECW/GLYvJ2K5oeBq44hgeO\r\n6fgye4GW68A02xykaiK0st/p445joBAXjsnMTqwIgjgVgnwI5SZy1x2fdC1h\r\njETLXvW1tNNnzxCOSya3uOmRN43NcXW2vbR80u8yAlMQC8xmpOAmBbbkM/Ka\r\ngWPvE6B3cSt1wWIz+hcFD83ugCwDUgp25aFjSvILlfWA7k4LO9id9+WSV3RJ\r\nq0Vns7DH4JbtGPQd6sdg0GnWuGjOgR67uhJ67hkTWKluF/UekbusPxXzL+hM\r\nq2bnvc3bDTI8wYwVGTsAHZi9afEFTenAvCUVQHAWiih5tBXMY6Tj7G193vIZ\r\ndTmicTMr3XRn85H1p6f0MiK1EQ2IW6ik7nuUVsLZKlii+5hxdVDOstd3YrND\r\nykOTLM7r4y1QQm3P8xkBVyM1nNJ9jLnlFA+PbYuwmn2HjJqv5d8WsMrUR1WF\r\nu9aTYQRG6r5VhJzCFscAZrrTAoYMzSvU5zl93hK8tAgOfIkuXozu+yXlFG1r\r\n4hbGvkyb6E2jNsvZl8aArBVz7YMqTRF8uAkuB5D1D8Om/iz7KnCmWZWCSCpM\r\ndeRoh7aJ+yAF/7VtsC5DaHfGX8CshgD7Aok=\r\n=ZekN\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "1e87b682ef67ac62c86c34546ce84e91ab1e6af9", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.19.3", "dependencies": {}, "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.0", "gulp": "^4.0.2", "jest": "^28.1.0", "eslint": "^8.15.0", "rollup": "^2.73.0", "ts-jest": "^28.0.2", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.6.4", "@types/jest": "^27.5.1", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.0", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.0", "@typescript-eslint/parser": "^5.24.0", "@typescript-eslint/eslint-plugin": "^5.24.0", "@typescript-eslint/eslint-plugin-tslint": "^5.24.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.3.2_1657284078488_0.7496144949463104", "host": "s3://npm-registry-packages"}}, "2.4.1": {"name": "countup.js", "version": "2.4.1", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.4.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "50e8e8de62824ff0263a50436d414e312069e8bb", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.4.1.tgz", "fileCount": 22, "integrity": "sha512-NicJ8CZkPwOAAyivQl4oSeb0qoxtekg6JmlYtLQKQt+hbRD5Wz4zVrewWl/ixNOkFPISn4c9Z/GzfUaIVERJQg==", "signatures": [{"sig": "MEUCIAVDR6ZHOUTHb9SMSX9rTenreoqtgrqMoAsrL7ydrHBmAiEA/iSzabvAsY6LzicCwCAAW+3zk/atI9zdrgLFp4tjscM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 92761, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjz9gcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqv3g//XHo4BF0y4qxtZKsXJqnjM/4wpsNDmkz98kmfF2zcFBALE1e5\r\nMGNn2Di2JVroDwyjNEtFv1sCN4NqCGoKG+swA5KEccSM2lu5PvpoEr3Ld5/B\r\nq6oARHjsyAzD525++lFfxksiPEWG4sMSZTq9by4dSWM0qXkaX9oGYfCgIWdQ\r\n/M+fC+UwSt4mnAKwLEzuYJANOOpbMH2+TJzJ+rPAdete85Eth13f72h96nyp\r\nko1ftM0xPBfGe0WVIqJl0FM1DgQnD3JUfg4fY49BjW8NEIWhi0501s858mWO\r\nWmkHTg73Sj86x4Uq041bL+o9HUCcnIVzbpJixqiujul8j0MfRH40GS7IzFR+\r\nocNuxdBWZgCse7r92PmefhupHr8MlnIhd8dSTSBXMJBP+AkiGdzrWOxhmUKr\r\nCO9q/uGyS9e25tKvuZ0Enh9EYA1UomBQ66z2BAy99Tt/8iS1sW+Bv/t1lMCS\r\nlfNycmWnuBwDnJmcbwuWuuKGh4YrF7pgzD6YxndvgKNfmlhh9tBXzKAjal4C\r\n/m6MqmBHubLzscNa585Jvn67gCYQ1aKoZpvfJX2z/f2owrewA6XcisRInecK\r\nGzWS3La1Tw1EvEMvZo/ceRK4WItWScIGAb+aFnkrUtemUHuzk9XgngaUjvgj\r\nGt5NNfCltkMca0SezhOpFyq9A+fOdzmLl2M=\r\n=6kXF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "27e537e1d86b31737e0d05e399c43553b180e76a", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.1", "gulp": "^4.0.2", "jest": "^28.1.3", "eslint": "^8.26.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.4.1_1674565659968_0.13101222916568167", "host": "s3://npm-registry-packages"}}, "2.4.2": {"name": "countup.js", "version": "2.4.2", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.4.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "723538b4b2b01ebe2ed0e6759d41b8f97dc00c34", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.4.2.tgz", "fileCount": 22, "integrity": "sha512-EExCcu5rd7ffBj65B3CVNuS1HddN1Y4WuTfJEuocJXwZlNnlXZQ4sD9M/Cq32ZS0zR38F9vGMCw/iFcVImrNbw==", "signatures": [{"sig": "MEUCIQD8FKN8d3YiPrcstrUGob1jG2cavLaMdlhfb283xQGQugIgYTWkD6gDAB3fCz9i6/L1neBacH/tQmr71p8jS73bKRY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 93247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj1WF7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpsUw/9FZRMoGiyTey4eCpVw8VaEpD/0M5k5Ip/fMFCCz87N8mffAmI\r\nw7txHGoF5Wy/RmTf7DSxrCbuv7WF74QWpHd6h6uMGeac6/ATC6CEKxUj+kCr\r\n3rRLS+P75Y/goKbOGT+zjyOGpX4oYmrOxF+fYeNuJ0lAbM1/O/hqC2pQQktJ\r\new6Rv59hEqisn0RB8ZbdjqwML9xA/6R7maIUZCMFNHVxSA2uXCZMaD60n8Rt\r\n6viU9Vb/Dpy1oae/S944yD5CkGM87syUK6N7XAWTulmMxwqPxp/GV9Tc5+T9\r\nIcTQcwQrclULaL8dKMKdhAMBjx1OrJMDdwXTrKDHD2sULm9gsvoY+sJ1F1J/\r\n+PZ5bRl4LVRO9nNJApfE3h7UzjkQ3M15FcI2I4X4mgJsKz/gs3xzh9sbsEPj\r\n3AEWcBR+/j9g3+NjQgaSlTHQnI31rerfugHagvxG+YdP3y+4++Rs4Iq0RFUu\r\nytY9Rrs4oz5kLlKygaWCQZpnRTQUD8ieXi9/UMeD/lWo43M34iYzh1YgOalY\r\nBvA248KwxG42VTuwJbW1bJti5Z9/VPLnUUWe8AHBpot8JiGM1YI3TwKQ3exf\r\nidv/OItv7Gn4bSpfMg+TJRVIpopnAz9Pe9RyXKHKcMKD+gzrUvkJVm+li5c+\r\n0uzdl3JqEQmNYyn5iSD6LxwjKiRdkPqdYSA=\r\n=OhRk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "dae5a2565b0d25316faf30d91097ee4691142ab2", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "6.14.17", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "14.19.3", "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.1", "gulp": "^4.0.2", "jest": "^28.1.3", "eslint": "^8.26.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.4.2_1674928507673_0.7902199095060805", "host": "s3://npm-registry-packages"}}, "2.5.0": {"name": "countup.js", "version": "2.5.0", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.5.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "156d300044b6dd8239f6e8315addfc3f8143b5a3", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.5.0.tgz", "fileCount": 22, "integrity": "sha512-/59H8Q6wzu6VfHeqGUgXoyh6kgboGr5mALmRKi8YA11DlcaXSnT1PZG6mTyBRLco4ZjExKlmfNHeMbQgZvis9Q==", "signatures": [{"sig": "MEQCIAE9JwsX2sfyZsvoJRnsMUCZUFGFiNUWxiBT3I0ywLMJAiB6Kocw2c9Df8+cGec2OG9HVpNGr7VTJJ7U8qk6qRa9PA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 94088, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj/7fLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreIhAAj+S3yi43JWxiKVYWkMj6F9W4bY8UvbRLokXMbI2fCnHKNSek\r\nTEEFWKgFJGhKLQGb4MRrRc3ZEpFH3chCr0vdWogMCCG2wIw2NjHdtbVNGcDn\r\nGhRBmTogfcI5oHJxZUmqlqygd703O3pePu7eHQhNZFTZZDHU9veaafJvwKeb\r\n525Dk31DGK2v9NSXMKwyK4yNjYUiCTcykLIEAaYhw9Kjajfv21jFic4uV6rh\r\no4cqWCDpdSEKb7aVOhJXw9Jc9pcvqfKaGWQAfaWfwEg8ylzp7DwaHx7KQejQ\r\nQXq2RddEAxpiOwJR+Mk8o9LtmHx6TPbpTZRTD+Wnp7YzV8LL99S/1IKIXrF5\r\n9w/KOGvi5yRT8I4W9HLRAixJ5n66CP8z6s5vw4zQ1e/MPv/YMRYA3lXxLJu3\r\nL8iXKV5vXrdweyP+ANlbzSzIzq90pywdUynOm0O9o5xE0kZFcGp0P5X0BZS+\r\ncQe8qMt8SI/UCxS8V13Dtb4afTV/3YbIxhsybzawljTtYqp+RquuzU0WhBFy\r\nvccow+TV0UxhJ1OFnmkmx9GB+RFwfVYS1pkNs8/2Sh/4Vx2ZdAY41riH6mRP\r\nv+sCljjZKRAOysVpaIgPf2NOWZRs+91hBLhvZauuYKZd3dSAl+QZPL2BFeFU\r\n+P6qU3D0cxCiMC1ZnXFr+6q8xHdAI1Njpvk=\r\n=3cVq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "c12680abb9913469fb3f31f8c0566fc0f59719cd", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.1", "gulp": "^4.0.2", "jest": "^28.1.3", "eslint": "^8.26.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.5.0_1677703115143_0.4590196908812916", "host": "s3://npm-registry-packages"}}, "2.6.0": {"name": "countup.js", "version": "2.6.0", "author": {"name": "@inorganik"}, "license": "MIT", "_id": "countup.js@2.6.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "db25f37653c5ee996b4dfa03d1ab2f430c903fd1", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.6.0.tgz", "fileCount": 22, "integrity": "sha512-GeORCrCcaFUHP3RNf0/dWK+XQX+fsdtrMO31mNvsbKXNNG+DMTcgZ4dWpIG9BnOS8t5+iJbaRXgaaG9oLs0N4g==", "signatures": [{"sig": "MEUCIH92TrF/MZzy1bjsQq1fEVwR6AsPfB6RasMTfLQk18OgAiEAyqRSWwbcjQt7sxcihAanwWCGFlmij5GiaL5t1Q4naFU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 96320, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkD589ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuwBAAoVx8GAGVLPbOZFjpXn/zV/Vz+iAScebd2EZu+TKO59R4ee/e\r\n+xKdGrBJDU5kscyUQ7CzfESBt11XQJWByyrGQGhlE5i04E49zhjvrYbujH5D\r\nyrpuBL7U3/dVvFogU7TPaKJxu/lK3sXkpqVGHTehTY4vG4wB6rHC/csFGscN\r\nDKH0PSbWwd+ktNSYk5X/JWHllHanoSGqauFaJsl8qneGxgdY9uv+u7/ae+io\r\nleNVGKMkXARaLStaBfSqOR7YERuwkXybAN/DUaJD8W/w/maEJjjDUdsMqtpg\r\nP39Gt5SiQaY8UGfaYHA4sU/H0gajsQSXcSI2UO9duapV49yz4Gi1NJqpVPh1\r\nR89k9V1QOf1OWfmH0IGyQIkk8bDOiuf1s0zeDCosfggeM0G3oJnFNnzqL5oV\r\nFnHnbJG5xrqy9pgSit3/hiS2UtCqCv5NnSUJDi+J06Mi7QPSZjhGutstx5ot\r\nPsn0caAvNluhdkL4b8mPA54ALfdCCgjye3mf40FuA03N4g4eLvkFTFyn8+Qr\r\nRQqAzftqh1SGz+shnLKwSbtSUrU1uMIOAm1VqwL3hBAEK6iEzSJyNQu2RcIY\r\nzh+npkGSwOIteqhrZpQ1bIpM/MPVsW+4fDY8Grph1bGVnNj6eOrLsOerG1Yk\r\neU68Pk/h+CXAuJYzSRyT6vW5LbXKpiaaEB4=\r\n=H1lB\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "49e46aaf5397f6c051760798205ed3433f6e5002", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && gulp && npm run build:umd", "clean": "gulp clean", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "build:umd": "rollup dist/countUp.js --format umd --file dist/countUp.umd.js --name countUp", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"del": "^6.1.1", "gulp": "^4.0.2", "jest": "^28.1.3", "eslint": "^8.26.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "uglify-es": "^3.3.9", "browserify": "^17.0.0", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "gulp-concat": "^2.6.1", "gulp-uglify": "^3.0.2", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.6.0_1678745405512_0.9404303790156183", "host": "s3://npm-registry-packages"}}, "2.6.1": {"name": "countup.js", "version": "2.6.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.6.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "7458a9bfabc965467e3d589498eacb5ba1c8d951", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.6.1.tgz", "fileCount": 22, "integrity": "sha512-EK2CAi6TCGP7YVnb2iyaSNLF1RH00XQXX/TSYoy1tmM9n0uuU7Wa15VE0WRwptYLBEcwQjwuyT/IsIKBKt/KoQ==", "signatures": [{"sig": "MEQCIDn/3llLqADe53u/eFr7hT1aVAFOeaULrP6vqBzuIrIyAiAZPZ7q2NSOGF08QbP+1Axs8DVjyG1Jn5J3tKWowRhVTA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1235223, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkToCbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKzA/6Ap7DXPuGu+bgA9GWamsc0SbzN3UELobp4R1dwQIBxo9EsBn5\r\nrXa95qCrFdBf2EzX9vN4sc3D3HEOJjUu/IxiVk4OISgWa2uHyz6tPB85qLW1\r\nfa6euqU+rHiShpcZsgUi8eafZaPci5UBxObn4rk9pMQ+rKPpjkFBZH57h88i\r\n6zwRxwb4XxpawrAJa+w9rTmD7sXUZEhBpOeq/7VqAGEaVdISbNHADhFqi1uL\r\n07vKhCNAfICdcFJ9DTOFJNu/xD8PSptF3oqwELjEScyVzxItmSnDGEF9OY33\r\n1a5cv2gVLc/c7lSfyc/TTXuTrWwA10S6MIdjISuME+FdhlmFkykccYcHlFER\r\nHzROP1bM3vSD9bFehiEhUgTEycXCH21tKJ9XhgrXbN9sx7CL5jm0aGxXm9lh\r\nXnoxlA/rQjKb8RYuZRnfRf2VNm3UXER8Rra9P33IXxNuVPsuOjskJP6C3NGy\r\nXq5PuBIxn8Rhqn3hSJD+RaKUx+5SQSSO1v9ijPdrROM+4+rnfNRN3E57SjMo\r\nEbS6048aPO/WSKXNeDyUyaddOYm0A5ufqtXhodHjzzeKpm+i1gEaX7VKLWxL\r\nFS0d42O9kph+4UgWrHokfwvjHzDQwujQmTOGosTRdopvWogdFTIxbP31jzPl\r\n5GHtHsulelepoGxkHATzU9WGXuPXnLLyXxA=\r\n=BijG\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "9c2f33ce29c81143956a37381afd62012630f306", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.26.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "rollup-plugin-terser": "^7.0.2", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.6.1_1682866331624_0.03541959944188533", "host": "s3://npm-registry-packages"}}, "2.6.2": {"name": "countup.js", "version": "2.6.2", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.6.2", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "692bd7433163da3073b8b903f32f071470be1185", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.6.2.tgz", "fileCount": 22, "integrity": "sha512-PRvoilkebwr1MKaRAllyJl2cD7mgunGZWXLaSFL+YqHXlJpjLX7SHTWonRfIx2xg1Xf7SPbo92bOOonSIomRYQ==", "signatures": [{"sig": "MEYCIQDDN5sdtrhP78as7ybj99JTK7gIyc+cxsIOvSPuIAAiRwIhANZot1XR5XILmHn38uEyDdEzuhrjwbW7Ij7bKnZ4MDzZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1235570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkUCRiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpY9A//cHvJheLXKFDBW2mXsgkShE2zfPwyOw1GgRxNJVQ8nbjKbBJV\r\nPFBuNqiA46eFs2zCVUdJqp45bqF3EwQllEabo1BpPgwF0EnjkRcb51YEeikO\r\nUghUpA9VT+28na1pRVlVWt2wJNY3q2bDamwXoxtDc/9bGMY+Fpe7mSryndzA\r\niF3enoshqWf/Jw3zWlQ6Cta1sZQfkcZyTIHEwUNUI71O29hunLFf0LhiHLSU\r\nSdaEpDmuXIbZiRLdK2FK38BOC6nhuqIFCbLeUE4VRK47jULEh1FuggNkWSd3\r\nrLFfzamI32fIV8E+8TwYxVVmNv9/SJ89JEwtbWA/XfBVKx+Wm+qQH7oyRzuO\r\nzIKYj2+AfJiaP+Nxj8SmYUrawK/VsLLbVonvyxtND/xBo4dFXcwX+MN9AOAT\r\ntKhnvde2JqYNllQy1SVsIm5qQVqmf4VSdT/5LNEXkoBzytFYTq+VA7GA7aPM\r\nKjvjhQGa5Zv2Ce0bwyEp3tbztISvQoDCincakdlQEgL/fmqN+X/5JCUJCHL2\r\nDg9aTlUyQLb7kDI8/SayGPbT0jeNoBVbyIQrp6Ozne73XjNiQLDIwm7VHIH1\r\n4NTflcDn0oHbbO28qbrRxOftE7IZPzUzzmEW+OVggvJ4PF7GlQn9DRY+M7ib\r\nHEtqxTr2B6b/B9lMTdICSSH+AkF67x0oXKY=\r\n=34uI\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "85bf6626cd422abdc501e94cf30cc69e942e49f9", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.26.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "rollup-plugin-terser": "^7.0.2", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.6.2_1682973793796_0.5692247888617594", "host": "s3://npm-registry-packages"}}, "2.7.0": {"name": "countup.js", "version": "2.7.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.7.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "a5521bd935f0ae83417d0128e73f2a2d2543c9c7", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.7.0.tgz", "fileCount": 22, "integrity": "sha512-IP9nYLGgW//0If73eXQdFlReGhpFGHaStqB1v82FknxnUWueF6HFuuOXySW4sEDMc88PsZL1EOn/NPkfTZalmQ==", "signatures": [{"sig": "MEUCIQClZHwFXK6w/XY4gCfpnVf066QwrmPKW9J8QGhKvWgf+wIgZxT22clcMPYnNXhthE8s2Gk8XuEiBe5cyiR8z5BxEQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1235570}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "8c7c66185c53f5de7ed7e0f301ef16c56d42d843", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.26.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "rollup-plugin-terser": "^7.0.2", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.7.0_1687972007702_0.0474725780372538", "host": "s3://npm-registry-packages"}}, "2.7.1": {"name": "countup.js", "version": "2.7.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.7.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "077d93bb08622abfe1ea4366792d12877a6d8a06", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.7.1.tgz", "fileCount": 22, "integrity": "sha512-ZIYPFB1e4QSgs3HCVPrt0T6C7VHthQAH0ieIyNj6aGEwDUlj0Qq4He227/lqKmcEkp54kDqKQrQDh85XZ74Rzg==", "signatures": [{"sig": "MEUCIQD+4xWI5eTVZhZvg1AcPtW7CuPWp9889YBRW5JGeKCXAgIgad+QBpUCzyXhnURih3bhCgKKQcIFEEKkepQeQ6GbJRA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1235570}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "b21339fa4bdd2340a49ba38f989776ae6ccb7a2c", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.26.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "rollup-plugin-terser": "^7.0.2", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.7.1_1692103504764_0.011979874620240638", "host": "s3://npm-registry-packages"}}, "2.8.0": {"name": "countup.js", "version": "2.8.0", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.8.0", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "64951f2df3ede28839413d654d8fef28251c32a8", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.8.0.tgz", "fileCount": 22, "integrity": "sha512-f7xEhX0awl4NOElHulrl4XRfKoNH3rB+qfNSZZyjSZhaAoUk6elvhH+MNxMmlmuUJ2/QNTWPSA7U4mNtIAKljQ==", "signatures": [{"sig": "MEQCIGcU3sygsRzf5mpu4np/QvmyXb8PKpe4gzxbK2Q6t4KcAiAc9T7AdDtCpu0njiVzYwdI8K9Ezs+UwgFiOl+GoZSeNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1236411}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "gitHead": "9f9b0b4d1b46fde3e6bad866c01516ac827d98a3", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "8.19.2", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "16.18.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^28.1.3", "eslint": "^8.26.0", "rimraf": "^5.0.0", "rollup": "^2.79.1", "ts-jest": "^28.0.8", "typescript": "^4.8.4", "@types/jest": "^28.1.8", "http-server": "^14.1.1", "eslint-plugin-import": "^2.26.0", "rollup-plugin-terser": "^7.0.2", "jest-environment-jsdom": "^28.1.3", "@typescript-eslint/parser": "^5.41.0", "@typescript-eslint/eslint-plugin": "^5.41.0", "@typescript-eslint/eslint-plugin-tslint": "^5.41.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.8.0_1692982477245_0.7071614364990082", "host": "s3://npm-registry-packages"}}, "2.8.1": {"name": "countup.js", "version": "2.8.1", "author": {"name": "<PERSON>"}, "license": "MIT", "_id": "countup.js@2.8.1", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "homepage": "https://github.com/inorganik/countUp.js#readme", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "dist": {"shasum": "1adf4df59d07be14001e46210b8892d0e87c3dba", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.8.1.tgz", "fileCount": 22, "integrity": "sha512-6IYd+u0Dg2kRfxsYlWft805tPrEeGY97N0zicr8VOISS6ig6CXLroOt9olkIjOgeGNO4qCPx06FwJr62Vo6GOQ==", "signatures": [{"sig": "MEYCIQC+o54pnnYVkRUTv9gfbC426lIm07jyLLgbUOXvhjnLjwIhAPyqrAK//8xLkWeCi67YIsUp/RHSa6kRnmMEaLOknpLB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1236497}, "main": "./dist/countUp.umd.js", "types": "./dist/countUp.d.ts", "module": "./dist/countUp.min.js", "exports": {"import": "./dist/countUp.min.js", "require": "./dist/countUp.umd.js"}, "gitHead": "289280c9ace8d465ef451c37a1e12e94510baeec", "scripts": {"lint": "eslint -c .eslintrc.js --ext .ts ./src", "test": "jest", "build": "npm run clean && tsc && rollup -c rollup.config.mjs", "clean": "rimraf dist/countUp.*", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test:watch": "jest --watch"}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "repository": {"url": "git+https://github.com/inorganik/countUp.js.git", "type": "git"}, "_npmVersion": "10.2.4", "description": "Animates a numerical value by counting to it", "directories": {}, "_nodeVersion": "20.11.1", "_hasShrinkwrap": false, "devDependencies": {"jest": "^29.7.0", "eslint": "^8.57.0", "rimraf": "^5.0.9", "rollup": "^4.18.1", "ts-jest": "^29.2.2", "@eslint/js": "^9.6.0", "typescript": "^5.5.3", "@types/jest": "^29.5.12", "http-server": "^14.1.1", "@types/eslint__js": "^8.42.3", "typescript-eslint": "^7.16.0", "eslint-plugin-import": "^2.29.1", "@rollup/plugin-terser": "^0.4.4", "jest-environment-jsdom": "^29.7.0"}, "_npmOperationalInternal": {"tmp": "tmp/countup.js_2.8.1_1745415244617_0.33887198263697615", "host": "s3://npm-registry-packages-npm-production"}}, "2.8.2": {"name": "countup.js", "description": "Animates a numerical value by counting to it", "version": "2.8.2", "license": "MIT", "author": {"name": "<PERSON>"}, "main": "./dist/countUp.umd.js", "module": "./dist/countUp.min.js", "types": "./dist/countUp.d.ts", "repository": {"type": "git", "url": "git+https://github.com/inorganik/countUp.js.git"}, "exports": {"types": "./dist/countUp.d.ts", "import": "./dist/countUp.min.js", "require": "./dist/countUp.umd.js"}, "scripts": {"build": "npm run clean && tsc && rollup -c rollup.config.mjs", "clean": "rimraf dist/countUp.*", "lint": "eslint -c .eslintrc.js --ext .ts ./src", "serve": "http-server -o -c-1 ./", "start": "npm run build && npm run serve", "test": "jest", "test:watch": "jest --watch"}, "devDependencies": {"@eslint/js": "^9.6.0", "@rollup/plugin-terser": "^0.4.4", "@types/eslint__js": "^8.42.3", "@types/jest": "^29.5.12", "eslint": "^8.57.0", "eslint-plugin-import": "^2.29.1", "http-server": "^14.1.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "rimraf": "^5.0.9", "rollup": "^4.18.1", "ts-jest": "^29.2.2", "typescript": "^5.5.3", "typescript-eslint": "^7.16.0"}, "_id": "countup.js@2.8.2", "gitHead": "021022809d99da8b8b10ffdbdafef28435f8649c", "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "homepage": "https://github.com/inorganik/countUp.js#readme", "_nodeVersion": "20.11.1", "_npmVersion": "10.2.4", "dist": {"integrity": "sha512-UtRoPH6udaru/MOhhZhI/GZHJKAyAxuKItD2Tr7AbrqrOPBX/uejWBBJt8q86169AMqKkE9h9/24kFWbUk/Bag==", "shasum": "ea49e8781693612cf11d81209a230058ef226da5", "tarball": "https://registry.npmjs.org/countup.js/-/countup.js-2.8.2.tgz", "fileCount": 22, "unpackedSize": 1236533, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQD7MCRjV1z4Uv1RZVnIxuomcr5eSeR+UJR8dzsy8ifxqgIgU9d8pjDVemGZirNlDHnXbzVzr4giR0IVbLIFkzAlRck="}]}, "_npmUser": {"name": "inorganik", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages-npm-production", "tmp": "tmp/countup.js_2.8.2_1745585954260_0.1664778045682751"}, "_hasShrinkwrap": false}}, "time": {"created": "2014-12-22T21:34:28.079Z", "modified": "2025-04-25T12:59:14.647Z", "0.1.0": "2014-12-22T21:34:28.079Z", "0.1.1": "2014-12-22T22:27:47.979Z", "1.7.0": "2016-04-07T22:59:28.620Z", "1.7.1": "2016-04-23T00:03:25.694Z", "1.8.1": "2016-12-28T21:29:44.714Z", "1.8.2": "2017-03-02T15:59:06.912Z", "1.8.3": "2017-04-04T15:47:28.438Z", "1.8.4": "2017-04-18T14:44:27.142Z", "1.8.5": "2017-04-25T23:20:04.427Z", "1.9.0": "2017-08-23T23:08:20.825Z", "1.9.1": "2017-08-24T18:37:37.628Z", "1.9.2": "2017-09-14T14:07:52.493Z", "1.9.3": "2017-11-07T17:28:15.129Z", "2.0.0": "2019-02-21T20:34:55.686Z", "2.0.1": "2019-02-26T15:39:20.782Z", "2.0.2": "2019-03-01T16:35:33.828Z", "2.0.3": "2019-03-05T13:41:28.938Z", "2.0.4": "2019-03-14T19:42:32.394Z", "2.0.5": "2020-04-28T16:48:31.330Z", "2.0.6": "2020-07-27T13:49:54.619Z", "2.0.7": "2020-08-25T12:50:25.186Z", "2.0.8": "2021-07-27T20:08:21.559Z", "2.1.0": "2022-03-02T19:52:54.089Z", "2.2.0": "2022-05-18T22:05:49.379Z", "2.3.0": "2022-06-27T15:39:28.246Z", "2.3.1": "2022-06-29T19:50:05.982Z", "2.3.2": "2022-07-08T12:41:18.704Z", "2.4.1": "2023-01-24T13:07:40.129Z", "2.4.2": "2023-01-28T17:55:07.799Z", "2.5.0": "2023-03-01T20:38:35.292Z", "2.6.0": "2023-03-13T22:10:05.672Z", "2.6.1": "2023-04-30T14:52:11.889Z", "2.6.2": "2023-05-01T20:43:14.105Z", "2.7.0": "2023-06-28T17:06:48.003Z", "2.7.1": "2023-08-15T12:45:04.996Z", "2.8.0": "2023-08-25T16:54:37.610Z", "2.8.1": "2025-04-23T13:34:04.874Z", "2.8.2": "2025-04-25T12:59:14.484Z"}, "bugs": {"url": "https://github.com/inorganik/countUp.js/issues"}, "author": {"name": "<PERSON>"}, "license": "MIT", "homepage": "https://github.com/inorganik/countUp.js#readme", "repository": {"type": "git", "url": "git+https://github.com/inorganik/countUp.js.git"}, "description": "Animates a numerical value by counting to it", "maintainers": [{"name": "inorganik", "email": "<EMAIL>"}], "readme": "# CountUp.js\nCountUp.js is a dependency-free, lightweight Javascript class that can be used to quickly create animations that display numerical data in a more interesting way.\n\nDespite its name, CountUp can count in either direction, depending on the start and end values that you pass.\n\nCountUp.js supports all browsers. MIT license.\n\n## [Try the demo](https://inorganik.github.io/countUp.js)\n\nOr tinker with CountUp in [Stackblitz](https://stackblitz.com/edit/countup-typescript)\n\n## Jump to:\n\n- **[Usage](#usage)**\n- **[Including CountUp](#including-countup)**\n- **[Contributing](#contributing)**\n- **[Creating Animation Plugins](#creating-animation-plugins)**\n\n## Features\n- **Animate when element scrolls into view.** Use option `enableScrollSpy`.\n- **Highly customizeable** with a large range of options, you can even substitute numerals.\n- **Smart easing**: CountUp intelligently defers easing until it gets close enough to the end value for easing to be visually noticeable. Configureable in the [options](#options).\n- **Plugins** allow for alternate animations like the [Odometer plugin](https://www.npmjs.com/package/odometer_countup)\n\n![Odomoeter plugin](./demo/images/odometer_plugin.gif)\n\n## Usage:\n\n**Use CountUp with:**\n\n- [Angular 2+](https://github.com/inorganik/ngx-countUp)\n- [Angular 1.x](https://github.com/inorganik/countUp.js-angular1)\n- [React](https://gist.github.com/inorganik/2cf776865a4c65c12857027870e9898e)\n- [Svelte](https://gist.github.com/inorganik/85a66941ab88cc10c5fa5b26aead5f2a)\n- [Vue](https://github.com/xlsdg/vue-countup-v2)\n- [WordPress](https://wordpress.org/plugins/countup-js/)\n- [jQuery](https://gist.github.com/inorganik/b63dbe5b3810ff2c0175aee4670a4732)\n- [custom element](https://github.com/lekoala/formidable-elements/blob/master/docs/count-up.md)\n\n**Use CountUp directly:**\n\nOn npm as `countup.js`. You can import as a module, or include the UMD script and access CountUp as a global. See [detailed instructions](#including-countup) on including CountUp.\n\n**Params**:\n- `target: string | HTMLElement | HTMLInputElement` - id of html element, input, svg text element, or DOM element reference where counting occurs\n- `endVal: number` - the value you want to arrive at\n- `options?: CountUpOptions` - optional configuration object for fine-grain control\n\n**Options** (defaults in parentheses): <a name=\"options\"></a>\n\n```ts\ninterface CountUpOptions {\n  startVal?: number; // number to start at (0)\n  decimalPlaces?: number; // number of decimal places (0)\n  duration?: number; // animation duration in seconds (2)\n  useGrouping?: boolean; // example: 1,000 vs 1000 (true)\n  useIndianSeparators?: boolean; // example: 1,00,000 vs 100,000 (false)\n  useEasing?: boolean; // ease animation (true)\n  smartEasingThreshold?: number; // smooth easing for large numbers above this if useEasing (999)\n  smartEasingAmount?: number; // amount to be eased for numbers above threshold (333)\n  separator?: string; // grouping separator (',')\n  decimal?: string; // decimal ('.')\n  // easingFn: easing function for animation (easeOutExpo)\n  easingFn?: (t: number, b: number, c: number, d: number) => number;\n  formattingFn?: (n: number) => string; // this function formats result\n  prefix?: string; // text prepended to result\n  suffix?: string; // text appended to result\n  numerals?: string[]; // numeral glyph substitution\n  enableScrollSpy?: boolean; // start animation when target is in view\n  scrollSpyDelay?: number; // delay (ms) after target comes into view\n  scrollSpyOnce?: boolean; // run only once\n  onCompleteCallback?: () => any; // gets called when animation completes\n  onStartCallback?: () => any; // gets called when animation starts\n  plugin?: CountUpPlugin; // for alternate animations\n}\n```\n\n**Example usage**: <a name=\"example\"></a>\n\n```js\nconst countUp = new CountUp('targetId', 5234);\nif (!countUp.error) {\n  countUp.start();\n} else {\n  console.error(countUp.error);\n}\n```\n\nPass options:\n```js\nconst countUp = new CountUp('targetId', 5234, options);\n```\n\nwith optional complete callback:\n\n```js\nconst countUp = new CountUp('targetId', 5234, { onCompleteCallback: someMethod });\n\n// or (passing fn to start will override options.onCompleteCallback)\ncountUp.start(someMethod);\n\n// or\ncountUp.start(() => console.log('Complete!'));\n```\n\n**Other methods**:\n\nToggle pause/resume:\n\n```js\ncountUp.pauseResume();\n```\n\nReset the animation:\n\n```js\ncountUp.reset();\n```\n\nUpdate the end value and animate:\n\n```js\ncountUp.update(989);\n```\n\n---\n### **Animate when the element is scrolled into view**\n\nUse the scroll spy option to animate when the element is scrolled into view. When using scroll spy, just initialize CountUp but do not call start();\n\n```js\nconst countUp = new CountUp('targetId', 989, { enableScrollSpy: true });\n```\n\n**Troubleshooting scroll spy**\n\nCountUp checks the scroll position as soon as it's initialized. So if you initialize it before the DOM renders and your target element is in view before any scrolling, you'll need to re-check the scroll position after the page renders:\n\n```js\n// after DOM has rendered\ncountUp.handleScroll();\n```\n---\n### **Alternate animations with plugins**\n\nCurrently there's just one plugin, the **[Odometer Plugin](https://github.com/msoler75/odometer_countup.js)**.\n\nTo use a plugin, you'll need to first install the plugin package. Then you can include it and use the plugin option. See each plugin's docs for more detailed info.\n```js\nconst countUp = new CountUp('targetId', 5234, {\n  plugin: new Odometer({ duration: 2.3, lastDigitDelay: 0 }),\n  duration: 3.0\n});\n```\nIf you'd like to make your own plugin, see [the docs](#creating-animation-plugins) below!\n\n---\n\n## Including CountUp\n\nCountUp is distributed as an ES6 module because it is the most standardized and most widely compatible module for browsers, though a UMD module is [also included](#umd-module), along with a separate requestAnimationFrame polyfill (see below).\n\nFor the examples below, first install CountUp. This will give you the latest:\n```\nnpm i countup.js\n```\n\n### Example with vanilla js\nThis is what I used in the demo. Checkout index.html and demo.js.\n\nmain.js:\n```js\nimport { CountUp } from './js/countUp.min.js';\n\nwindow.onload = function() {\n  var countUp = new CountUp('target', 2000);\n  countUp.start();\n}\n```\n\nInclude in your html. Notice the `type` attribute:\n```html\n<script src=\"./main.js\" type=\"module\"></script>\n```\n\nTo support IE and legacy browsers, use the `nomodule` script tag to include separate scripts that don't use the module syntax:\n\n```html\n<script nomodule src=\"js/countUp.umd.js\"></script>\n<script nomodule src=\"js/main-for-legacy.js\"></script>\n```\n\nTo run module-enabled scripts locally, you'll need a simple local server setup like [this](https://www.npmjs.com/package/http-server) (test the demo locally by running `npm run serve`) because otherwise you may see a CORS error when your browser tries to load the script as a module.\n\n### For Webpack and other build systems\nImport from the package, instead of the file location:\n\n```js\nimport { CountUp } from 'countup.js';\n```\n\n### UMD module\n\nCountUp is also wrapped as a UMD module in `./dist/countUp.umd.js` and it exposes CountUp as a global variable on the window scope. To use it, include `countUp.umd.js` in a script tag, and invoke it like so:\n\n```js\nvar numAnim = new countUp.CountUp('myTarget', 2000);\nnumAnim.start()\n```\n\n### requestAnimationFrame polyfill\n\nYou can include `dist/requestAnimationFrame.polyfill.js` if you want to support IE9 and older, and Opera mini.\n\n---\n\n## Contributing\n\nBefore you make a pull request, please be sure to follow these instructions:\n\n1. Do your work on `src/countUp.ts`\n1. Lint: `npm run lint`\n1. Run tests: `npm t`\n1. Build and serve the demo by running `npm start` then check the demo to make sure it counts.\n\n<!-- PUBLISHING\n\n1. bump version in package.json and countUp.ts\n2. npm run build\n3. commit changes\n4. npm publish\n\n-->\n\n---\n\n## Creating Animation Plugins\n\nCountUp supports plugins as of v2.6.0. Plugins implement their own render method to display each frame's formatted value. A class instance or object can be passed to the `plugin` property of CountUpOptions, and the plugin's render method will be called instead of CountUp's.\n\n```ts\nexport declare interface CountUpPlugin {\n  render(elem: HTMLElement, formatted: string): void;\n}\n```\n\nAn example of a plugin:\n```ts\nexport class SomePlugin implements CountUpPlugin {\n  // ...some properties here\n\n  constructor(options: SomePluginOptions) {\n    // ...setup code here if you need it\n  }\n\n  render(elem: HTMLElement, formatted: string): void {\n    // render DOM here\n  }\n}\n```\n\nIf you make a plugin, be sure to create a PR to add it to this README!\n", "readmeFilename": "README.md", "users": {"itcorp": true, "filipve": true, "ungurys": true, "markreyes": true, "elihimself": true, "minime5417": true, "w916797724": true, "warcrydoggie": true}}