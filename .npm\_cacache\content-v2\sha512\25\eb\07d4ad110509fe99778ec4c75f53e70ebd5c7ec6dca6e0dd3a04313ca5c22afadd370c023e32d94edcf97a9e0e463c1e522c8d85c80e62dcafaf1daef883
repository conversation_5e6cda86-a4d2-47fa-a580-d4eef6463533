{"name": "@parcel/watcher", "dist-tags": {"latest": "2.5.1"}, "versions": {"1.11.0": {"name": "@parcel/watcher", "version": "1.11.0", "dependencies": {"chokidar": "^2.0.3", "@parcel/utils": "^1.11.0"}, "devDependencies": {"mocha": "^5.2.0", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.11.0", "@parcel/babel-register": "^1.11.0"}, "dist": {"shasum": "a05ee752d47bf3627b77a64a6089404683edc255", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-1.11.0.tgz", "fileCount": 18, "integrity": "sha512-1ySF0sH06jyhpaErW1UWC7BNgkAl6PJyBjuu2cLTW1o71J2iQqgGt95cbuqmfmjI3l0xYN+nauDFqHERaj7Z8A==", "signatures": [{"sig": "MEUCIQDWbRuCdmcLC8AHf9jwoko7s6COc7A+TjUKiaZarLeN6wIgHnfElCXhds8ORjzgp6ktzFioOPWcc5nFrGt3ZgVZ8gY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33136, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcGT6FCRA9TVsSAnZWagAAxBsP/35f3i/81/gJfapOSRQW\nmFnxiTv0WOaIHHYt0gS0qDT1Vt3SoAQsoZ1qxF905VJPxlq0z8J1EOD67P4H\nU4D67ktl0AwJOmP0DnRzmbVbdAbJvZUQivd3VkqOy/EuALc751f6Sn9m8RUN\nsbJOsl44lZHWHKNenjWm3qGz+uigMtK8HPbrexxREL2F9yYcRvaqqvyc+xxB\nGbohrVeHAUMFzRoibEaY9XKxBT21oRuRzg+g6VacResNnijdDkLk/kfAf66B\ngJgXsH66222X0QjwYFCVbFcxiW99N9H2eH6oTPC4bH26nSARpfRpKIAXD7rS\ndtvcisLcGetbp0ywSoVNX0JTcspk+TxZwVHzaRoU2h3HtQoNgCzWHLld0Rtr\noPe5P7lpBk3xxh0CxtJvYWjsc1HaqzE511zZGCH+hzR7Hvkird4GvGO7VTRq\nsNqOjuKpAIKLrlixUf+JkK8vgfVeYIr3NXOVW5s16zvTd493jDu/Uk196g/x\n08aS1ltFU1Qeo5G0qvBNKSIkgRpIIxemaYNJv7K/9/85ssqK14Z85xGBZt47\nLK/HDE1e9ykWhmJRLYrHqU3Nm7m+j1xiGaV6cf+V320snvtsFVosh5JCvVoK\n3HSXU+A+6edP83NfvS/mjdL+Y3xHSB3eF+21+A3lp57WYUgEmMjq+gt1euFG\n/zpF\r\n=U362\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.12.0": {"name": "@parcel/watcher", "version": "1.12.0", "dependencies": {"chokidar": "^2.0.3", "@parcel/utils": "^1.11.0"}, "devDependencies": {"mocha": "^5.2.0", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.12.0", "@parcel/babel-register": "^1.11.0"}, "dist": {"shasum": "769024b2a810b0c3b38c310f297d104c77df3660", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-1.12.0.tgz", "fileCount": 21, "integrity": "sha512-yijGiAqG7Tjf5WnFwOkiNWwerfZQDNABldiiqRDtr7vDWLO+F/DIncyB7tTcaD5Loevrr5mzzGo8Ntf3d2GIPg==", "signatures": [{"sig": "MEYCIQDFORn+AcOCfD1kcVguBDzsxLGfmljh4myygTKsCIDlmgIhAPsPsYYDNDEVpbo5YDh0A4Ar8xCCY2PJ7x3eDFPSDGTE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgUNZCRA9TVsSAnZWagAA6isQAKOcfrdxXzADN+wUBs2A\nPg8nZcEWpBGp24YhMyOlWxkoxKlUJW6uBip+hWGK5mtfmwwoWnKF0YeJ2Mj/\nUNTrMDMvSyrMe9T/YFAtotxhgL+GLmV5TA4l4qwZgJZnwocJ29kycZttXa/t\n1p3SrEd93e5iDydRFVytGQCcPbemd5dPdpZffOV5O6qt1cCRSShXPDQJj1OK\nCmkFXkEyCb6pDrQjYOVd2sGszdGKcEV7dhIrek4ZTRjG8e7T3RBOjRqwFuOn\n8+gcg6OTv0RXotrIUvZw7WUVX4aabbgnz/QyDZaWWs6FuJjWCailBb/cTtFS\n/q/q63RtLHGRnG/qCr/PpG8s8ivSA2FiAdCYn80kv9rKAuZSpO5i51N4kgBT\nKoOHRLQwtVdQXRDMAffm6wijaCqBJbUcOFBagoUEl+aSipRwRD2QIUtLO97p\nNSrroAUlG5WJRlysU4ZKCPHlKC78uVgoifA1+6iBLwTTKUCrCYkO0fsWndjB\nf3VNtBpuBkgjrdfUtk8u1J6QSJ052zc7ZKTvjv2OQSeYadPvjzXggWfMJ9/5\n1hnzLlszledC4e3TtF+gjRPJGywt1mp4CMoq2iCe3NlMgdj7Kmxq1f4zz9TA\nx3DVXdcExH04mLF05g5nijbepW2dpBK1OALXJx4BfWhK2OngRhF+9a885++u\nIAE5\r\n=iRNx\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.1": {"name": "@parcel/watcher", "version": "2.0.0-alpha.1", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"mocha": "^6.0.2", "fs-extra": "^7.0.1", "prebuild": "^8.2.1"}, "dist": {"shasum": "3954d1fe8343a17886a2290d8304aa126ca8bf46", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.1.tgz", "fileCount": 37, "integrity": "sha512-FK3ojn8ngW1Hl8veOc53nEPWLwXYNQ+S8qpkJOQph7ggg1OWa7838tLdI61nrL0iGX5gDYtZoNq9RWD8PsRTyw==", "signatures": [{"sig": "MEQCIEL6XGvRyVacFt0mknRU5ZXpRpLpESMITROFzpCzns6pAiA+30JJzLZ+v2Ue7MI724NdJ13zb0uRgBlGnIedd3uwag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 228717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcov+5CRA9TVsSAnZWagAAV28P/RX9ZcoaVJGAVHOKA9w7\n+JMQbHuvsm8zYSwBroKG8vmbgi0fkPAWwQoxHQWbajR/fAdc9ePKXePl31QQ\nPZZ3pHGUXCoelzCxlj0BTrMDIsIOWCzB+SY2BEMB7yjAKnOEsSdl7mZhV1aX\nzCBKv0TKbOUYkvMtW95P5zAlSUocn/OsVKXLIIpFV4nfiEUzIJXeU5Zolhyq\nl8mkkPRTraQ7lrkyd3CxelFpI7JM441UMfpfZmhCxdvSKJkuQiEdPXpSmf/W\njWgAOkK6coza96lscQuDFYP9PZ9h1ce5EBRqdkQC41f566aIpKGfC9kI9cjU\nhnydVzOEevgt6BMSvkBRvbHLuc0mo0l7coOFwSNAgTUSBi+0XsrtSS0hrupC\nFOEzv13QzTMGbdwkc0VfuMqSOcASzW6BxL7Yzw9czULBZ4VWUc6qolsgss5D\n2Si6BNVoxXieGWFbLjQYTedMLTQEng2yZnobFXnXPlViS8/oWNGL2ByoJuRG\niEMyYLeYEj3o/5hA9hkjswyZBT4QrAs0KQQ6hM3syDPB3vPp+sSsVpzcFdav\nQmntqv5+qG6Bi4DntZiBT4YvCKdBDnXY1/gqY60iZ3TK7BTFwxfony8KdL9R\ngZlDP+dYPdhq2TckBIWU/l9jaHq+vSodJlAIokSyDlEMSpaegmk5XPSBA76A\nbwMx\r\n=HFcv\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.2": {"name": "@parcel/watcher", "version": "2.0.0-alpha.2", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"mocha": "^6.0.2", "fs-extra": "^7.0.1", "prebuild": "^8.2.1"}, "dist": {"shasum": "f7124c34d4386105d8ec43fa81864cc2790b1434", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.2.tgz", "fileCount": 37, "integrity": "sha512-MEslD2btlI7bmT3I72JKt3Fh11apn2YgaWBJ4wNMRzM5RBADAxuKQvGkqIOL7qR4PMWqbibWETYN9v7rb+Eb+w==", "signatures": [{"sig": "MEUCIQDyqyCFK5DibJqFNhViBGskGUuVh+iR8/dWVyJHo/x56gIgdo3O4Cx9T6AkL4I6kqToTe4hJXKeMgYHb1ehj+MLlVc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 228717, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcowRSCRA9TVsSAnZWagAAXQkQAItYAf2qEMKwB39czE3Q\nzzvxH/TmPdkwh35twn4XiG5F/wmPb96a1mQUkhDC8ikVk2DQEp/5a4zvRcro\nnkQBnKo9E1qvN+Mk8yuv9uoxVZqpDdFlKGLMmEugrvpPBFJrqjx1EFd4hxBG\nQ435e2xt/gxvcjpkKvD96D/16PH9cvwpESfbhl29pLSiJ4XznutwIGQBfgUE\n2Vo5kDdXFfi4meB9GFuYw4H9aDpxyjLt+2oIU8V8gE0/9vN/2xUW3pfXtzqD\nm0CynFK93Yd8damb8UrDU/wygORYVewCZs1OtveudtoZjHgez4LNHFx0x2v8\nr0/OAUqxafnu44UkRXnisaAPy6r9ZbAaQV0eZe1MWzx63HDE8qugDGpNdZl8\njgP2OmD3vC3BCgXxEnydMMPkfX3eIXcr9mU0zIKw0HtCqlernUWtYZdvvOwR\nHVAnfKXZy3FsDsSrQocgL+Q2s/THSkXmd/niIdIRTROYErqFXQC8QUwvnL13\nN6L6jpeQ19te66xVYRI97r1xvYIpllWa3sSjdAogz0upKSnjiScqBHtF0RKC\n+J0eGVkGKr9AiLF8XQGi9YFE/lil2nyX+6Tw8kg8EUJQigXP4aH8jas1EDa3\nCKtfc1OtthMeeT/8UJae0FtcV3CbKTJ8fNVlhwU+yxPr+e9CZYhWKjzKORFV\nPL8F\r\n=l7K5\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.3": {"name": "@parcel/watcher", "version": "2.0.0-alpha.3", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"mocha": "^6.0.2", "fs-extra": "^7.0.1", "prebuild": "^8.2.1"}, "dist": {"shasum": "af81241b734f9a96fb6771d5900266c00fa62d6d", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.3.tgz", "fileCount": 36, "integrity": "sha512-kR30TWdVrApH2R/UZ6uIWp72SdINaCC5Tk63TPMthDbSMbjwb1kXJvOrNbIDTOoxyitHH6xZICeyBCTf78oWag==", "signatures": [{"sig": "MEQCIAq5A3pfNgcXK4QJci0tiWePoUal9Xq02ILw2HJPE2VlAiArwpltY50CCfKV9aZFHHFtpDL04/Ur6ZihQAPS69yc9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrDJMCRA9TVsSAnZWagAAKI8QAItAcQg6zvumEDElvIPo\nGz6/58y5KVrlRWQ2n8t2tUc4vlnD6CmVdrmNVUaVMoKCp6K943cyqGv7XT4J\nPNRfw31sWa3LO+b/vpFRv9TZZGXY6F6ItEg4Wk5rxlQUXxfADaRJdufEu0Sq\nFzykOZH4uQan0bY1oL3M7a8JkU/xBoDN17V/fn1J43g3OugdJevEavY78zcC\nU0En2wxApcxhfYSsvSlUieOiqVaUINQvb+PSUqWIDaJC+AsUjlCZNn0oCi+t\nsp4IJLrue5ZLs1xMF7c5wqw+FkVzHgHqlqMMubQYZV9PqDpKiv/bmYAB0A1u\nvTZSgvuur9nQ0EBlQzGzqwkwK5C1P+w5FCZ7Hq2gjVajoCmif92iYADRbwQM\nPm9cf9syjxLwNVUnoRnqFWzOvcH1aUkJE6ET9iItJQrRnLlj6zkORLBWFCJF\nzir1tOhJa+0xz29l+uH5XDeKaEpdILCToeezy7vY88xi3lQj/U/JrIl3ak25\n2KJUi6Q3nJGthDRTM0zv0x4SA3yoJR+u9vZvBHCaWUARXon7F2uwHw4vSW5x\ned1dw43fo24cZ9JprXbPpp9EcsA8zdA9tD/Pi5l7G9Th3gxCocL9SdJiv+N8\nXMuYXPEFeQ3QkSqeED6P+RrNYkyfhvXQK97esG+yBVAcHT2GYbKdo8zo5ZsT\ncn8m\r\n=lsaX\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "1.12.1": {"name": "@parcel/watcher", "version": "1.12.1", "dependencies": {"chokidar": "^2.1.5", "@parcel/utils": "^1.11.0"}, "devDependencies": {"mocha": "^5.2.0", "@parcel/fs": "^1.11.0", "@parcel/test-utils": "^1.12.0", "@parcel/babel-register": "^1.11.1"}, "dist": {"shasum": "b98b3df309fcab93451b5583fc38e40826696dad", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-1.12.1.tgz", "fileCount": 21, "integrity": "sha512-od+uCtCxC/KoNQAIE1vWx1YTyKYY+7CTrxBJPRh3cDWw/C0tCtlBMVlrbplscGoEpt6B27KhJDCv82PBxOERNA==", "signatures": [{"sig": "MEUCIQCuKWsvwVdwyCq+28sY75SQJ7wpftmXksVKeWVq3wEHtwIgI3CbusAQMPEpKLF9wzVPUY/aXA7pUY9Yz1jtgJHSSMw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33199, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmhzpCRA9TVsSAnZWagAAZ/UP/RBZM3M5aFntM7oDZZIm\nGBaofbSMam9+WIgHllvEbWoFzzpkXTPQgjhXLzocHZQhp92pHYkcLOk3wqic\n1Vz8982out6GVp20WgQW2ocB46OZaCRxzUDUtpOkcpy+ybthmAjrTHiFyvDc\nQu3WZSk5vMy8d0OQLMqZS4SJyzlLshoxSNSztvBgCHsiWoJFkCdDuhPcvhSA\nDVrfYG+m8z+Lf0GNlVSEQCI/kQAgiwtjcQ3uPnowj0mweswP4KPJrsZ9r8GA\nNBZIKrpCi7TJFLelTWk81xiOYZAbdkEkmZsY7YTf0VYjrwzori/8gn+GSCXA\nCzQ+Z08mqEEM7MK6KS0DVZYu7Z55gCd6S7HaudtBkLZ54F9IDS6beFQZ2hXu\niSLx8hfuMIux9fzLNuZRCsqZ3antyiCiViaZUDR/iGQKsImCN7HaFy1HEKgm\nhB0yz2oakVjLbsM19avV5RRWVktqyA1Hm+cbmUNeFny9ovvtaD8lXXOa/EiJ\nwgrXmzICAhQ5FDuIT2tiPmq98mcMKFaTkb1F041LvYQx4zUQ1LTcJig4dksK\nMtBXXhC4vs1+93S1UFxooejdomApfb72H57/lNYqclKevyPM8imAIVqcQHDB\n0i/+a2XG/C+YZ8rTOiMJUV8D6UnlLbwbX2ko+H5Tp3e70xscMX8oyKBrXfQa\nXUbW\r\n=V3lr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-alpha.4": {"name": "@parcel/watcher", "version": "2.0.0-alpha.4", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"mocha": "^6.0.2", "fs-extra": "^7.0.1", "prebuild": "^8.2.1"}, "dist": {"shasum": "d2c88ed7945c00373ab984bfb39b2394f882b794", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.4.tgz", "fileCount": 36, "integrity": "sha512-J8<PERSON>etm6Yeh27lNVr02/bTxqQ5dAs040C9OB1Zs7C4kaDCq/Dx7FVph0aRKDhAiZ0BiSBwgZEh7xmD4ZU844kxA==", "signatures": [{"sig": "MEUCIASD223hNjtsjN8oL7c7SHpBhkDG6DJIPlrQpijaSBAWAiEAqWFBOH2QoevR7utixGHs7yXsi3ECWgMqINqTNcFVyLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdwxbiCRA9TVsSAnZWagAAcWoQAITahFa0/ZmpRz5efePz\nT9UVx9q1aVBLIkzju5iydAy0/EN2cXn/dClje+VvOq4aIcImhvktGyfHHCvr\nVIYpbwTdumlUoxRgowsNiyQL7jd6um6UcKnzFj6yMkbvJmefUpwkcDWYtj01\nJJEI0Y7TlHXOfxnx9tqYqF9PUZKDucEL0wUxQlBF7hNJdlUSHOmwWMEZzHcK\ntKbZsMErvmTmwV7hE6SMv6IX9GGgw84/XZ6aJTrYroUKKRpsySQwN5XFlh9N\ntP/zm+gKYB72fd6cj5qo54k452ibps1GMV1TyiFCZVo9ePZJ+UQqLRZioALf\nFsjOpsf60On1pDHQbCnQ49IY3CEvlSwvqOikiJIoxtQZBf9Qi9kszuNtzZGq\nEIQJOIYee/YfeOkxWujMeA0UQ8htErVS4VxbHKJxELgiGebVOL1ptyG9Jbu5\nhHaKwXvc/KHUqGGcFPH7GZ2vCMdAYCGSebWDtDitMELe3VvxmXsO89GdbAPD\nbdPX/wy2jxQ/o5JbIyWEt3woFfCqwiavcztDr7uirPZVnh2KYW0wcdAWi/Lx\nSUbgrEFWV3hEm6yKDafvR3bO2ZgNF6l41N70HH555k9wW5Q6/+BEdefgudmU\nrxL00XKqCx25B0iL2mq3sb5HdeAjDng8LjjrLm56hPHYzO9qZPFpb4ixxNwg\nITJd\r\n=e9jJ\r\n-----END PGP SIGNATURE-----\r\n"}, "hasInstallScript": true}, "2.0.0-alpha.5": {"name": "@parcel/watcher", "version": "2.0.0-alpha.5", "dependencies": {"bindings": "^1.5.0", "node-addon-api": "^1.6.2", "prebuild-install": "^5.2.5"}, "devDependencies": {"mocha": "^6.0.2", "fs-extra": "^7.0.1", "prebuild": "^8.2.1"}, "dist": {"shasum": "b16e8af9e6cccf249e094075071701d01f73a1be", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.5.tgz", "fileCount": 43, "integrity": "sha512-QE5S2KgyM04pCECr+ZbwKZuzNrrV5eUadPYDhSRdrVc9uOIXfxUPGHgRsp99g+No9Zo+FI9EWL9jEiHmmAb77A==", "signatures": [{"sig": "MEYCIQDi1Wl1UCaa+dq9MOaq5vG4rzCNw8Mt8tBq/YpR/YMpNgIhAMsj3tUV86e5yVmPspCQttcKyEURGreOW/BUtdWSNMa9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6194249, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeQFCMCRA9TVsSAnZWagAAe9AP/3rJeGrpX82Pgnoa/bET\neNsk8uN50GXvEG/smEl1cRrxDSisw/rn716pTrxPK7mKhK6eSyYepaoZPlIM\ntF51esfNyYieCcuje+yn4rlX1nfYP4h3bxDI1o/hr1Fwxmy7074i3TL2yOej\n0cyYz0gus5855FDmqKuBYR5WZyyhru6rSBgU51eqCemXigdxcrCXviPJ/w+z\ns5Lz3KGzztDO0h4fHWVdwwsEAIYdN1UnyvnEBgULFXzjwrqc85hMi56vicLV\no6MiI4Y7Y388KmCAtiDNdx8uWd1GR9mXiE2WJXMlCUcu3f1tn9Ro5jXaT+O8\n2V2JaU4S9oAtCm+92jQzuahu8YgpP55NBiOOF7ElnVNC4ZB30NADS5tcdCBS\n9+LpO34j7I47YY1fXExIo52aD9gSO3xJPbndw+HYVHtV2elic63Z255UCX6U\nvuibFtVgJVPWy5+MeSRcrtbG/aK4RrwiI0dOPROKcLPBUn8k/pH5H96Y8NVk\nscjy3Vur6uVlVK+oc5QqXF7uIj5UGx7BfQm6pp7CXuE/PREr65a2gmZ8VyNX\nCMgooX7xi3PGWSihAa0ACl5C6n3poV05Das+MeXjb1nsvQrv4+SbjJ3+U+Dw\njpKI/+76kz9STqsq04YkDDj+naQ7GcKEgrxzjGGdMm6V4ldWPW9/svUVJ0WT\njoad\r\n=VZ9V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "hasInstallScript": true}, "2.0.0-alpha.6": {"name": "@parcel/watcher", "version": "2.0.0-alpha.6", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"husky": "^4.2.3", "mocha": "^6.0.2", "fs-extra": "^7.0.1", "prettier": "^1.19.1", "prebuildify": "^4.0.0"}, "dist": {"shasum": "239430bcd88e02ef39c97ba30935cadce82c457d", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.6.tgz", "fileCount": 37, "integrity": "sha512-vlLVEel0fLIvPI+iPwttPff3wj/OPmg4AJA6UvgQKNwX8prhu+qW3C2KOqapkh7sturtJaA5o3RwuC5O0gqZEw==", "signatures": [{"sig": "MEYCIQDCdWkDyDQBjTDE5Wsn8YBRSENXZnyYc9rwrRID1rYhQwIhAJsI+cnrlIM1VRSaXPG7Se6osbvcHu6RMF5EXjAWOUdr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1692104, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7PbtCRA9TVsSAnZWagAApP8QAII6C7jSrnWIURxtNSYp\nKXueFITeA+q06+jEKjK/BFzh0CPeFPVhfFT1OExpruEpW41QLMLPv8vOUqaF\nsvPLXdu1ElZY0mBTqIhaMokTDlJ1kmofjHpa/QC+CxPA/Yqqijr/c4c09Jwj\nvwdYOdkT2gOkPIHcpTWq6sQ72OU+sADylDmnYhfpGtL4dtc4sjMYBkH1DnAf\nzLqwabOC+a6VnMV+nVDguj33mnzVE5ILZwoP4ZYLIc1Ykd4iI9KitdvcVApP\nArIR9CIk9RC0C6n9cowLa8ir0ptyL36C1Mgq5e7UswksCWl/c03mVMtDkzUR\nQi5uFRvZPFJhsAVx3pUpQqZY3BvuTMEgP+aeWaOhOBzWDjlPL/JjmUfhbYfb\n5rqLNCp+tQgOKSKJkxZEkLQb+c2hxybcGxbEzv98jjhOQrpm+XJ5N5JL/BYg\n/NttHYK4YuSHuqqiG0ohMHUtmbmCFXzxAKLXNjJCxQD4ia39Fi7ThHPEprDM\nAzyN+t4iQ2FDoxVFkz43eFuGXZoT8krbH05IY6wU5kwe6j3+3qRfc+blqdWQ\npdwfOIzk7N+gnG6Sckz84o1AAS6LG0/IjyCDlTRyuECRWFC9Fz7jRrjYD6NO\neEXn+faDasmVrkV5Xyf4WdhHLVNa0s5P5M1s+AKDPfS/w6dbgVSAqeD/eWA/\ne4i+\r\n=AgqQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0-alpha.7": {"name": "@parcel/watcher", "version": "2.0.0-alpha.7", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"husky": "^4.2.3", "mocha": "^6.0.2", "fs-extra": "^7.0.1", "prettier": "^1.19.1", "prebuildify": "^4.0.0"}, "dist": {"shasum": "377f273adde0655c29b46d6249a08c0fa06dcfc5", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.7.tgz", "fileCount": 37, "integrity": "sha512-ry+kXaOe22YV1UV5Wnv99Hb+gyjjljOYOaSN1Nc5qHL83SxKhawivI8pKSqoaCSlE8aRnIz8NQ0x7pABUyIpng==", "signatures": [{"sig": "MEUCIQDJyx/OwegAsSNcFRd2RDPn7OhNKHuCc+fXaCwh/ehAvgIgCV06bFy3yIEc4LzU1oE77SP7mHcUfXGPS+750qX1Pkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1687979, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7QjRCRA9TVsSAnZWagAApCoP/iqfahiPBmq3FLjEFLTM\nSDkdviooZAZzCsFN4FDoNpGVQcIFRCsG7UuBMPcu2zTu6YZTs9UV0Aek6DWC\nVEickGXTT7IWKIsNJX68k/lXgapENPFPBNmgaX0iBk9wvH4AMOk49Zf7aEK2\nsBYY5ElWcx05yCpMdCnXDXRk0GUszrbM1Fv+CClu0FIc53sDEbyHnIIoeyWs\nCmFLZ96PEvMCLmExB+fADXa7chYLxv/bvk1hjswlK2eW7n04nIN1xJhhQYH+\n0B7Ply9+W50WygTZXOiImDT06x6Rpjq88pOs4196Cszpv2istaQBhCPtcyKw\nv0jXKBfAyOd3VDZ74ybvl4mFPsCYZQoe3GYP9svEIKXRibnGNE54XQj6OqhZ\nJqX2K2b4hRduaotkmTpmbz4ykiiUNOcEuVF9s9/L+tj1zlO5yxabioig3tcW\n3+fKr+Vl5CGJWgLv6UCvWisJk8NDaXlFgBlJlKvtUnMmAYcK2d5fkpFQ3G9d\nV88j28FbkeqEHr5NkaOWYKsRT0h7AYek/R4AgFXxlTlFIG51m3DihITpBwVP\n+SBtZJZ7VJPV5bTZrdw7m/06eQhnE+kCOIqbHRD0C5sJgfL7kUhMu885A8id\nGK03TEJn1s9ovG/d8eQ2CQsg9Ng5PJ9SP2PTxfPWNXrjN8X87MvHChKB/dV7\nP/Na\r\n=nFow\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0-alpha.8": {"name": "@parcel/watcher", "version": "2.0.0-alpha.8", "dependencies": {"lint-staged": "^10.0.8", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1"}, "devDependencies": {"husky": "^4.2.3", "mocha": "^6.0.2", "fs-extra": "^7.0.1", "prettier": "^1.19.1", "prebuildify": "^4.0.0"}, "dist": {"shasum": "7aee9504b87eebc73c794c611a6673e58b81e0b1", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.8.tgz", "fileCount": 37, "integrity": "sha512-9aQu1SFkR6t1UYo3Mj1Vg39/Scaa9i4xGZnZ5Ug/qLyVzHmdjyKDyAbsbUDAd1O2e+MUhr5GI1w1FzBI6J31Jw==", "signatures": [{"sig": "MEUCIGHuEUK3tkSG+B+UAIR/ykURa3cG90nXXMQJ6kUDgA6jAiEA4TkAiql5wRxEOjmluqTFYAAAi8LZHHLN6gckf8xG8Is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1721238, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfBsEUCRA9TVsSAnZWagAAXgsP/Rwxko3P6RD4d/6J+722\nq0lpyyWjO9BmPLySnKuRUBBHtgJiHcAbx8O3ljMCqw2qnfkK2tMiZXyh5bZs\nRxSYQHqTNk+p1TuEHLyl0iWE8qXDZ8e/sOkoKPT3ea3oRrm9D/aL1Hr/VbCE\nnNz1MbyEW6GyPww2Vo7EBEauoY6Ou1tTMVagKw+4UorFlTLyLsixLt/Sf+h9\nAQAxvCadpHwCgicQPhsBWjRiXjfCr0fmYg5QeB7BupL8u9ySt0bqhhWsBlmR\np/rc5+DQPLwVMJ/uO0dFuUlexJ8asl1boeDlkJ8sdIRQOeSsvPyb9zfNlrA7\nEtQtm0HMWVLwyI8DMDXDbP/MyeI2mU5j5+6+HBZZwW+WTecSShOGyJSaX7+I\nrf+8nsz8BRR6eCy66s8TaOc1LXsKhGkz17nbn4ZmsSR1VISuqx7yvZEK/8h4\nXilAL7Vg46ZtPKVwGs9Cq+ZjYiPXRViukCjft5jWYphrTX0fV0NaJSAeoE5G\nrp+e7mueHJfU2r2FMgFjB1WThIaFFYUKMMte6OSxFMK5fhr9hWWbfCIzn1Hn\nVlGaAiq1T+Af/lVVNFBhA3koFpJtD2GgDCmhrzIQhaSgvtI6N5cJCO35mAAx\nIFFiZQZ4Q6xWC5DI2eGTNN5yTZfVhX8lMPd2T40oWuixN4csuXN/8hVMK74J\nxzG/\r\n=YHKW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0-alpha.9": {"name": "@parcel/watcher", "version": "2.0.0-alpha.9", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"husky": "^4.3.0", "mocha": "^8.2.1", "fs-extra": "^9.0.1", "prettier": "^2.1.2", "lint-staged": "^10.5.1", "prebuildify": "^4.1.0"}, "dist": {"shasum": "58010b069a8e64f0c3d0059263f6a525522398d2", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.9.tgz", "fileCount": 37, "integrity": "sha512-k3luHa2W4W42uAe85v61HFMJKBf8txJmEJBB4BlHuNoFomA7lLX35nsg1APFYS/HKvf0oIEqEL1HvPRx+Dt+QA==", "signatures": [{"sig": "MEUCIQDYRO8CRouXiuNf6xV0AhQze7y7fzRGPsbQwU8/EvgUtAIgQ7e8SZlisnd/HAAqgbB4CyqkM3eFtcIbzOKXTvVOtEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1741274, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqFSXCRA9TVsSAnZWagAAp0oP/jg32MZ0s/9m5ir4Bpxp\nIvU6D1MeVRu+hhm8kauVfsk7F9DL9H87cqiPCV+vYjtYCiApFAhl7ULMDuEQ\ngV5uw215jGY/diL3uJBFELPNCGgXipQ1x0pQQxwKhXSAJ7EaC51MQwMTD8Ee\n2i415C0wxl0qbDIGfKN66XxF4/XLWuTIvJzcRq2JoGkmBm7kk6ZCklrKbF3D\nHg5/vLvwKek7Q27fSCXDPHkdiko38+7Drr3S7gpdUMP88nQ4MSf6mnvJ9a9h\nuKQL1pIUQN0szSjpfG3iPIeLiKdKdhy4qqtqqYq5uRXPEmmtN1ZpDfA/eP+6\np8M/snRjvcPNzsycv6BPG9XJd60lkJq5kEoKuY74qzZJ9nl0LjWbW72e/uO6\nEUJDGYzYopufKv7f2GRcGIjyMLojtAMaztArRP+ubit5//ADyPwpDgyhXkhb\nNVGsqP7066BLp0+IVRqdeMMQKoFmaZHv4XjbVYQ1svzvUPsD6Q71zyE3wbZl\n/Se7SpElfIlj0HpmSYsOomePeQneUBhqaTiV8Bqwdre148QI/wt5P1u7XpOL\nDBys6QKfnFjV5brpPBAGrqeXa7xFEBmhME4Q2xPx5UoyvYSKjMrpEugEuIRE\nRqQLNCAPH+rfiCaR89N2/1/hKh6mVwka1E/6RTg+b8K8Gewvd3xLnf/+DvtQ\nfM1t\r\n=KxPV\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0-alpha.10": {"name": "@parcel/watcher", "version": "2.0.0-alpha.10", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"husky": "^4.3.0", "mocha": "^8.2.1", "fs-extra": "^9.0.1", "prettier": "^2.1.2", "lint-staged": "^10.5.1", "prebuildify": "^4.1.0"}, "dist": {"shasum": "99266189f5193512dbdf6b0faca20400c519a16e", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.10.tgz", "fileCount": 39, "integrity": "sha512-8uA7Tmx/1XvmUdGzksg0+oN7uj24pXFFnKJqZr3L3mgYjdrL7CMs3PRIHv1k3LUz/hNRsb/p3qxztSkWz1IGZA==", "signatures": [{"sig": "MEQCIGv4cF7UO5iNJrFeLESZnzsrBQe9t5ya+vNS7HsxYquyAiBjiSRm7+O+e3Plxj8xLFn1EsO9FW8h5mAGeOCD3VgAHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1743076, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgKUFGCRA9TVsSAnZWagAAffIP/AuVfyQeUYPWzd7oU+j3\njQP2uDa336jWUlsblaODfO2YRP7ZQZKPBJZpmymBSGw9BESZV3l1A6QTiDo2\nFjC8ePPr5shyju2Bc/E1mfZF14qkpb2bIl29vWJkQXkkjnWWuO5GpS0SfwGz\npUGcFvQvKku5B/7UlwwYbEHj+FW1KRtu81T3V2t9GakNNxxdJ8APC1RvOseL\nVwHsWYZBYc5X2rG7I/wy+EBv9DDING0RPasfivyQ0LHTlxkUlPjhY2Iqp+x2\n4Ci2CQ4RjZd7qw2Uf9Org6PCK5k41CfUnODsk18HQnsAEZt8GEBLy09Vti8+\nYHTpydEadXK5/9tpUj6ZCVVFgBOFeFVD7Wm+Tq1g5nz/I2glxTe2GC4ikaf1\nGT1hzJ4wE2oaN4Un6tkFTsCce2J85L/of+fm4lVIv+mXyYIhxc1JH/9xfn3X\nUxNW5rUmvJS7r78EpQogN1lkmKUhEy4hng9f8RioS4N0qiqTShcDtDijMqZ0\n+PTOJUhbR/McNY+u6AEamECXvqb7keXvLsaBp6cBV4LLY7baYuhnVz2GVu4W\n0w9QCCZSl6jmbbfqB2GNocyf59E2vjLzm3my1qGN60rC7nEfMkI/YUhuJcBh\nJ6BOZALzg+dM+5iJP9s0ck3TmgvnIxrfmYjpbfjEKSM17ZlBM8UG7jaNJheZ\nrc32\r\n=UoXk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0-alpha.11": {"name": "@parcel/watcher", "version": "2.0.0-alpha.11", "dependencies": {"node-addon-api": "^3.0.2", "node-gyp-build": "^4.2.3"}, "devDependencies": {"husky": "^4.3.0", "mocha": "^8.2.1", "fs-extra": "^9.0.1", "prettier": "^2.1.2", "lint-staged": "^10.5.1", "prebuildify": "^4.1.0"}, "dist": {"shasum": "8d6233d4416880810438cd2628e6a35273241ab3", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0-alpha.11.tgz", "fileCount": 39, "integrity": "sha512-zMIAsFLcnB82kkk0kSOZ/zgyihb8sty0zVrsz+3ruoYXkchymWsCDsxiX4v+X2s8Jppk3JE8vlnD4DKs3QTOEQ==", "signatures": [{"sig": "MEYCIQD7Td03klz2H9Sv7XS5Ss9KjG2wK2Y3cHWREQ7ommW11gIhAL4w4Je5Nn6LnXOpJQmg3sEcA5JrZWuBa50jNlmb08Bd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1743005, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhLl+WCRA9TVsSAnZWagAAj5UP/RVgZ16agP1BBKqQQx/h\nyHPhPxothTE/Gp3xa3mEwkDUkefpFaT5hnt0p9zMBy9avSnoloa7u6HHH0Kb\nHwW4eosQ3t7WrgDGUYZSwhVCgaZIsYuflmVVcXwuJLtoE/Q80VSRJ8/RAtA4\nd5Bdp30E33rWp++maq5C+gelSyVBN5tQsLIfCZA8GZyRlhEEJpqPJvrJrOjA\nYGliWihOYnJehsKhUoZ5i4Fsp+DJYYU1DwJYnGlvK3wyH3f0Sh354cEey7+N\nyQHIXN6hfxfLFT52A/w3TvkRq9GWwmCyLxXZ4pIx+i8XzdfXX41xx+swk47k\nMUCO/n+H0unI+HZvwvuhD9IzAM6k/hVXUgNX3AGSUWx2K7TnahSqc/L4Hkfl\nT9N7PnMe341vnrwjRxdlnshSBTw4W5woxQGugPd6Iee4zjGeZk+grRRQMarQ\nI4KWxm+Nxx8vMBBhqGLvul3m7esH/RZ9OkT0NXv+VIhde8QuIyNyxVF3TueA\nPh46MBpygxedfU1pWn3qzzWvUFzC4VVPNQ9fyET5KECY9PNY7GaawFNx8cPn\nG8eZb/axGg+EWg9GFUa5w4+KNsWY5s8K8TZiRrlrCvEuUC3mUDEi2JTfNGfi\nlfgZpZfrfD5jHaykAfXLHuunu9ZatXNOOSQ8hLthfsBLqAA+XX8anHuln3Le\nd1Gn\r\n=kowx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.0": {"name": "@parcel/watcher", "version": "2.0.0", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "ebe992a4838b35c3da9a568eb95a71cb26ddf551", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.0.tgz", "fileCount": 40, "integrity": "sha512-ByalKmRRXNNAhwZ0X1r0XeIhh1jG8zgdlvjgHk9ZV3YxiersEGNQkwew+RfqJbIL4gOJfvC2ey6lg5kaeRainw==", "signatures": [{"sig": "MEQCIFqzIP8MBSo0s5pxNnoa7/yWyMJH9SMvDFtIE+krnlivAiBg5RUqmIUz1q+qeSLbomHcm4OSpH6TMnXgSpYQ1RqD0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1961896}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.1": {"name": "@parcel/watcher", "version": "2.0.1", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "ec4bb6c43d9588a1ffd3d2abe6df5b501463c62d", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.1.tgz", "fileCount": 40, "integrity": "sha512-XegFF4L8sFn1RzU5KKOZxXUuzgOSwd6+X2ez3Cy6MVhYMbiLZ1moceMTqDhuT3N8DNbdumK3zP1wojsIsnX40w==", "signatures": [{"sig": "MEYCIQCuK3p9W3MgXL9rtz6tPj/uRsXBB7j1v8IaGX94QQhOJAIhAJg23HAoZfOBSw68iUlJRwJ/3OIapWqqhzKjb1XaoQkU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1969870}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.2": {"name": "@parcel/watcher", "version": "2.0.2", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "46bef14584497147bad5247cfb41f80b24d24dfb", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.2.tgz", "fileCount": 40, "integrity": "sha512-WGJY55/mTAGse2C9VVi2oo+p05oJ0kiSHmOjV33+ywgKgUkUh6B/qFQ5kBO/9mH686qqtV3k2zH1QNm+XX4+lw==", "signatures": [{"sig": "MEUCIQDUAzGyLe/+tBiarKKJ5PF6yV5ZQptuulVXbpff5PX/KwIgHFJ/fbn9NtE+NpwLfjkKj5RLmLkjypArqSeiOTnZRiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1970009}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.3": {"name": "@parcel/watcher", "version": "2.0.3", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "2bae7720f2b9c21ea0b89bab55479c7e8937231e", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.3.tgz", "fileCount": 40, "integrity": "sha512-PHh5PArr3nYGYVj9z/NSfDmmKEBNrg2bzoFgxzjTRBBxPUKx039x3HF6VGLFIfrghjJxcYn/IeSpdVwfob7KFA==", "signatures": [{"sig": "MEQCIGR7fdtE9kpCkRJy/g35Cr9l1Z3UL2zrkCj4X2BvbsvVAiAx1TCZvgxD/4dpjBM16VZV/4qiUWO7UiZJLD4bZ+XuFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1965213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhm+YVCRA9TVsSAnZWagAAczMP/i/HboHhdH1lIG7AZy2o\nKxao2pm2NcnGDGw8Ar8DJDdFd75abmG8DJ0meRtyImNZ/QXStWu5YwZPOtXr\nrWy0jyctrxkvxVW385pGbMwVBtnm8Hkldg2L0RXlbZw3XaMZzcZMp9g3VwWF\nGzeMa5ppsPr6P6lnhflWWrT0H9iEvbc7GgQxBa/fB3ax+jSx92d32CLHIdjf\ny7us3KPFyBMc/ts9WkQQBMWjF81B5ZVWV496TNa1NtmNmoGjbF2BuFnuH9lg\nQM0klkEaS4GBA3+yvA9GvBOmXcRK/oRIvx5yLpH6p5LMezVTsfxlQTdhjCx4\nZbCDSrsybEP47/9+QAolTnCA0FbBNULkFePm93wnL4wTQvL8vR7o1i5KucsX\ntrq6LuN58VzvgywOgA4MSGV9Te/zg9hk0DhUORI8KnbDmWdBCiUOASMs85LN\ntcfjIvSii6cXTc/s4wkVA74NAv8Tf8xMTFRtCsPAbAoziPxnjJRq4k5PM4ou\n5R34dAgnxritzmWVNrwP8FI3XYTocLyAyMhzCHz9g025k3Chzb5LE6FOIzWP\nQzbDO7iNJiGZYmmWTcH3nXTeinZ4haKBFFUcPoUxSwOiiyp8DyxwROO6xPw3\njrY/2pUQwJBRvgXntH/G4GaZ+BUQSqFmQIQJsz4HTaPSf+j8CZH9xstNVnPj\nhQAd\r\n=fYY4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.4": {"name": "@parcel/watcher", "version": "2.0.4", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "f300fef4cc38008ff4b8c29d92588eced3ce014b", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.4.tgz", "fileCount": 40, "integrity": "sha512-cTDi+FUDBIUOBKEtj+nhiJ71AZVlkAsQFuGQTun5tV9mwQBQgZvhCzG+URPQc8myeN32yRVZEfVAPCs1RW+Jvg==", "signatures": [{"sig": "MEYCIQCuXoDogHPRWTItGY1cVOBZrMSPyNX65d/hsdOauvwYuQIhAJ4qP3NfgV930cZIu/08DAeW77X/tek5mR9teh1kDo1A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1965472, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpQ7XCRA9TVsSAnZWagAAwBIP/jDcgGvpHSSbnjpRfHB8\nmrdFxfdTnjLKdYzJ8o+Jx8rPdFaPEtXR1uzH1kzcWTUviM9/0UEi/GDgwqmB\no9sbsZEPYVYdWpSdSvkejcggfbV/ajYrKnlUij8jm9WCmVvZbOAhcKM3QDe/\nAOzGwBQadKG8Zt1+0B6xC3vZJB3t+5UQiYZB6sroEQZqdflNXw1QpeMSQclD\nuFuUaBN+H+/8Ekbb/L9BeK0L61vw28VOtAUKGRY2JSNIhZ6ZjRGBf3O6boQF\nEiKn0MsdSp03KjnBORKA1PckCSHML+HqVIlEAnsO2P5H+jTea2CaG6DcgM3R\nQHUwC47KBiULjYnHPkU0QUbvJjhClmlenL2WSrvHhOZt9XhOKDubf93LWLc0\nEpVWbbdwV0AS259WEFKDyvlY/j/9+otRHBpvWY6SLiNcgRJ63ZL0bFl+/r9p\n6t1LtfdK1cE2eOCZjdjXeaAbmycFGak2WFsj4bMXqI/7yzl/MaGrJ+Eg/Hwg\nJZTbtnEtFj/Yz2dbRnh1d0AYT4Tpjc4G2mssmM/ZykzP3mQj0QPCsYLcVPnZ\nKyODaN3TbHp6gfR87sCYs5FaTY0zoU5n/DFNE95UISPXOwpYJHnAXwn+Vv9o\nK+2gvohZYgBdj2fGrFD2/R3+4OIiFnCF9iy8wIoxrpgA57AcL/KxC64Mntzm\n0485\r\n=6oTx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.5": {"name": "@parcel/watcher", "version": "2.0.5", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "f913a54e1601b0aac972803829b0eece48de215b", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.5.tgz", "fileCount": 40, "integrity": "sha512-x0hUbjv891omnkcHD7ZOhiyyUqUUR6MNjq89JhEI3BxppeKWAm6NPQsqqRrAkCJBogdT/o/My21sXtTI9rJIsw==", "signatures": [{"sig": "MEUCIGLy3VYdAX7JitixQ1L7Z2h6PS0O/83KR5Z8xKSXrlW9AiEAxUfimSbgcieE/BKTLJr6K4jdp3UX+HrpSJyojgu4YlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1965654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuq4xCRA9TVsSAnZWagAAqJoP/j0mYPvmsel7BrI+7TIY\ndA8FEkv4ACUJtgX+NYS+Hxf8WDpANn7zvKusX5ZfWolrMYdtp/bbLNP4MOwc\n9DpxV4tQFQRd2CBJQsOWeb82w0p6HZOaOOEupGNNH52eCM3o0FunORQWPZGF\n5F0Wc9AVSyX9M3+GnvHFgTiZpFrousCB5QYInvyuKtRuJPeYhK+QnKNRPyeD\npPMKbk1gHp91hNxqujGkA5KlzafNd+4pFMRrr9FApAG8RCdrjG0Zn57mMbT9\nPKby9aNQzJW+mN+QLNib/CAInYWZlhRekEh2vYeCXKI+qBfMPGS8KmHw/L5f\nYtdwfwY6jif52hnpjCLG1T7m6sF75GVOrE8BePzzY8hlIFV27E3F1caobQQX\nvtT4UoUM/zvdkGUQXAgd4cBe6WgR4mzTT/g2fs2n2SGqCKFC5MfKuvEsOayW\n6lEelhmLo5w5b62QSqFeRMQ18ppMoxneSOk3DWirLfyF9C8g07MZt/1p9HHQ\noCkPPsSJ5CRckOZmXI2t8NSdls/ltUfckyHcS/7ZZbxbEi8wzme0SzYCdmIs\nwLh8b5PDIbHoMpVwaL9knibTltrG4OIaqZ4VYaF/6r8dc4ZoUsTMRnmLLLHe\nmpsZAFmCIGUfYa+NEcomwkqILYex//Wo/pDxnx2qUYJplNxiVHsB2sWgvc6B\nug1W\r\n=sFNx\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.6": {"name": "@parcel/watcher", "version": "2.0.6", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "9087e44ea93b2d7e901d479bb9b0578f25b2173a", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.6.tgz", "fileCount": 40, "integrity": "sha512-6CaHXp6BNrFY5375OGQLSeaxfO8csgGWbO1U2nUqufDtUks7ZIG5wAyj/wR1zkOxRrhN0EaZWvlgSKYqo7a9lg==", "signatures": [{"sig": "MEUCIQDSha+F5ayBpk92Nczl9AAenvh6AkqrSyTExj8+kHIrXAIgPSXtCHCrw4mxosujcUc3QcXgSrJVqe12QCAEU3HP8iU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1781917, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjYd0PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmGw/+Mu3wHjVuvrLW8OY7YH8b69wt5VmASjGYmfkqAxXABPpNHeSs\r\nicdZ4L0MmRxoulOjttk3yQ/0QUbdwb/OyYVTMrHJNUbq8qFgwcIBVCY0rGvP\r\nGZWLS8ge8k9LBaGZXpshhv2UGU5r8NvCFqXBGcMDMUlDPQDrbdbH8qPvqU6J\r\niyhJmcYMXiu4/m7qb6t/qN649K7cP4fdw+hLT9G7352Il45IsKW8g0bhpITL\r\nl09BCAUXAJ3aZIL/09WQed6OB4gELEanDOCXUUVMPTpd7oCCy6eE+yqxKWax\r\nGpZX4i7SmKPDOT4mGeoNM1SLYfVV29m1wY3WDWCKhF318575aD0wnJtjHMHG\r\nG2L7uvto4vqSXXlQ/QKPxjRQjN8d5vydx76qgeT8cvSfWs1CTD1MWP3Yy3wl\r\ne6Tbycd0RIWqiMDq7WXLQTKdAvRzAOikWeGaC9mHhyvbDBcXKK1EmIVRQZLa\r\n/nMkfO9cgEpVIZ8lu/DUBaV6hoeJci/Kd60n9Ej9z2YNK62e8830yYMvbOt6\r\nVBOvQubmIJfSFBVMDOQEjQ9BbYzU0FMosxMV4lKrTAyWK91todpzUKmG1e0V\r\nybRUW4aqtWj1zJDx4NSRALtySLrDsn6Z6oVV/NpYrdS3NHqzJjp0opvHUudj\r\nNUBRKw/2XVzm0JpzIFBp7nXxgPaojXWcTtw=\r\n=pKk9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.0.7": {"name": "@parcel/watcher", "version": "2.0.7", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "c95fe1370e8c6237cb9729c9c075264acc7e21a5", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.0.7.tgz", "fileCount": 40, "integrity": "sha512-gc3hoS6e+2XdIQ4HHljDB1l0Yx2EWh/sBBtCEFNKGSMlwASWeAQsOY/fPbxOBcZ/pg0jBh4Ga+4xHlZc4faAEQ==", "signatures": [{"sig": "MEYCIQCUM64oPIKTGkFMmpMySqjzNXUSQTAomcuV/LH6RBmxlAIhALIn7bjys9t7BLS4cMrHtmDcJ+Z3sHMNa6bfpjnuRMKY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1782783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjZJ9kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppnBAAmYZhHaC14gREbFGDnVvsVVfhABflk4ZOtM3TMceSeKja/NsV\r\nv2fHARrMSh5ZgF6y4jjioAT2FPunpSnOdsrMyPR13G6j3Ok2An4oSra50bfw\r\n0VFnkefUMBN5HalgizzLYckDMMNIBfdyhwtd8C2PIGTuOgumUw6xouLFGF7D\r\ne4Pn7Nbdkm2XFu+65zBj1MZhowxfEMj2MLVX8QbA2afDQi62CjXEb7PoQ5me\r\nOfJdZoSxtDH5oqLjnuIa7CGtUaWzgKnwnUlozJn8Gp2mbtxW8R6sDJU+mjL9\r\ncKzCtQ1+Np2LHNUStTcQmkvhX+bOue3EW0ZGkS1IgqTTVNBFcS4QjCKv6q3X\r\no7GXosf2FGBr+GC7p8p0W+eaABqR59IRkBF5+JbJN1XAP5X+XxF5wHCm8jZ0\r\nSFYEdKu6cSPzcn8Nhkl7WUnYAEGdRfORtZsAwqXr6KRjmMrHT2P3gREVO+KB\r\nhTYC0oUBC6+duTHwOMNgVMXaPh7DiwOorxrYq3m5ByGhE0DHu3+u7WZbbKyw\r\ngi2GClTUCrJs20bLCg3wqjE1Szt6Sjn+PzxZ5P5J8LKMT4hVYo9mPhJFDd2m\r\nW/8tClajJCjoaWQZmJUu2Bd4Vn8gegOifmTVkwGISzjTj7StngDkfwbVPacU\r\nnVbWdIJqAJFxJGgrB/Vn/PK1X9D7TEcgt0s=\r\n=R1VJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.1.0": {"name": "@parcel/watcher", "version": "2.1.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^4.2.1"}, "dist": {"shasum": "5f32969362db4893922c526a842d8af7a8538545", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.1.0.tgz", "fileCount": 42, "integrity": "sha512-8s8yYjd19pDSsBpbkOHnT6Z2+UJSuLQx61pCFM0s5wSRvKCEMDjd/cHY3/GI1szHIWbpXpsJdg3V6ISGGx9xDw==", "signatures": [{"sig": "MEYCIQD6SpthHYVDLdyPTmcachax0TzR/63zrvuSaI1FYFAnEgIhANbDqioHUDjtzhCRKH1Nc5ZkVUW1emCUbtOBir5qo/B1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 2194421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjtmRnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDzA/+M0wNuNFmM8woZjv1qG+HLNAxC5rioUTIN5Oxr1/mSecfRfg4\r\n6nF9erBvxoWIl8BpSG+gdV3K00/dRFxaCb8rSd/7qWjOQ9b+KzWjOD6xPWOy\r\nMfTAEVOC4LqfXzt2amjpm2cBR5LaDyh5aR3k7uRfvGftNVAhUE4EwrtFJjLh\r\n7Dji25ipR04DK6yAqmj5cpp+PEz7zS6VSQNIAAiTpFHZUb5SLIJx1oPOaUds\r\npKkvug/RMQUUG5FvRQmf2lReoe4EfagxLtuzgISSGpYE+zPcZNfHDi475nY/\r\nZN5CtLRSjMWobx+/8vouUYKpqE0Bxex2ss+90o9LqmxWWgTSUmUYe4MqrvLW\r\n5RAfoR4jo1ZWmbmLdqv6Dj7t4lSsoxjDcKcZhduhCSjlmYQFeK5nRwI5150g\r\ntPTwLXMTwFc6SEGNlGOBKj9/R0hcB4c4psiTjeHAO729Mg3yg82507jTO6MJ\r\nwnlOvwlAO8xz3bhnLBSJEbFmrLbS+xh5HbIM0OSYjZeQ+2Nm3ployx9Wjzuv\r\nN208oIKESWoPq4JYqiuCfkT/Ff8Dm+OYGYESgWyhr9NOUIKee8PZYpPcRiSp\r\nyWEH4BF9VpwjWKeODWnZWT9b1DGPGtV/ROP6IK0IjVMGCsL0TwsHmQU/U68g\r\nannFT08YUZ8sOYbv+Ri7zyAzcN4ySpbDHcY=\r\n=iX6d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.2.0-alpha.0": {"name": "@parcel/watcher", "version": "2.2.0-alpha.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-x64": "2.2.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "da10a3dd8dfb4d3e79aa0109dbedc3dc4eff3115", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.2.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-Tg46S6pUZ/VFVQi+01JUwWkMfRV5b9y+q09BGhqzqmqFr+FqOKTcf5YSFupBOBELL6/TIzlPAdyhdQ3iasGhhg==", "signatures": [{"sig": "MEUCIBr2wbmgLPK12Lx8lCgcCXd/Ouubvx0GLl2iF4pVZzfUAiEAksXGzSUA1HQ4E+CnRSWXOWFOFeqQ/z7WzX3HmonfDnQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14357}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.2.0-alpha.1": {"name": "@parcel/watcher", "version": "2.2.0-alpha.1", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.2.0-alpha.1", "@parcel/watcher-darwin-x64": "2.2.0-alpha.1", "@parcel/watcher-win32-arm64": "2.2.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.1", "@parcel/watcher-android-arm64": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.1"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.2.0-alpha.1", "@parcel/watcher-darwin-x64": "2.2.0-alpha.1", "@parcel/watcher-win32-arm64": "2.2.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.2.0-alpha.1", "@parcel/watcher-android-arm64": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.2.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.2.0-alpha.1"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "cba95844fcb8fca27f759bd5f9a212992d14c704", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.2.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-68z4Tio3DeolrQL9Gre3kLkfMkPPE5RTsRAnnIPYKwUeEKKawrFtZgLvtMsD91x3ysdqwDVpVfU77PJ0cLsmOg==", "signatures": [{"sig": "MEUCIDenIYAKMS7EKBYfddvM6mTTE7YpifIIQ+6LtHp0/oDkAiEAnYIbrbt5Fd3VrMjL+9mtH2w767mGaV0OmaFM6xppxBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14818}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.2.0": {"name": "@parcel/watcher", "version": "2.2.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.2.0", "@parcel/watcher-darwin-x64": "2.2.0", "@parcel/watcher-win32-arm64": "2.2.0", "@parcel/watcher-darwin-arm64": "2.2.0", "@parcel/watcher-android-arm64": "2.2.0", "@parcel/watcher-linux-x64-musl": "2.2.0", "@parcel/watcher-linux-arm-glibc": "2.2.0", "@parcel/watcher-linux-x64-glibc": "2.2.0", "@parcel/watcher-linux-arm64-musl": "2.2.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.2.0", "@parcel/watcher-darwin-x64": "2.2.0", "@parcel/watcher-win32-arm64": "2.2.0", "@parcel/watcher-darwin-arm64": "2.2.0", "@parcel/watcher-android-arm64": "2.2.0", "@parcel/watcher-linux-x64-musl": "2.2.0", "@parcel/watcher-linux-arm-glibc": "2.2.0", "@parcel/watcher-linux-x64-glibc": "2.2.0", "@parcel/watcher-linux-arm64-musl": "2.2.0", "@parcel/watcher-linux-arm64-glibc": "2.2.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "92067954e591d239c3ecfa08add205f88f476068", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.2.0.tgz", "fileCount": 6, "integrity": "sha512-71S4TF+IMyAn24PK4KSkdKtqJDR3zRzb0HE3yXpacItqTM7XfF2f5q9NEGLEVl0dAaBAGfNwDCjH120y25F6Tg==", "signatures": [{"sig": "MEYCIQDKsmnXdjbtSJYo/NIlATCQkfgwTXacYVNA37//PjsySQIhAO0QwvFRO+KbKleL3O9s9iUcRW1eRiGI8J39LpqLvjl6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14710}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.2.1-alpha.0": {"name": "@parcel/watcher", "version": "2.2.1-alpha.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.2.1-alpha.0", "@parcel/watcher-darwin-x64": "2.2.1-alpha.0", "@parcel/watcher-win32-arm64": "2.2.1-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.1-alpha.0", "@parcel/watcher-android-arm64": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.1-alpha.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.2.1-alpha.0", "@parcel/watcher-darwin-x64": "2.2.1-alpha.0", "@parcel/watcher-win32-arm64": "2.2.1-alpha.0", "@parcel/watcher-darwin-arm64": "2.2.1-alpha.0", "@parcel/watcher-android-arm64": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.2.1-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.2.1-alpha.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "117e4ff05c3fc9c72060fe0761706b61d90fc503", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.2.1-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-/pOCqIpA0xswCULEKndY7bVKzgt7MtKERE6IKfBLpr/e/thb9po6ES+aEcfol3YDEMYY13pbGdAK81A+LjICUA==", "signatures": [{"sig": "MEUCIQDhXQW0tMeUpWqsz+sIk5pzID04VXiQU2HwGAe4J3hxbgIgBaGxPFPPfiWaPx+W1lqf2S5TYO7GUid97VgyCuzboKY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14798}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.3.0-alpha.0": {"name": "@parcel/watcher", "version": "2.3.0-alpha.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-ia32": "2.3.0-alpha.0", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-arm64": "2.3.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.0", "@parcel/watcher-android-arm64": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.3.0-alpha.0", "@parcel/watcher-darwin-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-ia32": "2.3.0-alpha.0", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.0", "@parcel/watcher-win32-arm64": "2.3.0-alpha.0", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.0", "@parcel/watcher-android-arm64": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "622489b36f3ce841484f1b572d615f3d299ed394", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.3.0-alpha.0.tgz", "fileCount": 6, "integrity": "sha512-Z/zX2KVnfnOkqG8x2iphmbLTeVWNFHpkwVHF8KfVqsFtL9mwimJnFpsmhWh8UnpTEW15saEEZPr98ftcW/FY6Q==", "signatures": [{"sig": "MEUCIG31a0VCQgMKsjYxYofYaqrKoN8+YVMvj/L4sxqftXy5AiEA0EGZgwCRlwPjPvKqYGyFeARl5bx/jKg4rQeJjnWcTss=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14335}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.3.0-alpha.1": {"name": "@parcel/watcher", "version": "2.3.0-alpha.1", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.1", "@parcel/watcher-darwin-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-ia32": "2.3.0-alpha.1", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-arm64": "2.3.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.1", "@parcel/watcher-android-arm64": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.1"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.3.0-alpha.1", "@parcel/watcher-darwin-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-ia32": "2.3.0-alpha.1", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.1", "@parcel/watcher-win32-arm64": "2.3.0-alpha.1", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.1", "@parcel/watcher-android-arm64": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.1", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.1"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "2829b574f120cb2222943fd459c87a1001f41a6d", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.3.0-alpha.1.tgz", "fileCount": 6, "integrity": "sha512-mPsG3KZMyOTAEYnyQDOsFyE0ulOISVIQXZoUqkZUOLEbEA9n2exi97ZPPggr7HHZUfoAjJzFoyxYii2BXXS8pw==", "signatures": [{"sig": "MEUCIBDjYaAvm3MQq0ghOQ81Jl1rHSbDYn2lX8VirnG2+9XbAiEAnuF0og7HdAacj2OJc0aPsrChsNNRUMKn+BgBMQzGWP8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15199}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.3.0-alpha.2": {"name": "@parcel/watcher", "version": "2.3.0-alpha.2", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.2", "@parcel/watcher-darwin-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-ia32": "2.3.0-alpha.2", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-arm64": "2.3.0-alpha.2", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.2", "@parcel/watcher-android-arm64": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.2"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.3.0-alpha.2", "@parcel/watcher-darwin-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-ia32": "2.3.0-alpha.2", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.2", "@parcel/watcher-win32-arm64": "2.3.0-alpha.2", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.2", "@parcel/watcher-android-arm64": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.2", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.2"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "3942838b502f5573b80dfc75ab3882cf7828e5ed", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.3.0-alpha.2.tgz", "fileCount": 6, "integrity": "sha512-Sr8xPFkWRwiNgJ2Gf4zAtZWXtvzRYDPF87n4GvFvj7hSLErJPuKynymmkcgj/76NHzUSkdjCvwj51Gq5jaA/4A==", "signatures": [{"sig": "MEUCIQDa2TrN4WVhm4WwAJrN8colf9olOn48VEoPT0F/zWk4uwIgcD02TmKJPlCUfPhPOqzBiIaPMBRT0uhuPSlSWQ0bWJo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15157}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.3.0-alpha.3": {"name": "@parcel/watcher", "version": "2.3.0-alpha.3", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.3.0-alpha.3", "@parcel/watcher-darwin-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-ia32": "2.3.0-alpha.3", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-arm64": "2.3.0-alpha.3", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.3", "@parcel/watcher-android-arm64": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.3"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.3.0-alpha.3", "@parcel/watcher-darwin-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-ia32": "2.3.0-alpha.3", "@parcel/watcher-freebsd-x64": "2.3.0-alpha.3", "@parcel/watcher-win32-arm64": "2.3.0-alpha.3", "@parcel/watcher-darwin-arm64": "2.3.0-alpha.3", "@parcel/watcher-android-arm64": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-x64-glibc": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-musl": "2.3.0-alpha.3", "@parcel/watcher-linux-arm64-glibc": "2.3.0-alpha.3"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "51cb6dcad9a6058e97065ae69c5fb7968a275fd1", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.3.0-alpha.3.tgz", "fileCount": 7, "integrity": "sha512-M5s4PkCeKck9PNOEVsn/FhBWWH9n51eInraP6vi46DCIXAEbfWLyIj21vXAIL8s9HlghO8fY0RtZqu8bCHBrFg==", "signatures": [{"sig": "MEQCIEEwatoSsS83XkwBAnh5EDiO6L5aebulBXq4SXoOMQMvAiB2A+9Y5ahjyHq50RBWUlM9KPijhOjSXMLU4DyjMkbZMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17182}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.3.0": {"name": "@parcel/watcher", "version": "2.3.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.3.0", "@parcel/watcher-darwin-x64": "2.3.0", "@parcel/watcher-win32-ia32": "2.3.0", "@parcel/watcher-freebsd-x64": "2.3.0", "@parcel/watcher-win32-arm64": "2.3.0", "@parcel/watcher-darwin-arm64": "2.3.0", "@parcel/watcher-android-arm64": "2.3.0", "@parcel/watcher-linux-x64-musl": "2.3.0", "@parcel/watcher-linux-arm-glibc": "2.3.0", "@parcel/watcher-linux-x64-glibc": "2.3.0", "@parcel/watcher-linux-arm64-musl": "2.3.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.3.0", "@parcel/watcher-darwin-x64": "2.3.0", "@parcel/watcher-win32-ia32": "2.3.0", "@parcel/watcher-freebsd-x64": "2.3.0", "@parcel/watcher-win32-arm64": "2.3.0", "@parcel/watcher-darwin-arm64": "2.3.0", "@parcel/watcher-android-arm64": "2.3.0", "@parcel/watcher-linux-x64-musl": "2.3.0", "@parcel/watcher-linux-arm-glibc": "2.3.0", "@parcel/watcher-linux-x64-glibc": "2.3.0", "@parcel/watcher-linux-arm64-musl": "2.3.0", "@parcel/watcher-linux-arm64-glibc": "2.3.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "803517abbc3981a1a1221791d9f59dc0590d50f9", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.3.0.tgz", "fileCount": 7, "integrity": "sha512-pW7QaFiL11O0BphO+bq3MgqeX/INAk9jgBldVDYjlQPO4VddoZnF22TcF9onMhnLVHuNqBJeRf+Fj7eezi/+rQ==", "signatures": [{"sig": "MEYCIQC5DraXfeTltan2QOTVaJTJJnKsLbbSwD63Jnb6z8KIDAIhAN2cOYbiYiahVjcTUHl40DpvS2eNRobFq3uNLSzTfUj2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17078}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.4.0": {"name": "@parcel/watcher", "version": "2.4.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.4.0", "@parcel/watcher-darwin-x64": "2.4.0", "@parcel/watcher-win32-ia32": "2.4.0", "@parcel/watcher-freebsd-x64": "2.4.0", "@parcel/watcher-win32-arm64": "2.4.0", "@parcel/watcher-darwin-arm64": "2.4.0", "@parcel/watcher-android-arm64": "2.4.0", "@parcel/watcher-linux-x64-musl": "2.4.0", "@parcel/watcher-linux-arm-glibc": "2.4.0", "@parcel/watcher-linux-x64-glibc": "2.4.0", "@parcel/watcher-linux-arm64-musl": "2.4.0", "@parcel/watcher-linux-arm64-glibc": "2.4.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.4.0", "@parcel/watcher-darwin-x64": "2.4.0", "@parcel/watcher-win32-ia32": "2.4.0", "@parcel/watcher-freebsd-x64": "2.4.0", "@parcel/watcher-win32-arm64": "2.4.0", "@parcel/watcher-darwin-arm64": "2.4.0", "@parcel/watcher-android-arm64": "2.4.0", "@parcel/watcher-linux-x64-musl": "2.4.0", "@parcel/watcher-linux-arm-glibc": "2.4.0", "@parcel/watcher-linux-x64-glibc": "2.4.0", "@parcel/watcher-linux-arm64-musl": "2.4.0", "@parcel/watcher-linux-arm64-glibc": "2.4.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "esbuild": "^0.19.8", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "2d3c4ef8832a5cdfdbb76b914f022489933e664f", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.4.0.tgz", "fileCount": 7, "integrity": "sha512-XJLGVL0DEclX5pcWa2N9SX1jCGTDd8l972biNooLFtjneuGqodupPQh6XseXIBBeVIMaaJ7bTcs3qGvXwsp4vg==", "signatures": [{"sig": "MEYCIQCSgwZl97RurQ0bcMPkD4R1Ffqgx/UdQ9G1odhcNCEAswIhAKa/kRu09cZY73N23A7C+/QpsZiWLTocl1Kv0IlB7LvJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 17033}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.4.1": {"name": "@parcel/watcher", "version": "2.4.1", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0", "@parcel/watcher-win32-x64": "2.4.1", "@parcel/watcher-darwin-x64": "2.4.1", "@parcel/watcher-win32-ia32": "2.4.1", "@parcel/watcher-freebsd-x64": "2.4.1", "@parcel/watcher-win32-arm64": "2.4.1", "@parcel/watcher-darwin-arm64": "2.4.1", "@parcel/watcher-android-arm64": "2.4.1", "@parcel/watcher-linux-x64-musl": "2.4.1", "@parcel/watcher-linux-arm-glibc": "2.4.1", "@parcel/watcher-linux-x64-glibc": "2.4.1", "@parcel/watcher-linux-arm64-musl": "2.4.1", "@parcel/watcher-linux-arm64-glibc": "2.4.1"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.4.1", "@parcel/watcher-darwin-x64": "2.4.1", "@parcel/watcher-win32-ia32": "2.4.1", "@parcel/watcher-freebsd-x64": "2.4.1", "@parcel/watcher-win32-arm64": "2.4.1", "@parcel/watcher-darwin-arm64": "2.4.1", "@parcel/watcher-android-arm64": "2.4.1", "@parcel/watcher-linux-x64-musl": "2.4.1", "@parcel/watcher-linux-arm-glibc": "2.4.1", "@parcel/watcher-linux-x64-glibc": "2.4.1", "@parcel/watcher-linux-arm64-musl": "2.4.1", "@parcel/watcher-linux-arm64-glibc": "2.4.1"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "esbuild": "^0.19.8", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^5.0.1"}, "dist": {"shasum": "a50275151a1bb110879c6123589dba90c19f1bf8", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.4.1.tgz", "fileCount": 7, "integrity": "sha512-HNjmfLQEVRZmHRET336f20H/8kOozUGwk7yajvsonjNxbj2wBTK1WsQuHkD5yYh9RxFGL2EyDHryOihOwUoKDA==", "signatures": [{"sig": "MEQCIEwAZOArl7VSsq658pHOYhQIjhnOkCI4XfT4Lph9cnmlAiBQ8TctGUkqKGSCPtncnU8FeQ+UDcgMJ+b6FS3qmt+xBA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16179}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.4.2-alpha.0": {"name": "@parcel/watcher", "version": "2.4.2-alpha.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.4.2-alpha.0", "@parcel/watcher-darwin-x64": "2.4.2-alpha.0", "@parcel/watcher-win32-ia32": "2.4.2-alpha.0", "@parcel/watcher-freebsd-x64": "2.4.2-alpha.0", "@parcel/watcher-win32-arm64": "2.4.2-alpha.0", "@parcel/watcher-darwin-arm64": "2.4.2-alpha.0", "@parcel/watcher-android-arm64": "2.4.2-alpha.0", "@parcel/watcher-linux-x64-musl": "2.4.2-alpha.0", "@parcel/watcher-linux-arm-glibc": "2.4.2-alpha.0", "@parcel/watcher-linux-x64-glibc": "2.4.2-alpha.0", "@parcel/watcher-linux-arm64-musl": "2.4.2-alpha.0", "@parcel/watcher-linux-arm64-glibc": "2.4.2-alpha.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "esbuild": "^0.19.8", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^6.0.1"}, "dist": {"shasum": "60d727d0594192512bea6927cce6ca610819db93", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.4.2-alpha.0.tgz", "fileCount": 7, "integrity": "sha512-SHBGWwLtzbRAt/lP76bCmsqysbnQiyocW2+kQAWTA7EkfXETLrHpbkbQb6HHkvXK3HnV4GBvKqRkKWneE95a0A==", "signatures": [{"sig": "MEUCIQDKR7ZUJOeQkNpdwryVeuigG3BaHweKspfQnZBEjEtNcAIgX3Jne7vRQNHq7utP/HltLOohU+yQCFzIjADvHlnG098=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16283}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}}, "2.5.0": {"name": "@parcel/watcher", "version": "2.5.0", "dependencies": {"is-glob": "^4.0.3", "micromatch": "^4.0.5", "detect-libc": "^1.0.3", "node-addon-api": "^7.0.0"}, "optionalDependencies": {"@parcel/watcher-win32-x64": "2.5.0", "@parcel/watcher-darwin-x64": "2.5.0", "@parcel/watcher-win32-ia32": "2.5.0", "@parcel/watcher-freebsd-x64": "2.5.0", "@parcel/watcher-win32-arm64": "2.5.0", "@parcel/watcher-darwin-arm64": "2.5.0", "@parcel/watcher-android-arm64": "2.5.0", "@parcel/watcher-linux-arm-musl": "2.5.0", "@parcel/watcher-linux-x64-musl": "2.5.0", "@parcel/watcher-linux-arm-glibc": "2.5.0", "@parcel/watcher-linux-x64-glibc": "2.5.0", "@parcel/watcher-linux-arm64-musl": "2.5.0", "@parcel/watcher-linux-arm64-glibc": "2.5.0"}, "devDependencies": {"husky": "^7.0.2", "mocha": "^9.1.1", "esbuild": "^0.19.8", "fs-extra": "^10.0.0", "prettier": "^2.3.2", "napi-wasm": "^1.1.0", "lint-staged": "^11.1.2", "prebuildify": "^6.0.1"}, "dist": {"shasum": "5c88818b12b8de4307a9d3e6dc3e28eba0dfbd10", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.0.tgz", "fileCount": 45, "integrity": "sha512-i0GV1yJnm2n3Yq1qw6QrUrd/LI9bE8WEBOTtOkpCXHHdyN3TAGgqAK/DAT05z4fq2x04cARXt2pDmjWjL92iTQ==", "signatures": [{"sig": "MEUCIC7jJks1W1Ih2h+yqbP2CVHgdmuddsCjnSK44z9pGBfkAiEA9SEBfeQ5mGNEIY+izTLwEioHMoMFfvGMhCCyxkEx4+Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 129459}, "engines": {"node": ">= 10.0.0"}, "funding": {"url": "https://opencollective.com/parcel", "type": "opencollective"}, "hasInstallScript": true}, "2.5.1": {"name": "@parcel/watcher", "version": "2.5.1", "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "optionalDependencies": {"@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1"}, "devDependencies": {"esbuild": "^0.19.8", "fs-extra": "^10.0.0", "husky": "^7.0.2", "lint-staged": "^11.1.2", "mocha": "^9.1.1", "napi-wasm": "^1.1.0", "prebuildify": "^6.0.1", "prettier": "^2.3.2"}, "dist": {"integrity": "sha512-dfUnCxiN9H4ap84DvD2ubjw+3vUNpstxa0TneY/Paat8a3R4uQZDLSvWjmznAY/DoahqTHl9V46HF/Zs3F29pg==", "shasum": "342507a9cfaaf172479a882309def1e991fb1200", "tarball": "https://registry.npmjs.org/@parcel/watcher/-/watcher-2.5.1.tgz", "fileCount": 45, "unpackedSize": 130794, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIHg1AUHGJTZhfPmaujtelKg5cmOExINx7PGGn6fJ2CAeAiBTH/lrPrXPiiWAhZ6m7S2MBFPvDYx06AgmHknWv+T3Ug=="}]}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "hasInstallScript": true}}, "modified": "2025-01-26T03:11:57.712Z"}