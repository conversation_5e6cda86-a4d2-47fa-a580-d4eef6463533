{"source": 1103625, "name": "aws-cdk-lib", "dependency": "aws-cdk-lib", "title": "aws-cdk-lib has Insertion of Sensitive Information into Log File vulnerability when using Cognito UserPoolClient Construct", "url": "https://github.com/advisories/GHSA-qq4x-c6h6-rfxh", "severity": "moderate", "versions": ["2.0.0-alpha.0", "2.0.0-alpha.1", "2.0.0-alpha.2", "2.0.0-alpha.3", "2.0.0-alpha.4", "2.0.0-alpha.5", "2.0.0-alpha.6", "2.0.0-alpha.10", "2.0.0-alpha.11", "2.0.0-alpha.12", "2.0.0-alpha.13", "2.0.0-alpha.14", "2.0.0-rc.1", "2.0.0-rc.3", "2.0.0-rc.4", "2.0.0-rc.5", "2.0.0-rc.6", "2.0.0-rc.7", "2.0.0-rc.8", "2.0.0-rc.9", "2.0.0-rc.10", "2.0.0-rc.11", "2.0.0-rc.12", "2.0.0-rc.13", "2.0.0-rc.14", "2.0.0-rc.15", "2.0.0-rc.16", "2.0.0-rc.17", "2.0.0-rc.18", "2.0.0-rc.19", "2.0.0-rc.20", "2.0.0-rc.21", "2.0.0-rc.22", "2.0.0-rc.23", "2.0.0-rc.24", "2.0.0-rc.25", "2.0.0-rc.26", "2.0.0-rc.27", "2.0.0-rc.28", "2.0.0-rc.29", "2.0.0-rc.30", "2.0.0-rc.31", "2.0.0-rc.32", "2.0.0-rc.33", "2.0.0", "2.1.0", "2.2.0", "2.3.0", "2.4.0", "2.5.0", "2.6.0", "2.7.0", "2.8.0", "2.9.0", "2.10.0", "2.11.0", "2.12.0", "2.13.0", "2.14.0", "2.15.0", "2.16.0", "2.17.0", "2.18.0", "2.19.0", "2.20.0", "2.21.0", "2.21.1", "2.22.0", "2.23.0", "2.24.0", "2.24.1", "2.25.0", "2.26.0", "2.27.0", "2.28.0", "2.28.1", "2.29.0", "2.29.1", "2.30.0", "2.31.0", "2.31.1", "2.31.2", "2.32.0", "2.32.1", "2.33.0", "2.34.0", "2.34.1", "2.34.2", "2.35.0", "2.36.0", "2.37.0", "2.37.1", "2.38.0", "2.38.1", "2.39.0", "2.39.1", "2.40.0", "2.41.0", "2.42.0", "2.42.1", "2.43.0", "2.43.1", "2.44.0", "2.45.0", "2.46.0", "2.47.0", "2.48.0", "2.49.0", "2.49.1", "2.50.0", "2.51.0", "2.51.1", "2.52.0", "2.52.1", "2.53.0", "2.54.0", "2.55.0", "2.55.1", "2.56.0", "2.56.1", "2.57.0", "2.58.0", "2.58.1", "2.59.0", "2.60.0", "2.61.0", "2.61.1", "2.62.0", "2.62.1", "2.62.2", "2.63.0", "2.63.1", "2.63.2", "2.64.0", "2.65.0", "2.66.0", "2.66.1", "2.67.0", "2.68.0", "2.69.0", "2.70.0", "2.71.0", "2.72.0", "2.72.1", "2.73.0", "2.74.0", "2.75.0", "2.75.1", "2.76.0", "2.77.0", "2.78.0", "2.79.0", "2.79.1", "2.80.0", "2.81.0", "2.82.0", "2.83.0", "2.83.1", "2.84.0", "2.85.0", "2.86.0", "2.87.0", "2.88.0", "2.89.0", "2.90.0", "2.91.0", "2.92.0", "2.93.0", "2.94.0", "2.95.0", "2.95.1", "2.96.0", "2.96.1", "2.96.2", "2.97.0", "2.97.1", "2.98.0", "2.99.0", "2.99.1", "2.100.0", "2.101.0", "2.101.1", "2.102.0", "2.102.1", "2.103.0", "2.103.1", "2.104.0", "2.105.0", "2.106.0", "2.106.1", "2.107.0", "2.108.0", "2.108.1", "2.109.0", "2.110.0", "2.110.1", "2.111.0", "2.112.0", "2.113.0", "2.114.0", "2.114.1", "2.115.0", "2.116.0", "2.116.1", "2.117.0", "2.118.0", "2.119.0", "2.120.0", "2.121.0", "2.121.1", "2.122.0", "2.123.0", "2.124.0", "2.125.0", "2.126.0", "2.127.0", "2.128.0", "2.129.0", "2.130.0", "2.131.0", "2.132.0", "2.132.1", "2.133.0", "2.134.0", "2.135.0", "2.136.0", "2.136.1", "2.137.0", "2.138.0", "2.139.0", "2.139.1", "2.140.0", "2.141.0", "2.142.0", "2.142.1", "2.143.0", "2.143.1", "2.144.0", "2.145.0", "2.146.0", "2.147.0", "2.147.1", "2.147.2", "2.147.3", "2.148.0", "2.148.1", "2.149.0", "2.150.0", "2.151.0", "2.151.1", "2.152.0", "2.153.0", "2.154.0", "2.154.1", "2.155.0", "2.156.0", "2.157.0", "2.158.0", "2.159.0", "2.159.1", "2.160.0", "2.161.0", "2.161.1", "2.162.0", "2.162.1", "2.163.0", "2.163.1", "2.164.0", "2.164.1", "2.165.0", "2.166.0", "2.167.0", "2.167.1", "2.167.2", "2.168.0", "2.169.0", "2.170.0", "2.171.0", "2.171.1", "2.172.0", "2.173.0", "2.173.1", "2.173.2", "2.173.3", "2.173.4", "2.174.0", "2.174.1", "2.175.0", "2.175.1", "2.176.0", "2.177.0", "2.178.0", "2.178.1", "2.178.2", "2.179.0", "2.180.0", "2.181.0", "2.181.1", "2.182.0", "2.183.0", "2.184.0", "2.184.1", "2.185.0", "2.186.0", "2.187.0", "2.188.0", "2.189.0", "2.189.1", "2.190.0", "2.191.0", "2.192.0", "2.193.0", "2.194.0", "2.195.0", "2.196.0", "2.196.1", "2.197.0", "2.198.0"], "vulnerableVersions": ["2.37.0", "2.37.1", "2.38.0", "2.38.1", "2.39.0", "2.39.1", "2.40.0", "2.41.0", "2.42.0", "2.42.1", "2.43.0", "2.43.1", "2.44.0", "2.45.0", "2.46.0", "2.47.0", "2.48.0", "2.49.0", "2.49.1", "2.50.0", "2.51.0", "2.51.1", "2.52.0", "2.52.1", "2.53.0", "2.54.0", "2.55.0", "2.55.1", "2.56.0", "2.56.1", "2.57.0", "2.58.0", "2.58.1", "2.59.0", "2.60.0", "2.61.0", "2.61.1", "2.62.0", "2.62.1", "2.62.2", "2.63.0", "2.63.1", "2.63.2", "2.64.0", "2.65.0", "2.66.0", "2.66.1", "2.67.0", "2.68.0", "2.69.0", "2.70.0", "2.71.0", "2.72.0", "2.72.1", "2.73.0", "2.74.0", "2.75.0", "2.75.1", "2.76.0", "2.77.0", "2.78.0", "2.79.0", "2.79.1", "2.80.0", "2.81.0", "2.82.0", "2.83.0", "2.83.1", "2.84.0", "2.85.0", "2.86.0", "2.87.0", "2.88.0", "2.89.0", "2.90.0", "2.91.0", "2.92.0", "2.93.0", "2.94.0", "2.95.0", "2.95.1", "2.96.0", "2.96.1", "2.96.2", "2.97.0", "2.97.1", "2.98.0", "2.99.0", "2.99.1", "2.100.0", "2.101.0", "2.101.1", "2.102.0", "2.102.1", "2.103.0", "2.103.1", "2.104.0", "2.105.0", "2.106.0", "2.106.1", "2.107.0", "2.108.0", "2.108.1", "2.109.0", "2.110.0", "2.110.1", "2.111.0", "2.112.0", "2.113.0", "2.114.0", "2.114.1", "2.115.0", "2.116.0", "2.116.1", "2.117.0", "2.118.0", "2.119.0", "2.120.0", "2.121.0", "2.121.1", "2.122.0", "2.123.0", "2.124.0", "2.125.0", "2.126.0", "2.127.0", "2.128.0", "2.129.0", "2.130.0", "2.131.0", "2.132.0", "2.132.1", "2.133.0", "2.134.0", "2.135.0", "2.136.0", "2.136.1", "2.137.0", "2.138.0", "2.139.0", "2.139.1", "2.140.0", "2.141.0", "2.142.0", "2.142.1", "2.143.0", "2.143.1", "2.144.0", "2.145.0", "2.146.0", "2.147.0", "2.147.1", "2.147.2", "2.147.3", "2.148.0", "2.148.1", "2.149.0", "2.150.0", "2.151.0", "2.151.1", "2.152.0", "2.153.0", "2.154.0", "2.154.1", "2.155.0", "2.156.0", "2.157.0", "2.158.0", "2.159.0", "2.159.1", "2.160.0", "2.161.0", "2.161.1", "2.162.0", "2.162.1", "2.163.0", "2.163.1", "2.164.0", "2.164.1", "2.165.0", "2.166.0", "2.167.0", "2.167.1", "2.167.2", "2.168.0", "2.169.0", "2.170.0", "2.171.0", "2.171.1", "2.172.0", "2.173.0", "2.173.1", "2.173.2", "2.173.3", "2.173.4", "2.174.0", "2.174.1", "2.175.0", "2.175.1", "2.176.0", "2.177.0", "2.178.0", "2.178.1", "2.178.2", "2.179.0", "2.180.0", "2.181.0", "2.181.1", "2.182.0", "2.183.0", "2.184.0", "2.184.1", "2.185.0", "2.186.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": ">=2.37.0 <2.187.0", "id": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg=="}