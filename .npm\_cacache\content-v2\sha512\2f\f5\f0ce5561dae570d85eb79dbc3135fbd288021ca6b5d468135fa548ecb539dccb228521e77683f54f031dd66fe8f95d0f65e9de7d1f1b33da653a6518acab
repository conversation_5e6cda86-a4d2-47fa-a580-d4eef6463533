{"source": "xDaNmlS3J8LRk7IfDb7e9Gp3gcy/xDr1QZXCKC997SMkqKVemm2guY9vQpRI6omD11L5cfCI+STpoAdxUgnVsA==", "name": "uncontrollable", "dependency": "@babel/runtime", "title": "Depends on vulnerable versions of @babel/runtime", "url": null, "severity": "moderate", "versions": ["1.0.0", "1.1.0", "1.1.1", "1.1.2", "1.1.3", "1.1.4", "1.2.0", "1.3.0", "1.3.1", "1.3.2", "1.4.0", "2.0.0", "3.0.0", "3.0.1", "3.0.2", "3.0.3", "3.1.0", "3.1.1", "3.1.2", "3.1.3", "3.1.4", "3.2.0", "3.2.1", "3.2.3", "3.2.4", "3.3.0", "3.3.1", "4.0.0", "4.0.1", "4.0.2", "4.0.3", "4.1.0", "5.0.0", "5.1.0", "6.0.0", "6.1.0", "6.2.2", "6.2.3", "7.0.0", "7.0.1", "7.0.2", "7.1.0", "7.1.1", "7.2.0", "7.2.1", "8.0.0", "8.0.1", "8.0.2", "8.0.3", "8.0.4", "9.0.0"], "vulnerableVersions": [], "cwe": ["CWE-1333"], "cvss": {"score": 6.2, "vectorString": "CVSS:3.1/AV:L/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<0.0.0-0", "id": "RDGC1FSy7XorAGnGPepwaSXxfLi1gu3l5iw4MqYTzRK7ozSO9ZffUnyCqqK9cFYHpsZcmivaZIWByJF08OJDCQ=="}