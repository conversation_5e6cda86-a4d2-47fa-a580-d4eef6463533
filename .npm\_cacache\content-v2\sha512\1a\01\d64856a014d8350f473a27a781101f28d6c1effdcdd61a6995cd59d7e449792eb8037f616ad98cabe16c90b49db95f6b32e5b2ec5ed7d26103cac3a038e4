{"name": "micromatch", "dist-tags": {"latest": "4.0.8"}, "versions": {"0.1.0": {"name": "micromatch", "version": "0.1.0", "devDependencies": {"mocha": "*", "should": "*"}, "dist": {"shasum": "f58fc1198dc1ba5b97cc2e301d28f0f3ee05ca2f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.1.0.tgz", "integrity": "sha512-GQRzYCrGSoSGGU3QgckEij+psNPgh2N/7EGbW7q92WE9jMmRjAsrEH4pcioy62cNFsOJP2jxURvfQ2O6jQbEMA==", "signatures": [{"sig": "MEQCIFAbX1/2+RqDVCvYAcvICtlS0QMgxVb0P+hp3jHn3tKnAiB20Bnle9M8i96wPZw019hL1G3fGZ2q6Vc703ZBYK8eZg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.2.0": {"name": "micromatch", "version": "0.2.0", "dependencies": {"braces": "^0.1.5", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}, "dist": {"shasum": "e0b82107687dfd6e4ff8f085c62adf8a61a24ae3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.0.tgz", "integrity": "sha512-vM+tW7aGPXW3tipt1SKW9rUnkAbcXVn8k+qlM2LFD4V3n9RdkdQoslfKwaXWfqaFRQ8NmVH5PUrW5czOewn/WQ==", "signatures": [{"sig": "MEUCIQD1ydC6JxOigfY6OPeyM7aTZDTt/SBeoG3pexyeVoYxnwIgHltI3fLyHOFcn69LXBiZr37HZ/e3k48Lyt8vxyzJmtc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.2.1": {"name": "micromatch", "version": "0.2.1", "dependencies": {"braces": "^0.1.5", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}, "dist": {"shasum": "2a8684d31401b7d4d4fd70890eda6ab53b3f8260", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.1.tgz", "integrity": "sha512-zsOlEJzty2Qa2FwqDmLb1n9bTx22omw617rWbejid4ikoFQy+aMhYuty11CBvUvspABurrCsL2ql2xZCeTrOGg==", "signatures": [{"sig": "MEUCIQCD/b4XixJMn24mXt0yX6xoHMsKeyWnTFYFrxNxEaC+7wIgbTL0iZki9OhSQBoDyWUo6rGnrXi0QttYrvQCjIu43Jw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "0.2.2": {"name": "micromatch", "version": "0.2.2", "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^0.2.2", "arr-union": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}, "dist": {"shasum": "228dc16354377e1b2dc2e4114b7fc53219b8ae57", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-0.2.2.tgz", "integrity": "sha512-E7LJruEuTXuqjc8g1trRpxh1Ak9DQOj3CedFRFhpNi6KRwYPC5xDJg/dudWkkTsnNYdmonoC5d1X64hGsjP/sg==", "signatures": [{"sig": "MEYCIQDRuCiXSZA7Wps4PfJVLuCjZDw1viOCb6v0kdFiCUxjIQIhAOKiCHdA7ekgzgh/BXEhzcEAtw5mr4I8niAfqNe4Dp1D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.0": {"name": "micromatch", "version": "1.0.0", "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^1.0.1", "arr-union": "^1.0.0", "filename-regex": "^0.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}, "dist": {"shasum": "57ddb18dc0edfb6ee882a77581afb61970906b85", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.0.0.tgz", "integrity": "sha512-5XdaJhe3HhSWkvvHy729IhIaLqccppEckCBAXXmOOzeimGekqLOXCisJpXh6Yek+QdvVXgOtdqK0BRPUI5wUdA==", "signatures": [{"sig": "MEQCIC7P8d/ZYzVnO65ZV+QE3s+BYVvZTN2MgHEJ2brv88xOAiBDOsNCurdzJpx6qa+7Kk6WfuYa/vRiZQLrbssBplOn1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.0.1": {"name": "micromatch", "version": "1.0.1", "dependencies": {"braces": "^1.0.0", "unixify": "^0.1.0", "arr-diff": "^1.0.1", "arr-union": "^1.0.0", "filename-regex": "^0.1.0"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "*", "write": "^0.1.1", "should": "*", "minimist": "^1.1.0", "minimatch": "^2.0.1", "benchmarked": "^0.1.3"}, "dist": {"shasum": "d22539682fdd903b70df220018d8ac0defeb8434", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.0.1.tgz", "integrity": "sha512-juRHcNORljLfREsoh0AW9oTq8ViIssD4VQzhwiPJEMWyOHLXC9y5CEIUQ72TIjgQQPrPWnXIoL17Or4Z9KeKOg==", "signatures": [{"sig": "MEQCIG4vRT8Lrc0bsW1E/fcp5s2vZoqao2LmM20y0zYZbYAOAiBwAHeHWFrGvNVQKzb//5QDQrBG0FsRDEzr8N1wFBA0TA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.2.0": {"name": "micromatch", "version": "1.2.0", "dependencies": {"braces": "^1.4.0", "is-glob": "^1.1.0", "arr-diff": "^1.0.1", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "771a95449e4c2573f4c62df684c9a3fe430ad226", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.2.0.tgz", "integrity": "sha512-jg3y77ZoUZtsAs/OPzIRoSx75piKdZymF2L8sz0uSUJG18w5LsmaQNgV9ZdGsJunyKndAFa/fgJFFazKN3yxUA==", "signatures": [{"sig": "MEUCIQDLNNIp6IXXaROUBwJXol0pWNGjbn7M0c7jz9Ii5eX9pwIgMDKsc/yz9rtMJkSZSCt+nK8eCVzxb93+8noCaIrmRaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.2.2": {"name": "micromatch", "version": "1.2.2", "dependencies": {"braces": "^1.6.0", "is-glob": "^1.1.0", "arr-diff": "^1.0.1", "filename-regex": "^2.0.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "d38c0ea77731a1714c46d273469cd31322ea02ef", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.2.2.tgz", "integrity": "sha512-ZLecoVApC2mZVyf++chmRd0c9j7Csj/xMQv7gX7PRdC5CWkmn8aRRvilazTp3ZEJ+Ma8xpa9p6jbDx3AFYrRXg==", "signatures": [{"sig": "MEUCIGme+UKyy4cfk6Mcw9ZaSFg2BZ6VW3RhEVfzyrOIw2ArAiEAjSxjPMWr8JWIvFok6C2NUhN4Jr8s8g3SKhKRR/tz3lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.0": {"name": "micromatch", "version": "1.3.0", "dependencies": {"braces": "^1.6.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "62099439ad54fcad188dc013e184eb7261c68f55", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.0.tgz", "integrity": "sha512-b1TBX7YDexNCqZwtxrRy5yTXbBxA8O1GEv1i4d218OdIdIjOBr7z+xQzkn27Qaxm5FbkB8tN4t5mxtO0RZzOVQ==", "signatures": [{"sig": "MEUCIQCsMpSps4V8HS7KKFKmti4AJodQNPm/ukPc41yKMx9yRgIgLt8S0G8D0TWToo3Fi0O464rNB89mshsNyOGA3jgQwiE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.1": {"name": "micromatch", "version": "1.3.1", "dependencies": {"braces": "^1.6.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.0", "glob-path-regex": "^1.0.0"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "f96117bc7c34062da5cebd721af5c40b402052cd", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.1.tgz", "integrity": "sha512-z5/VwgKwSZNcjsVqtaqgGamNYJzxOM1EKHICHkWUjhBe+P3Z+/EUf0Ak0KnbCBCTt0U4CCZksyurrspGaXxgsA==", "signatures": [{"sig": "MEUCIFvjmisMnG2HiHy9WjV9FvvEC5iRPxnXjvW5TvXnJ3nrAiEA21jgrsezns/oVPMr57bEOZYDMFxjwMu4k0x2iYgZZ6g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.2": {"name": "micromatch", "version": "1.3.2", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "5706c4cb24c7040fc3f57a2b464f76f112a5b6b9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.2.tgz", "integrity": "sha512-317eXxI7Ql8ucklUPqigrllSwFOYfQHKWrFMZMhPYj69JfxUaKyIeEFbjX5l8oMxg3J7CXrzTM/emt8FP2HLeA==", "signatures": [{"sig": "MEUCIQC3hQOAg3mdVZ3jZe6Wjm05cayPBWgz6oU5RN9fMD69zQIgB1RvgX6xuHcXfe65w6FkSmFbhsIuFMnF1efgrZZ1USY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.3.3": {"name": "micromatch", "version": "1.3.3", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^1.2.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "86985e4d3dc01d71cab4ec63a3e317c2d129d268", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.3.3.tgz", "integrity": "sha512-k0wScZxxJwPc5on6eBJn0Z3m1LfbSEYpZVWnisV8je1xUlzQm7Og2RFfSDM0FzKbIGU+38IW9rW5QKCZK5mJNA==", "signatures": [{"sig": "MEUCIQD9I3q14wWuAvSAXiBzyZF5Li7lmxQy3VHy0E26XlstEAIgMFwm4nMtjRFpCAHT5r8rdg7bncripGjnWtTUj05ixO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.0": {"name": "micromatch", "version": "1.4.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "13f9f50b37d31a7138f5c1cdc1e72769083505c5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.0.tgz", "integrity": "sha512-ibVVM8/ZXS3HN6rQPF7dgsnn25E9aVF+1kNNqJ9JqQuOksYicAXznF/MOu2gxSa2BvKLpW119+8ileZpqEpdjQ==", "signatures": [{"sig": "MEUCIEHGOjZJQysFXwP9bAZwLHFP6spnPEZTbzMQ+VQEqPmrAiEArQDYZ+BI0bSRVQrgIg/RvvtbafwgdPJ+wcRe8ygRbdo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.1": {"name": "micromatch", "version": "1.4.1", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "cc7a81a85441657fdc36de1db9235724e55a6124", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.1.tgz", "integrity": "sha512-zzEWNHeBUbNWPhCFz5r7VZ4npFiTEPVgREtAT/KhRHnVvK14dCs9n+tYZE+o3jeG48Ld6apXkny5w0DvckUzWQ==", "signatures": [{"sig": "MEQCIF3xJoZgFotpsGuNLNH+QBZLOonYjFdx2w0sC+dc7YM+AiB8hN/VzNQaHBC8QFfI1xvMTo38JtL+JLRM7wZ3L13nPQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.2": {"name": "micromatch", "version": "1.4.2", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.0", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.0", "regex-cache": "^0.2.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^4.6.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "1b1d1b9ef357c01a33239b170894709b037fc085", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.2.tgz", "integrity": "sha512-VNu46T/+GcM841nBmivGaH/j7h/U4IYvlZXgBTD2SjQtj5D59MhjPDStWfggqeflZZqqSCaw1BcyA8ZzDkMysQ==", "signatures": [{"sig": "MEUCIQCnLdq5DVOoc5Z3N0JQ89X7npV1IAguFQkG1J/bTAbXfwIgA49aNXcCJQAIaUG8S9/sxGXNBz8m4ROaHhb+peezWRo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.3": {"name": "micromatch", "version": "1.4.3", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "853731f751b2ca52e04fdf8ee429e997c1d488f5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.3.tgz", "integrity": "sha512-QrM7/3azDMr6wx++iAlfJOCIXdm1Esk4dpkyRCR1/ApMgu3wO5XjCfjCJmIc8btt7HFjaATKAqAeKZ3BIS1Wiw==", "signatures": [{"sig": "MEUCIQD8Riz8+e8lB1ZLaHhIoeUmcEbCaoCF0rZuYKtwzeqLEQIgWAGFQNl2VGbbTcQRYkZlg8rY5rkXOb/o+QhRJz35xlg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.4": {"name": "micromatch", "version": "1.4.4", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "24b2fe56e81ff7701e6290f39a1e88d143402b1f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.4.tgz", "integrity": "sha512-C6mVKf8OPUIqdVGlShQ+5d8jjicC72Qe2maAg7Treiwr6uEwDsPsAF/gG4hmv0GWo6te0g3M0ui9z6ld1s5SbA==", "signatures": [{"sig": "MEUCIFCPUbLE41VAAP/xSh8Mnj+TBAa4UGiilLwMit/aNJjyAiEA2eviqAsvOZgK43jvwMto4WzFAYNE2UVasSad6AKlUiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.4.5": {"name": "micromatch", "version": "1.4.5", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^0.5.1", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "eced3fa4cc87a8a8f32c142e238c17670600769f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.4.5.tgz", "integrity": "sha512-BgoJz5iwA+8cw3XUZL2eIFuZcpcPUcZDCfGwhyOCoUnem5yhAq3hDNCfU9h0ma8spNZxBbuCM6JvKd7z56OVtA==", "signatures": [{"sig": "MEUCIQDyXba9VSRsl3TV8vAItTZVoyjkhkvvdKuoOwgf+BVkzQIgDUMIVSmnazyYu+02xo/HhF+MiCO+Vb1hEpe97ZQ3g7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.5.0": {"name": "micromatch", "version": "1.5.0", "dependencies": {"braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "50bd67d41604ad1b749249c055abadee914a5ebb", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.5.0.tgz", "integrity": "sha512-FHsVECj5JHEYp/XTU8bJGxJ30YUpBMY1hMMTLxQMCriDFjvox5O3S6Ze/m6rM71D9J24hL3iK3NHsw9cFoIi6A==", "signatures": [{"sig": "MEUCIQDpsZ4YkkoNQ25W5FT/oizhNEiZywk3z2S1FHn3bS/UMwIgSUPJhNMLa0QniFHNd+e8JfPzd72CxPCOF/YhKM/C2Kc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.0": {"name": "micromatch", "version": "1.6.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "0e5c87d8ea63a02da6d5706c9c4cc3c753129bfc", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.0.tgz", "integrity": "sha512-RvJfr0+4VSJFc2LhJu+VOaSjQwvMbDoYFssaJspunSOHHjnn7C57JPNY7SBd8wUBLiAhBKjYiZ/cyw81se9QGg==", "signatures": [{"sig": "MEUCIQCBpfsSaXsjbpgmLlHAbvbW6u+ihlaP2XDg73ro70hJKAIgVyEARVQgPlwgr/L+CfSEGvMhVHLxxYxptWSHVyubFRI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.1": {"name": "micromatch", "version": "1.6.1", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.0.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "be9c756f85a6c04f2839625936f37eebd1aa3231", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.1.tgz", "integrity": "sha512-6xfdEs0lyvcXVjOa6egEb+LyIRux0OuiimDKnMtzYLZuD6FiYDorTGQgg30XrsMVGm3ZIYMmLkFY+2zgg+kd1w==", "signatures": [{"sig": "MEYCIQDHM1P1sBAZlLHBE/Eg2+i4sVAx0Wq7kYOOW5oYAWEhuwIhAIXz7MMMqE0lVDLbvPeC75HxsZemVcJ1jZ4QPk/Kr0wv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "1.6.2": {"name": "micromatch", "version": "1.6.2", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.1.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "38fab47076aacece6ead77ef38472392f7b4bfb9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-1.6.2.tgz", "integrity": "sha512-aq9dbJx8tG1narL9gpZKV5QoVobLgwNreir/X7tqZpS+b+7RjAYUqLYk+8DP5iZ8bw7iHmpNiuQ87dbU4WpeuQ==", "signatures": [{"sig": "MEYCIQC5RSbGJjViuaHl1H+2u1TLxDy39l5WOta9FwGrj2gPxgIhANHRGXCw+uQ6m6v2+Zb5DkdQeNsBaBzF3DFCGZ/D6S7X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "micromatch", "version": "2.0.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "extglob": "^0.2.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^2.1.1", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "fa102f0e510e0b7861987cdb53e22526448da1a4", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.0.0.tgz", "integrity": "sha512-CU9ZaGKPEkL7hUtzaBYporDZkYCOjviXVJ6gwByyRpnSNgcZdMh2HwRlG6XKPt1tXRM6ErXD272xvMn1Et5OGw==", "signatures": [{"sig": "MEUCIQC2/pNojBNPlU/PjxDlCu4fXZISb1ZQDBng0ihlr13p9QIgIo7n9w1uibqapcVrflNT2gZYQ8L/hx2HWBhp+43/cHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.0": {"name": "micromatch", "version": "2.1.0", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "48e749678e84b51616045c63c8c1dd1f1773495a", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.0.tgz", "integrity": "sha512-zKMrPQVCAx6xEiewqWy5qbv+cMlYC1RgUfTvq9E5oUdtnhdfVEabV/5dYXuCoLeHFUDQvLJ1trKV/NY90UZyrw==", "signatures": [{"sig": "MEYCIQC88k00K/SN6485LhpG4/oHeieVyGbzBXFcnho5AtNH9gIhAN/hXy5OJ0VZOapeuSGELtZkqEuqZ+GeMxRxVaPe5vmj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.1": {"name": "micromatch", "version": "2.1.1", "dependencies": {"debug": "^2.1.2", "braces": "^1.7.0", "is-glob": "^1.1.1", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3", "helper-reflinks": "^0.4.0"}, "dist": {"shasum": "545300a2564bfbd579fc499c95f99d8a7aed19d9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.1.tgz", "integrity": "sha512-hL6jjzR+N4/ODDcUPdGV8+I/kPw7zfoGQvOgukHc7i3K1LuAp2Cn4v+Ec8WLC0GC1xO1iFSZLqRr0cq63tfbIA==", "signatures": [{"sig": "MEUCIQDJ4mONz6JAxJBV0KpqWO36ZaSTA+cNdOuUL7ZC3oEiXAIgd7eWmybv7TcAfo3kPqfW+cZkjBVEXMyYiQXglgQ8Nw0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.2": {"name": "micromatch", "version": "2.1.2", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}, "dist": {"shasum": "d86316f5b713cce7ac07474fb55971918fb4f48c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.2.tgz", "integrity": "sha512-Q0JVZ8YUWBFZGTZ96v50RSMvrsh0al8tLQFXg5jW0JmwrWj6t2XdFCu7hcaLN6Bp5XJtI6KawmmkPSgr2GhLsg==", "signatures": [{"sig": "MEQCIBwo4IK3ZOFX83tlWr6sHmQCDNrMXl5e4g0ybd2jADOFAiBgfFqzGLFezhasqpZwAYPjvK4nXkndld4JYdSH3A9UGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.3": {"name": "micromatch", "version": "2.1.3", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}, "dist": {"shasum": "8439bc163e8e12949b67b7e3382675e6c4448892", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.3.tgz", "integrity": "sha512-gdLZ5V+NVHKDZ764rFMfqw8x1qZnCWN4y1hgu8eDsW/5L3OzKYKgec7ABVlpGBNmDteleUmgFbYKzxSshThZvg==", "signatures": [{"sig": "MEUCIGol0EMB+j1xbzvNjwPop30RoBmmZgYXtD4uyURhiVRyAiEAsd5yqUT7Mg5E/RLLYDOnxV4bRNSlMj/b1HJ0aqTj+PQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.4": {"name": "micromatch", "version": "2.1.4", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}, "dist": {"shasum": "4e23f89ae0fa4fd72eac59261fbf7bb41970cdc8", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.4.tgz", "integrity": "sha512-Td1DYyBCj7ugwWF+IZRtiwImrkNFUEZ4w1WHKEMknKWQJlzxmL4Zya5wtiMFHgbUUEeb81m0QG/Mj38fUbObTA==", "signatures": [{"sig": "MEUCIQCgjD9+f1W0C8CUjDN8NG4QNPCME6/LVacC9ertnICHfAIgb+h3wIFXFY0Zi3HyoM7QpLWxdrxGXd+rCgOa2D7PuSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.5": {"name": "micromatch", "version": "2.1.5", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.3.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}, "dist": {"shasum": "e356977873e69f94de02439355978f4a26e8849b", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.5.tgz", "integrity": "sha512-FYjzTY02TJXwvxfzEdELtZC+G/wz/KbHUZVnCorE6DNbbNZsxLGZkTXhjmbz5I2ROJtCIdAgiJKVZrB/Q3grMw==", "signatures": [{"sig": "MEUCIA1srSt5hhP5DsRucK9IA/ClQ94f7J/h3139PXY7s+zlAiEA4oXymKrHQgnDC/xs0W58Ypz/Hf1ar64+fWTUeZeVpvI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.1.6": {"name": "micromatch", "version": "2.1.6", "dependencies": {"debug": "^2.1.3", "braces": "^1.8.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.0", "object.omit": "^0.2.1", "regex-cache": "^0.4.0", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.1.0", "write": "^0.1.1", "should": "^5.0.1", "minimist": "^1.1.0", "minimatch": "^2.0.1", "browserify": "^9.0.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.3"}, "dist": {"shasum": "51a65a9dcbfb92113292a071e04da35a81e9050e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.1.6.tgz", "integrity": "sha512-b6Pr6gJmAZciSjQjFSLXQs9lEs3iY2D6S/uS14gYW9lWYVmCybBPx5XbvTF10BXH8B+jXgWfI5jzsvFxqC8P7w==", "signatures": [{"sig": "MEQCIFZhjU2g50clQKO+IjYJeW64rJOsaXtshw+6V2sWqNSHAiAsc6MPE6l+xxGFRmE+itw5YAZ1upTSp3KNXGccojWMaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.2.0": {"name": "micromatch", "version": "2.2.0", "dependencies": {"braces": "^1.8.0", "extglob": "^0.3.0", "is-glob": "^1.1.3", "kind-of": "^1.1.0", "arr-diff": "^1.0.1", "parse-glob": "^3.0.1", "object.omit": "^1.1.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "expand-brackets": "^0.1.1"}, "devDependencies": {"chalk": "^1.0.0", "mocha": "^2.2.4", "write": "^0.2.0", "should": "^6.0.1", "minimist": "^1.1.1", "minimatch": "^2.0.4", "browserify": "^9.0.8", "multimatch": "^2.0.0", "benchmarked": "^0.1.4"}, "dist": {"shasum": "e7281bf971100827b890e375d994f12034898ff5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.2.0.tgz", "integrity": "sha512-C+bwIqRp177ofuqmibBPxudfy9J4IHY+I/U60ocDbQOoY1H1nj9nL8uN+D9M402/qnQ4LZgv9BLOIWDVxxwaHA==", "signatures": [{"sig": "MEYCIQCv7V94J0tgtFf1K5GK3Ap25f2B6AzJvpp5Fsb73vDjFwIhAMkGAbyAKUi9K7FMCJvIARpGvP71e+3F33J7tWDcpMo9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.0": {"name": "micromatch", "version": "2.3.0", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "browserify": "^11.2.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "951b2c4468c5d77885a6df558ea9a33823e7d238", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.0.tgz", "integrity": "sha512-imzLEnZs3omGptcUytU1GCt7XsA/WZwDg/TIX4ApSCl+JwqFFJedlHRK2yLHj9uPo1vlL8Nw2DR8zFcgUoR8+g==", "signatures": [{"sig": "MEUCICMUjJuTUOkup3fbGZ+faMGFESH6ahw6HLhgRpJf/BvPAiEAkv6gwxuc9GMt/qbCD6gW93m/A8pMc7f41SJlwIF3M94=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.1": {"name": "micromatch", "version": "2.3.1", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "browserify": "^11.2.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "f9cadd05bf513511288f0f22b89b84e0e93c1646", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.1.tgz", "integrity": "sha512-hjBW51RnqXaFrjCSAe+8Ligdp21bDPTh4i+rlQ4qWXYP2eygnrQeKuCa4l8e2Fymmrm3eaCPH36jUCa3RkSYlA==", "signatures": [{"sig": "MEYCIQC7Gocp+lbslbCvIFSFPaZDTUOjf3coZ9ZtXWZRg3NmQgIhAOWFfWn2MJg5NgxYJTdyyKXRBMmefg1/QYce5eU6UiS9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.2": {"name": "micromatch", "version": "2.3.2", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "fec97df21776b01ed72bdff7295f64072b6e2d42", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.2.tgz", "integrity": "sha512-7lDNvYe0dzH7fsyYpLtjCV7Pz9aDrpAUjyFeCeBwLiaSWBnxbAfBLmc3pB/VyoqVEVja4PrAZdmr22P473ZiTw==", "signatures": [{"sig": "MEYCIQDF5dUVRk//EBlMTvCigVYsWabsKWBXJoQSJMlvJknlzgIhAJvQ4SAP7n+Xz7RUfgvgV3KZ3+daV1gqgc4dH9ZNrqfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.3": {"name": "micromatch", "version": "2.3.3", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^1.1.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "f8357e265f5768f2ca799a3241d1995788d31648", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.3.tgz", "integrity": "sha512-2ijwu2YhIOQ9FCmdZaLdtq8BvE4MTek7RuRu/wdg206/IpJKFmFxJU7TRyJ+vE9J0MauMUAShWUFykLlKl7pRg==", "signatures": [{"sig": "MEQCIGhsZ3OSV7N12sw4GD4XQjqzLaHR03sJp1H0LflBXLSbAiAAuOkhwPYGUN89l2b1LOWWdC92iCVRc5njB0oGBC/s9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.4": {"name": "micromatch", "version": "2.3.4", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^2.0.1", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "b17874eb365b371065fcd44c27d6e1585638ae4c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.4.tgz", "integrity": "sha512-TaQx3Nw4E5HTt//Q1YlwVBFfckcwTfDD9BLGpnx5jBIqkPe0DWa4P+KpVHuMdsJ53SfoK8gxjyiJDwASR1ycxw==", "signatures": [{"sig": "MEUCIAeJk6nJbRGlaZPEEmDBjxbefQf9s0k6pbhG96Q5HzLKAiEAl2lEYqxc63z/xyUeVGNbl4e/dOx/vRQjv7MHhit7gyw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.5": {"name": "micromatch", "version": "2.3.5", "dependencies": {"braces": "^1.8.1", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^0.2.3", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.0", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2.3.3", "write": "^0.2.1", "should": "^7.1.0", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-jshint": "^1.11.2", "gulp-istanbul": "^0.10.1", "jshint-stylish": "^2.0.1"}, "dist": {"shasum": "d8dfed89e28419d073489be55c33f0b05c273217", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.5.tgz", "integrity": "sha512-Xm9hdaMOJrGA+hxR6IpiBxrMDkrzYXgaNJMz8Vst/wb1kv0SigQwFS4wWBvQ0+Hb0afwitHYNe23qTcO3PCz1w==", "signatures": [{"sig": "MEQCIH5+6lb8fGh3J9XNNx5gKuYIkEQDAbTAPTbLf+O3zDfXAiBjs27//7rqZ6KCX1oXfbXWmTyoQz3g/iHxiDadPlnn0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.6": {"name": "micromatch", "version": "2.3.6", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "lazy-cache": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "*", "write": "^0.2.1", "should": "*", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1"}, "dist": {"shasum": "67ced00422d36c9f8bb01d391fe69637b45aa866", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.6.tgz", "integrity": "sha512-hee<PERSON><PERSON><PERSON>cib3225WcK6d822rW5wlbDkdhjkZbPWkGNeJQ/EvCSlkFBEdnDY5lGn3AW8TGcS0lkmOXOR/KVXcWcLw==", "signatures": [{"sig": "MEQCIEvmvJp57+dEahm9STS2OaZvsH6cb29UsY7XRDJV0VuTAiA4vmVlYx0xXkrfnj6ZrVMO+Iket4yAfPHxnvtIJ7RxkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.7": {"name": "micromatch", "version": "2.3.7", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "*", "write": "^0.2.1", "should": "*", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1"}, "dist": {"shasum": "2f2e85ef46140dbea6cb55e739b6b11b30eaa509", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.7.tgz", "integrity": "sha512-G0mcWeWiyuIQIrb1q+PW2IavR10QlVEYCKpw7R8FjkzhAOSwtj633GCAhQrx1uLQtfc1h3CQWazD8zv+Rf9C4A==", "signatures": [{"sig": "MEUCIHbyzyeqkD2wli+AOLz1qWIYYftcBrP1LURmu6OERuD+AiEAz9Bv3bh6PqYEV/lVR/BFYwdJCBMXxwAmyDIJUehJL5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.8": {"name": "micromatch", "version": "2.3.8", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "dist": {"shasum": "94fbf8f37ed9edeca06bf1c8f7b743fb5f6f5854", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.8.tgz", "integrity": "sha512-PQOHX68M7tTh3MiVCxD0ZqaFLlFCr2akgLvAaMCW1aqSi+eaCJbPsJvRNZ95aC3TsQUUziKVKPjMDXJbIInohA==", "signatures": [{"sig": "MEYCIQCMOZU04hqycyxzBhPHHffkccml/i6SQPGbTZ8BCazOHwIhALWBqYZDEoPC1Gx4rvivdK2y6AAGUdZiYRJoyLuI0GQK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.9": {"name": "micromatch", "version": "2.3.9", "dependencies": {"braces": "^1.8.4", "extglob": "^0.3.2", "is-glob": "^2.0.1", "kind-of": "^3.0.3", "arr-diff": "^3.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.3", "array-unique": "^0.2.1", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.5"}, "devDependencies": {"gulp": "^3.9.1", "chalk": "^1.1.3", "mocha": "^2.4.5", "write": "^0.3.1", "should": "^8.3.1", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.2.0", "multimatch": "^2.1.0", "benchmarked": "^0.2.5", "gulp-eslint": "^2.0.0", "gulp-istanbul": "^0.10.4", "gulp-format-md": "^0.1.9"}, "dist": {"shasum": "26370d1989d8029f91034ab667f5f020ccd4f8fd", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.9.tgz", "integrity": "sha512-QZU30YOYs0E+4Y/CC1QgqBqmeY3RmH2PYwPpyS5e43+I1xvSnJF/p6iBoBzrnLDVm1vjKLOHx8fnXU3T4u3OlQ==", "signatures": [{"sig": "MEYCIQCVM2mA0VjeUYZyHE7ADrWn5SE+mzNykowyOZ6HzG5JdwIhAP/f/r7qeqzjPJVYB/6z4Lb3u1zUicQgb0TtV1PApF7V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.10": {"name": "micromatch", "version": "2.3.10", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "dist": {"shasum": "f4fb3175beec62795a7b8c24d5f745c3680660ab", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.10.tgz", "integrity": "sha512-s4QloPs9pCkHMwjH3NMvZAXpCXEizLcR7QN1+fkoPmmAFx34ukor4ZdN9onkfQrOOye+Z44n2DSJ1vw4Vr7Atw==", "signatures": [{"sig": "MEQCIHzuW7DH73loLHmYyiWfjla2lr13xva85VKriaIaulplAiA5XxNayntnbwIaI8XKDekVu4Ynmu+s/FFH+vo61kom9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.3.11": {"name": "micromatch", "version": "2.3.11", "dependencies": {"braces": "^1.8.2", "extglob": "^0.3.1", "is-glob": "^2.0.1", "kind-of": "^3.0.2", "arr-diff": "^2.0.0", "is-extglob": "^1.0.0", "parse-glob": "^3.0.4", "object.omit": "^2.0.0", "regex-cache": "^0.4.2", "array-unique": "^0.2.1", "filename-regex": "^2.0.0", "normalize-path": "^2.0.1", "expand-brackets": "^0.1.4"}, "devDependencies": {"gulp": "^3.9.0", "chalk": "^1.1.1", "mocha": "^2", "write": "^0.2.1", "should": "^8", "minimist": "^1.2.0", "minimatch": "^3.0.0", "gulp-mocha": "^2.1.3", "multimatch": "^2.0.0", "benchmarked": "^0.1.4", "gulp-eslint": "^1.1.1", "gulp-istanbul": "^0.10.1", "gulp-format-md": "^0.1.8"}, "dist": {"shasum": "86677c97d1720b363431d04d0d15293bd38c1565", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-2.3.11.tgz", "integrity": "sha512-LnU2XFEk9xxSJ6rfgAry/ty5qwUTyHYOBU0g4R6tIw5ljwgGIBmiKhRWLw5NpMOnrgUNcDJ4WMp8rl3sYVHLNA==", "signatures": [{"sig": "MEUCIQCJr3I542G0h7b8tAUZ8AequLEIJui8oUKLmGD/7QE00gIgRSuGepu/6pvjkZKmCBDzg/A6Wl01aZUJ235qcBQBSSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "micromatch", "version": "3.0.0", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "extend-shallow": "^2.0.1"}, "dist": {"shasum": "c926f11cd75e887dc3b82968909575280731049d", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.0.tgz", "integrity": "sha512-jXbEGkzAwEzFuofLH4JGjM+o1puUK0KlJcLYc5t7zXuutGC0+mRKVqKU264ZQzY6nf/lwTqX2JxOMc7D5lRIjw==", "signatures": [{"sig": "MEQCIHGKknQAA/Ac0MAcIQQ/W16q9uNBe1nIuTqBKwhv6YFnAiBatkIwTVeHmqw0jZY3W42X1KwiwM814WMk0L29OF3rEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.1": {"name": "micromatch", "version": "3.0.1", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "extend-shallow": "^2.0.1"}, "dist": {"shasum": "2e1bd0ceda6cf78d04b14d281c922129e48ef323", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.1.tgz", "integrity": "sha512-NzCifOxXgufa3ZUdTdOqvPlFsSoqn9CwSEbID5lM5HhvBKg+NozKqkSA39yuP7EE2qlRtNf4qji0Iw4WpVvvZQ==", "signatures": [{"sig": "MEUCIQDiomHRe82ezZkb6d//qgePWMeTufl6k9BE2rO0Rgs+CwIgJS+Sa9kAqjGBkLr6E/C95G6/pkj+sVPAE8Z7vXfIpJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.2": {"name": "micromatch", "version": "3.0.2", "dependencies": {"braces": "^2.0.3", "extglob": "^1.1.0", "kind-of": "^3.1.0", "arr-diff": "^3.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.1.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^0.2.5"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.3", "bash-match": "^0.2.0", "gulp-mocha": "^3.0.0", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "dist": {"shasum": "bf77b8c860f342d73ac96bf4c495ed3cec05875c", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.2.tgz", "integrity": "sha512-DJPmTvzNtZ7PExARW6ovsLRP6rxkv6IkdZLVhD8hpfduvMKNRjl2k7+CwqEvURC3R45BVex8TTPeA/YDVEoggg==", "signatures": [{"sig": "MEYCIQC3ss69oCHh9ZlI741njwAVvbzYvftEiyWSjSvNgJek8QIhAPzOK6ZQMD7hQeYq3xUJalNrUHTDEbN1HkNqvw1Wf/E5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.3": {"name": "micromatch", "version": "3.0.3", "dependencies": {"braces": "^2.2.2", "extglob": "^1.1.0", "kind-of": "^4.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^4.3.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "dist": {"shasum": "af3339640157ddad39b81a09956d8877cc4b421a", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.3.tgz", "integrity": "sha512-uTjtugrI0sC0KbJNLZtCmyrg5oFESH++4QqLUdHLrKugDbCz+9V5983YfjpfJhmxqf5f2H+JV9JJhS0brPS9TA==", "signatures": [{"sig": "MEYCIQC9Xh01MQxoBRVfJCcfJP7KuqiTpxcwPLh9bOOH8OAMBgIhANFDbZTYyor+jTURWB1+MdL0b+vX2pLG/cn0jt6jTc05", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.4": {"name": "micromatch", "version": "3.0.4", "dependencies": {"braces": "^2.2.2", "extglob": "^1.1.0", "kind-of": "^4.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.2.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.4.2", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^4.3.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.1", "gulp-format-md": "^0.1.12"}, "dist": {"shasum": "1543f1d04813447ac852001c5f5a933401786d1d", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.4.tgz", "integrity": "sha512-W07uHmC2/7xleHWcYgg4iASgcUSY9TCBnap3gAn+331X7hMQKyRDGzcHzMsadQKa5KNcM/55hFocVggIyTuVFQ==", "signatures": [{"sig": "MEYCIQC9vLItAZxDXKfFO9QzqShFjuMJXiwxDAu7LKeo2tKPogIhANUNtz8kB3joUMSu05zDVBSVCLEaXs83yfME8zdSa2DK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.5": {"name": "micromatch", "version": "3.0.5", "dependencies": {"braces": "^2.2.2", "extglob": "^2.0.0", "kind-of": "^5.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "99717da26f83c050cd712ab5ea59f71821f758f9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.0.5.tgz", "integrity": "sha512-NszdHwRzt/Kj4ee6W0TArIZ0kPN48pUN8jXTdzz6iDpJ16fxiYjB1KjMI138S450+9iKqXcmpY4MTT5pB317Rg==", "signatures": [{"sig": "MEUCIFkLs3TYPZxtN/r6RtkSon0o8SJTaaD7qD18q89itdaqAiEAnKW8MwHuQhn5lYjkCNg29vQR89SpMlQLavYZGe1T1dI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.0": {"name": "micromatch", "version": "3.1.0", "dependencies": {"braces": "^2.2.2", "extglob": "^2.0.2", "kind-of": "^5.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "5102d4eaf20b6997d6008e3acfe1c44a3fa815e2", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.0.tgz", "integrity": "sha512-3StSelAE+hnRvMs8IdVW7Uhk8CVed5tp+kLLGlBP6WiRAXS21GPGu/Nat4WNPXj2Eoc24B02SaeoyozPMfj0/g==", "signatures": [{"sig": "MEYCIQCvS7yKe9ddx1MWNu/OwhKmdQo15nTBDvz7HBVHj5vAbQIhAIK7ILXZppSNxzQDhXNuMCrGInnN2Wlo788kcaUSbB6A", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.1": {"name": "micromatch", "version": "3.1.1", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.4", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^4.0.1", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "1c491537524916fb5b60c8f6cccdf25fceaa0def", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.1.tgz", "integrity": "sha512-wSN8CKMA+JP1FEwVDKZBQDz9VaMfA8wNCRG/HxUHu7Mb5TjXq9UvA+Ss1iuUkXUhcCDWtwORNf2mZHJTW9fPBw==", "signatures": [{"sig": "MEQCIG+6yDZtskZXJpjWaXKHqgkXQLqNZzOOTbO6Xt7tngzEAiBfLwJJNMksjy8mqYxKwybyXYoAccZPMCwwoOqsQOI9ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.2": {"name": "micromatch", "version": "3.1.2", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.4", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "2737f3b16f4a7f12af3591bc30da4aa4dfbaf23e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.2.tgz", "integrity": "sha512-dIgY4Cr0Xq1NzICdDCPB9KQvjqNm23VfWXTZOSysk/1SzdjwkjnGozvLHS589VG07iGHOyHL6uYzvvhZ+Pc1pQ==", "signatures": [{"sig": "MEUCIQCF5hdyj8z+aPPpj9iXbqF1CFwkGknREvj/V7tiKBsI9AIgVk+aowGxoz6uPO+eRocwCXKuHGGOzrKW3hOSNnw6FLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.3": {"name": "micromatch", "version": "3.1.3", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "ae1ee52aff9c990a83ff8fb69891aeba2847c85f", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.3.tgz", "integrity": "sha512-gVCSW2StFfuHZYfh/p/HJpdTyB/YX/mr/EATvmw9zMQa6BSUioG4hg4duKEKc47OaXioikzhgFYS/m4EyLmXXg==", "signatures": [{"sig": "MEUCIFzZ8vW5VCCC1gGijBsPWSQLoouzEr8M8vi/GJcd70wlAiEA1JKkPguF7G9+OE/3j7dIxSLc1QbO6Jdn/kW9CywAZe0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.4": {"name": "micromatch", "version": "3.1.4", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "bb812e741a41f982c854e42b421a7eac458796f4", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.4.tgz", "integrity": "sha512-kFRtviKYoAJT+t7HggMl0tBFGNAKLw/S7N+CO9qfEQyisob1Oy4pao+geRbkyeEd+V9aOkvZ4mhuyPvI/q9Sfg==", "signatures": [{"sig": "MEUCIEcCygjisWAx3TdWbHXGmwnpPaCyXKxWblwh8igIyk4kAiEAxRFn6SoT9LdT1B36MNNgKLi6tkrtzinmy/8oaCemMf8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.5": {"name": "micromatch", "version": "3.1.5", "dependencies": {"braces": "^2.3.0", "extglob": "^2.0.2", "kind-of": "^6.0.0", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.5", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "define-property": "^1.0.0"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^3.0.1", "is-windows": "^1.0.1", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.2", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "d05e168c206472dfbca985bfef4f57797b4cd4ba", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.5.tgz", "integrity": "sha512-ykttrLPQrz1PUJcXjwsTUjGoPJ64StIGNE2lGVD1c9CuguJ+L7/navsE8IcDNndOoCMvYV0qc/exfVbMHkUhvA==", "signatures": [{"sig": "MEUCIEbXGYuPU5qx8SFCnFy+J3SPtMWpgRwBHBuSwt8N/dCmAiEA0QhmVGSJrK3hGMsHkHB7aWUo5QCrjvGOj5jdXk8eau8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.1.6": {"name": "micromatch", "version": "3.1.6", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "8d7c043b48156f408ca07a4715182b79b99420bf", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.6.tgz", "fileCount": 10, "integrity": "sha512-6hezhKgmSIRZeSCiVB84GOmH1Ajvo8XgnaEq/uPQ/wv0g+MQlaVonSEru7VMDZXzRWFoclakpADfInbg/5FGjw==", "signatures": [{"sig": "MEYCIQDquz7/kHy6HNgZC7lEW+zqiTPECpONjn2miJWSqks/BgIhAKeVX6S6Y7q7my0iqJ2Un5Pqanf5SvDCLchwH8d8Mkwf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84778}, "engines": {"node": ">=0.10.0"}}, "3.1.7": {"name": "micromatch", "version": "3.1.7", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "ffbb25a79585d9cdbf9cb134ac21c5853dba0eb3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.7.tgz", "fileCount": 10, "integrity": "sha512-uQTIoHx8MmOgQ/ZKAh9Oa4sGmn+wia5/QLQ5zBR5WCcPrnchTgUJCCEcZerQec67XqUSfE1OUEtUps/gRFYDSg==", "signatures": [{"sig": "MEUCIAfUWu5cT56LztiVw1z5NMK1RlsVGzQkvevsUE9CS4sFAiEAn0CSrWDYJidXMD+J2QMgsv9/bog98/HzifQs5XSADh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84866}, "engines": {"node": ">=0.10.0"}}, "3.1.8": {"name": "micromatch", "version": "3.1.8", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "5c8caa008de588eebb395e8c0ad12c128f25fff1", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.8.tgz", "fileCount": 10, "integrity": "sha512-/XeuOQqYg+B5kwjDWekXseSwGS7CzE0w9Gjo4Cjkf/uFitNh47NrZHAY2vp/oS2YQVfebPIdbEIvgdy+kIcAog==", "signatures": [{"sig": "MEQCIDZU0OYhuhDhgYk0xW0MvGNYx7GsyzukhFlDvFKj4yrwAiBcDNc+zTkajJB3ariWqnLkrqBtMCGRxarLrVUWVyYM+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84866}, "engines": {"node": ">=0.10.0"}}, "3.1.9": {"name": "micromatch", "version": "3.1.9", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.1", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "15dc93175ae39e52e93087847096effc73efcf89", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.9.tgz", "fileCount": 10, "integrity": "sha512-SlIz6sv5UPaAVVFRKodKjCg48EbNoIhgetzfK/Cy0v5U52Z6zB136M8tp0UC9jM53LYbmIRihJszvvqpKkfm9g==", "signatures": [{"sig": "MEQCIBKxgqIJnynUH8Zr3Pwt+JDZFnPf6ZW35KdvNRG4otNiAiB1bpciOpGUtMJ8rutbn8xPZR3Xgqb5RAu3ykamKiYTxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84810}, "engines": {"node": ">=0.10.0"}}, "3.1.10": {"name": "micromatch", "version": "3.1.10", "dependencies": {"braces": "^2.3.1", "extglob": "^2.0.4", "kind-of": "^6.0.2", "arr-diff": "^4.0.0", "to-regex": "^3.0.2", "nanomatch": "^1.2.9", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "object.pick": "^1.3.0", "array-unique": "^0.3.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "define-property": "^2.0.2"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.5.3", "for-own": "^1.0.0", "minimist": "^1.2.0", "minimatch": "^3.0.4", "bash-match": "^1.0.2", "gulp-mocha": "^5.0.0", "is-windows": "^1.0.2", "multimatch": "^2.1.0", "gulp-unused": "^0.2.1", "gulp-istanbul": "^1.1.3", "gulp-format-md": "^1.0.0"}, "dist": {"shasum": "70859bc95c9840952f359a068a3fc49f9ecfac23", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-3.1.10.tgz", "fileCount": 10, "integrity": "sha512-MWikgl9n9M3w+bpsY3He8L+w9eF9338xRl8IAO5viDizwSzziFEyUzo2xrrloB64ADbTf8uA8vRqqttDTOmccg==", "signatures": [{"sig": "MEQCIGeoxg8qcC+sShxYOjexCAJiJus+IviDFBpP9KD1PG1TAiBTg4Mnf6Mfhk0LjN93oRFEatuLRM5D8lf87pELil9YXg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84811}, "engines": {"node": ">=0.10.0"}}, "4.0.0": {"name": "micromatch", "version": "4.0.0", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "3d9e7a815fabfb009a10fa5adc268242c6d6088e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-THzpRAtp/NcyqnAzYwvP9V1bMAM4zFs2AR02wwxNLzEbi6Mn2suaQ6lhiD8Ug+X3L3g9grohOe1NGb2m+72eeA==", "signatures": [{"sig": "MEUCIG8Nv2u2v5npz0fskWd8wdUjHxcxhvN4vW0D1nOESCuiAiEAksGXoWEYSjabpNhcDW4S6GGscTEzXIqGjrppFQkWBHE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60867, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrecVCRA9TVsSAnZWagAAJ6QP/RpXqLXDqtzJnJEDwB6y\npMzgcaBqWbIwCfuZpAywyekDFtWYD5PyObgwyWAUupCAw8fqqUiWTIzBZJ/s\nrHklis7ox/angLUFrfTQGEU71THjY3eR2rTHtLJOEwlE5Q3LcHv+39nCfJru\nuiNH19sdV2QJGLdvBRJ7AmvHn7iTi/DrblTAXxtVr/crzu2NHrIYIj3gdwfK\n2swdZB2XC7tyWEOGp/ldBVMZXscbiK0yI5N/J2X9UwY0WBLJorg0uAX1Cnxw\n/HuwEbuRI8yTBvd46JQ7o0QOYCgUaUz2+4Cy4dvkoY3ElGAw/JkcotMi8I5e\nxXRZZCjPn4VfF2hOBxNy0EPhX5rnG9OhrU9kF6TQNuQRWlyFsRV7TwvNBb/1\nVtr5UHozdVmsjRZm/RpuVT498AnVTTCS0YgQZoGXoixKfAbM1UTGmT9+DTOq\nrsir0Dq2zEdwKGdZdi5qFVkYzvtVihVOxA90wozkz0K4UI0qVIURX3p6rkAO\nAyH+C/KbTX7oeckmaczGl/ph4yQLNTySnhls9fMWQlsiw/o5jeBmtO1fR8Ks\nonetBTOjtP5m2ZSx0R06WBNfNHYSD8SMHOYRVVoUTf7tYnA+zFoiPE/fVOsO\nDqVb8hg/ufy+JGmz7ait0hlOIFDNsaQGd/2UXoQ/8gwHvkMF5nxb9iCbeKnE\nwoZy\r\n=oCfg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "4.0.1": {"name": "micromatch", "version": "4.0.1", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.3"}, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "1bebd1a6e8dd8cff4669f3a687bdad62fb18571e", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.1.tgz", "fileCount": 5, "integrity": "sha512-6yawNHAc4S9Dh81xZCkZ5sXKH0/ly0t1DiOc+rnqzi0OvwS4DgRZU+HYTNDIgULgZXTNw5N8Vhxh2va8nEO6BA==", "signatures": [{"sig": "MEQCIGRbsJVQGWegHhEUMDcpjKmVeHvvVJ5qRxVxXYA+mPUgAiBxBHCJCj655l8LrStyDwKnvW+L9ExCbWOVKXcmMVRaMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 60935, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrhliCRA9TVsSAnZWagAAqKwP/00itZNKIjobjQSjWOIU\neLDx6eB9KNmhlkVKloBKta1azT+dpfHFdkF2uUvt+0oFXOt5rIj9mF6zD5Cz\nvrQR2+nTZvWnYsDya3Enge/B9+g0G5GtOGuRHD2KdUeVx92g83+RzF2I/EYx\nbs9YYIOV1/X7bx1VnnbLgce6tTP4ZgCxXyj7wAGAX2b+YqbNs6gki1eQpwFw\n/Gq1vrg+sVYr2bj+P6oZ1LWW8lKBIqfL2AtfE+UF4WFAoIuir/CYRqyczKc3\nm4PK61kPJkoRDXcqFsdwJlcAS1p2DrV6J7cYIzklIBHpdaDFRRDLVScu3Pc3\nlwjRjLNk2QO5speTIX7Vvq4pNMYdsytp7Puwv6hORnCR7qRnOFg6FPVXp+2O\n9s3x0CkHt+8HnBheKSxAb4IdEyyymYBWfJf9sq8qQCZY28bRJ9kzPOT58LPm\nas1ZG/SbBL6+pV1VMQmw8sC8fgyFYSPDWRcGDoCkIaHpfnYbYe94F7DPDcr9\nC7IjpKjq+myuQZYwCeB1CZ2/7AmnOM8Prm/o1nqHi5fPB1JBh4IGlQRaYTYX\nq3em8Io2ShysO+kFjLwdoado2CoIwWuNik2W+76tvI2sOoQhbAi+vyzEtGwJ\npefMpRrH/YVSGo9lZVsSksSV9o0lboslHM8ee7a1SzFh41cpwKg7/mPOMPpn\noymU\r\n=bDk9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "4.0.2": {"name": "micromatch", "version": "4.0.2", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.0.5"}, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "4fcb0999bf9fbc2fcbdd212f6d629b9a56c39259", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.2.tgz", "fileCount": 5, "integrity": "sha512-y7FpHSbMUMoyPbYUSzO6PaZ6FyRnQOpHuKwbo1G+Knck95XVU4QAiKdGEnj5wwoS7PlOgthX/09u5iFJ+aYf5Q==", "signatures": [{"sig": "MEUCIGnf+QkvkGb3nS89NKrdP+irw3EvZmC8I3aEGLArgC8pAiEAtzJB+K98yi4GYnE9tAbutn6SiXqtwtvUtArcPqnz/wE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61091, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcuz5SCRA9TVsSAnZWagAAdtEP/2PjcydeP1zQ8zsCdqIP\ndJHWFH5DgLB/etZdmB9chTBrY2Ns7eSI8wv/Am3S0z3K78xE5ncjXu0RXN/D\n9O1ojsBWsY/tZhMDZ5Gj9rPCpHB5jj44ch4KTlfSQ/aWbKk7pDB8nP5yZkZa\nQYsZvJd/igGO6EI5b7Jfw/OvO2X21MYdVd2LfdLYsTjsdIJ0/D0leYkvfC1R\nWtFlmicwbskUedFyCxhizVIkKhhqYgIKQbeeOyCbmU7U+yoaGd4PXkKiwqkm\nKreiRsy8tLLEcrdZKwMsrM+HC8JwhNcs4WuhaGEYU85aPaESs1XRJ5Nwryac\nb7KiZc82/T/D+1bjZdmi85imDh8gJGlJfzusnFcFHQZ45Y7GJn5ARkVhkcWs\nfHBEYYaAqzjdVcotzS0UAx984/XWllu35HqCj9FGsLMolFFHSOCIkKW6P9at\n1xImdNKCo5Q7cP1ckUS1cRJ0p7glmGz1F2H20fKB+zNl50awIv4VXQOkOWQA\nCecaTn36GGQ1LUe9jJRjJ8rCyfhKkSzOVlHpHDmdOYdZ4Z5LCKOWxZamAMk/\nWffyYGSnfS5oYuyv4+dT/IS/smLa4j92PIicXghcDeIj/9KG+bytehmHM8Qz\njyCvu8NLoHDrQvOLtyaJKwxRRE7jLa00a83Ond77/8swqDbFP4LnF8pZzQod\n/UAK\r\n=zQak\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "4.0.3": {"name": "micromatch", "version": "4.0.3", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.1"}, "devDependencies": {"mocha": "^5.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "fdad8352bf0cbeb89b391b5d244bc22ff3dd4ec8", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.3.tgz", "fileCount": 5, "integrity": "sha512-ueuSaP4i67F/FAUac9zzZ0Dz/5KeKDkITYIS/k4fps+9qeh1SkeH6gbljcqz97mNBOsaWZ+iv2UobMKK/yD+aw==", "signatures": [{"sig": "MEYCIQD+I18D/0/eRPwauREFlC4jqnNxgOjstANBkRlVHV5rqgIhAOGNYPv4wEVe9x0z7r32EzNtuNGrxVqStPiaOyEb7znM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61070, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcJ6mCRA9TVsSAnZWagAAyWYP/32yylOWx10uzcBcYU9F\noQMI0YR2Xj7odh/2EQLukgzKILkjyYzib1P1fldyjkoUOah33tkb+2fZeHC8\nePBOU+ofhhFpLNzxt6radc7yiPL5JVNBarCA+9fdciyP4Ztvi9ctjU49fFCS\niLWSv+Et1AgSRnjZORoNwet7XNLnP1Y406KqJWfWLom7ribwLHoyy8Bpn21N\nqI4FNXVgLsuJLmFYcmQ3cIVgnWdf6rCzWW6MhrGW8tn7CoYaPUmyc6ErRtBQ\ns13MXxi/xSNZFEnegEZT+LubNq6Yj2oPYzKW/lyO6YooYbLtrmj60Fz+6/wd\nm/I1hfdefMHbeZZKYL/aHHUAtyBv0td/YJoB9OJoagCTwZZgiWaKpvRMOwqR\nmx58sVvN4XymluXnNtl9VLI8gR84iyCQUBBKgwTVR62d9fbCqhD/op2CULxt\ni9FkG4iBIxG3ZNZVCpl9Pt0v5zIBLaT7ZeaK5L3ElUMx1mDc+lesVDDSVSVx\nCCPLI3Nv5opASPH/rzHcpq6Sjm+AmadzWKxD+A/VLgsjcV3py+fFBRnr4fKV\nmkQV+dDCckawbPh94hWgews64g2KIVMZknFzqAtrNWxrcGxUHy5nD6uFR2Zu\nz8UqH3Ey+4eBF9OYJdwwmEEVU7M6VLbKix2MMXEv1fjhlz2dCJayj0TljaR4\nOS70\r\n=h3Ju\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.6"}}, "4.0.4": {"name": "micromatch", "version": "4.0.4", "dependencies": {"braces": "^3.0.1", "picomatch": "^2.2.3"}, "devDependencies": {"mocha": "^7.2.0", "minimatch": "^3.0.4", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "896d519dfe9db25fce94ceb7a500919bf881ebf9", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.4.tgz", "fileCount": 5, "integrity": "sha512-pRmzw/XUcwXGpD9aI9q/0XOwLNygjETJ8y0ao0wdqprrzDa4YnxLcz7fQRZr8voh8V10kGhABbNcHVk5wHgWwg==", "signatures": [{"sig": "MEUCIBiTPFiWkxkgth1Ur4hsZLlgNSwQZVdZ92O74GcPd2gvAiEAhdkBvbSxJdbA/pZrTLkUsTcc62lEv/YVQu3c/+BDn2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 61526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcY4/CRA9TVsSAnZWagAA1BYQAIteJIuhbmNS9RzGRM6R\n78wakYXfLd9YHjaKU9UwW2xoPFn0OoIIKodcHHDOOfWGHe96rDOIYqvNFo+S\n3cidwhFh7EePKlyL9EpPKCGXaG2JuCeJ3nB76kCt2m4+1AOh/+oDiwBT5wNe\nS1mirQzU+GAyEVRgQHRKWn/VBvtjG4aG4teKhIuAr4ZgJh7hMUy1cJY1pBd1\nORGU0ytLPHsA6C60QHclvV3J5Rser+DtcueoEzHhykrr/5EGVa0A77gW1kz5\naYn0d8Vx0s8Y2LiPOnL2i4s42Z2U7RT3VjivYIxtD3yakB+fkXIghEDxC66D\nTgOvEyBhueXfHhcpS3Rg6rZQXw/fbGwAckgHSTmfck7zmVxs6x8n5RWJzmlP\nWAv343VYqFMHd2m11XL1KKQ1I6mtda+UwpiYrOHr1DeEqe3LaFlFNDVc8qQO\nfCfviEcGdv7T9iBbSYoNlY9qwoku9wDwCXtZ1lZsPhc4xeMmdveXq/uOTv2r\nxCDsWsoRcIXJfKJYjbYChCwpTDs/EabXIHwWyKPFz0FDhGN7qGLLOB7zQIYP\n9lOlRIv6xRZQH7+4NMkfyKBy6e1HncM3JE0oy7SAPE4sfkgfDnUiPYadUL9w\nS6+wf19JBYVYJzqrwgyLc1iQ3rdWc8DKb8IDimJYFToznbnriqo5p2+ZbNqE\nb3Gm\r\n=/5bc\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.6"}}, "4.0.5": {"name": "micromatch", "version": "4.0.5", "dependencies": {"braces": "^3.0.2", "picomatch": "^2.3.1"}, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "bc8999a7cbbf77cdc89f132f6e467051b49090c6", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz", "fileCount": 4, "integrity": "sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==", "signatures": [{"sig": "MEUCIQDMlu9ADvGDYCdgefb3WrC+hs7KxtMOM9aYsev2uDY48AIgAu/o5IQA/odSPqSKWXPeuw7mx4VfBHkCxGcEIlxO1S0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55947, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPMcjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1vBAAloppMNdWgbswe+RXiqrcsSpra8OFTxbwOOd8l0+tydhwJ8yS\r\n9tANTGLGX8w14fAdSV8IebVjjq1QNWQLlH0ofpRb4TOAYBclFkx4WMPp8s9T\r\n+vUC4fZyJsAd3QzfuWGMcUya9h8AX/S4Np3wwGnkQfg0eqpjxtkUNNyjJ0wi\r\nZeogQsi4SajN4SaJMs6Uef4PqJiyhLYyZVFMMPfXKtFud2q6hlp6LW+/v3k4\r\nV/T31dRooeUuCwADQSynf0Y05Zf9I6aEBk0FuDty6HtWf9pYZrFKGCK9JVBT\r\namijpFImSgxjL1zkytZ+p8d/+L0+IImCBVyhqH7MBEgO7IkVrjo6nMiJU/+A\r\nnh9AeYFZ0Hv0hAUZEtG+f8bKZ7egl3odWFOB9zH6+hfuJe2TwPIGGnQZAUK0\r\n+7n5i5SKsjwbAkpeIgm/1bgqW6QBPHnfGgTF9PyUgn6Hko8/688z2sfmiwbb\r\n7sZckXtWTo/vZ2K/364k19Vr4FI+HIp4BndAk2TA/zxfij99Pfu/I0c8fthV\r\nJq5AEgYu9HFWz7nFHRMHLQF0Y5cQwtMrWOy990USCEYyaQNjkJIL6oVZvNHS\r\nx+WUEmpVBB9BjtxH/ADQ8SAspnEFIs4/8mEOrsp49rF4joloFAMNXfKeSKWv\r\n8GCopBneL1eOO6NG/cXZ/M41/o9lUxbVBrQ=\r\n=lDr1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8.6"}}, "4.0.6": {"name": "micromatch", "version": "4.0.6", "dependencies": {"braces": "^3.0.3", "picomatch": "^4.0.2"}, "devDependencies": {"mocha": "^10.4.0", "minimatch": "^9.0.3", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "ab4e37c42726b9cd788181ba4a2a4fead8e394a3", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.6.tgz", "fileCount": 4, "integrity": "sha512-Y4Ypn3oujJYxJcMacVgcs92wofTHxp9FzfDpQON4msDefoC0lb3ETvQLOdLcbhSwU1bz8HrL/1sygfBIHudrkQ==", "signatures": [{"sig": "MEUCIQCbYyO7EgwmokmY6Vs+PiniiJAHS7rpu7RijTE8hhOBXgIgSp95sJVjqMVu8e9W3JwuS4Q2uvqAIqeLKQZrG2OUQlE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57044}, "engines": {"node": ">=8.6"}}, "4.0.7": {"name": "micromatch", "version": "4.0.7", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "33e8190d9fe474a9895525f5618eee136d46c2e5", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.7.tgz", "fileCount": 4, "integrity": "sha512-LPP/3KorzCwBxfeUuZmaR6bG2kdeHSbe0P2tY3FLRU4vYrjYz5hI4QZwV0njUx3jeuKe67YukQ1LSPZBKDqO/Q==", "signatures": [{"sig": "MEUCIQCoqmLDH3dRKVCJvEJ3ReCeqMKCKVg7rGGyiumTxoU3SQIgEDFoR6C5ze70X6ZhoFpQsadAVMROyLvvy0GYhBM2QNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56265}, "engines": {"node": ">=8.6"}}, "4.0.8": {"name": "micromatch", "version": "4.0.8", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "devDependencies": {"mocha": "^9.2.2", "minimatch": "^5.0.1", "fill-range": "^7.0.1", "time-require": "github:jonschlink<PERSON>/time-require", "gulp-format-md": "^2.0.0"}, "dist": {"shasum": "d66fa18f3a47076789320b9b1af32bd86d9fa202", "tarball": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "fileCount": 4, "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "signatures": [{"sig": "MEUCIQCbM5vTauiWZIyEboHJF/YXuneSxEyEJ+2VHaEdyLD4zQIgU0seoGyOpfB4e80kPjxYidBvIgonDpZpZzNN8w25BUU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56599}, "engines": {"node": ">=8.6"}}}, "modified": "2024-09-18T05:25:40.049Z"}