import React, { useEffect, useState } from "react";
import { useLocation, NavLink } from "react-router-dom";

interface NavProps {
  ulPosition: string;
  liStyle: string;
}
const NavSection: React.FC<NavProps> = ({ ulPosition, liStyle }) => {
  const location = useLocation();
  const [hasScrolled, setHasScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setHasScrolled(window.scrollY > 150);
    };
    handleScroll();

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Determine whether to show the "Home" link
  const showHome = location.pathname !== "/" || hasScrolled;

  return (
    <ul className={ulPosition}>
      <li
        className={`tl-nav-item ${liStyle} tl-dropdown`}
        style={{
          visibility: showHome ? "visible" : "hidden",
          pointerEvents: showHome ? "auto" : "none",
        }}
      >
        <NavLink
          role="button"
          to="/"
          onClick={() => window.scrollTo({ top: 0, behavior: "smooth" })}
        >
          Home
        </NavLink>
      </li>
      <li className={`tl-nav-item ${liStyle} tl-dropdown`}>
        <a role="button">
          Courses <i className="fa-regular fa-angle-down"></i>
        </a>
        <ul className="tl-submenu">
          <li>
            <NavLink to="/course/engineering-classroom">
              Engineering Classroom
            </NavLink>
          </li>
          <li>
            <NavLink to="/course/medical-classroom">Medical Classroom</NavLink>
          </li>
          <li>
            <NavLink to="/course/foundation-classroom">
              Foundation Classroom
            </NavLink>
          </li>
          <li>
            <NavLink to="/course/IIT-JEE-repeater">
              1-Year IIT-JEE Repeater Program
            </NavLink>
          </li>
          <li>
            <NavLink to="/course/NEET-repeater">
              1-Year NEET Repeater Program
            </NavLink>
          </li>
        </ul>
      </li>

      <li className={`tl-nav-item ${liStyle}`}>
        <NavLink role="button" to="/our-team">
          Our Team
        </NavLink>
      </li>
      {!window.location.pathname.includes("about") && (
        <li className={`tl-nav-item ${liStyle} tl-dropdown`}>
          <NavLink role="button" to="/about">
            About Us
          </NavLink>
        </li>
      )}
    </ul>
  );
};

export default NavSection;
