{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-http-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.2.0-beta.1", "0.3.0", "0.3.1-alpha.35", "0.3.1-beta.0", "0.3.1", "0.3.2-authHeadlessImportTest.0", "0.3.2-beta.0", "0.3.2", "0.3.3-authHeadlessImportTest.0", "0.3.3-beta.0", "0.3.3-flutter-preview.26", "0.3.3-flutter-preview.27", "0.3.3", "0.4.0-beta.0", "0.4.0", "0.4.1-beta.0", "0.4.1-ext.0", "0.4.1", "0.4.2-beta.0", "0.4.2-geo.0", "0.4.2", "0.4.3-beta.0", "0.4.3-geo.0", "0.4.3", "0.5.0-beta.0", "0.5.0", "0.5.1-beta.0", "0.5.1-runtime-hooks.0", "0.5.1-siwa-update.0", "0.5.1-siwa-update.1", "0.5.1-siwa-update.2", "0.5.1", "0.5.2-beta.0", "0.5.2-beta.1", "0.5.2-ext.0", "0.5.2", "0.5.3-beta.0", "0.5.3-beta.1", "0.5.3-custom-iam-policies.0", "0.5.3", "0.5.4-auth-dir-v-next.0", "0.5.4-auth-dir-v-next.1", "0.5.4-auth-dir-v-next.2", "0.5.4-auth-dir-v-next.3", "0.5.4-beta.0", "0.5.4-graphql-vnext-dev-preview.0", "0.5.4-graphql-vnext-dev-preview.1", "0.5.4-graphql-vnext-dev-preview.2", "0.5.4-headless-s3-not-for-production.0", "0.5.4", "0.5.5-beta.0", "0.5.5", "0.5.6-amplify-export2.0", "0.5.6-beta.0", "0.5.6-ext1.0", "0.5.6-ext10.0", "0.5.6-ext11.0", "0.5.6-ext12.0", "0.5.6-ext14.0", "0.5.6-ext15.0", "0.5.6-ext16.0", "0.5.6-ext17.0", "0.5.6-ext18.0", "0.5.6-ext19.0", "0.5.6-ext2.0", "0.5.6-ext20.0", "0.5.6-ext21.0", "0.5.6-ext3.0", "0.5.6-ext4.0", "0.5.6-ext5.0", "0.5.6-ext6.0", "0.5.6-ext7.0", "0.5.6-ext8.0", "0.5.6-ext9.0", "0.5.6-graphql-vnext-dev-preview.4", "0.5.6-graphql-vnext-dev-preview.5", "0.5.6-graphql-vnext-dev-preview.7", "0.5.6-graphql-vnext-dev-preview.8", "0.5.6", "0.5.7-graphql-vnext-dev-preview.0", "0.5.7-graphql-vnext-dev-preview.9", "0.5.7-graphql-vnext-dev-preview.10", "0.5.7-graphql-vnext-dev-preview.11", "0.6.1-beta.0", "0.6.1-beta.1", "0.6.1-beta.2", "0.7.1-graphql-vnext-dev-preview.12", "0.7.1", "0.7.2-geo.0", "0.7.2", "0.7.3-beta.0", "0.7.3", "0.7.4-beta.0", "0.7.4", "0.7.5-apiext5.0", "0.7.5-apiext6.0", "0.7.6-beta.0", "0.8.0-GqlExtensibility.0", "0.8.0-apiext1.0", "0.8.0-apiext2.0", "0.8.0-apiext3.0", "0.8.0-apiext4.0", "0.8.0-gql-ext1.0", "0.8.0-gql-ext2.0", "0.8.0", "0.8.1-beta.1", "0.8.1", "0.8.2-beta.2", "0.8.2", "0.8.3-beta.1", "0.8.3", "0.8.4-beta.5", "0.8.4-beta.6", "0.8.4", "0.8.5", "0.8.6-beta.7", "0.8.6-geo.0", "0.8.6", "0.8.7-beta.1", "0.8.7-mapsto.0", "0.8.7-mapsto2.0", "0.8.7", "0.8.8-beta.0", "0.8.8-codegen-ui-q1-release.0", "0.8.8-mapsto3.0", "0.8.8", "0.8.9-beta.0", "0.8.9", "0.8.10-beta.0", "0.8.10-codegen-ui-q1-release.0", "0.8.10", "0.8.11-beta.0", "0.8.11", "0.8.12-beta.1", "0.8.12-npm-pkg-cli.0", "0.8.12", "0.8.13-alpha.11", "0.8.13-beta.1.0", "0.8.13-beta.2.0", "0.8.13-npm-pkg-cli.0", "0.8.13-pkg-npm-install.0", "0.8.13", "0.8.14-alpha.35", "0.8.14-alpha.39", "0.8.14-alpha.41", "0.8.14-beta.1", "0.8.14-beta.2", "0.8.14-beta.3", "0.8.14-beta.4", "0.8.14-beta.6", "0.8.14-ic-changes.1", "0.8.14", "0.8.15-alpha.18", "0.8.15-alpha.26", "0.8.15-alpha.27", "0.8.15", "0.8.16-alpha.0", "0.8.16-alpha.1", "0.8.16-alpha.2", "0.8.16-beta.2", "0.8.16-beta.3", "0.8.16", "0.8.17-alpha.38", "0.8.17-alpha.40", "0.8.17-alpha.5135", "0.8.17", "0.8.18-test-api-package-migration.0", "0.8.18", "0.8.19-sub-username-identity-claim.1", "0.8.19", "0.8.20-sub-username-identity-claim.2", "0.8.20", "0.8.21-alpha.18", "0.8.21-alpha.22", "0.8.21-alpha.26", "0.8.21-alpha.29", "0.8.21-alpha.31", "0.8.21-alpha.32", "0.8.21-alpha.33", "0.8.21", "0.8.22", "0.8.23", "0.8.24-alpha.19", "0.8.24", "0.8.25", "0.8.26-alpha.0", "0.8.26-alpha.3", "0.8.26-alpha.4", "0.8.26-alpha.5", "0.8.26-alpha.23", "0.8.26-alpha.27", "0.8.26", "0.8.27-alpha.0", "0.8.27-alpha.1", "0.8.27-alpha.2", "0.8.27", "0.8.28-alpha.7", "0.8.28-alpha.20", "0.8.28", "0.8.29-alpha.1", "0.8.29-alpha.27", "0.8.29", "0.8.30-alpha.1", "0.8.30-alpha.7", "0.8.30", "0.8.31-alpha.0", "0.8.31-alpha.12", "0.8.31-delta-table-improvements.0", "0.8.31", "0.8.32-rds-support.0", "0.8.32", "0.8.33-alpha.1", "0.8.33-alpha.3", "0.8.33-alpha.13", "0.8.33-alpha.14", "0.8.33-alpha.23", "0.8.33-alpha.27", "0.8.33-alpha.38", "0.8.33-circular-dep-fix.0", "0.8.33-circular-dep-fix.1", "0.8.33-circular-dep-fix.2", "0.8.33-upgrade-graphql15.0", "0.8.33", "0.8.34", "0.8.35-rds-support.0", "0.8.35-rds-support-preview1.0.0", "0.8.35-upgrade-graphql15-2.0", "0.8.35-upgrade-graphql15-2.1", "0.8.35", "0.8.36-rdsv2preview.0", "0.8.36", "0.8.37-alhotpatchfeb.0", "0.8.37-alpha.34", "0.8.37-alpha.35", "0.8.37", "0.8.38-alpha.0", "0.8.38-alpha.74", "0.8.38-alpha.75", "0.9.0-beta.0", "0.9.0-category-split-test.0", "0.9.0-category-split-test.2", "0.9.0-category-split-test.3", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.2.2-sync-fix.0", "1.2.2", "1.2.3", "1.2.4-alpha.1", "1.2.4-alpha.2", "1.2.4-alpha.3", "1.2.4-alpha.6", "1.2.4-alpha.7", "1.2.4-alpha.9", "1.2.4-alpha.11", "1.2.4", "1.2.5-agqlac.0", "1.2.5-agqlac.1", "1.2.5-alpha.2", "1.2.5-alpha.5", "1.2.5-alpha.6", "1.2.5-alpha.9", "1.2.5-alpha.10", "1.2.5-alpha.14", "1.2.5-alpha.17", "1.2.5-cb-test-beta.0", "1.2.5-transformer-without-feature-flags.0", "1.2.5-with-standalone-transformer.0", "1.2.5", "1.2.6-agqlac.0", "1.2.6-agqlac.1", "1.2.6-alpha.0", "1.2.6-alpha.18", "1.2.6-cb-test-beta-3.0", "1.2.6-cb-test-beta-4.0", "1.2.6-cb-test-beta-5.0", "1.2.6-cb-test-prod-1.0", "1.2.6-cb-test-prod-2.0", "1.2.6-rds.0", "1.2.6", "1.2.7-agqlac.0", "1.2.7", "1.2.8", "1.2.9", "1.2.10", "1.2.11-alpha.7", "1.2.11-test-tag-1.0", "1.2.11", "1.2.12-no-internal-synth.0", "1.2.12-rds.0", "2.1.0", "2.1.1-rds-1.0", "2.1.1", "2.1.2", "2.1.3-rds-2.0", "2.1.3", "2.1.4", "2.1.5-rds-3.0", "2.1.5", "2.1.6", "2.1.7-amplify-table-preview.0", "2.1.7-rds-4.0", "2.1.7-rds-5.0", "2.1.7", "2.1.8-construct-publish-test.0", "2.1.8-nov-14-cut.0", "2.1.8-nov-14-cut-1.0", "2.1.8", "2.1.9", "2.1.10", "2.1.11", "2.1.12", "2.1.13-alpha.1", "2.1.13", "2.1.14-ecs-tagging-permissions.0", "2.1.14", "2.1.15", "2.1.16", "2.1.17-secrets-manager.0", "2.1.17", "2.1.18-implicit-fields.0", "2.1.18-rds-5.0", "2.1.18", "2.1.19-cors-rule.0", "2.1.19-fix-publish-tag.0", "2.1.19-gen2-release.0", "2.1.19-gen2-release.1", "2.1.19-iam-auth.0", "2.1.19-iam-auth-with-identityPool-provider-1.0", "2.1.19", "2.1.20-data-schema-generator.0", "2.1.20-gen2-release.0", "2.1.20-gen2-release.1", "2.1.20-gen2-release-0410.0", "2.1.20-sql-gen2.0", "2.1.20-sql-gen2-1.0", "2.1.20-test-binary-size.0", "2.1.20-z-data-schema-generator.0", "2.1.20-zz-0411-gen2.0", "2.1.20", "2.1.21-0411-gen2.0", "2.1.21-gen2-release-0416.0", "2.1.21-gen2-release-0418.0", "2.1.21-gen2-release-0418-2.0", "2.1.21-gen2-release-0423.0", "2.1.21", "2.1.22-acdk-upgrade-2-129.0", "2.1.22-cdk-upgrade-2.129.0.0", "2.1.22", "2.1.23-fix-sub-owner.0", "2.1.23", "2.1.24", "2.1.25", "2.1.26-gen2-migration.0", "2.1.26", "2.1.27-api-stable-tag-2.0", "2.1.27-gen1-migration-0924.0", "2.1.27-gen1-migration-0924-2.0", "2.1.27-gen2-migration-0809.0", "2.1.27-raven.0", "2.1.27-raven.1", "2.1.27-raven.2", "2.1.27-raven.3", "2.1.27-raven.4", "2.1.27-sandbox-hotswap.0", "2.1.27", "2.1.28-gen1-migration-1218.0", "2.1.28-gen1-migration-1218-2.0", "2.1.28-gen1-type-ext.0", "2.1.28", "2.1.29-gen1-migration-0211.0", "2.1.29-gen1-migration-0214.0", "2.1.29", "2.1.30", "2.1.31-gen1-migrations-0304.0", "2.1.31", "2.1.32", "3.0.0", "3.0.1", "3.0.2", "3.0.3-async-lambda.0", "3.0.3", "3.0.4-ai.0", "3.0.4-ai.1", "3.0.4-gen2-migration-0930.0", "3.0.4", "3.0.5", "3.0.6-gen2-migration-1015.0", "3.0.6", "3.0.7-ai-streaming.0", "3.0.7", "3.0.8-ai-next.0", "3.0.8-ai-streaming.0", "3.0.8", "3.0.9-ai-next.0", "3.0.9", "3.0.10-ai-next.0", "3.0.10", "3.0.11", "3.0.12", "3.0.13", "3.0.14", "3.0.15", "3.0.16", "3.0.17-grant-stream-read.0", "3.0.17", "3.0.18"], "vulnerableVersions": ["0.8.38-alpha.0", "0.8.38-alpha.74", "0.8.38-alpha.75", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.2.2-sync-fix.0", "1.2.2", "1.2.3", "1.2.4-alpha.1", "1.2.4-alpha.2", "1.2.4-alpha.3", "1.2.4-alpha.6", "1.2.4-alpha.7", "1.2.4-alpha.9", "1.2.4-alpha.11", "1.2.4", "1.2.5-agqlac.0", "1.2.5-agqlac.1", "1.2.5-alpha.2", "1.2.5-alpha.5", "1.2.5-alpha.6", "1.2.5-alpha.9", "1.2.5-alpha.10", "1.2.5-alpha.14", "1.2.5-alpha.17", "1.2.5-cb-test-beta.0", "1.2.5-transformer-without-feature-flags.0", "1.2.5-with-standalone-transformer.0", "1.2.5", "1.2.6-agqlac.0", "1.2.6-agqlac.1", "1.2.6-alpha.0", "1.2.6-alpha.18", "1.2.6-cb-test-beta-3.0", "1.2.6-cb-test-beta-4.0", "1.2.6-cb-test-beta-5.0", "1.2.6-cb-test-prod-1.0", "1.2.6-cb-test-prod-2.0", "1.2.6-rds.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "0.8.38-alpha.0 - 1.2.6-rds.0", "id": "amph4ONhWOR1+xTwkAQ7gKoKwmx8ROzYPm5T5whN9VCNRBnLpdmMIN6v0N3htEaRYbBX1RTAbfdGeu+0S000qg=="}