{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-api-construct", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["1.1.0", "1.1.1", "1.1.2-rds-3.0", "1.1.2", "1.1.3", "1.1.4", "1.1.5-rds-4.0", "1.1.5", "1.2.0-amplify-table-preview.0", "1.2.0-amplify-table-preview.1", "1.2.0", "1.2.1", "1.3.0-rds-5.0", "1.3.0", "1.4.0-construct-publish-test.0", "1.4.0-nov-14-cut.0", "1.4.0-nov-14-cut-1.0", "1.4.0", "1.4.1", "1.4.2", "1.4.3", "1.5.0", "1.5.1-alpha.1", "1.5.1", "1.5.2-ecs-tagging-permissions.0", "1.5.2", "1.5.3", "1.5.4", "1.5.5", "1.5.6", "1.5.7-implicit-fields.0", "1.5.7-rds-5.0", "1.5.7", "1.6.0-secrets-manager.0", "1.6.0", "1.6.1-ecs-tagging-permissions.0", "1.7.0-cors-rule.0", "1.7.0-fix-publish-tag.0", "1.7.0-gen2-release.0", "1.7.0-gen2-release.1", "1.7.0-iam-auth.0", "1.7.0-iam-auth-with-identityPool-provider-1.0", "1.7.0", "1.7.1-gen2-release.0", "1.7.1-gen2-release.1", "1.8.0-data-schema-generator.0", "1.8.0-gen2-release.0", "1.8.0-gen2-release-0410.0", "1.8.0-sql-gen2.0", "1.8.0-sql-gen2-1.0", "1.8.0-test-binary-size.0", "1.8.0-z-data-schema-generator.0", "1.8.0", "1.8.1", "1.9.0-0411-gen2.0", "1.9.0-gen2-release-0416.0", "1.9.0-gen2-release-0418.0", "1.9.0-gen2-release-0418-2.0", "1.9.0-gen2-release-0423.0", "1.9.0", "1.9.1-cdk-upgrade-2.129.0.0", "1.9.1", "1.9.2-acdk-upgrade-2-129.0", "1.9.2", "1.9.3", "1.9.4-fix-sub-owner.0", "1.9.4", "1.9.5", "1.10.0", "1.11.0", "1.11.1", "1.11.2", "1.11.3", "1.11.4-update-awaiter-s3-key.0", "1.11.4", "1.11.5", "1.11.6-raven.0", "1.11.6-raven.1", "1.11.6-raven.2", "1.11.6-raven.3", "1.11.6-raven.4", "1.11.6-raven.5", "1.11.6-raven.6", "1.11.6-raven.7", "1.11.6-raven.8", "1.11.6-raven.9", "1.11.6-raven.10", "1.11.6-raven.11", "1.11.6-raven.12", "1.11.6-raven.13", "1.11.6-sandbox-hotswap.0", "1.11.6-sandbox-hotswap.1", "1.11.6", "1.11.7", "1.11.8", "1.12.0-gen2-migration.0", "1.12.0-gen2-migration-0809.0", "1.12.0", "1.12.1-async-lambda.0", "1.13.0-ai.0", "1.13.0-ai.1", "1.13.0-ai.2", "1.13.0-ai.3", "1.13.0-ai.4", "1.13.0-ai.5", "1.13.0-ai.6", "1.13.0-ai.7", "1.13.0", "1.14.0-gen2-migration-0930.0", "1.14.0", "1.15.0", "1.15.1-ai-streaming.0", "1.15.1-ai-streaming.1", "1.15.1-ai-streaming.2", "1.15.1-ai-streaming.3", "1.15.1-ai-streaming.4", "1.15.1-ai-streaming.5", "1.15.1-ai-streaming.6", "1.15.1-ai-streaming.7", "1.15.1", "1.16.0-ai-streaming.0", "1.16.0-gen2-migration-1015.0", "1.16.0", "1.16.1-ai-next.0", "1.16.1-ai-next.1", "1.16.1-ai-streaming.0", "1.17.0", "1.17.1", "1.17.2-ai-next.0", "1.17.2-ai-next.1", "1.17.2", "1.17.3", "1.17.4-ai-next.0", "1.17.4-ai-next.1", "1.18.0", "1.18.1", "1.18.2", "1.18.3-ai-next.0", "1.18.3", "1.18.4", "1.18.5", "1.18.6", "1.18.7", "1.18.8-gen2-migration-0205.0", "1.18.8", "1.19.0", "1.19.1", "1.20.0-grant-stream-read.0", "1.20.0", "1.20.1"], "vulnerableVersions": [], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "<0.0.0-0", "id": "KNE/tdUbJTrJmlW0Xxf2CHnvgNruBg+kbwcMR85H7NH12uiGnlZQfqL0BcSUN7jHO2fxv7f0leRi3cwcgKGLjg=="}