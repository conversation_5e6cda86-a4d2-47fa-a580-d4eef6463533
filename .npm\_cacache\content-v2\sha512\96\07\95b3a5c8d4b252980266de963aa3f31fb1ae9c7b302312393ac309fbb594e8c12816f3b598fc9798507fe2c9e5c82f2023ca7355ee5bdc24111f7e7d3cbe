{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-maps-to-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["1.1.0-beta.0", "1.1.0-mapsto.0", "1.1.0-mapsto2.0", "1.1.0-mapsto3.0", "1.1.0", "1.1.1-init-w-override.0", "1.1.1", "1.1.4-beta.0", "1.1.5-beta.0", "1.1.5", "1.1.6-beta.0", "1.1.6-codegen-ui-q1-release.0", "1.1.6", "1.1.7-beta.0", "1.1.7", "1.1.8-beta.0", "1.1.8-uibuilder-wip.0", "1.1.8-uibuilder-wip-2.0", "1.1.8", "1.1.9-beta.0", "1.1.9", "1.1.10-beta.1", "1.1.10-npm-pkg-cli.0", "1.1.10", "1.1.11-alpha.11", "1.1.11-beta.1.0", "1.1.11-beta.2.0", "1.1.11-npm-pkg-cli.0", "1.1.11-pkg-npm-install.0", "1.1.11", "1.1.12-alpha.35", "1.1.12-alpha.39", "1.1.12-alpha.41", "1.1.12-beta.1", "1.1.12-beta.2", "1.1.12-beta.3", "1.1.12-beta.4", "1.1.12-beta.6", "1.1.12-ic-changes.1", "1.1.12", "1.1.13-alpha.18", "1.1.13-alpha.26", "1.1.13-alpha.27", "1.1.13-ic-changes.3", "1.1.13", "1.1.14-alpha.0", "1.1.14-alpha.1", "1.1.14-alpha.2", "1.1.14-beta.2", "1.1.14-beta.3", "1.1.14", "1.1.15-alpha.38", "1.1.15-alpha.40", "1.1.15-alpha.5135", "1.1.15", "1.1.16-test-api-package-migration.0", "1.1.16", "1.1.17-sub-username-identity-claim.1", "1.1.17", "1.1.18-sub-username-identity-claim.2", "1.1.18", "1.1.19-alpha.18", "1.1.19-alpha.22", "1.1.19-alpha.26", "1.1.19-alpha.29", "1.1.19-alpha.31", "1.1.19-alpha.32", "1.1.19-alpha.33", "1.1.19", "1.1.20", "1.1.21", "1.1.22-alpha.19", "1.1.22", "1.1.23", "1.1.24-alpha.0", "1.1.24", "1.1.25-alpha.0", "1.1.25-alpha.1", "1.1.25-alpha.2", "1.1.25", "1.1.26-alpha.17", "1.1.26-alpha.21", "1.1.26", "1.1.27-alpha.0", "1.1.27-alpha.1", "1.1.27-alpha.2", "1.1.27", "1.1.28", "1.1.29-alpha.7", "1.1.29-alpha.20", "1.1.29", "1.1.30-alpha.1", "1.1.30", "1.1.31-rtf-release-phase-1.0", "1.1.31", "1.1.32-405patch1.0", "1.1.32-alpha.9", "1.1.32", "1.1.33-alpha.1", "1.1.33-alpha.7", "1.1.33", "1.1.34-alpha.0", "1.1.34-delta-table-improvements.0", "1.1.34", "1.1.35-alpha.4", "1.1.35", "1.1.36-rds-support.0", "1.1.36", "1.1.37-alpha.1", "1.1.37-alpha.3", "1.1.37-alpha.13", "1.1.37-alpha.14", "1.1.37-rds-v2.0", "1.1.37", "1.1.38", "1.1.39-alpha.3", "1.1.39-alpha.7", "1.1.39-alpha.18", "1.1.39-circular-dep-fix.0", "1.1.39-circular-dep-fix.1", "1.1.39-circular-dep-fix.2", "1.1.39-upgrade-graphql15.0", "1.1.40", "1.1.41-rds-support.0", "1.1.41-rds-support-preview1.0.0", "1.1.41-upgrade-graphql15-2.1", "1.1.41", "1.1.42-rdsv2preview.0", "1.1.42", "1.1.43-alhotpatchfeb.0", "1.1.43-alpha.34", "1.1.43-alpha.35", "1.1.43", "1.1.44-alpha.0", "1.1.44-alpha.74", "1.1.44-alpha.75", "1.2.0-category-split-test.0", "1.2.0-category-split-test.2", "1.2.0-category-split-test.3", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0-cdkv2.3", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-ownerfield-pk-fix.0", "2.1.2", "2.1.3-5.2.0-ownerfield-pk-fix.0", "2.1.3-alpha.3", "2.1.3-alpha.9", "2.1.3-ownerfield-pk-fix.0", "2.1.3", "2.1.4-sync-fix.0", "2.1.4", "2.1.5", "2.1.6-alpha.1", "2.1.6-alpha.2", "2.1.6-alpha.3", "2.1.6-alpha.6", "2.1.6-alpha.7", "2.1.6-alpha.9", "2.1.6-alpha.11", "2.1.6", "2.1.7-agqlac.0", "2.1.7-agqlac.1", "2.1.7-alpha.2", "2.1.7-alpha.5", "2.1.7-alpha.6", "2.1.7-alpha.9", "2.1.7-alpha.10", "2.1.7-alpha.14", "2.1.7-alpha.17", "2.1.7-cb-test-beta.0", "2.1.7-transformer-without-feature-flags.0", "2.1.7-with-standalone-transformer.0", "2.1.7", "2.1.8-agqlac.0", "2.1.8-agqlac.1", "2.1.8-agqlac.2", "2.1.8-alpha.0", "2.1.8-alpha.18", "2.1.8-cb-test-beta-3.0", "2.1.8-cb-test-beta-4.0", "2.1.8-cb-test-beta-5.0", "2.1.8-cb-test-prod-1.0", "2.1.8-cb-test-prod-2.0", "2.1.8-rds.0", "2.1.8-rds.3", "2.1.8", "2.1.9-agqlac.0", "2.1.10", "2.1.11", "2.1.12", "2.1.13-alpha.7", "2.1.13-test-tag-1.0", "2.1.13", "2.1.14-no-internal-synth.0", "2.1.14-rds.0", "3.1.0", "3.1.1-rds-1.0", "3.1.1", "3.1.2", "3.1.3-construct-uses-jsii.0", "3.1.3-jsii-build.0", "3.1.3-jsii-build.1", "3.1.3", "3.2.0-rds-2.0", "3.2.0", "3.2.1", "3.2.2", "3.2.3", "3.2.4", "3.2.5-amplify-table-preview.0", "3.2.5-amplify-table-preview.1", "3.2.5", "3.2.6-construct-publish-test.0", "3.3.0-nov-14-cut.0", "3.3.0-nov-14-cut-1.0", "3.3.0-rds-3.0", "3.3.0-rds-4.0", "3.3.0-rds-5.0", "3.3.0", "3.3.1", "3.3.2", "3.3.3", "3.4.0", "3.4.1-alpha.1", "3.4.1", "3.4.2-ecs-tagging-permissions.0", "3.4.2", "3.4.3", "3.4.4", "3.4.5", "3.4.6-secrets-manager.0", "3.4.6", "3.4.7-implicit-fields.0", "3.4.7-rds-5.0", "3.4.7", "3.4.8", "3.4.9-cors-rule.0", "3.4.9-fix-publish-tag.0", "3.4.9-gen2-release.0", "3.4.9-gen2-release.1", "3.4.9-iam-auth.0", "3.4.9-iam-auth-with-identityPool-provider-1.0", "3.4.9", "3.4.10-data-schema-generator.0", "3.4.10-gen2-release.0", "3.4.10-gen2-release.1", "3.4.10-gen2-release-0410.0", "3.4.10-sql-gen2.0", "3.4.10-sql-gen2-1.0", "3.4.10-test-binary-size.0", "3.4.10-z-data-schema-generator.0", "3.4.10", "3.4.11-0411-gen2.0", "3.4.11-gen2-release-0416.0", "3.4.11", "3.4.12-gen2-release-0418.0", "3.4.12-gen2-release-0418-2.0", "3.4.12-gen2-release-0423.0", "3.4.12", "3.4.13-cdk-upgrade-2.129.0.0", "3.4.13", "3.4.14-acdk-upgrade-2-129.0", "3.4.14", "3.4.15", "3.4.16-fix-sub-owner.0", "3.4.16", "3.4.17", "3.4.18", "3.4.19", "3.4.20", "3.4.21-gen2-migration.0", "3.4.21", "3.4.22", "3.4.23-gen2-migration-0809.0", "3.4.23", "3.4.24-api-stable-tag-2.0", "3.4.24-gen1-migration-0924.0", "3.4.24-gen1-migration-0924-2.0", "3.4.24-raven.0", "3.4.24-raven.1", "3.4.24-raven.2", "3.4.24-raven.3", "3.4.24-raven.4", "3.4.24-sandbox-hotswap.0", "3.4.24", "3.4.25-gen1-migration-1218.0", "3.4.25-gen1-migration-1218-2.0", "3.4.25", "3.4.26-gen1-type-ext.0", "3.4.26", "3.4.27-gen1-migration-0211.0", "3.4.27-gen1-migration-0214.0", "3.4.27", "3.4.28", "3.4.29-gen1-migrations-0304.0", "3.4.29", "3.5.0", "4.0.0", "4.0.1", "4.0.2", "4.0.3-async-lambda.0", "4.0.3", "4.0.4-ai.0", "4.0.4-ai.1", "4.0.4-gen2-migration-0930.0", "4.0.4", "4.0.5", "4.0.6-gen2-migration-1015.0", "4.0.6", "4.0.7-ai-streaming.0", "4.0.7", "4.0.8-ai-next.0", "4.0.8-ai-streaming.0", "4.0.8", "4.0.9-ai-next.0", "4.0.9", "4.0.10-ai-next.0", "4.0.10", "4.0.11", "4.0.12", "4.0.13", "4.0.14-gen2-migration-0205.0", "4.0.14", "4.0.15", "4.0.16", "4.0.17-grant-stream-read.0", "4.0.17", "4.0.18"], "vulnerableVersions": ["1.1.44-alpha.0", "1.1.44-alpha.74", "1.1.44-alpha.75", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0-cdkv2.3", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-ownerfield-pk-fix.0", "2.1.2", "2.1.3-5.2.0-ownerfield-pk-fix.0", "2.1.3-alpha.3", "2.1.3-alpha.9", "2.1.3-ownerfield-pk-fix.0", "2.1.3", "2.1.4-sync-fix.0", "2.1.4", "2.1.5", "2.1.6-alpha.1", "2.1.6-alpha.2", "2.1.6-alpha.3", "2.1.6-alpha.6", "2.1.6-alpha.7", "2.1.6-alpha.9", "2.1.6-alpha.11", "2.1.6", "2.1.7-agqlac.0", "2.1.7-agqlac.1", "2.1.7-alpha.2", "2.1.7-alpha.5", "2.1.7-alpha.6", "2.1.7-alpha.9", "2.1.7-alpha.10", "2.1.7-alpha.14", "2.1.7-alpha.17", "2.1.7-cb-test-beta.0", "2.1.7-transformer-without-feature-flags.0", "2.1.7-with-standalone-transformer.0", "2.1.7", "2.1.8-agqlac.0", "2.1.8-agqlac.1", "2.1.8-agqlac.2", "2.1.8-alpha.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "1.1.44-alpha.0 - 2.1.8-alpha.0", "id": "P4NyEV5qLhmd5n0lsVO91XwmJ0Le52lRYouPHdkCo2ueGQ45o5lhv1YlPtiVh+ygdj8TJeM6vZMcuYta1I94SA=="}