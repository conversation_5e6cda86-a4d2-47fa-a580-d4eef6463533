/* Our Team Section Styles */
.team-section-wrapper {
  margin-bottom: 80px;
}

.team-section-wrapper:last-child {
  margin-bottom: 0;
}

.section-description {
  color: #666;
  font-size: 16px;
  margin-bottom: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.team-member-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: transform 0.3s ease;
}

.team-member-link:hover {
  text-decoration: none;
  color: inherit;
  transform: translateY(-5px);
}

.team-member-card {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: #fff;
}

.team-member-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.team-member-card .tl-1-teacher-img {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
}

.team-member-card .tl-1-teacher-info {
  padding: 20px;
  text-align: center;
}

.team-member-card .tl-1-teacher-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #2c3e50;
}

.team-member-card .tl-1-teacher-sub-title {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 12px;
  font-weight: 500;
}

.teacher-experience {
  margin-top: 10px;
}

.experience-badge {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .team-member-card .tl-1-teacher-img {
    height: 250px;
  }
  
  .team-member-card .tl-1-teacher-info {
    padding: 15px;
  }
  
  .team-member-card .tl-1-teacher-title {
    font-size: 18px;
  }
  
  .section-description {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .team-section-wrapper {
    margin-bottom: 60px;
  }
  
  .team-member-card .tl-1-teacher-img {
    height: 200px;
  }
  
  .team-member-card .tl-1-teacher-title {
    font-size: 16px;
  }
  
  .team-member-card .tl-1-teacher-sub-title {
    font-size: 13px;
  }
}
