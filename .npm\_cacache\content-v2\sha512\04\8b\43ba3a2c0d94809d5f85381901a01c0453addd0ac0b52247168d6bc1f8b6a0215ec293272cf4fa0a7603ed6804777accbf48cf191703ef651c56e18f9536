{"source": "TlYHmS8BZBwkbsPHbTfH2/e91KJnOnqfC63ZoKdiRy+yt6biPd8BwjBdyfPUht1+ZO675I3Gg00RQPYaWiOm8Q==", "name": "@parcel/watcher", "dependency": "micromatch", "title": "Depends on vulnerable versions of micromatch", "url": null, "severity": "moderate", "versions": ["1.11.0", "1.12.0", "1.12.1", "2.0.0-alpha.1", "2.0.0-alpha.2", "2.0.0-alpha.3", "2.0.0-alpha.4", "2.0.0-alpha.5", "2.0.0-alpha.6", "2.0.0-alpha.7", "2.0.0-alpha.8", "2.0.0-alpha.9", "2.0.0-alpha.10", "2.0.0-alpha.11", "2.0.0", "2.0.1", "2.0.2", "2.0.3", "2.0.4", "2.0.5", "2.0.6", "2.0.7", "2.1.0", "2.2.0-alpha.0", "2.2.0-alpha.1", "2.2.0", "2.2.1-alpha.0", "2.3.0-alpha.0", "2.3.0-alpha.1", "2.3.0-alpha.2", "2.3.0-alpha.3", "2.3.0", "2.4.0", "2.4.1", "2.4.2-alpha.0", "2.5.0", "2.5.1"], "vulnerableVersions": [], "cwe": ["CWE-1333"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L"}, "range": "<0.0.0-0", "id": "ORzUAC13y/+esY4gWGjeuZ+4ccdMftLULJRemSJWjagd5F1/Dal/Rlx5fTJcCUw5K8BrX6cW6VFaG3gOaOPW5A=="}