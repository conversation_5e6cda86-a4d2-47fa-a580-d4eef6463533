{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-relational-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.2.0-beta.0", "0.2.0-beta.1", "0.2.0-custom-iam-policies.0", "0.2.0", "0.2.1-beta.1", "0.2.1-beta.2", "0.2.1-geo.0", "0.2.1", "0.3.0-auth-dir-v-next.0", "0.3.0-auth-dir-v-next.1", "0.3.0-auth-dir-v-next.2", "0.3.0-auth-dir-v-next.3", "0.3.0-auth-dir-v-next.4", "0.3.0-auth-dir-v-next.5", "0.3.0-beta.0", "0.3.0-graphql-vnext-dev-preview.0", "0.3.0-graphql-vnext-dev-preview.1", "0.3.0-graphql-vnext-dev-preview.2", "0.3.0-graphql-vnext-dev-preview.3", "0.3.0-headless-s3-not-for-production.0", "0.3.0", "0.3.1-beta.0", "0.3.1", "0.3.2-amplify-export2.0", "0.3.2-beta.0", "0.4.0-ext1.0", "0.4.0-ext10.0", "0.4.0-ext11.0", "0.4.0-ext12.0", "0.4.0-ext14.0", "0.4.0-ext15.0", "0.4.0-ext16.0", "0.4.0-ext17.0", "0.4.0-ext18.0", "0.4.0-ext19.0", "0.4.0-ext2.0", "0.4.0-ext20.0", "0.4.0-ext21.0", "0.4.0-ext3.0", "0.4.0-ext4.0", "0.4.0-ext5.0", "0.4.0-ext6.0", "0.4.0-ext7.0", "0.4.0-ext8.0", "0.4.0-ext9.0", "0.4.0-graphql-vnext-dev-preview.4", "0.4.0-graphql-vnext-dev-preview.5", "0.4.0-graphql-vnext-dev-preview.7", "0.4.0-graphql-vnext-dev-preview.8", "0.4.0", "0.4.1-graphql-vnext-dev-preview.0", "0.4.1-graphql-vnext-dev-preview.9", "0.4.1-graphql-vnext-dev-preview.10", "0.4.1-graphql-vnext-dev-preview.11", "0.5.0-beta.0", "0.5.0-beta.1", "0.5.0-beta.2", "0.6.1-graphql-vnext-dev-preview.12", "0.6.1", "0.6.2-geo.0", "0.6.2", "0.6.3-apiext1.0", "0.6.3-apiext2.0", "0.6.3-beta.0", "0.6.3-gql-ext1.0", "0.6.3-gql-ext2.0", "0.6.3", "0.6.4-beta.0", "0.6.4", "0.6.5", "0.6.6-apiext3.0", "0.6.6", "0.6.7-apiext4.0", "0.6.7-apiext5.0", "0.6.7-apiext6.0", "0.6.7", "0.6.8-beta.0", "0.6.8", "0.6.9-beta.0", "0.6.9-beta.1", "0.6.9", "0.6.10", "0.6.11-beta.1", "0.6.11", "0.6.12-beta.2", "0.6.12", "0.6.13-beta.1", "0.6.13", "0.6.14-beta.5", "0.6.14-beta.6", "0.6.14", "0.6.15", "0.6.16-beta.7", "0.6.16-geo.0", "0.6.16-sub-identity-claim.1", "0.6.16", "0.6.17-beta.1", "0.6.17", "0.6.18-beta.1", "0.6.18", "0.6.19-beta.1", "0.6.19", "0.6.20-beta.0", "0.6.20-codegen-ui-q1-release.0", "0.6.20", "0.7.0-beta.0", "0.7.0-mapsto.0", "0.7.0-mapsto2.0", "0.7.0-mapsto3.0", "0.7.0", "0.7.1-beta.0", "0.7.1-init-w-override.0", "0.7.1", "0.7.2-beta.0", "0.7.4-beta.0", "0.7.5-beta.0", "0.7.5", "0.7.6-beta.0", "0.7.6-codegen-ui-q1-release.0", "0.7.6", "0.7.7-beta.0", "0.7.7", "0.7.8-beta.0", "0.7.8-uibuilder-wip.0", "0.7.8-uibuilder-wip-2.0", "0.7.8", "0.7.9-beta.0", "0.7.9", "0.7.10-beta.1", "0.7.10-npm-pkg-cli.0", "0.7.10", "0.7.11-alpha.11", "0.7.11-beta.1.0", "0.7.11-beta.2.0", "0.7.11-npm-pkg-cli.0", "0.7.11-pkg-npm-install.0", "0.7.11", "0.7.12-alpha.35", "0.7.12-alpha.39", "0.7.12-alpha.41", "0.8.0-beta.1", "0.8.0-beta.2", "0.8.0-beta.3", "0.8.0-beta.4", "0.8.0-ic-changes.1", "0.8.0", "0.8.1-alpha.18", "0.8.1-alpha.26", "0.8.1-alpha.27", "0.8.1-ic-changes.3", "0.8.1", "0.8.2-alpha.0", "0.8.2-alpha.1", "0.8.2-alpha.2", "0.9.0-beta.2", "0.9.0-beta.3", "0.9.0-category-split-test.0", "0.9.0-category-split-test.2", "0.9.0-category-split-test.3", "0.9.0", "0.9.1-alpha.38", "0.9.1-alpha.40", "0.9.1-alpha.5135", "0.9.1", "0.9.2-test-api-package-migration.0", "0.9.2", "0.9.3-sub-username-identity-claim.1", "0.9.3", "0.9.4-sub-username-identity-claim.2", "0.9.4", "0.9.5-alpha.18", "0.9.5-alpha.22", "0.9.5-alpha.26", "0.9.5-alpha.29", "0.9.5-alpha.31", "0.9.5-alpha.32", "0.9.5-alpha.33", "0.10.0", "0.10.1", "0.11.0", "0.11.1-alpha.19", "0.11.1", "0.11.2", "0.11.3-alpha.0", "0.11.3", "0.11.4-alpha.0", "0.11.4-alpha.1", "0.11.4-alpha.2", "0.11.4", "0.11.5-alpha.17", "0.11.5-alpha.21", "0.11.5", "0.11.6-alpha.0", "0.11.6-alpha.1", "0.11.6-alpha.2", "0.11.6", "0.11.7", "0.11.8-alpha.7", "0.11.8-alpha.20", "0.11.8", "0.11.9", "0.12.0-rtf-release-phase-1.0", "0.12.0", "0.12.1-405patch1.0", "0.12.1-alpha.9", "0.12.1", "0.12.2-alpha.1", "0.12.2-alpha.7", "0.12.2", "0.12.3-alpha.0", "0.12.3-delta-table-improvements.0", "0.12.3", "0.12.4-alpha.4", "0.12.4", "0.12.5-rds-support.0", "0.12.5", "0.12.6-alpha.1", "0.12.6-alpha.3", "0.12.6-alpha.13", "0.12.6-alpha.14", "0.12.6", "0.12.7-alpha.3", "0.12.7-alpha.7", "0.12.7-alpha.18", "0.12.7-circular-dep-fix.0", "0.12.7-circular-dep-fix.1", "0.12.7-circular-dep-fix.2", "0.12.7-upgrade-graphql15.0", "0.12.8", "0.12.9-rds-support.0", "0.12.9-rds-support-preview1.0.0", "0.12.9-upgrade-graphql15-2.0", "0.12.9-upgrade-graphql15-2.1", "0.12.9", "0.12.10-rdsv2preview.0", "0.12.10", "0.12.11-alhotpatchfeb.0", "0.12.11-alpha.34", "0.12.11-alpha.35", "0.12.11", "0.12.12-alpha.0", "0.12.12-alpha.74", "0.12.12-alpha.75", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0-cdkv2.3", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.1.2", "1.1.3-5.2.0-ownerfield-pk-fix.0", "1.1.3-alpha.3", "1.1.3-alpha.9", "1.1.3-ownerfield-pk-fix.0", "1.1.3", "1.2.0-sync-fix.0", "1.2.0", "1.2.1", "1.2.2-alpha.1", "1.2.2-alpha.2", "1.2.2-alpha.3", "1.2.2-alpha.6", "1.2.2-alpha.7", "1.2.2-alpha.9", "1.2.2-alpha.11", "1.2.2", "1.2.3-agqlac.0", "1.2.3-agqlac.1", "1.2.3-alpha.2", "1.2.3-alpha.5", "1.2.3-alpha.6", "1.2.3-alpha.9", "1.2.3-alpha.10", "1.2.3-alpha.14", "1.2.3-alpha.17", "1.2.3-cb-test-beta.0", "1.2.3-transformer-without-feature-flags.0", "1.2.3-with-standalone-transformer.0", "1.2.3", "1.2.4-agqlac.0", "1.2.4-agqlac.1", "1.2.4-agqlac.2", "1.2.4-alpha.0", "1.2.4-alpha.18", "1.2.4-cb-test-beta-3.0", "1.2.4-cb-test-beta-4.0", "1.2.4-cb-test-beta-5.0", "1.2.4-cb-test-prod-1.0", "1.2.4-cb-test-prod-2.0", "1.2.4-rds.0", "1.2.4-rds.3", "1.2.4", "1.2.5-agqlac.0", "1.2.5", "1.2.6", "1.2.7", "1.2.8", "1.2.9-alpha.7", "1.2.9-test-tag-1.0", "1.2.9", "1.2.10-no-internal-synth.0", "1.3.0-rds.0", "2.1.0", "2.1.1", "2.1.2", "2.1.3-construct-uses-jsii.0", "2.1.3-jsii-build.0", "2.1.3-jsii-build.1", "2.1.3", "2.1.4", "2.1.5", "2.1.6", "2.1.7", "2.1.8", "2.2.0-amplify-table-preview.0", "2.2.0-amplify-table-preview.1", "2.2.0-construct-publish-test.0", "2.2.0-nov-14-cut.0", "2.2.0-nov-14-cut-1.0", "2.2.0-rds-1.0", "2.2.0-rds-2.0", "2.2.0-rds-3.0", "2.2.0-rds-4.0", "2.2.0-rds-5.0", "2.2.0", "2.2.1", "2.2.2", "2.2.3", "2.3.0", "2.3.1-alpha.1", "2.3.1", "2.3.2-ecs-tagging-permissions.0", "2.3.2", "2.3.3", "2.3.4", "2.3.5", "2.3.6-secrets-manager.0", "2.3.6", "2.3.7-rds-5.0", "2.4.0-implicit-fields.0", "2.4.0", "2.4.1", "2.4.2-cors-rule.0", "2.4.2-fix-publish-tag.0", "2.4.2-gen2-release.0", "2.4.2-gen2-release.1", "2.4.2-iam-auth.0", "2.4.2-iam-auth-with-identityPool-provider-1.0", "2.4.2", "2.4.3-data-schema-generator.0", "2.4.3-sql-gen2.0", "2.4.3-test-binary-size.0", "2.4.3-z-data-schema-generator.0", "2.4.3", "2.4.4", "2.5.0-0411-gen2.0", "2.5.0-gen2-release.0", "2.5.0-gen2-release.1", "2.5.0-gen2-release-0410.0", "2.5.0-gen2-release-0416.0", "2.5.0-gen2-release-0418.0", "2.5.0-gen2-release-0418-2.0", "2.5.0-gen2-release-0423.0", "2.5.0-sql-gen2-1.0", "2.5.0", "2.5.1-cdk-upgrade-2.129.0.0", "2.5.1", "2.5.2-acdk-upgrade-2-129.0", "2.5.2", "2.5.3", "2.5.4-fix-sub-owner.0", "2.5.4", "2.5.5", "2.5.6", "2.5.7", "2.5.8", "2.5.9-gen2-migration.0", "2.5.9", "2.5.10", "2.5.11", "2.5.12-api-stable-tag-2.0", "2.5.12-gen1-migration-0924.0", "2.5.12-gen1-migration-0924-2.0", "2.5.12-raven.0", "2.5.12-raven.1", "2.5.12-raven.2", "2.5.12-raven.3", "2.5.12-raven.4", "2.5.12-sandbox-hotswap.0", "2.5.12", "2.5.13-gen1-migration-1218.0", "2.5.13-gen1-migration-1218-2.0", "2.5.13", "2.5.14-gen1-type-ext.0", "2.5.14", "2.5.15-gen1-migration-0211.0", "2.5.15-gen1-migration-0214.0", "2.5.15", "2.5.16", "2.5.17-gen1-migrations-0304.0", "2.5.17", "2.5.18", "2.6.0-gen2-migration-0809.0", "3.0.0", "3.0.1", "3.0.2", "3.0.3-async-lambda.0", "3.0.3", "3.0.4-ai.0", "3.0.4-ai.1", "3.0.4", "3.0.5", "3.0.6", "3.0.7-ai-streaming.0", "3.0.7", "3.0.8-ai-next.0", "3.0.8-ai-streaming.0", "3.1.0-gen2-migration-0930.0", "3.1.0-gen2-migration-1015.0", "3.1.0", "3.1.1-ai-next.0", "3.1.1", "3.1.2-ai-next.0", "3.1.2", "3.1.3", "3.1.4", "3.1.5", "3.1.6-gen2-migration-0205.0", "3.1.6", "3.1.7", "3.1.8", "3.1.9-grant-stream-read.0", "3.1.9", "3.1.10"], "vulnerableVersions": ["0.12.12-alpha.0", "0.12.12-alpha.74", "0.12.12-alpha.75", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0-cdkv2.3", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.1.2", "1.1.3-5.2.0-ownerfield-pk-fix.0", "1.1.3-alpha.3", "1.1.3-alpha.9", "1.1.3-ownerfield-pk-fix.0", "1.1.3", "1.2.0-sync-fix.0", "1.2.0", "1.2.1", "1.2.2-alpha.1", "1.2.2-alpha.2", "1.2.2-alpha.3", "1.2.2-alpha.6", "1.2.2-alpha.7", "1.2.2-alpha.9", "1.2.2-alpha.11", "1.2.2", "1.2.3-agqlac.0", "1.2.3-agqlac.1", "1.2.3-alpha.2", "1.2.3-alpha.5", "1.2.3-alpha.6", "1.2.3-alpha.9", "1.2.3-alpha.10", "1.2.3-alpha.14", "1.2.3-alpha.17", "1.2.3-cb-test-beta.0", "1.2.3-transformer-without-feature-flags.0", "1.2.3-with-standalone-transformer.0", "1.2.3", "1.2.4-agqlac.0", "1.2.4-agqlac.1", "1.2.4-agqlac.2", "1.2.4-alpha.0", "1.2.4-alpha.18", "1.2.4-cb-test-beta-3.0", "1.2.4-cb-test-beta-4.0", "1.2.4-cb-test-beta-5.0", "1.2.4-cb-test-prod-1.0", "1.2.4-cb-test-prod-2.0", "1.2.4-rds.0", "1.2.4-rds.3"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "0.12.12-alpha.0 - 1.2.4-rds.3", "id": "+AOgl30wgpx7fys5T0Kgh0d9X283ttv4GfiSlfIFW6a4PT+L1w2231XatcM9ab4grAffw3vJb3IZvuLnKVk20A=="}