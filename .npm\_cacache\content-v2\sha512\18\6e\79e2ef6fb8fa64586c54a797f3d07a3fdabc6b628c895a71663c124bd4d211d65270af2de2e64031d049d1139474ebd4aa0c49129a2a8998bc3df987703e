{"name": "json-schema-to-ts", "dist-tags": {"beta": "2.8.0-beta.0", "latest": "3.1.1"}, "versions": {"0.1.0": {"name": "json-schema-to-ts", "version": "0.1.0", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "3cb21bc62bbfaa857b0dbb34f69456d7e0899593", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.0.tgz", "fileCount": 34, "integrity": "sha512-OQeuWEEyBrHBoZp1BdSBxYXHLS1KDyY67w4migBPOHrGJjiC6PffSGjAce6eIEaW+v1HfkEk/kASUJwXWolDwA==", "signatures": [{"sig": "MEUCIQDUBcWyldnJQxiopnYOS8dxwkxdxT5BwafSj5nadiH8YQIgD6zxbHtozMHvfAC/zlEhyaomG0U0CKREQaBCHveoaro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDc2DCRA9TVsSAnZWagAA6TkP/3yD1GklAeG2Pg/EEOue\n0netwNB8D0lOj3SlOFT9O5K4KhnZmlGny5RcEXJrjaPtNKkcaqIF4G5fGN+Z\nwbWfXV0gEbg6dEmhhJOclwXi2kMnb7LmVVhPIzsjftQBbZObomTUVG0fwgSn\nyoGZAxOgAd40LQMeTzfVTQRbiBDKK9nJguzTy9Wq/tOK1TLJmqnXy1iNrDdf\nq7xZM1sOwCQFEFUbG/FuOA7/JBR1UfL36cUP/XOJ8QfhwxdUfFBSZQWeDGFt\nuS1fGVgkq+LEFiKCqNW1BGCy0vvW9E2qNrs4U6HM1HYfrMzXEJRK+rcEcG6m\nmFe69jPyuL9nJoP/S8GvMEVpS1Ndq+1Tt0h5nxwNsQg7UVmFE3w7JlYk3K3P\n3iaBJGjdz5Ntbl28wQKW+g3V9nSMy7beZWOom8RLbwWxAHTQHrP4UjxAn1xC\nXeWK0OLqeU5sd59P7hw+pWXN+RIPfyLsfUmG/ppuk+NbLR2ZUQk3me7Ugreo\nlxbh/gMN4g4EjVODX/Hytf5dEIQPPDSFfP/rYYpHFkUIpxGHl/R/BC1MGE8C\nZmyXBnRHUlxzyqTmCyZfWP9Wz7DPu53+mlvbyIspuiU+FSLOcTGr4vN6bHKG\ndrUTIuZLCMEdnHILzFrUx1ym2ycX9Sjub9iEdEi7JzI+GgeojH9XwvxhL4xx\nYIZy\r\n=CXWx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "json-schema-to-ts", "version": "0.1.1", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "adb3df27c18150c2bbda90f59bd3f91c41d5eea8", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.1.tgz", "fileCount": 31, "integrity": "sha512-zrNIUF9L7clCIFpfty8R+uweMdNdjuT5/B7tYz0JW1QHqq1mLcfO7Buzon0KSzfwMbgjP0ZKznb80HHIHBhBnw==", "signatures": [{"sig": "MEYCIQCaWWd+7C6/cTWGSGdouFDM1UPNpVoTxlqG5nBYfnbhGAIhAIyhH3CXV5kSd5YQABipRlA0ktci1SUfiE4/z33FPjBb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42693, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDdHdCRA9TVsSAnZWagAAQzMP/iw9ech0ebQ83Mj1xHKw\n4RlgxZ9p72QCdJQypcMNCvqjeLOVXBMMUdnKej9clJXS2QTqm3Ntn4DMsqbE\n2uxxcyDOqmV2RoTLvxx2xN80tswypVmmwO3OXaMMRERos5i8fW8QhCTiHktx\nhpd4U3OowoPsgDTdlfhO/PqlF3Qoj7xmRGYKpgprCorCHPXKD4HGrc2qzVHd\nTawn0bjzktXiRz903Cu+qGoE+HkG90xXpChz/27ND1Jrx2GQNZywVpmgbNJZ\npxzZwqxAf9BJPxeiooLMBD3JvwbT/FgjpIg1DUmc2Ovw+yW2zjmhcVHnaIjB\n+BaZmUTZ45Te2c94oHDQEQN2G79hpf8yA7g0vWZDQIQmEkKSMvak5866OimP\njpvMeWQArxCCXPXHb3k7YoiIyJjajDLCV3MQ7y9+orxtkdpxvxFm+44j2XCF\nzZIF7ecGEJcqIvLTGhCb2RieTFPs0GC9Etf7GPiAEUzpCAACgqI7d4rXtN4G\nSOXPJorZ/xQhEzeZ04SPlmmh3P/aqgY4Mq0iETpIAylaCEjEnU+zxJdD9y+i\nYdy77gQ5yGQ5WKFjRsBrXofKwfMzNCICgsqvIGHM78+uiyZb2HF76wif3eGx\nuOkoU+JJ5CW2rsOwpmNo3113goe1yE+tySboTdi+7CE/0BIdARW4Gyory2Q0\nrS8I\r\n=t+MV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "json-schema-to-ts", "version": "0.1.2", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "76e44339407a43236e68d9c855a771de3c90dbad", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.2.tgz", "fileCount": 19, "integrity": "sha512-ews5Ru0PL1Es+JoK78BSWbr7nOs/eMzCms8lzXSSAqwfGB6g+RMqkBV1i6JRykwPYFYxBTEOC0v0MioWQevLIg==", "signatures": [{"sig": "MEUCIFuIaf83wRqAjE9fxRMmBczsdYJUTbnmAjihmAq0URH+AiEA+HVEQEbsPw8lSA4cJ/db7pHOPw8T1NIH8k1RffXcTO8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDdP3CRA9TVsSAnZWagAAJXQP/R+KcVzVuyLNn2yfMiHd\nm4/v3FQWZQVEc4iG2M3i6O/PGkWCjEKy8L+Jf+wcHaKIn7WOqMdk16v4Pf5z\n52IYsY2gP5bM+C3wte4lSoDXjywKCF783eBGySLf551Ifpiny+rFNQxftPRJ\nEKOQv+gW9IJ8zj4XmTNeI/wHOLE6IaZdMZqZ5PkUmB2IHcsLmJXlKxAk8sz9\nAvHyDXokKsiZAz1xSVJElFjGa9ilJ6JEn9Nc7vibLVkp8lb5dy3MScYFvJxL\nY8/rbmYjmVXB8fPCAKxvq2tYdeVlEfOyfvz7FFBL7FbDC1jwPWHUD+Hp6UVN\nsWF+SvAykvIP7rH7+TbkuM7K4OK6xiVBDUdRchiBnrYZjC+43mDyGoBCwgqO\njxQw+N6cp8sKjerTXyS6oUbhKstki2kpqh2efK+2Qx1t8pB5TPSTq9SEbYz4\nUxlZnh1j72+1U6ZNC88pn0z6tC0jB/Bjf37Y7AU3ZI3wklmue777NR2LFznM\n+Cw4HiKhyfezfnYX26GXpjUJMlpi1zUljzajGx39e4lKU38YmmYtkus8JxW/\nntSxw13Auy/hcUl73/d1lJLk4+iv7UZnBimV//PSaU1GIsTeslF+kGA1Hepv\nEYM+kgHkk79QkKLT87G6HfSZxYR3k1X47Tp4nw3dp3MJ3Jyh0lqMMt4hLQ0j\nyIgb\r\n=sYef\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "json-schema-to-ts", "version": "0.1.3", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "b8ccdeabe013ffdf7b481845d2c43daf59d538d5", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.3.tgz", "fileCount": 19, "integrity": "sha512-vC99pyh1Wqowa5ht+uLBoZHleFY7Yo9wAZvl4wFPoNS2ZsWcnm4cLOT8ijSeiQgqWUVudufjp83DZZyRkhZGEA==", "signatures": [{"sig": "MEUCIQCsmBB0vviG59B7HecG+CtaH4k2ilv88hK/7z3RtRSYMwIgc4+OvKRaTVkWABHQJW1MHo0sly8SWv8LE7j+Oqcc02Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13570, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDgvLCRA9TVsSAnZWagAAX4MP/jEVTF8EAwQQXymxsxyY\nXtDyULNpezvltH8Y+b39AtQT/gtizEOV2ozOJKKRgvYMXWfd/QKbZxbODuyl\n2Qn+VFrI3s4VE/mP2wWAk0SkNGoJScgIPGwsqW3irfhP9Eh4HiLLw25vhXDa\nYW2sEthARrB+H2fNRrEbXcESk9pUa8SlHk84hm/7mUKsckCRMQRY2IGvSAoB\ntcOac5RbFBRPUBvcmuRFuPlkKvUmXUM3/2K3bmph2fQ1JGyjvcxoJUU1DKRr\nNg7CT8KXRv1bMnxXGBi56k3MODLaVMNA/eT/qDMh6R/b5jNDgjcEjLuuxw0U\nqeB1RzhV6oda3cBMaH6PL0pEp1sz9P8zdWzHtFQbS/UKbF3yfYDsrKkFqMVD\nyInZktjCcEMBkgzqT81URpNnxQ2AQ5aoJ/rmGQAkluMCr8Rx77keSy92LEYE\n+B06Hcr7Ni9nUpCwOGdJ7EbmVKxpbKZcUR/rEv4aeaQRb4D5NDQ/zZAOSGhk\nBmKwIoeOlF+ZkWmJ751GCt8cuiildx17J3MhuX9G0xNdrwia1mm++ZfWL3Bd\nChjeMv9k7fIHzTVPrkeTlGxYXQowyzUgtM78wlqTigzSqYqQ6r/N0/ST+Vqc\nB2F6u+SpH+GRa0MGNLZ1p+BI0TF2z+tUls+H+Fs/53WfOp+s/sm0+wOtunlf\n5sia\r\n=7xVr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "json-schema-to-ts", "version": "0.1.4", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "90e552526b7258056e50f3c679e039b6f0294664", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.4.tgz", "fileCount": 21, "integrity": "sha512-f6m4mMZ4J3cFKK+OT6f/8WTIDh0kMpx7ZrvjgtLcbQbupa2x10AavE1riVUjKNq+OjG176VC3Qk++75paoONyw==", "signatures": [{"sig": "MEQCIDRtGwMA4hCzU+T0WuFyrTS1u9+4fSeVeG3VZsrNLFgkAiAZW6nK4bKADQ8Crli1XKsV+2AJno6vq/b08Ml/IReXxw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfEddgCRA9TVsSAnZWagAAlAcP/REsGFiNUqYNxQvn3SNv\nrP1oDuvBaYvnDhU+4Zmg3ILsAIhnqmGYZtXwkizq3Zjk2XOJOkbCteOmScIv\naTsoov+f3FfFdUEn/vtuKXxnrAqUqTsS70aroLZ/LH+15oI9tUbhfZ9qajl4\n/G7VRJACf4tICSCpgSA5DUNPIIB469WqfLhhXzhiYg/HjQmtjNh1y+K4smsu\nn5RN5tQ1D+9D65a8bQ5F0HUiln+GrntMr/BqGwvL6BXW8p6M+A52ma3n9Ggk\nhQG9Xvb49plc274iJmHGPK5iFSRMUym1edDzB0rATm5Ei/oUvei6M59Rk3We\nEh/L0lzri4nZu9ToPHjBi/gwwnWytfPqcg5tbzDR4oz96IfsXAOFS990mlXE\nv9VVe+qYX+0Pyba+RvxYlu+Xs3H9ek2O/XSXguLcsndtAhba/Dn671oZBjKh\nyZpkEBWOQfDls97XBqsJAoZHzN2IAyHWJJ4m3bitTtpZmRgNy3mSYvEfCeDB\nxfYO4PI0V6FZDbPMbsDI253IcSn0cYfXZPzH9OWkM0VZXPNvhDXB+0/xaTKu\nq5X6FUIN1P/xaK1fkgI2T0pdbMU6I7LVhEJKo1tydkWgNufV+gwpcdP1GYu0\nZQEzF42iwvx5SYDtPUSFYPEPInNguVP0EbK402HdIjhQv/tM5mjJHXexN01F\n7AGS\r\n=lKTS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "json-schema-to-ts", "version": "0.1.5", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "5845c0739598a265dfb57344e85f457a2565cd27", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.5.tgz", "fileCount": 20, "integrity": "sha512-56TTtoNj/1dP2PDfVMN+drk7WenUvL6Cu54DjZDNMamFNtMuDdhONImTQs2mGdenHvw3HBz/uEZDqzClRm//Zg==", "signatures": [{"sig": "MEYCIQDWy+3xEVzDuEqc59cSUsZ15xYPumgd1/VR0z12TsxXDAIhAPR0tyDYBDl2IorAiskECeGjn14c6I4IYgEJWIM7o8vL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGeHYCRA9TVsSAnZWagAA2r8P/1y1SbZfReTKkTVd/2c7\n4zyjivh3iL8I94F9uJ5MTRR0rloq5kR6hCnf2ARiYWgfrepaGuhk0lUEN+DE\n9K+KB+au7AUk0RbobBoaartXClR6CZ09hKHCFZiYouBNRehyVKaheCXK8frS\nwHVdqmNBXnsdBvGOrpCOW0MiarkuMCPkBzQwNStdhpJAcl71275JyEJU7UlR\nt6JyVTL9FK7V4KPdAavwMXeBMwxKVRPfdykk5d8QHdP8WjyKbMf2DRgkfTGZ\ngwZNtjzeOsnAL8sW9XzvoSBEuvupBI/OUM1rE0FmJCybWHYXDKBcZpl8LJfQ\nXlbmKNu7Wi+3+xW8CGTvnM2EBf6PC83UKRjoXmrPtyEgo1uvc48DUvL1/hyN\n5AkfnERKlrZuCIDy785uHhEVUMSVe1sL5ze1PR3qGaDNgTXwIhoh2TpzuGjL\nb72BkqD1vTdD9/7uPjB+gS5IOZYf46BBX84CWLdmeN7wAamtaEaEbJlMoMxH\nbE7DLLNIQBI6S/l7b/wS2sBed/wkej15pg5q+oelK17wgEENVfCMeX/A5gFR\nEzSQkLUS4mUgx1vJFas3mVovBNLBZOl2pOnWiRY/90OQKNbmh2xwEQzWYCoQ\nKb6n/Lenl/HHyuqh3+UaXTQiSgJBZJWj4acb8vrg8hJko7PUvM5DzR7ngtLD\nimYT\r\n=DDBb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "json-schema-to-ts", "version": "0.1.6", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "42b94d3b328d424ed60a5f6764c51015d4d98f21", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.6.tgz", "fileCount": 21, "integrity": "sha512-rKglogCRusOzQxYqMIjSM44QFWAVfQBn970HoLnaFMauYcs9YmxTcs5Rs3HCxM7mnVjB9QDZA5h07oYmoOnO2w==", "signatures": [{"sig": "MEUCID8e8iJPYVhnSybAIqaKfXuB+1tVVbxIkGCf3+DLjuxiAiEAgkG0j3fUxFhs0B6L/+EQY1KZLqV3LpZMhrR14jQslOw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfGpFFCRA9TVsSAnZWagAAnC0P/26R6tldU82bLdzYrSyC\nwrU1Mh0LWYBhKGq9eybBnwdSlS+uqCxgvBK6dPTFXhgoFgFL58HmCi1YSnlJ\nHXW2G6pol0zZ7zuKDs5xjeLuzCUwCU7lSqOGebLwrPkh/vB2fzjqflp3O6o+\nkZ26b5rRs95qXxIToPe8zfoHEyJPDr9RE4EEk8DK3JLZJJyJP4yJ5296JZii\nVPrjyT+nqD9FCtEObXWTPLY/FgfzNVkJAmoY/g+04LVLIz9mt42WRD+Aoj+W\nAwSaVARjojNxhfWSt+CmMUqPjhwipWhlrRlgHWBGqw6EdRE02mTxaTCMzCJc\nmmMxM1yadTRtu6lfJiKQ8OOHb3KdiouqEro/KqSzd7mvGs/3VgOD0IfI7ND8\nuWm3XTDr+piOR82HptmhGP+XxRiVab33VIGxy32vPC5vgoaYYladjt9Nt5oi\nSNt2gNm6BTfeUTtDH+olGmOggBqbEQaCOyUiCTf+LJgthqqVYgxENHB/jpBI\ngP6SXm23sJsfnE2QGjezXGMKo4A3J5c1NfYJXSgLOMMdNSKCoJwkYVN/gt47\nOnzX0/epQcP1n5ZlYbt/vjHUQ5f9Cz+Ocbw9LZZ1/1w0coRxRV+gLFZZXytC\n7qO+m3FNdn3izvyCR1BdUiTGbhraoaLoGFR69tdj7OXjq+DOpZiJ5EEnHSKj\n6uLY\r\n=X//A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7": {"name": "json-schema-to-ts", "version": "0.1.7", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "5b71f592ce48ac85026fdc4c81f7b382aab378bd", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.7.tgz", "fileCount": 21, "integrity": "sha512-FKpm3K3sGKHiEE4SMH5kgx31dZsm8/0YVTR5/SH3FB51/rLeK/RME98WgGi3Tvv4hl9E3z7ENhERAihGMlSXrw==", "signatures": [{"sig": "MEUCIQCSf11i5yUwymAbKFUZD4I89GINGg4PReSqU5icdrxewQIgaha9ZLwZ0JIKk7kl5lBlAr8i38Jnucrlp36bbvEnA6o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 19010, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfHcLiCRA9TVsSAnZWagAA9y0P/3lfQiE4DnXAJcyD3K/Y\ns1I1Se4i36eqVHzal+JsOSyw8jMSuhBFUVOrjCgWABsnvD0orQSmBwuVINlo\nsiIH2uasjKyNaawLPhNaHqX14mwL1hiBjXD4POiIni06arSk0mbyYDkJRIGi\nh22Nn8NaNaTU2CEBiQWZnMnNhaqGbYxJQyalwJm5DU+SVEBTjWzWrLIf4dJ/\n/TUOYNZss5fMRh/40dDfUlVm9PaVMNkxvQmh7tW9CXw2R8bqYURubQVT1wPs\n+gGc9va9o5o/RA5RGZ3Bd0U3gosUC+ZH2ROZgyJO/jkqn5hK3nbUgApsf2ix\nce5rII1J3N7vNJPHxvc3EqwGTbIIOsGDvyEGH36+deN6KglkzHwgn8cqAsI1\nzut3oagX7k9cYg0m0VuphmNvWPE63k2ZZ1nJj2HCnSAyG7tHTLIPa8/e4nIk\nsYcTW3l5+pAISUx28cRvjkT1kWM8Cv9uG4qechqbtm8dPK2yOJxc4sv8/3Q6\nsX/b3kZOyoxwvtGF0aiBkWcfezSaMA1AlB4IoLzEw6u9vS4Uj4ZxLGDQUQsC\nWCZ+5FObYBYfNfaBhtq/OHlQWGKoCuOZPdPECu0cvEy4L6ZRIiO1De7i6wYM\n16uOlJEbKnjHAkuAO1t068YY17oRFXZpEiqVEidFgRg6B9caTxQDk+n/uYt1\nXCno\r\n=/jwy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.8": {"name": "json-schema-to-ts", "version": "0.1.8", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "8c78696193d390baa492fd3fd66a16d1284df86a", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.8.tgz", "fileCount": 22, "integrity": "sha512-iBd7a4FuTEcfA+T2ThT+Sf+8wZXrhPO3e7epw6+dtjlLRO0HSo7FECCY8whNwGiPnD6e3WmT2iATlmN5XzUFow==", "signatures": [{"sig": "MEYCIQDUzeR+Xls9QDcCFjb3ADNFbekS6w8ENd+tTVpTaIeXfwIhAJgPbZq0OnTKeC9mBXqZMypxPnF1zLmhvzQ+wLKu1rXY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 21528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIbsdCRA9TVsSAnZWagAAu3oP/0ctgJQ0CPIjPDoVDtW8\nOzFEir803a3e8Mf/cGH34a4cBvvwmgbqWBhGiOwmIbGMVMPYzGfbyAfN/v7c\nfPGZ+5NHS0i/5l40QuO6C0MHyzve7xmNfdBq7Rob/aMK0G81XeuSkpx1B27H\no6Nc5xULKfk11pfQ9OvKr9rLr++f5geff2Fp3RbuYmE/8DUpBann91BED+OA\nWuTziQ6lJP1Kd1E2GEIlepnMfhvrzHEzAfiLsNHbHv+g6lC+RApaCaz2JXEO\nwx+sfVlCQ/7pRYGAnbJwkTICo/V4ZVMyfuhmqwbpCR0LQJ0ESQIvMLJ7n6mr\nHqK0k7uiO6RwS5nVbHCG1c+m/HsFcsfOZ9VjvE6JyAAGejtPGIvlmI0F1/wN\nUwvDpigBh9DRhIpUsiexHARavgdxLW1dGACbTPceWlsy4nQDaGWKtxTVbL23\n/BvLUsFRpHqWM+dEUsN0Om7f+hQKUKLpP7DabTXeE7w4jZwJQc20yyMgvsXh\npdVt7tTIWEmQRwfulgfCV0uUttM0ZhYm6iDiSxlj15RfRTxjvBj8hEFqGpt9\no6DDTBdfenCXDG/GAWt+u+ATAtTjKnT8NfrzsDzCNid8LWc0O3jfSz+RR/tA\nZXavi6a5gGStpmmmGKUrGPXMN1sDdacW2viS5xQj7SpG8LFi9Cb4+1Moccmx\njS3N\r\n=qOka\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.9": {"name": "json-schema-to-ts", "version": "0.1.9", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "89c8f0e43dda00ab4d27e1033eb65a94b5707d58", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.9.tgz", "fileCount": 21, "integrity": "sha512-cKsCdplLvLfxSzgntZW+TdJxW65gU2811fsYDsLAVOj5qKaIkU0rgR2MVw9U0QjHWS8vx0IlYsC/TXcSilf1sA==", "signatures": [{"sig": "MEUCIQCDM4b1KheVe9S9Ug7oRrxRgE1ZZ9C0660EFpoUQSGolgIgbISodqHG4xt/3bJrPXWqJcHxBntiMPlIl570gMPspeE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26542, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIvQ4CRA9TVsSAnZWagAAFEIP/3/ycZonthFTJEuZ6wJP\nRdyTleNpVYO0nO0oxf2nMU2CvFtd2w7gh5mgTl38FCT294WZz1Ep2uVsXUUL\nQ6cRAbmfvBC5mmtuGkLBqxVwk9lSNCGQoPga/7uBVA00QzpqIy3Wol3rMQ7h\nGdwsqFwkgLsqtEzRXJpvWoS7RQ25xR+ogtf5E1zveZmGFS7iruMXoqVQAbag\nwgVg8U3KDUGKBnsG2xJNqn+TtLtiCtJpXIzepSWdDLK7vmot8IgXBuUB5D1z\nv/QFSXWaR5f+8toEK9+zYM101VwEjlQNPBmssHvfjlNVNer+XWqUlWZ7V8+l\nsC+V39ZMUFd3fLVIL18I36M0+o6A3031I3mY73I41rfvaU+RE//8k16u9CoC\nSqk7q3e+Sq8g4KJq5Kyp+xx8t01XU4HLrGRgFfTU/i0USLbmqWXudTc3pV+7\n2ifhCwk3OQul0OWkNJrf7HDPYBxOGCJxq86+PwKtCeJKlX71RBdnrhJH+SO8\nhEVxnVe+mhXpFBDXQpRq6bPFZEkS6TjNnZJaxzC6LSdSL2iQpnB4uiqCWHAx\nvc6ZM73evaGyYqSj1zJtMoOsXcOi4GcwcA67AQ8CUY7N5YGBCiopBsCB5zwT\nbiisOTLzhLv8WOls9AHA4ULzzqfLerQeJSCaXIV/nJ1cJpSm0UtXoA5kkboi\n5PDF\r\n=4449\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.10": {"name": "json-schema-to-ts", "version": "0.1.10", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "00204676fa6a0d29fb7e837933117a842ff04a03", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-0.1.10.tgz", "fileCount": 21, "integrity": "sha512-6WeRf9PutoTtGvOL7Jvdk3Z3mRN8XHnhJqizbqIJ0522YXqdv1eB9se8sTDa8r79U0kT+rD5iCZfEeZdrt2ujw==", "signatures": [{"sig": "MEUCIC75VxdZqlnczpsqrMupJeM/KNjK/CeXR08ycWfX28jdAiEA/DbZXJmiUZTCT1FB637xzzFa7+BZxPgjWFkmx3ACeVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26265, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfLHZRCRA9TVsSAnZWagAAvaIP/02hA4+djhLCbsVar4Ol\nWObK1rZHFSKNHkzuqEHFGcU5mXvBt2Qe7Hp14zb6zLU4HhyrKrpXmQzs0mTq\nnk1tY84RNIKeNR+49gERIDgdHZoQbn9mYCOmhO3Pwk3kszEst1m6yzRzPfLV\nMAdoBSNxoYfUI82MdbwxEhTPRiiYa/UTOsMKE8VxIFGpiLcBkQgrN4JxcJut\na3WLaKpWw/TBBGQGSJahM5e/s1I2+6DVUS41U87odwPvJIIiisWHZylOroTa\nIltAbHRY1A+UvSYfLSEflxHqDUgezw+5MmpKC8d6xweCAjPhKOdX9WxGMLBm\n8fIrUycS5NP7erlxAC/YcXU77vNH/4NExbed/lH5gY+ylcqv2kubvWq22Pyw\nNjaoGhUCxSY8MNp/7gWub9y5AzGeX+tLr7MC18eg/lF7fqF/ThJtE4RLf/xl\nyqNjIsyDYdfrGp0CdzG3PLeZ2nUb2ZsljWUlI5MnDl/u+VNCnjmptLs0MRK/\nYbYpW8PEsOjhE8cO6PawbmO/3b8LBDtFwICYELiPawjEisvjI9TJyHs0nxUC\nY0prohOruq7pH0+7wpOuy5pqtYghrV8eBU1eMJR0FG2+0gCAMbh6UVXzwx4e\n/olzx7FKas1NKnZ5EG0GpQYq1EdRyJGa/w130kVF3BhWNiPhXtKomQ5CcPxM\ngBDP\r\n=7ANu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "json-schema-to-ts", "version": "1.0.0", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "42774fc1db65390ee8b17a5599b59adec400ef41", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.0.0.tgz", "fileCount": 44, "integrity": "sha512-0wdEX6GNeXjaZGrdPBXkbUprtZFE7no7vKMy0NVQW36lhUZCCJfCDZJ5EZG9BLYGbovv3+F8xIWficxWD9NHqg==", "signatures": [{"sig": "MEYCIQCBj8U8Huhg1msFF/QDduTZk13iSSE15V9wsoxv11JWtgIhAMqZsfwltbKGRYVc2oOuQu1zwx+8AbJ29+lfDVHE3UJu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfVWLtCRA9TVsSAnZWagAA1zcP/i4382LhslCVqrYaYu7y\n5VQ3ccuJ5aRzhBH/1eG6pMS4fa7MF1R5OEs9WOsCYZox2b3QCF6MqohoqqYT\nA4AWknMPsaNpdGvN9WN2t31OUkd4iiusQqfk9fxwziP2HWG98J94kNYbTodz\nr41oFZmpdxYK5I5Z8sK4CMWicC0TkIEUPD0yYJJGr4F8cYvvz1+It5hTAuoz\nB3D93OEfjCEP33w2yvfCXhOe4cWWL9gda2P0TpcZXjAWzvYA0cTqbBTPjVGu\nxRV/6l9MPSq/dvUG/G2MTVcQEIvIWm/f36kmrhz4iAHPK+4JdwgSSB4g0BrI\nlZpN0KuQ+ETd+Wwfw0+hT++QqTL81Id4LA/NcgMyJJQfwzB85amblqfe7aUy\nK0BoKcxa49pOvWkRy+9cKT/eYOMCM27cvynyts90MxV0Em26ViIwhTWPcrlJ\nfNRjHeUCy6zpQJS6NFQO8DuUl7XKIs6Xj7xY5/6my1BtNSLUvxaNSddat2gw\nEjckjFgbLouWUmwqr+ENhzma5hKLQpCaw/h4+eJwrIhTlYwlUlqepDsiqP5Y\nCtPvlGckTAiLTe/LV2pTOoW1HmMBU1QMjU594nyncipyMtQ/bVvByif8djCj\n01IFqMk9kW4VOl01P153V9n/d/mcFez8AGuH5cH6W/vn8SgeVnrS3hzPxqES\niQWq\r\n=7VUe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "json-schema-to-ts", "version": "1.1.0", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "abad0b86748da01f4bf0fe17eea6b4572256f7ee", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.1.0.tgz", "fileCount": 44, "integrity": "sha512-afQbkqNKie5lN34bnOkQvcKHEyIi5J1QjM7SGilsG1ARRX3IfFhldMyOUEJ7gIdKN5mJjAw5KKOwboeV5eHIpg==", "signatures": [{"sig": "MEQCICptzm8y8DO3ykHHJBUQ8XJsZ3crpkwJ7AjgH2gooO85AiAzoe0rrO0iOPmjaI/FPTbCansJrM+oGFuRyhJHEipx1Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45338, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfW9R9CRA9TVsSAnZWagAAM5YP/Rejp75xJ4/DLrm18D9H\n0SONCuqipAGlRkz1VEWZaqrmB0pui6rLlw/BdjK5dGG187SScaIoj9/HEGuO\n9fboRozo2DDEeI/AQdmzhv33my2slmg8BSSiqfmb9+Dwse+gpDFSd3VbOZWk\nXiI0Dc/UQNneLgSCkzyQDFj7BLiaciIeCuksavrK+d+QA/V7EEYb9j5V/zkO\ncX0lrHVcYrRXIWSPJaAb5ig0MmzHQfr97W74FU5rFJPGR53NcdZ+LUyFRD8h\nYsude8Mf5/y7J1s6hKKE+FWV2NGjAWdeZK0f+fchuDgD4ljhQnAcL2yIgy6d\nhbmh4vjqqykcItnvDbjAHZEkz0Ww7M4p0gU8vYWE+puDmRSgMv7AcCGzTrvY\n+50yX43rys7WnxWPq5TrxTHRQIGz8KJEfYWOkDjL+yPaJNor+iRhVV5QOg2X\nMj2pqHSXhcWMBjLfQNYkpitRkFnUe3rKFkTMXI+VkMYOUJgbMlK9XL437Txd\ntsuVOSLMPORxX5E6QIG4fMqC4ceHYaxNhqWA8ZjRSk7SrCPogdeh4KLPC0KJ\n1I0NwHMgY5ho8ryaWU/hgvvbKWFLlUlzuUPGAKZpgxK7Cej3W3XeVD8EQlK9\nEEKzTWDzzkuiKVreehq1i6UJ0epTj7Mq2z9Yi3nG6esmTU4ynIgoA6DinfOz\nkk/O\r\n=RK/I\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.1": {"name": "json-schema-to-ts", "version": "1.1.1", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "309a16b514ef213dfb28835d3a0f38308da43209", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.1.1.tgz", "fileCount": 44, "integrity": "sha512-7iTx4EnGASw56dTRyTXr9oGk+7HY0DbXY0pOruKMpIh+/BKvJTsG1d/K24kXccitwwAzVBq+5rs6VgGbS/edrw==", "signatures": [{"sig": "MEUCIGoCW2DrBolQFe8XhJimVCFYhd/qcm/qVk59ld+1yNfBAiEA2GKHkxJM4XbgJEblnU/nq5xs7y2tzFrDBSVROnPwsqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45209, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfXo0pCRA9TVsSAnZWagAAbXMP/iubA4UWVh+J+oKrdGBr\nCvPtMKTLhRGOJ/m3nTl0ntLxQyyUCNNSp3g9wNpsdU5bQ3yQzvsF/U6t+KuQ\nbnLbUWCso82NkNfH0TvxSaqAY4IX2GZ0T/0UVdxW2Y1vRetdOATdaUK0rXtT\n1R4mO/wq3T1iR9KWj/z0lwwmXUD2hXY36hm9xWaNnO9v0ptl99v59VwKeErx\nMAv/bgEEkpJlTMKMgU/dwkLehlzCYHWCEpeRxH6IhS3tLidIhR+zJn5IO9za\nRACaYVz1ln52VObYq+TSU+UfxOlS/IBAxwjz7XxArNA//TUmvRpaLtz1xtEr\ntzZTZG81PoNUBthFql3topWoDadarkBunm36dQkodJOmBiCgJC2lYo+KAHUY\nrmX0ZusWNOdG0DWUDM4Ff/HgykI3lxCi/KDZYqnkblzfDGZT51tkeCuGooUM\n510Cxw2THXCkE0Vzh2VeF+1pV55KVMDz3hMcT5WLHj0w10e+qM3JgGsZb6df\nnaEH9Dh70sArJpKCXT/fEqX2JFff4/AFQvLE/zgRW8A9pYZQZMOKBKj3f2xX\noCaEo9DMWWM+19ImeNPd2El5MOW6aWd1d4NCFNkAr2RviaMU3B1WtaPwaGNL\nltBsQskI7EkyJDBIohI1g4LJnAKDCvlhR/0Rs4kX5uaGqy/bFptDuJj2trSp\n+coo\r\n=JzdR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "json-schema-to-ts", "version": "1.2.0", "devDependencies": {"ajv": "^6.12.3", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "5ec828ad7c71943cb1583a32323e1f85f2237140", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.2.0.tgz", "fileCount": 45, "integrity": "sha512-/51ifjNnPCG9qY79CnTe5PSQ1ZxSfWkhz0MPGDJY3Mn/pSq19WPqfR8D71Zd0uBavrP/iO/lsUgdgDykFpfz2w==", "signatures": [{"sig": "MEUCIDXyrqpci6AzxxqDG1uFbt3dBTlROJaxQ+UE5EfxdRblAiEAhaDOJfUzRrUmSVXlxoF7hzahVy8y1b3BN4lht2NDNK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45373, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYUlPCRA9TVsSAnZWagAAgzsP/isjdVVB7ebfgxYhbroZ\nza7kd4UYZya3fy7peEtUffaUVjKg+e+1084LJw9anKc9W24cPSB99b7PSc4B\nlpwhzwHcSgiO8XZSCmNAZwDW8qBnDSY2a1ouLdgKaNVcKKTpwmBE068Dli4H\ngmXTgpZO5YgmEw0bq/lFId/sNmdo4Z7nINJEqe9xPHDLgMyr5/1tZWj7b1DF\ntkeHPmWEWhxoEwgRkexkSAhbXpEojzXRsDve4mDXAQxYJtMfhMID0f0j+YTC\nGSWpXKxqI6RKLIqwdgDFM7JweBIxJrHZt3dveuQvAWUtpV4x7Uge19N/kO2P\n+AVDqkj503yOjcoIGwp7+RDHTDM9PLfEpRARWncpeaLhOj3eTGUTBIB0iFiT\nj4GgwjVdUxRwOiNYG3Sj1HZ+DdpvN2kP9jAWzeu1rruzFdrRRFMWr5JB7txz\na4hF7GrK8I8MwUUnkJTD1yt4elkyfPs8uMWbnc9RPW9ZDsOipj8AMQIuuyPh\n2Of0XWYfRBSoBWS6nDGFXxGoGdFDWW8y7qCf+ZTMI/otcuOxIgS+/s51mTkB\neQ7jMpJSo4T6yZZkk+YkE0wmwv90rNKVZI5EeZU9NE5mz5rLCSWuJ7AAx6Ch\nY05ttkbjO7YFckxoLCmSXsstN7nJP/NmtAZ1opY/fjfQPxsPm+FyQq/Nw2wb\n2qs5\r\n=Hn74\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "json-schema-to-ts", "version": "1.3.0", "devDependencies": {"ajv": "^6.12.6", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "9a0f3543a757843c2bdea3cc567dc511802eae37", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.3.0.tgz", "fileCount": 47, "integrity": "sha512-CLd2U+UOlcIosQfqvNXj4bMEZoKcXdl5S86Gxa6exA9dvnHVR4CAJw+oCzPqo8794reE/xYx7LL+iiObVMfZKg==", "signatures": [{"sig": "MEYCIQDtq8wELwbgk/wodSzbDAcUya2GQ8R5BTaMNkDt23TXHQIhAOmKNlS7jLzZ4S2/SEWD5JfNkcM+3haRvc6fLztnjdyS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwjzgCRA9TVsSAnZWagAAA6MP/jqwb5/sA+ZSUf98NsTh\nUCu0vRtCAzUMWyolyJPQMx/eaN5TiBM+tsbE26agAxHvejUzF0TfHBeedV/7\nvFeGAXr2xwWRL5jKJ0HQLzjdx6uAormPG12eHNp5DCUAeCEadft69QRAIE5m\n1p7UGOz1NK/D1iyIi7RbT5gZz8su8jFQ0hywENHNJuN+PDfsswz8fRO+DlPE\n2VtT0SYQdmuyPtmHELafI2fmD4hiOlt4C9hH052mWuftMP1g+FnhZBOYM29O\nKyeXfWvBfegZUeoOHCnnycokKyKkQTSfI6kEu9EAYmTuKY4f7Qxe9iuGVvD/\nenH5Mj2QTaYMVHeD3antWK/U5oNw+5jSHKyEMoy2gAH6hTFmuqVGAoojtCiJ\nq14M+G4gbxKyFR5YsvystGymr4gAku8Tj6Zo8Ox+ItxU2JXtxL4ESEqjjPZj\nbopDp9BfBzrRUs1mrLoZcBwQnSML/PWdbErPOOu/zQEC+VjRuYrO0WJ8iMEW\nI4i5GYQRsjcDYTSbvLXHE3Ly06jdpomjGaREQtNPJCqLFK7g13wspP9ipx2D\nnJiHEWk+yJAroJxT1MgU4sdG5nzAJ6uTzbsN5GjhY+D7qAC1sMNPphVamLGT\nqUugzTiuk9ANwxdJvDFuVIAVbBVq/wx99hhwncPmKJB1+dJBe2LBEDBMVTUi\nFFld\r\n=kJuP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "json-schema-to-ts", "version": "1.4.0", "dependencies": {"@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "8df4baaea49ac73e27b8ce8d10c977adf9e7d098", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.4.0.tgz", "fileCount": 53, "integrity": "sha512-vDe4yt12YLkNpEi8Nq+/QgZlFJQkWmPpn/MxHyVdDsh7LEO59N9qFxY82oxW5YOFUpcMNVuhN1L9G8FdcxJ8jQ==", "signatures": [{"sig": "MEUCIBjEZBSJp7icZNylw32fpHxaWKDOOkmeL0bd4F3zbCFGAiEAp0r5eZNpM7kx/q4VBTTlLpgxMCgYTSFr1OKItufipdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwpyHCRA9TVsSAnZWagAAHOQQAJRf1UnSjMNzuGXCZaiq\nDpv1arDWEcn9Om6Lj83nLmscwBmhl0hGlr5Kbc52Y+ZSzBhfXIIzpCCdU3nF\n1DMPbsPJEvIKMsCfQyaXJZIOgrQvfVCz30AgGSkh1kPpdKGNDqAdsT7letYL\n9ExRJEnDp7O6u7a3+wEJ984uHCFcc0f2JFb+US2LCAukgGvS/e9GZvDh7c4n\nx8d7vk3YUdekWUTtwa5/wvi4tGE3OuQkrqFTFZSb0/F5qZ6dAGqPdAS9itKt\neO08ozeWX86GSEBtX4lNfx0RrEm0MuBQbiwgrICnIQyXgoRuVm87W2vdBMNs\nQq6f2GsHxv/wp0kjaik8Z0GRoTBZm3np32QPl//UDI+U5JQM1TP97dioj6P8\nb97g8t28mFGHxEyEkVE6tRFur5yUBXugNsrSqVu5/kRDU2HpfpC4q3EBVKYS\n/4njX/Yiuk6gAHsu/avAByyFWpPnO3nnZFAnW0usP5b0ckSgC0rdcNxs292L\n2MwPABUmH0dO0zkH+QJ97PZWWbrpjnhpI3h9F0o2sH1t5mglS1wQIjUWoyrn\njDHkJ9ROagUd6d8wIqV6zXk6dNMvmrGYNGaEtOxU7kXK+Nr0TS6bVseyHwKA\nT6po9KnPWsJDsTeSvc1p3LaYNfo2cqMfjHHLFZG26uBRbXqf3we+FBfTVF8v\nhMjA\r\n=awPO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "json-schema-to-ts", "version": "1.4.1", "dependencies": {"@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "608482a9c9a3ffa917fd035690370b1ddeeec3ca", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.4.1.tgz", "fileCount": 53, "integrity": "sha512-IzGQ1mdKWidEmxGAtgBfcfJz7y8uuohK4qIEDJdmtMqudp4rdYJaDjMiHQmn1DOTFInwRILsZa985QcSaSa9Nw==", "signatures": [{"sig": "MEUCIQDhHsgBvIbvIhK+cl5r4fkOtZnqqYbVivtQzKGX/8npywIgPJ1pz3XVvmN4cS7af/4hgxMNlefECxiynfYdga56t7Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50074, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfyA4jCRA9TVsSAnZWagAAEdYP/01zKwO7p6m5oXkYcy1J\ncbrPVTZNZ5KXXRElHYOkdwsobXupyU4PTA+CINYupMCTtlGqVcsLTH9CiQBe\n/lc9sXPgHjAWtF/6W17adggJjW9uVRBMxUOp3pybId0AqUu6HzHoHwbiaYmS\nxjw5gOk0YjEKaYJSVxPz9buKD5UZzWIvg3IU6kwZDWR5S+d2etdPajs3GVQs\n5MqtV74sCwhLbf5kewf2St8+PXUbTCICdXtMuCfW+ajT/7Q8Z+CpMiZXUktU\njJDgYxMctEnefwe6HYoOFj8Ci6r0cFWF6GQLLQJp9AtSMcMXUbYbbSsBjg6w\nClpTUFtEaE/6+kunP8qoEsClOezxfX76RRsDUFfUiFP6zReERdX1jtkwrmTP\nXE76cFaDoo62HsnypICwZK8AN5ot0050IHboBtknzE2fV3zH3VaEgVN0hS9i\nxBkeLPcYp3lp8fTI04OL1uMZ9OMTsB3lekDjZMh6JkhKcdmnJ9BI8RTviM3G\nhKCKSYItqoea5v/mfihJz0nCMOPYo5zEc9sht91h1vETt832R4jn6AKHufKL\nXuk/vfv1ST/pwzrsbJM8PePB0VaTwRKIGz22ziU1etlFfQ0idCylnU4sHRdd\nmpRedGNhqigCJWIDUiWqav1+0BA5XZtAxKtceoEOfbENQ31hBhO1tIggWlKL\nsrA7\r\n=5TYr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.2": {"name": "json-schema-to-ts", "version": "1.4.2", "dependencies": {"@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "544d33facddff4a3045cea0e55d9dd55b2d47ad1", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.4.2.tgz", "fileCount": 54, "integrity": "sha512-9O3IGtMRrKcepODnYJCgBJtCHRwqnJVuMjFW4tMtjAKP+WR/qFNrJVr610kT47dIBM/wEPwv5iF0zDQVLdW+kw==", "signatures": [{"sig": "MEUCID13SNUQ2DyN+nfpkJ2LY1lMX3WWJNh0G3ucXvuV+1gfAiEAi4rHbxExtwB5/oTe3SNaI3W+Cg+/zVbUuRtayWV5nqM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf5eKiCRA9TVsSAnZWagAAoCEP/09DE5nEKmdtgTN/hiw8\ngCSGQUIIFv1F8cllWp0GDAFmSBia3jR/acRbT5ug7Go03SHyJDoex9uUW9W7\nk5qwT1T7KB1nRhrBRY5Me/LfaL0gTnK3390uiQcyBve6li/ac0dxdXP75zqW\nDk/7yPBeJCDIfuTtrnESV8qETxS7yiGAEPGiHeE5BZ2cyoSJaN1GE/9Cgc3r\nCzHV1yfkYByWfegEOGUWvUcbKqqx/jpo8klUpqv1n6g9vx2LScS7egRG7tml\n+4peViwaTGqj+VpTcEXcn/nqForXopYbapSq/Dk997YjeQTgj063who5h1mF\nxHy1zlpHIDirTSNg/z05ZoFW8ugHI7anX3zx/ZMJHFQ4ZXtdCe9v6oWTsiVv\n1+3azRwKg5NpqWDvtIqB9DAlN15g1zP1ASL1m13ZfgePb6UFKmQa9dFgR/d7\nwjgYHmUX9CdgdSBSAhSsygRq8F+oHGMz1bYDlOoziL5o4h1SUEqg22lzciBW\ncojOpY69Ozax3321DPtgADLqt5S/R/2P/UScStOSubSiJ1DpkGqiXwXleXtd\nuKUcp85mltRaJnc6dCti7Jedo0VIfKioDz/BARDvLCHMhuszPul6tX/a0qTZ\nSc+euAEahEUZW6CULZD6O68yzs7Qiys9QGOCcSchiW+aoTWly0V88JCAVn30\n8Igl\r\n=U4p6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.3": {"name": "json-schema-to-ts", "version": "1.4.3", "dependencies": {"@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.1.0", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "b9600cc17f1d0624cdc3a83c115fb1ba5fe1f8d9", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.4.3.tgz", "fileCount": 54, "integrity": "sha512-jUkbNx4kPkhcJosTMSio+kk3OsFxuvq7dTySu/+czdYZ051Vj1PnudcxkJBB6LvVZxF7OhWhAflGfFU4gxvD3A==", "signatures": [{"sig": "MEUCIEsE/9XLBA8KPxbrRln/uJ+a/SH7gkBOn92GjGYO4JZLAiEAqrxWbH2V0IRph1w/r1mgDzPlyedaBUTgvF+FuUzHOFQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 50568, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6RCuCRA9TVsSAnZWagAAoXcQAIvO45MCsVW2zi0kWmnF\nK+wltaG5G5jAYrB/7TA+wPoNfK93idPEMCYbYpxdlDT7yVXKXwoYIbDvx+2a\nMN8x289bKNTrPF4sFsJ2Ia6Ejk+VXw1bluHTOBsSfrnIOMAUcf1ewl6aW4ZZ\n7FHuQtTp6qwqCrNO4F0nQUPEWQwXHGs484kLQ/PtbhWudNFcNiEtAb1CmN3c\n02U1yeWibLxRj0Gn1740kjKfmavwmCbOzUsIMmswoWQ1nrlEcv+Kwd69+haO\n3UdYmfsU+BeO5+Rh+kDcP3x1VFknm1TBCtnvwz/gBl+6rtA7ADozSkBbBPME\nD4NC0PYMBfBxD8Vgim5zVBolUyp3UG+TmO9qM6J/gqN+YBrMo4VUL7UkuO+d\nB4bNX7+xozOqrHRoka05L8AK73iHtuZju7pM+fmBK398znt4yqpLEs+ef7j1\nzWBLlAGwFuBU0KrPe6JJq9MwpG0JCkSdFwYU1mp8TLd2TsoV05NzwmqCvFuv\nf2JlQWOOfRZawBMZEbpEX/Zu4NZ8aOKe3DgGOTRxpWJpbk+SY2KwgvDK5wI4\nhu7csSgkU7+HlBYYybDm2r2zjITg7lBrBzVZIR1JBV7SVHR+21U6cq23wTTK\nYYs48JGhfSFQApTTMoCnMdSxVDyMFDDZUnR6h2uS0pabpkVMBAMOmw33JGIT\nNO2j\r\n=ZN+R\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "json-schema-to-ts", "version": "1.5.0", "dependencies": {"@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "acd201c9e0b9b7f4aea00f146902a490551e576b", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.5.0.tgz", "fileCount": 57, "integrity": "sha512-f3iYImyX8wIVQR8e4tsNWbsoSKhq/lO71SjZKl35THbmsN0d3zWyZgvo+wueZQVeTnpL1Df0uu9qoC6IICzdGA==", "signatures": [{"sig": "MEYCIQCfN+aHlLhfv79EmCbdXkbvxB4oEv8TPT1+L9c4/r1PgwIhAPxHCorsDPQeBhc6R5LVsGDswTF7sOTN2rhrC/awoA0x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 54285, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6kE9CRA9TVsSAnZWagAAOpAP/iEspno8vu9eIC1qDOTM\nieEhPmxaKWCYrfxn0udsoaVVxptRt1XZ3CCQuLnOdkLzYebQIw/Pom8FUqV+\nMRLfw8VHr4wpdExWWB+w1dfEKLWTZSYwss33IMhWF2MPt1pKtJc1q/dLuOlb\n5xz1Nose8wR2w7XLEGYWDuGLFGYWxN/sTzbtwEuAHAt9aSxO0o9J7EdNWMdz\nIQ5f/bLQVyXKcioU+KcrS2OuO84iezqyZtwcixbWGtxd1J8afeZifglfxmEP\nDVxRADqZkguNEN4Ga/x5zZ5HGoXE8caAIlVEIUHmktWId2tctyxUxnwPDijB\nB2xSpIwkrLzgbcDFM3InDHp5fuObt8UQrp5j4zZoNIkzxupk/GoMiZLXl87l\nawWaeQ2X4a4fBJ4IOao9iXmqrUtNZWG/mjfo8BD6Lnf/JrbktJTk6qFJE0nk\n4Ag9J8Es+OXsXnAMFmQNQWQgyo6USLQzJwPn7qcEoDpoZv5AYqTUDY352ulC\nKPM0835i53rcuijxLjeF546HXn7eXIeZ0WUcyPen/SCaukHzYhnq+kKEXp4q\nDLbthpEYPXNNrJCV6sbO+gEBVdt1oxRxfX6k8gkgqXb+86+BMFKwqZM8vBgf\nlhoZnAdO+CvWVeBfncujFOUWk4vBRMeqbh+H6KPZ69+5TMzHplzBZuITVbvp\nmxtx\r\n=/KJu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "json-schema-to-ts", "version": "1.6.0", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "b0ba7bbcd00730f9a75f56168a1c71a1ddf903fb", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.0.tgz", "fileCount": 72, "integrity": "sha512-RB1Tig1b6LgB5f8OsGzvAFjoRvxkgjfd9A93kGFrhFhunBjMueMnWe7n28Lys92cwlNmEb4nD4q5YcyLby1t7w==", "signatures": [{"sig": "MEQCIB8jykXqCE1ZaR57P5pa4RNdZP2c5zPbec1HgETLlbgbAiAqq4SRbOSCA/VPTmp7jZZVvWqOzZqgU0G8zRpDSrkLRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79167, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgK8VtCRA9TVsSAnZWagAAy84P/iRFCufbfQTlrhYDKdGC\nRz8dLDK1GRjNs0EAAutzgL4ZEkCcE0YKleMqONhywvYoh+epJwBkES8EqgzN\njiW6LzU3VvT1ndxdxMF6bEXfmtwgDPRDlrdMxbKBJsrg3haIgrZ2Rq2D7G4M\ni8oDknVrKIvolL0cvw+HMVxqDZGnsDv9Bcbv97t3HCdKrE9btnUfSv+/VDYs\nYxBYxK9qtSY2MU6hJgOGdsjS5upmUpfbH62CNBP6fUpa3yE1PCOat1NgxDkA\nUCFkun6SyB+zzNC/3PU8KQTwXDL/EvAS5hCgrHRo98nIhlZDoh9W22dP3BI5\nbtwM45AFU5dqjcMySgN7bT8W3qRGXs3pyZOx97jeDAFr4fGHIK82QUTbc6FN\nBSHY7UCHaMXzqEVrqUkSY7se9NjwwVu2usyBhGwbEjoyExkQFhKUudc2HhD4\nV7A95sEg6SHeSuLwT5wzHRQUqXDFK6Bhrj/MjjchZVC+2gELZ3W7wO3NL+4m\n28b9liMbybfABOZNKzD3D5xHXrQYshilE2ELepW9W8EuhUqXgSCueJ+HZNal\nRYdUOVoeJv9s73+zLY72U6Qzwlnw0Uy4LcmslMkrIeK2qvUja+MhBUF+sP3k\niIt4TiT1BnvPeUr6M/MyaE4+Umnm+Lq40dS1zaM198nGD3+KIQ67Ztf8bDZC\n/NZS\r\n=l/Qz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "json-schema-to-ts", "version": "1.6.1", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "acfef727076aa87a9c80f4e759a5cb387a7f3133", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.1.tgz", "fileCount": 72, "integrity": "sha512-r+9goc1QOdiJk7m1pLncnWRm+7PdjO3MHLau+wf+9kQnI+SupuWsa4wNgBw7UfzEq5PJ8Fx9rK8xM7oaB9SFhA==", "signatures": [{"sig": "MEYCIQC95TAeAxrBRae2bVq/GnPahwu2zp8pIIVAryRJqLiOLQIhAJjv29OURfuQE+mrtoUFIX+79dERQJ8mDP806WH/smEc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79146, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgV0ilCRA9TVsSAnZWagAAOjIP/RA5L+srKSyT4NNMWzV7\nZUVQABBGowyjSBzsrYWOn3RAJfj4SMyyNKFoi1edXKZ1S6BSwgKf5tbUhwUl\nXKFFsJqa/23ZvlvevP02uPinSb8dyZKzCuVezUDHZaGltX0Xo8N1eDiYznF+\noVBRBrXDL8GKNA2URZlvbKEq8hLCIdBha8MrZDmFJcgB4AHUkfCe3bcDXq5K\nk/o8TPW+Y2C7qeGDXYuM+1bSYbW68cb5g2jn4Kc7VBC4xxH0w1FY9gZ0uhg7\nfvBKdS4ECV6sikacgBIjOY2Dej/lVzf6cL1qsTxjf8ESOtCV5gk0H8Ch2Ejr\nl8zgPT5ilGWV+7i6XpDujcXE6AG5bakZnTdrXhDdYZMTXGUM8d1S0E+91EyU\naxIJnDBh5A3ODngs+3XM5Wb8jAikToA07C7+00rChDam+Qs1RGUQvfiAzq6c\ntbVSUAPk8OA+tOiyFUHzLdXG97wSf49FuZjjzoWSkjmcZV5vyxVw3j4zc2y7\nlNLmGUK9q3HcQ93qHmpk8Wopkxh0JC7p4XDFG5lDqDu0NY56dTJX8x5fJuF9\n1co8SKoCmblqkpz9ySpbjg/vxOTNwdtDGHMbiCp8RaFOwYIMRWOQhu/KXvK7\ntEajzb0WzoG4OuFfv2C5yllR7rtxeRsjU85HRGHbDw9COCGMuqhD7VFaRFz9\nAqrD\r\n=i32Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.2-beta-b9eb4020": {"name": "json-schema-to-ts", "version": "1.6.2-beta-b9eb4020", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "3f63365c3dcc78a67cb89572b0724fbdbe81336e", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.2-beta-b9eb4020.tgz", "fileCount": 142, "integrity": "sha512-Mr2MwS4CoR5rbnYfPCGIaGhKn5HiuKQoZqylZU9CuQSyaj6YsdfJmj39sutFJIWsJcy4pyUSN7vVV45Sf8RloQ==", "signatures": [{"sig": "MEQCIC+pMhIp5CTK94Vj1eGJ671F+yBUF4RUic+FvqOW5wI3AiBHW5yuYRV+YZSku76yzMS3LaNrlgcOHi9Lo88McbjjVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghHtQCRA9TVsSAnZWagAA7S4P/0Wd4uHJLlzpciCtylBk\noo3WJMWxoCKK5KqYhKpp/ZRgpbeEa42RzAgKRQ37k64dB114HX6QD5t+VFKM\n0WyjGMAApxtHxg0GSuqrKT1JM+AuD3qSLtOsLGZrA8uoQZ/FeegWU+EUqlRE\n47pcujYNynvfdgT/RezaCTxRf5904rO6kLGU3+5P59cPj1QufZbc78VZo/dW\ngYUT2IrkHVevygmoraEkCbWjvWj9HLVsuCDTkQom7stfjcIWlEulQZgwX60z\nhsJj3H5qnMPlAnRG/qWETIoPZ7fM6JI4abdmQWCUEPLFs3iQ72Ocb7d9YzV8\nias1TiT28Qoz3nn1tCIGUB3voFrwAPbxNOhqre0QHGUkBAEdp0LkWU0QiXGL\nQUB0qId3cHl4PJ1ePlacDcn+aunFOQGa3GxEhBWboWE9HoVYGSbDuT5sdIe7\nTjWR8PzCWY5iEcD10kNxyIOSy5uiW0aiAiDBA+iVjHY9bDT8TncOnk9RJAEE\nCjIdM0bYqeg/X6yCdG1a2pYxpw2hW2+SPuWPX7wD2BSdn3z10QJupbJPEdu+\nkQJEai1ds7zb9AYDrLo34DGC4jFklHu1/cqzGL5bQmyfkIACIqIc1UVVp3Uk\nYG5jlxxTys/Bh+A+yuS5DihUiRBgbvLIrbhD2VoRzFeiZrkOKu8/Ix0p+tNb\nyP/+\r\n=h+U+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.2": {"name": "json-schema-to-ts", "version": "1.6.2", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "5b3bdb763b723abc0ee98a27c391375a66e4df35", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.2.tgz", "fileCount": 142, "integrity": "sha512-6C65mIGLsfpC8Uni9XqWHtxjaCoBptlE99cm22W9sE9B2cB/rcwv/3mU2FTGDAzjg+6kT3MowpJT6eGp33NprA==", "signatures": [{"sig": "MEUCIQCMsAPm5nbCEpQTmJCQZ95KqPbrouRb+jXgCQgG1Kf62gIgWRacrYDr1efF6Dq9LNCSHAzS324+zyVRNsODtnpmY7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 150613, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghIDuCRA9TVsSAnZWagAAFvYP/17W5d6BPZbQxloW8Aqp\nYKPauEHj6sMGnh7RHrbGIZnkBg1Hh7LAkX/kjwbwXLmikUh9AsIRLD+xuR7h\n0ZdKQkFF0Bhom5dhfx7F+2N90eyq4CDWAXPIm5DAFFtLtk2ao40obZvruSkH\n0kjn1PS/hTTxyIcWFaVVLioJVxnY7D6vR0O/D+4pWqcPPcMHaKbKuCjDjcEE\nqjSoTmymsLMsg4gRBwAqwF8l/wTCcRWXfvLp999pCdR3jB8A00+1exGRMwNB\n0iuBEZrrb0F4OxW6zzzq0HJx2bYwALG1iL/93CbHmHnjyMMTjeQE10WeVWgt\nWZd/QMQtZdWg3MywqXlMvzVN7ZweQP820AWSz0J1TFaYnBLyXPAGT/niWA0t\nfO7Qnlo3VFYHSM57nvbbgrArm422x6qCN4+5DkelhT9rveEMVdo4LUFvDRWr\noJA4Hm/gYq5zmRFGeb4QdH8a0woyMQnTJr8wowhCxEjF1AS+P5L/P1Wz/U1m\naDZUCMYR8ZGu7HzXmdt02NFJAC+tORBb368vkBnbwx1A1w5rmR4Q63oTldiH\nvkGXEJZ6YI8OFZkly6B2LUDjGjqd//IVnG8EZ4v1+ft0iyjqtnPDRk06ssgn\nkBEbgNNu9Kb7znfbO92xEITQFztXTKmtAxlNQUhgPXR6Y9rEaL/K/Xo3zBcg\ntf4N\r\n=V8za\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.3-beta.0": {"name": "json-schema-to-ts", "version": "1.6.3-beta.0", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "2436cdbff361d24f3f32adad5a4a2257071569bc", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.3-beta.0.tgz", "fileCount": 71, "integrity": "sha512-b4tM/rV4Ghxu2a8Nt3xNUmE40/pxfQoMrmFX6gYA8hAp46ZurPEBq9br7BAbOZZkWbgvgu9sqtEeTIEm1hloug==", "signatures": [{"sig": "MEUCIQDDI6VdPxbC1x+9VbuNHAYrcAltAzf7158d3Ej/m1y4ZgIgQs9JJKHS3a4s6CEFdj3/50EBptVLRlaaVdUA1cfZeZQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghaVpCRA9TVsSAnZWagAA6J0P/jrCjNuv0+qtk0O+3BNM\nUEuMpEhOe6v5NvdQabXZcJgqxgkQvJK2KfqnnyeLVqQf8PG1PLxqA7s38c72\nJwpxvDUtMgvjA4pVUyDbv1S2tr8pLJm6WkpTwlOW5g0Af+nD49lcPb2UO+C+\n1UMl6Z7Z3dixn8Zaa+mnEVeB0fMN5oUfKfAqJ2QbNwDxhB0oXX9a/mywAhTz\ngZIBgYmJtqr2B2lxr9TjqATG3QRna7XKRuuCEeKAkILtxBGT9IiWdBlbYvMo\nS1aruWc2lm0OnapkHPzFnFlok2XJOj2BiJxsi6qtd953loeGlHCuyBzOsPpS\n36vMclixqZW2Cw5hbFPhFyrwi3xtoXa9kw0ctuirX4gBq5Hvw01j9OjMGu7j\nngbjHdAC21BF85cUwmv76OivTKgbLMxvT5Vj69v/lqVRtKfc5HtmE4ntUnLn\n2yLpCIQ+20dhScYbaYR89TcAQpos9bVW6sXFiA4EmX2+/ejKPls5kqjDYqcm\nxUtRmhgQFo6BfA96L/trrU/uoNrgz72vd0mQJgbRWbRfoV6WsQCI0VqeUtAx\nr7XYXWIFtLnYUGo9DQr8aCvuIUlORofmUY5pMASdAjefcQOVMUhLKksg1X1/\nb2MB6Q3ediJiUh8p49gPztCMa6rWZfBb83e65ePf5f9cPHZVnysVqW0yQkse\nC5Ef\r\n=Df5d\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.3": {"name": "json-schema-to-ts", "version": "1.6.3", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "a57f0c9095d96314639b8eb53c7b0ad60647390c", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.3.tgz", "fileCount": 71, "integrity": "sha512-/eFXCaQliumW5HRQatgmTsPYr/THt6mNfbhlMpESafRmwAr8v3nguEg08CS/2Jn0I/ZWgzRKfafdV1FrzCx9IA==", "signatures": [{"sig": "MEQCIHQeMrjQ1Cb4BmO0QSlxs+ViIl3wj+g2NN/hsXHlgBP6AiApYR9TPtbqGy3Bavmc5aGLRP9Mp+4YmObUvmuz0210UQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghaq3CRA9TVsSAnZWagAAslcP/20j7ddPhizsoNSyHbkg\nk9Vki6pGiCoE1HSdzY2Di82Klvog+B1A7wkBf+Be7ixaHpEzZf9dOVCmp12I\nPe+bS2SHO49B7WintwCDk4WddQ5msGmtiyWrxdWf5dwpPhxpczuYfIjynY4n\nvTMTRMm6lMTFWlgUPVMZJO75xmBGtF9Tuze/DAgHgb3E7BcctgdVCNI0xA8u\n1328ugfcA1mqifF8YisGPH8MzXaRJphFVGf4y3btzAZN/GNPjS+Pevw55hcL\nsNURN5EZEtnhyzs9QW1aeZ+XRk76tEQsaUFa/rz3gTD+8PaImTRSBrxsvS/e\n400O5/2VECxSBj2pVtHQz0WsH/0Wg/7z81zYx9t8lG1EzAO3KefIcu5eJvTP\nd7lpL0v+fiNRi3/YCNe7oUDqNW4LSOWrf409g9vd11DNsnyPKdmOyrsh/GgX\nHQz13N7C0mZyAhEoFhPfx4ft1po0K7oK6A2FRveEP4EiedvUF3B1YM96+D6e\nqaS+io4GQf5UCj1dDJwczck4H/XY8I/biRwpQV/Mliq+SphD8aUoe1S3RdW8\n4yar/9O3s5zwsv6SHkSvYaohBeXi8gOSFaTRbPDuw7uCd3JtpExGRNwaK455\nv8TwR400RRC94iZlKqD5XsvaTCt/tIh4yIEWB1dm0AmuBxm6uiyz0h2FCx6X\nbaFt\r\n=Qr56\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.4-beta.0": {"name": "json-schema-to-ts", "version": "1.6.4-beta.0", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "@babel/preset-typescript": "^7.10.4"}, "dist": {"shasum": "4f1b8e16a3af3196caa403b0ddf3824054f1c6d3", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.4-beta.0.tgz", "fileCount": 71, "integrity": "sha512-v+mQOhqcuNoQrWpQxeDruMDOhEqM6d+qzvURY1wVRWyi3DKMZ34/qEJyJ4fOzc0W7w0fa57XRUqL5ZSIbzFreA==", "signatures": [{"sig": "MEYCIQCzaMKBuW8MYhZrqP+xBmPQkq6kl9n67GGDkpyx+9tr/QIhANmRGEyCKahBnxBIgSbZe0K5LpEjM9mvfk10Nzeik2zo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78999, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJghatXCRA9TVsSAnZWagAA2+sP/iZ9jjB4nJi7VyP2Qzqd\nYGUw7M6GM0Du7iJpZVkgPbLJHFCuexhVokyBqU0lhbI0OMFwR1wZ2n57IJW8\n/WHg4MUHi154TSwxh5njco9IxECSrnFEmwYk0PLh0Z8ht5FcSfaZys9TbMzi\njk4HHWk3xuAvMeJPDqyADiSKuSYDrmLzLLDbHTlo/EU+KO2zhWj4zlpnFSBM\nLeJxLnmePOU0sHfkt2kIWGts+M0g12X9o68IQ2FgdJDauFL/tWeVzv2FIQ88\nU22/D9EOl3l/ABRmVsmOGYgIXbt24a3nrI+6rn2X6HNXtHiMWvS/nik1c0nI\nB43EwqoOj5JeQA1xIWKk/JhYFlmscWK9plk//HhQrxSAfDfEC9JqGm+eJfYM\nWkp+Ro5yovjpaRwOvGVbigEGM/dI4OnSdrdybXrhVgn/uqsCaG+ZG/jj6hcH\nGjbAZRYrczt2WPnP5Mic67a9+ybq8IiE1ugVqicKc/xX9cnPZu3H2ir48nq3\nwFIVRMZ8DkrFvhn+OPBjVJ90q/pC6Pand8vclDeKIeI8vSfx0eiUBnOrt4m8\nc77bKNqJ+ZlMBLYJXMW+VWKJEzSuccHWOl3HWHsl3/xO7vs6m/A1gpBW7MP0\nXljou2q6UEa0ZMlArAayRi0NMAW/Y75WE+nGo3cFaD89PeDIlpwLMig9mgdQ\n+pun\r\n=qrkq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.4-beta.1": {"name": "json-schema-to-ts", "version": "1.6.4-beta.1", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "rollup": "^2.45.2", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "rollup-plugin-dts": "1.4.10", "@babel/preset-typescript": "^7.10.4", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "d31e4000bbdff2eaa0057a4e6a8d4b1631e3a68f", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.4-beta.1.tgz", "fileCount": 72, "integrity": "sha512-PBgt9+hh7/3/W9pRtF52NFySQCobLKK1uddnM8wTI24/PvNn9y4TDjHPwmSNEjxng1M4lJ+4Xhrjq3tEi/bJvQ==", "signatures": [{"sig": "MEQCIBfJHlAEBLqV9J7BAwLL29miuVXqw7F8jcwM1K3z0DSeAiBSda0bNLc3OQoG9JKUjsOuFH4XHKmhxUU1riDGDsUhDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79985, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglrRhCRA9TVsSAnZWagAA3FgP/iYYPYu3BN1TRrA1xbpZ\nfLg3Bc0Zyu9KcWNRcB5Sh6d24kw+AZ+S8NrNbSI76v+Zr8X0y4IagXhUvCBc\nPsj23hlMuIqpcKEgofA+ddSb89AUHkvochPyMhGsphJ9w+j0dijdhPVqglCf\ndD0m1cVTxM4LBYB0kEUu2OBeSCb3DLxbyNHRtlXsDcg5zbA/WeSgWop1i6f0\n3i9ISYFDUr2YiQJIAw3g/Svlr/qsq5r3QjqYo03Sqk89IJDPm1mmQfyjUKcj\n7TWUc1x2MTyEjOUJq/AuXw/6/2L/4vIqwS+GckErdtklppeceRp092bv6PQs\n7ms6A5hjhwJE/sjsm1fKd23yCRsP1Ec9SFrA4MFUCgJs4sQIiqEE09xmMG0I\n9gSlKz766vY/3YahwRUUGJt16j3iA/2q9JMVFsV0xjHFZysJ77Dm3JiNrmdW\nolV+VmsUmP3T0PQJpzxSLBcmh+gQh6tMuXcPn5K0NvdUULkE+lZL/D8TUl1z\nqyJsGeZhZ1H6ygWyi2Gfniy8FoxaIEcMH27eqii4pOtLD2cuimUmpbeSmOFQ\nYutkcTQ08qEpUBMtMzv1dNotcNC8PfXIVjI6UPQTM9IHFjZcF2SHGxxkQFba\nFX6rFu/a7nqhiyOFIhBMhWbQ9UE/kasz/f9jXslKXSOuynJdDBwQ8PrmGLMJ\n/J0B\r\n=Q53B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.4": {"name": "json-schema-to-ts", "version": "1.6.4", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "rollup": "^2.45.2", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "rollup-plugin-dts": "1.4.10", "@babel/preset-typescript": "^7.10.4", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "63e4fe854dff093923be9e8b59b39ee9a7971ba4", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.4.tgz", "fileCount": 72, "integrity": "sha512-pR4yQ9DHz6itqswtHCm26mw45FSNfQ9rEQjosaZErhn5J3J2sIViQiz8rDaezjKAhFGpmsoczYVBgGHzFw/stA==", "signatures": [{"sig": "MEUCICzRcgjnEruUjcgKGcAq99eb6CXYWx5TW70TGsrlaB7uAiEA8oUxRIXSOTeP4/4pLWpqxcjH/Q2ukmA1da+wt+D+FYc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmCHlCRA9TVsSAnZWagAAQx4P/jMEJmi9MEzJ6uhi7m38\nxwwYNpOBvxETo+YfEJNQUWpB8a3zJ1G51VspwUEwcVZHeTcbfF1jpfr6csMZ\nk9z8GD7ETmppPLivotNEWhYQDcDgXsNj4CdDgU+ILLjmmRi5BUiYgMquh2bI\ngx670nbcefPnJTErHFrHemF49w5wrTBSgYyfwCzoSUuayvrUlbFGNPg75eXc\nzlK6q3BiKsypql5R7sFMklDX0Q07BKUA4hCsFH6hsrm4kK76h2lrHfSZbn7/\nsKaZQXjO2m2QT7GoQhdyfu/m2xU0UKZpEyzwx1qaA7LE/KCD/TJ6Zc4TIgiN\nvh7xzMheU7gbk/iK2iO8+o81C+is/w4zcuG/cluWM1WsqD2SvRXgzWx9ia/R\nIQc1cizTgnb0hjKM0HaVKcJYUuip9+SdQVfPdPwYFtwzRibCoIuuS1+xsqTK\nS6PM2j0Ps2MsL41bP1uV7cGL4Xyo+lQrqdmjC/LBJPjF8ZvoRcmheKo4QX52\niCx0FgbCUA1j8McYK8o7WHm0GJn2J6BVjyriVJTYIjeqyfsj2C+akOqcgVc7\nZZWBAP9XAvYMlERbbh3h5VnFmj9z1AL/4+zeGFLuFqWg/JYr4cEvve6yAed5\nIxpTrMOOvoe2t1Ocox7jU3fPdVPAM0VRRYN7T0ltNDLs7664lif8J6PP8r1K\n5oWP\r\n=Rf90\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.5-beta.0": {"name": "json-schema-to-ts", "version": "1.6.5-beta.0", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "rollup": "^2.45.2", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "rollup-plugin-dts": "1.4.10", "@babel/preset-typescript": "^7.10.4", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "aec8b39df4af037c52c53da1c3dfbf3a6393ce66", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.5-beta.0.tgz", "fileCount": 66, "integrity": "sha512-VyjAKHYuGaFg88gW/BMRQh6dreR3QX/UwjyL3HP5qddoRJokzLlGTeU6rKu8pJYx+J1E+RwxPo1th70nKKo2pQ==", "signatures": [{"sig": "MEYCIQD0eq2SdDnb8xvDyaedDIE0HOG7hiIWVJjWmP+J8QQ6cAIhAJn4zzGMTFjCDus2C9w0I+nUpHCqJa86BE3N7XODXDuw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78856, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgq4bICRA9TVsSAnZWagAAebMQAJLknTZ7qv0Yp4MFEmMx\nNRVfswkfI23qDUmknRvsNEYh/H/iW1f3VMZWFaQ8Oz6H/LvvlKtFZzb1qkwq\n4FVpsUG7tAN4wGESTbjzyX+00oVpao4p2veBNZwhb8uon7R5abmbvzuUAVyX\nkjVbf6+LXG2Vs9ETnwvUnt9Yf9UAJGv2T/fEqNi+0/qL5BdqQs09oc1GtyYR\nh+k71qRk+89jwH/pUi3dlpwXKGEn0wHugKxFLoRsDCf6zX+v7oaC8TIiJ/Dz\n+Ybd3ljMKJqdRi7BWZDvC/SmPAxJTTu4Bm32mpBShqvvPY39xmkcgUma7ZaO\nvB8nSeym4FGhZ8Lwu8dsKL3p8q/80luWmdJY0bnpuOafj6QEML1YOnA5eY9+\nh/nNUAoolE++g5GLEgi0oitVEbo+LjXOIDg1FMZYhx83zxVmw4Vp7G6iArD5\n+dfMnnOpyt7mr9iB4RSKtGOd/EVGvgmeey12KmFsUOlqWdcnCFpI6+sblN6U\nMYkyZoJXjRYenQv8YRDCVp5xiECrkjhdsSXXKRm/WPN5FCdnU9yu6Ik7+yO+\nuMkFK0Gs+n4WJtbRyw2RyX4IoU5OMEQoYSp+btheYznsfDW6dd8o4f8s/Zgw\nK7nDRjDgkcQJ68v1ggQoRYuF5Q3pUrAlq3NJai7ozQhpkFl3IezKq/yxX+da\nmeh0\r\n=NIDl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.5-beta.1": {"name": "json-schema-to-ts", "version": "1.6.5-beta.1", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "rollup": "^2.45.2", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "rollup-plugin-dts": "1.4.10", "@babel/preset-typescript": "^7.10.4", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "973fda38f827231fe397e6b6bd30bbf8d6fa7fca", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.5-beta.1.tgz", "fileCount": 61, "integrity": "sha512-Beh88ib1O+PZneFJAo34RElfwWqAZ38mVYlNivIVeAudPFYfl4kMhK/iL+HcuOIacQ0NAeaeUZAMzFayt0XJMA==", "signatures": [{"sig": "MEYCIQDqy/7ja+liJEU+gVSZm37tPRjUO0qkJ8hbkzXRSjJkYgIhANB2LyTbSnYJotLhPny3Jtyij2NIVMon+PiYVzqSMWPi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76680, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrOXnCRA9TVsSAnZWagAAwz8P/1t0lFp5GcwfR7H4cWbZ\nGFoItGwgX5Z9/LmAe12eY+dWVdWe96mLEOnL/oqBUnGTotiBiMzAbs8FlcG3\ninYKbEngMQW529nlHxbiufCOQU/Jz6SyEZvqPly1qkcCrCl3iNk7jPNsAZzF\nFm5/DR1oQICCA97hqxO5lm6A/q04QzDExv3Rs8ZCANvFrQBbud6b0ENKhxbZ\nTs+Mj9Ui+0LSbTzJhtvOoW58QISI/+6tawlBCX+p46/tDrT4IEaT6uTBWQEX\n6aEs16301xUoZkLJiSrVEKM/fGPiCxHEwmP7vHiKg2Hr68I0EP8uay8uWtE6\nF1WFp1dqWNZbLiUUjX/EfYTr+tU5Ul9vqA7wdGc2xwcipK7zHsFvTIMQbCZY\nu/x1ZzaOHoS4+5Z4dqWMuTcu/SzeGr+znEGfH4HGa5dAKlo+Dy/EY/QIc09I\n6aB8Ai8q78QiDaH2gg2TH/be2IZ2bULNgRcdHTi9ieP9dz1kT9do1+fiCSXl\nff5lOounDThytbyTTc2eBqQVvTn+S1g4fN7Sa5epbqhhPgcDv7xsvi7rC8S1\n1y5kjSanosljN4xG2n29tGJkjhhEq2oeVPfp8JAzLc/8CDKNIllIgUtVReu2\nPcdlhTtyq0TF7ab4oIyQ35haQG3D2NhPc082cZqyZkh6DIo9srcB0p3ImDqt\n9xsF\r\n=0iop\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.5": {"name": "json-schema-to-ts", "version": "1.6.5", "dependencies": {"ts-toolbelt": "^6.15.5", "@types/json-schema": "^7.0.6"}, "devDependencies": {"ajv": "^6.12.6", "jest": "^26.6.3", "rollup": "^2.45.2", "babel-jest": "^26.1.0", "typescript": "^3.9.6", "@babel/core": "^7.10.4", "@types/jest": "^26.0.4", "@babel/preset-env": "^7.10.4", "rollup-plugin-dts": "1.4.10", "@babel/preset-typescript": "^7.10.4", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "9cc1d3806d33bc7b4700354b8d449b6ad79c3473", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-1.6.5.tgz", "fileCount": 61, "integrity": "sha512-BLUdzgz50XV+NMrSg8+KrUvV7Oh1eb/kLsyPbkWXFTXTloWkAsq7MbAppibE+DyMy4PasS/7bFQPwTIHx35r+A==", "signatures": [{"sig": "MEQCIBaQVA9yv1yUtER09VT+NUpD2XmPttMnz+Iu5RWEDAhAAiAU44a1PkxwIwyJRuzDWz7TfOxygypIDRHPWW6iYF0efw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 81592, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2cjMCRA9TVsSAnZWagAA+GwQAJM5ALLC6Ij/z9IHCQWf\nwBYnP3jlZK2F+Bx8HfbNQor22mj2FSWIM/ODV6+htlBrUXt1klSj/nVhfgEB\ns5eImol3+flLPPi9MxWLfdXE93UcVXYyMEmf7BFhdWfFd8is+D5fYntYdpF+\nXamgPnaYzArDt3s5aY2wO6J+shMsr9a+sQKXaeTUY+3s9j1jI9Zs9H0hG9Lg\n7rk2G6lF+8gOkdVlqp85f/Rjm4M9I3q/1sg7KUqcKBiB03dLx804Icj1e13l\nzUYtrGfOxX7yt6mOYDhgLjy4je/l+zO0O2a27c51gGgVwZxk/VraZ9kAnFEf\nRlUTbO+20Z7nM2h3rZKPrKIiMP2KlrfLFL1bCZU1tFzil6P+Bcd1ezEHHyHk\nHlffpC16c25zS5x306+uYix+AmJEe2yvMrKdsGqsI03WvuJn8MyFbvk6b/fE\nogG7XalGZ7nJdi7n/w/krlDAx3KRk6PajBJcF9j+ROb6iR7V0/1CTHQqV4ob\niIOPCpxr3fVVHwlgSFrUcqotpo1ERp1SaWwGqTAsMHJ0eAM+C+lHhpi6Zh20\nb2JjenhvYfOxAOY6P/pv69rHsYcygutvMoLIDkXH9gHwkywjz2AoMzP6DDJI\ngKyNYg8giCi8QbgP4h5CUmzu4kz+lSrPjwLHuhxPkkRLe7VcYt6vYkP7sjih\ncG09\r\n=/mjF\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0-beta.0": {"name": "json-schema-to-ts", "version": "2.0.0-beta.0", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "28699ee7e0f30e9035477a855c89bf7ac8f68bf6", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.0.0-beta.0.tgz", "fileCount": 32, "integrity": "sha512-bw1uMOJkDeStBuPxmTnwe2cMkn2NXXH6cxccJN2mH66TroZkITgpUdpydY+RQ2gOVCUuLEyxkZKWROXGfbwvpA==", "signatures": [{"sig": "MEUCIGUlXNT4IteJGhmuulI0RXC5BT0prHBSsyb11JtZDGYcAiEAr5CMRDz5gsRQrbdEXavZix7ddeWsKPsDzw1DdbB0gMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUIbCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNzQ/9EiZQqSO3P/ac3mFFOH666IFAm/m16snR4EOAAK2Pjg7NnbHm\r\nY7fpoxY+eGHEc2q7pDZKEKjIuW0zCvReieoU5oq5/Nj5UA6yKL03aWXNLo/2\r\n6LF/5OdVPcb40LaPAUVvi0QAv6eSio9LWCI5JeYJ9oHtBM6i8OmcQNiOtcSM\r\nQdAeZ0i1TznQfHHTMUDNwxdBXr7pd862ggy6I5GjjsOnNYMa1OmWofnkz9ZQ\r\nIdJDtJhDbaGyKhmMGjZ6SBT3fOelOuuY438FRPu0/eokAFpkboLnStNK9+3p\r\nrXPg9tKZFcE/URJafn2475PrJ6+Y2v6L2wbOpkB7QLFNq4L2QMNjRpPgaPcR\r\n7G1ulz7nikFFhps3T4spHcVx4mY6CWSbuVTH+9dy2EHuVwEFKXAUIAFzaAGg\r\nuDnEAsVCBf0sqPe6sxognNsLZ6dGLTWhWLeV0bOX7Q/7gPFE/GofFV34cev3\r\naYYPj5wKYrK7TD0DpC/kdL07JYp79n3h3Cj9QZRn6tSfbPf6m+DO3K2wtzw1\r\nubj4boMnfhiAC6Yzz3kGhe3NV7dSsq6Ac68BRpylmsJubMcN9c/MFmcZcytl\r\nVSEguXFdo6jLYgbwjdodQZPAWBriFD76Eyu2rq3AlUmG3mNTLXcKtuGbqJrz\r\nQ54IkCJG7KzdCgL6KG8DOZ3DTq5+xghSbLE=\r\n=p0GS\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.0": {"name": "json-schema-to-ts", "version": "2.0.0", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "301818285d4822f7a49c966031dd50b909a699ee", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.0.0.tgz", "fileCount": 32, "integrity": "sha512-vpz6Qg4H+N8HO8x2dQOnIVWOhmdGnD+AQUq/XQBvQTXfmT/XXcxulSWKScGtw11so7gCido3yEuFp9l8CPTM1w==", "signatures": [{"sig": "MEQCIBuvpJ//XoSWDg40bANIb+c6fOjwYYaBFmNJcndFmcGjAiA25XZk5ckVhXkYOR8Mj75DXr6Pk99v9ljfTzbgUIwEOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43600, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiUNH6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiRg//fDhY0VTs7oZdumzSqiBLpjBNh0bTpgppUq8xA96sXnYsrYdk\r\njGrmwzjYAWvZEbakmtzkE/ap9P3K/Z9dXnKqTZCqWRRZBVUmRMw7eUOinUhv\r\nxsTN0M219VG8YqD3DTpJHw8lIxDmgo7K1/+FIwGWGoNHUni0oAmY0Fg0iMIV\r\nXVy3dyKd6UCil6Vts1HbAUIQPzc+/gLt7bS2kZsU02PbH820wR+T3NfDd7Gf\r\nqrUPjHSbX8s1qiFElRp51MS39DRv5Cngcer3KDmD6NDzUS1qnqV9viRGbaPA\r\nXg+5mR37uCLNiC6fc0VdWe3GFWY1f0ueVyy7/XFCAsZK87QdE5l6eb+4sdlc\r\ngntso/kYD9dbJzaW9CtdqjN9Rt4Hmvw9l9iCidD4aCj/pWn9gAIQmW43Y4u6\r\n3hlW1u/KtRNIPcxuvihSn9JSKJG0g/etzKY0WPSBqn3Shq+UUufZ5OOdvyJ9\r\nO2uYBku6pjPBStOFWAWEEubm/y2XwESTc7GCKoq1EyvuQLrF2cFvbPy+x/c8\r\nQ4j3BweUPw8pOox3acKUjvPCKjcXUVYWqPFqqsPI4H77bbA7HP+WtEq9wWh4\r\n1nYL1ipX3Cu+q7x3kDhAztdAYrvTvYzBYomaOpOffqyEps+IDL616gtvXR1d\r\n8yNvD/FSD6NWDJ89r2oOk5LmOnkEsTbBufQ=\r\n=Mox9\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.0.1": {"name": "json-schema-to-ts", "version": "2.0.1", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "1e08fbf3ceb15238a42b082f12b4a43570e2fe6b", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.0.1.tgz", "fileCount": 32, "integrity": "sha512-P9rqwWc+Uzt3j5pO7k+rpNmblmCIwrNvOOI7IO4myWCd06t+iad4NlTuy4F3ebsQpnOXpmsQYu2GodCQtrPzVQ==", "signatures": [{"sig": "MEYCIQDJo2UtMq2RRPGPrIR5YzlQhGef+YltxfHWhG4ow08iHgIhAL2+hY7BKm3mr0ZKtDk47niu0SLHL1TJwJKDZRXO1rz6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVB2SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjaA//dsGUvcrRgShj4dLhxpdyGPQlhXsdrby4f7t9MTxk2tFtHqiQ\r\nTeQy83tX9Rk2t5bzScyOAS3xzsvb43c4xE2zLaecqfD86vrKBtMLie6LSedi\r\nrnxqMF/mBgcTbXM6qp7l9hM6qtqEVOfIZBMjZvdVIIFsWt3zMcFWEHbcptad\r\nUv/2zQvuBCjS8s3MhHlA6vl9ZG0t5NU2jU9xQ6dC3gkY8OX1mDSkYnKUh7va\r\nZOxj7d6R0aRM66wrhzcupbLs3kayUstMm85jMYekXRM+yusoraVhxkFSpxZ5\r\n0AGcDXjQXnSP9ws+6gt+8J508CJQz48WgZYAqFEPTJVmWWf7+eTg4a3bxFW0\r\n8PgUTR559rGQ2kq5MpvLp9abGua+b89gpeozZTpX2SOaGFgElp+UFHjv8fT2\r\nKBClzmpXKD+cCz1o3dazFob0ClUPvmkzBqDEI0HwTHS5h8wvvLZgQPiAbNbz\r\ny72RciURdNN7YYKyCerQAVAXYUwTXzLlgEw9eI2F0Q06/A5AL8K35QQP388k\r\nbDE4nmF6BsuNnpBAS8fE7KIWaTPAw7OIcZ1ATzE5CODIFGrnnVd9wIxGD4qz\r\n8LeI7zUTRoTDnaQqXmN1j5wF+oiMpbkMST+VmPItO21A28/lqN4y/kKi2tzE\r\n0bGazNFIit0+0KPtnp++D0kvS1USV/7EqBk=\r\n=/Flo\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0-beta.0": {"name": "json-schema-to-ts", "version": "2.1.0-beta.0", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "ba43e84636430d818fcffcb54cc6d30128a1a6f1", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.1.0-beta.0.tgz", "fileCount": 82, "integrity": "sha512-ICxhu9GWUNG9mTfOGpeMYk4ptrcaP1PCP/4++AhgnCVvwhmvDOJ+6OtFHZSmKgiw4wjMysYSrdExdxlyMBkSCw==", "signatures": [{"sig": "MEUCIQCw2IIzRHjXVHhmvVDWuxuWDxGOvuByLSizezzWFVYqoQIgemQWJkHgwrQrIeqJCKtfgkRizDv5Y0ESeYjVh47w0Os=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46862, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVF+ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKUQ/7BTbEs1MtL9mxBsqMWK1wo5wh/G/x/68CzE04gFvl2Xa99vFD\r\niLmnzvKIrpnlSwf421Dcl0DhzfMGaMk9vrOC6sOisMM1wcBl3POmS69OjCFh\r\nDervA/zHXElCL8vNaJ5h2v6W8W5mkUQXOs+7U7HfT70VmSTvJ9CLAc0314Jh\r\n8c+YiqjSR02fOBQbE7PqubrQVyasaisxn0xOgvBFootBrrSgcDDd8c/0IqAh\r\nSYjlCTQ6nAlxHwi68f0dnxAKZOC7eDBET5obQpNuHigDl3ZyT8WxOU1m1dkq\r\nq97oDxDyjJ2wpzXHMEbpdu70r2TxBulOdAPkOQTcIg0YBnHb7viTo6XdQDU2\r\nszLppds9mcfiaYh30s1HwNTBoY1g+tNyZA8L9/IZKgoJl9XGfMGyzNghE4JK\r\n64OI4Dn6fCtOMMzkSF/Zj/eYox9WRw54aPGMxaGbJ+JJAoOJd0WD1v04iWQs\r\n9kzVDC1snQBmp4i536nPls3defCMIxXdz8HYZcpjgxkTW+o/9TEKjBl3SspF\r\n0MAGlrDeX8kzBcuEWeNLjLXfyeM4w4lyYEyU/i0PtrtBvhOD7ZQs/okWpYvN\r\nvXktY/lGZtr1kZt0D3R/t2tAc8/iHhPuXA3eoFw8JaujNwr0cm4o8s8J2gZy\r\nkrOq2fVasBZJ3FcXK+5l3REgBA0je/hstpc=\r\n=wnNI\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.0": {"name": "json-schema-to-ts", "version": "2.1.0", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "8fcfcbffd457d13c7fe1e04dfb090e6181f077ef", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.1.0.tgz", "fileCount": 75, "integrity": "sha512-WMC+JLtADliQx+FZU8Hxi/3VnLFgTDQRRZDsqC4olrufCTx8LbreYyXs0iN/DXq43LxxQMkNNuPph8OU3BrP/Q==", "signatures": [{"sig": "MEUCIEaI8R7GEyqOLj5EPkt9RHPuiCHQ2F4SixnDt8qukW2gAiEAvzyixcMxCxK90LTTVGqrzBBIHjTCbEg+W2y9qkCx/OE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVGKuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVjg/+L+aMljhQ5c7gvkCBnzihUKwrI0JQCZAQZetDr6j2Icne+H59\r\nvP8FrlLc32zSSIV9EbGuy+Jp6sKOU6H7JEPpQejKVLVXrfLXRHB6qLEWdWYJ\r\ny2pf2QVDgoI4lIRO85lB4a62eHV4luwcaSSdNtSTNawtv6K0HBncHDlyMYzd\r\n2GphMLKAUXN77d2intgsCEOmDGhqMxRhv2TLlGw30Ol6aoveG4Yr9anXnUZW\r\ncQF2pX+8rm55tnBpE+vmlO0C1KklNWMi4gkyXKTR+Hki78hvsGqL3aB01mwd\r\nFH/eTySz90011tgZeRG/kyiXg4DGdh7sXBnsbY7qtBSSKhf+o9guEPraIb6x\r\n3HxloPZ8Q8bY0NjgJGZyFXDt6KD0njLnhPth3PNkI84LEFIvysceC8w5YaBJ\r\n4h6TOnkyWqaUUzeL7MieKPiP2w7QHeyvDnSAgkYIgD3DnbbfPSo0Vo+rOT2z\r\nlOwApR27ThqQzkmHD0fN/YYoIOHcXnmupgWqKz4Jy5hBSzTeX9h2R2gy7XRb\r\nmW0qTszUa1kuC5bUr96s1ImqBgy3CTFciUyYG3Ihx4h4K0MJ0xAztFNGZan/\r\nZBPl+QhC+gdcJ98lB0ZIHnywfAOfW8NpgpWJg6w2mDyF4QtlLzwT/gvZ90fK\r\nSbBA54TvtKSf7IrO/8n0qYHrVm52MfJlA1M=\r\n=pEUJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.1": {"name": "json-schema-to-ts", "version": "2.1.1", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "238e2a53ee938f1b22cd8f587fcb3f2f61e65d28", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.1.1.tgz", "fileCount": 75, "integrity": "sha512-2YqyPFiVN4TL7UCAHvUd3Me42J5Ff5tKEF3G7Mmniyl1b0AWhl/YuPrcChR6mcjmny05UL9gsV4dOeIHGjGBEA==", "signatures": [{"sig": "MEYCIQDBMSe6mn9dbq6fDunXWgRkn/8ru2xkJiQuexliY24+tgIhAMUnMJ6gHantijITJdisZHvhVAi9yEYw5YNN4HpU53p5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 40707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVLYrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFEw/+Js/QcvC7eFy38kVExsPwVkD4JbBRvgD+iAYNKgL6yKlH2+Dt\r\n/vp+Hj/rSICnXEdSnIxj5nqXMy0ONahZkKrB+YRcXZ6GD1u037UuWT9wx1Ol\r\npYvk4NZM2ROAfYyreFdBXPa9Dy6/KjSwBR+D7Df7c2alSnL4JWCNXVLtgX5j\r\nwxEG3vJ17HaT9WmS/1oFmBgi4PyznUb/31gn9Uu81Oo95K5UBmQi/hP2tZUR\r\nHliZyo7rMC3zlYA9eX7vRkL7py+4Spi/Qv+ya65EL/mO2OYgX3Gfb89FcKVY\r\n/aMA4ur6HPKaR10QAH1Wmf/QquXuYdDbN4hoH8CcRA+tL/vnmDVQ4H98v/jo\r\n4OzrOinO6iagfl0sMrnn9tLXO5vSN1rtTdiziG7V5qGfrGuIk8VsVNeMUnAX\r\nf4ezQmCOi2MJEcwOIEs6VckL+2h3AeelAwV+Is6N1pVRT6WynPL5LD289lK3\r\n+D7NSNFSOf0EiFIY1SnBxBGPFyV4epmsncPBKWT36qjk7Z8Ka8ka8/hblaxf\r\nKx6KyXi6fIDNGCXked3px2BbCjg6n5hAoN8wj1FcZInEieFyp9DmhcUZ36d/\r\nCZEg6vM+DGmD01yh+TrlRfA5tjDpgzkEdxeKU5DjM0UgdUyEdZBhOUJrPKBy\r\nVIfZP293tWeC3A5Sq9SjnSrKJ3l68h5zScw=\r\n=byuT\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.1.2": {"name": "json-schema-to-ts", "version": "2.1.2", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "80fc4a85c02d989deb5a177e7964df0852435f0c", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.1.2.tgz", "fileCount": 78, "integrity": "sha512-SmtnFwLbUkIyloL2X/sYs4xXOpv1n9IdK/CiLF5Pk04WZGVEX2orieE4KGuba/uMv1f2WrwntVQG7V+lF2b5gw==", "signatures": [{"sig": "MEUCIQDNViANNri7J+7aWhFuZ+DkeeDQQ7vnXzPttTS6PoQjdQIgLx8EGal03Rml2ywVL37Nygcrn6BNk+7c7d6/hOJl6R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 41811, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVqLRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoT3A/+INtZBy5gj37QdySIL7/YSv4CDAbMHqvZAdFQBGK0YCsy/WMX\r\nzV7j4wNvhmzM/+TK3XuxGuzCijDg4VgoWByZQBQa9c/XU7SuQPtaDnH212bp\r\n45AOncrMY5lQXWXL0jYpCQwjwGndnCjJkt5vp6+VF1/RV3RnibPZW4jGjOfQ\r\nf+SsCtqvUQHiatKBK5RuchbJyfLG/xCtUZ9EMoy3wUY7teE1qJddPZxN80rh\r\n0G1E+/90LTx+fuiSBe+WsZqncQ4ELm6S51+nIsMd/zit/Pf3D7+bYvRsVqRa\r\nuqAl/BTX3lXHz8ooC+I5ANWinoktIrkY9k29GojXqwNFjrtaTj1qj0/fT/iC\r\ndhCDopCskNyz72kgVnA7wDRSL2AOCcd1rwmOGfM30B6PcaviG+JX8l2ncjEY\r\nBFTY46OOKZ47+LsszddQCtmDhtrL6s/S02FWuwkY35FSqQRUShT6IVAbcNPM\r\nh2yPWc7IKww05SlsnyyAiGN5weqWZ4IdRx3e4x8kQqa7cnhbPSDP603GLwme\r\nhuAmV797+qSJEPvRXxtU8dEjB0h9rQzUDaXdf+iTVKiGirJ6Le6Ofd9SSTPC\r\nGWOxdDfnTh2dZ96vudyajPiXPI+NsyA0egnX50maBPtKoVybQir+1GmTPdYq\r\no3+BQvfGoOqaG581Fo7skPaAM5fkXcNsn9s=\r\n=HzKF\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.2.0": {"name": "json-schema-to-ts", "version": "2.2.0", "dependencies": {"ts-algebra": "^1.0.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "c721d995f41070d69a34b1a3bf9d0ce8742ffda0", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.2.0.tgz", "fileCount": 84, "integrity": "sha512-iGG2SWjElCW1ZtX5J4L6fE00IL/NpJFKSlui/Ebe0VSc5MNNVtsxt6vqSySSx0SSfhxMAy1q4DATFvZ4L3InXA==", "signatures": [{"sig": "MEYCIQCbl5BjCWxSkUxFt0bu3tnPM1HMbjvo+/0laOQ/PJtyEgIhAJLhRT7NSa2BayJNn9ryJfzudAgwGKcH3FDFt9wICnwY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44365, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWKkmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpkIQ//WZ0CmBQVujrt261wI3XH5B2fY9pfO9/r8osX7s73anIuD5J5\r\ndaw21i2a7WcXOL8mxMfPp0TOTsPcQ76ID2AOPMOJopkFQ0n+IsHRCpg4l9H/\r\neLERxfA9euzFD/4fgkBwruNH6m6b5Wl3jLeieucoNhZdGgWT4iR+QrAtxzaH\r\nLpgmJxw3+yUrTIFCpYJUjza5qPc15GXjb0P/LAxqgSvwBrEHiyJMDXbMvEU8\r\nGie02oXug6rBnBMtkd3zE6BTJYq97lr86RSd2o59iWIwblgApCjan1yR89Po\r\n6kVw3u6VqJPm12TTeZ6mKRGSHan6Ed7szD6vjncXdjTFpXbjCFnVDkHUzB1E\r\nNi6xUMkWmfWouWPkaKMOwaV3V9ENtSFiVJIRSdqLY2lUILRAplck6ggM+/ND\r\nemIJlO3EUyL/96db10olJlW6zhou5d0I9O5lBSgao3b2gZUFzVFBJniuIPAE\r\nIYWYLEMh1mZw1t63HyW2Pgi+e6Wzl5eOOMc6/17ZEHL14WsWp9ARY9tdiIEN\r\nHZS5V8rEJ7tm39BkJiA3lmfDFov6t+EzakWY86TqpfRRgJiY8sUd1WZXAOyq\r\nx0dR4Qz9njZr4zvhuKbulNp8Sp6cyU6evNdPM9Wqvr08+cZr6Sygt0jl8NZL\r\nXg6+ijnqFKi3NpclL7HFpRP1H4mUiKI5XWs=\r\n=LlVk\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0-beta.0": {"name": "json-schema-to-ts", "version": "2.3.0-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "d80d45cf68be968e2340ef811296d4d8ad5d5bc1", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.3.0-beta.0.tgz", "fileCount": 61, "integrity": "sha512-<PERSON><PERSON><PERSON><PERSON>r5WjbtGTYVpfSTQ6Ao2kVIMeknSVhgtJba2ZWgjaEPwkCuB+OcARb0289XqAMA13p9TZm4p8W2al2jvQ==", "signatures": [{"sig": "MEUCIBl2QvwPoTJ51XPeCaY++8StQiRgq9MJJbXUf1G95SOKAiEApiziGkZLRQLWQ23mnDWPq1B5my0I0lXorJ/4cbvxpeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42514, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibsd7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmouSQ//QXp6himj2CpuZ7fxdBKnsnuXJIP5QydQFl1VOyoE6LeQY1sj\r\nHmeVRVU3h/rwV9j14fuWOb39nCDdpwFgf8FwAcdgLG1deS5Hs9CFbOJ5fhpw\r\ntAa4hB3gXr1Pj22BtR1k2+Yaa4RbMhZTetVB8CT2kEwDo/Q4U1+DBdAS95KY\r\ndg87KzLh6NL9JfVdwLTJgFDIa/e2nMEQTgT9lwWtbFq31RpuI2QGMq+iLfO+\r\nnMlCUnEuL2OIvWr389yVb6j5EprIxybxmkZnjQsMlowm1DsdbbnmWBUUQFcL\r\nsTgblHxPO25me0BRJYV8t1m0fNAbE2Yo3jIKnKROck/2kjYoB9pErMA1NDkS\r\n/IZWPQIk6R0PK0vcfHfgMdJPywkoJ6dXIjmFMhYP7QPrM13Sxcbc33xaPYme\r\noagUD1zn2xyAvn+rcVniW+VpoOuiqs7OioCJ05iyq7ymfrkVBJPWRdPfYYfO\r\nQMmtiFHbbulJAUUDC93YqxpYbzHbrebYwBmchS5J/r2N6wwoVvDw07w+efsf\r\n5cyoxRnhqSWdqjAlW47qlpKDBaWWP1m6Lg9MUPXhY7G3W4hwoK743YntXXdG\r\nrjeVXIlWNPkt466u+icXGUCnUHGy8SMlEMlLNfXs7Bl2MZ+TimOsVGrbzE4q\r\nS7zCO6iFMLz2ZJ43JvgjTdk32xcU6+xijS8=\r\n=pyAa\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.3.0": {"name": "json-schema-to-ts", "version": "2.3.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "56f4f63204c53d88458bec9525328c7d6ef73e9f", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.3.0.tgz", "fileCount": 61, "integrity": "sha512-qBE94lvOfcVmedIgHkKNhDxTG1gPZW8pPIUpRtbPee54jGF2RZnyEOpDdowCU219sXCJ8SDVEMUCG4oMFw7pgA==", "signatures": [{"sig": "MEUCIDoILvYU8s6a/3BljBKSKckAI2OEDNMqp842b9MZhLbKAiEAi6Y+EQkRwpidlrLj/HHjD/v+LG3Lf7ze1S7zRDRah8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 42507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibsxiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrjZQ//VXdH4HAwpMjo3JB0HPskn96AnC3X5DX/m01M5903C1UIHKki\r\nCZ/vpW8x6Dw38XTZzyTLM6X5EI7zVZyX+2s1vFRci6MLVh1K8ElZrdarWezq\r\nrQ5lUmWGP37zjmyxiofJpvp4GTS/LCz6u9JgPAtyQyhlhzylNP97KTBGTONB\r\nc06EFaAqtmqxblx371NSIvzPrWMfN+hfBuWLPEmjkaF8MpNIUBpYmwtx2KBU\r\nnpkrgGIWArW4XNR7VHa72itEZypjE1NsVUTLPQU3SAHubzUTbruwDfDpJt7x\r\n4wR9CvvQ0tNm63IEthbkVjsup0iLDrxna1kzbmXYxGqE0vubGZLLSGVImXNg\r\nvkEtN3nis4uMlGtJijUHihTw3c+BWS4eYnazT7Kk9K+2SMGqwJJoPoRwMyKX\r\nQ/8JQlXhCv6qD1k0f7IzcAPM0Nobq6nTUHEGp7n/SBdVlqWLihqUWrnv+/Xn\r\nElDfwTY07/aZHBUzhqS1lmJ9pedXz25rkqmQO36u7JodoP+cU9X+FZpGerFg\r\nOkS80OS46cPrAhNuUFvnkOMMk9ACHCCPOO49kIo9ehJ4wz9YUeW8Ck1x27yW\r\nF9S+bC97iwYOK8VZZgZslkL1cMRPTtkE2+GUMJCiywJyzWpfYnNkZSi4Ugr/\r\ngTRBAfuLbjGQlN7TyeSk7OUteuo+PRL8lws=\r\n=RFeE\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.4.0": {"name": "json-schema-to-ts", "version": "2.4.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "0dd832a624862edd66883f8edbc102c85157ba35", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.4.0.tgz", "fileCount": 69, "integrity": "sha512-q1AHdOUDAvs+vp+E4UlENJWXyRFBuMSQyg61/nVFYWBInTBxQZOqsyR4QZiBs0qiOpO9fXlMrSgcExJRkcBkNw==", "signatures": [{"sig": "MEUCIQCx/A2AnW0JTV/5hcPBT8wYxpfBBDhN5sRRW9wfZ7KvRQIgK175m6atciX/seOy4QlpTBn/UW+UXAxtBhicwD1DEiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieYEWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbXg/+PjIdHsrATnhEKxRitIV47ODXJaKP7OoxJSyY6pCx7fEDlMzZ\r\ntuDJyW0XPnYkwt28ZUJ1un1sCzMemao7mgUXircb9kPNB7nND6GRBbKI4epH\r\nZagtgqRYyt7poacj/+ZmUGHFgqqdbHgbT/oluEdTEBKcBxwSKAe0kRHO3bxJ\r\njRam8Mlbrh/efKtlSfdGC3YJEzojoyNdfiec5U0V/6r4NPlsLS0g61RGgZcW\r\n6wlwRBU/Iea+fF4laZIopW6ibWBpp9ecbz6Nv1DfJsoY0ML/VamXZNEtdkv6\r\nCB8XXtDQH2LNf4ZrGLqsQqc04ckmWDori8e0wUdC24LnyufCTzVspVFbrTpS\r\nbgXQORW8yuHgO0Y6rEi8JZTSl2WGq59IAkGSIEH/dMy1P7udpxo0qOLLywXi\r\nbkuE537Pr6//K07wVeDb59RA6ctOcFE7QLfhkmB1v2hImnUBK9qiPOfUmgyO\r\nq4Ew/xWGt5D2PwOHWv9I9mFlJRYcLO8g1i9431j8iyameLIf3NKP/+fKUbob\r\nrkp8UjZiToKSeyHmztDBtO8PfsmeuZ9bWsSyE0ocE5yYe1/KgNAvd/WwAjNs\r\nq2IZEQF4vxtA/LQPdDgGDTZiYUULtRYNR9UJ47GUiEISLHZRfx9xauCgZfPx\r\n6fTubEVARrO9+3SzxoJ8qNCpZ2RGuJq7sx8=\r\n=iySn\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.0": {"name": "json-schema-to-ts", "version": "2.5.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "a768ee94e15a16d43df5016a952b542fe2677761", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.0.tgz", "fileCount": 73, "integrity": "sha512-CIqDe3agE3MKUafzxsONPlBhN4S177hD+dKHEwV4l8uWPmUdVufS0EtZ/wd9Tks6RfpoKeBpraQiXs8HpzUerA==", "signatures": [{"sig": "MEYCIQC33qFMuvTQC5suXmmFbw968Djln1VF8BJstFW7tj4pCQIhAKVFtL4fhAJaG7vLeaIdSpw8oX2ERO9NF6rJv/WTMGLs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 297744, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihr3cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodQA//RIeg4QPg7/Znvb1SV51HSblmtApcefxcYcvO709Trh8AucZe\r\nMFRT7ONhMw1CKgGF625qWNUxZhdkt9Yink2c8vg5jWSAQFY+2t3PqVZJM4oA\r\nvMsdUrg/fk4b7P3Vht825vUTjY3/MzlLJAeDkQDTzpFcObBPQ4VADcUVq5Ul\r\nYmqqkn0NsNprgKJrKub6hIPLSUf/NjVXdmhnO6udQORsYEe2xX3LoaHOvo+V\r\nl+WSexmEZKF6rODbOQajYDND9KMOagFycPuPXJH7VaIVfE5plL8ZxruSURr4\r\ndAxi8HKcV9foKDSstV36um6EL6aI4WtDgHwQZL0s7CiLJsBBujpm93m6iHk7\r\npr0LOuGI25JCgPj7DNNdMwZEiDdhJ8xDDIl4uz3QYJLbIyZDnLDq4NgT/R02\r\ncyIt8Dj2jspyMANCYDfrSfx1tW9r1nICUlHHKwuKQIPZdS44m3kmTKjypSJP\r\nEXH7/qPygG0McLwLJv5kxbi9mSP8TPZ8bp+5dC1lzB6MHJ2ZVKUZLqNV3oy1\r\nDiuxafEPVBZ7xJKO+uYo9Im60YFfkcFY+nk6UhupR+jRZUPICGpHhALglqsS\r\nn2BlcUBBA6vphGwz1O0yApcoI4lFo3Y3X2N7o7AXLp68Zi92tM7bOqP5MXmE\r\ncsbIxkmyolrj73PMwmV2frkdXKgN/iZEMkY=\r\n=MBZr\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1-beta.0": {"name": "json-schema-to-ts", "version": "2.5.1-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "fc50688962098a2f06528d25d1d0eb7a177ab2fd", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.1-beta.0.tgz", "fileCount": 73, "integrity": "sha512-7XszncSCM9FebRR8EG/ES1gCy1Twsm8fHAuarGZTOoIuKA/rP8NqRIi4I9oU95xfdgiXfw4FfxwFomSRmHZVzQ==", "signatures": [{"sig": "MEYCIQCd1jsTNQNVg8xJF5dEBEV4IgNiTnEZT8Y4Du8M0ZbJWwIhAM+0SOCGHBzumjfJ7dNWNJwEnhvolcpZZhr/BnP/LNlB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 48790, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihsg7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqZA/+K/ZnWLs48NfraalDQcn7N1+FHdYXYq3w6JDv1Pt3aAI3UM3u\r\n76gnw7fpfuHTswoCdP6ZR6u0pqTQ2cGW/PmstxZ9V/pFIjf0FdJ81P+URe64\r\nhs8BJwgXSXRaLzGrEJSihf4Cjis3aIMS2453gsAX1LeEPc04yip2GsmmWFKA\r\n75eDyInCigsKxaNZsqcZWZLD6MBy2Gt3yC+FX31ayccCVwh4cEpPbzy55/A/\r\nvbAhdm/q8l+oYegypc1lUPQsnHEYWxZVQiPBWIuifraDuPM0/dZC2YGOqrTd\r\nqlrtapNazk8o5hP11TP6uImK087utORempqHEfBdp0giQwfhzMGAcItQrg4R\r\nfS+bO1w+7P9mPfu4wgZS1eZ/T9xfWjCvJVqSFkjqxq4i9LzqPz6s7PPVnZTT\r\nnoTiJ89jX/w+5L28f2a1bZndccIPrsvFjS/C9hknPRDud/IB+W7T32+sk5ac\r\nSb7OgpGyJbw0RyicUM89sSU4/BBObFpMSAw2emAEBNVv2OMDb/qJsMzI9sCS\r\nFCE/nujAK0yPLNdSHVBwtyclaaDdienbjeyE9ufs6KXoVCJNmaQ5hANUTB8i\r\n8T9veVHQvTWMRUFazMxPw9ompiNrQbOuvnsQsr+MDdk4cOGHqkdi3zEviEeW\r\ngShHXRGG+gb2dD3qoXsB41Ky0lA4WB2Tzd8=\r\n=n1Id\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1-beta.1": {"name": "json-schema-to-ts", "version": "2.5.1-beta.1", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "9add5cd202069d981469d3eb81a4d3b5d9a9b3f9", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.1-beta.1.tgz", "fileCount": 73, "integrity": "sha512-YBRAw8c32P7jywlmfYz1weef+pTE33ooEA+3svESpOFs/f7930Y07xIKlM1MpJSixRf8gmmaxDhcQshju2IRLw==", "signatures": [{"sig": "MEQCIAHMYRTX3H3ZOMhbmFMYF2tl1YlQEnQP/AkQOHnY57QTAiA0x+gX0M1w8jrqHl2GqnFvhkvkvbRj1TKhgtJ1OfKvMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49810, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihtFxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocEg/+NTeYoGxKlHNIaiv6wOLggyM+/4FjRFo2xGk0qrnFj3HRkkyJ\r\n9f3tyZFg8g6uowU5COGX0ubj8ovfzrcRNkalyKoaakYOgXt3Bk/Hg9xMPAEZ\r\n+QO+dxW8osyfG2nZm0qwOG2n3RYcLA0lqIfDrbafQ4xXC/V1QKal+Mp8bBRZ\r\nijBQ6BSA7BS5FXQM0bWdUrkosjZLxZiNhBOtSR+gHE5cb1bXm1EDV71ZEDT+\r\n6Mz9t2cy/5TUqqEYKzoTg02EaMPiS3rcsYwMViVjdEAa7CKfhcM4NeDwkBK4\r\nVjNVHmzspboqUJfmF4UVCu8xji8xeJNpXegJElVCzjG75TSG3WoBnYAut/dt\r\n/swBemMRDqRKrzAfVkD0m0cbVS9yAZDz9brqGkoquvhL/OCZqND2H6j6mhEG\r\nHjdA9bR5bhYVxNsvKza9KMiZrP+EfNfdfBo+h4TmPA57aA/qv+U4Hu9fLjur\r\nFGPiP3n1oaKRiwjuS7CX2PKeyZuKTaB+UnDZVSqHS9rYJ63h+ScoLJuUetoD\r\nEx2r14tmme32MTv2I8Wa111uEyk96VertOe6WSVFlr8YoSW0vstNvIShktUS\r\nPXBp64KEERR4lE0/sAEcgkqlxvQM2EvqHrdQJFaDTn+i4hVghliSoJmQOfPr\r\n7tf+4j6AXHGENELWYNDTynvRUTDotlMkKTo=\r\n=iENI\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.1": {"name": "json-schema-to-ts", "version": "2.5.1", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2"}, "dist": {"shasum": "6fc1acce2545206e78057912279a54b504b7fbac", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.1.tgz", "fileCount": 73, "integrity": "sha512-0+SWFvC9aauIsFQ75vMPAVS9R6HvGP025amJx+BWYMPve3t8F8zn24ekvMHBtZEMSTbY41kwjhrFRj/6SJCApw==", "signatures": [{"sig": "MEQCIHlSX3mJA64BEoVJnTnKrq0gr0unY2FR2iWDsGjfLaw8AiAmvma7FguP7ay+M+NoljK8KKwSsEkTgYIawGXxsZVogQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihtNTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoMw//V6jP4Z+6/Jq2wXiun/0CIfpQPAD7loq6Pnz4fgpg69n+aHXV\r\nUTEQJiKHuBkdEs+Aa2dOsilyB+h7v1wQDcLeqMCCOehnVRS9BWzwcst9nkdc\r\njTiJsL5D0r+9Tzbj0M+oHW+myFgDS60I92xWCwoxM09nRIn8aH+h41la31JY\r\nfKOoRlR1r2yPAdvf4NTL0E9EW1ArkSrLRv4lHquGkWB63IfMBB3E4aaSFrZe\r\nyc5Elh6aFnyyQxICeNryUc9En3Za3mWJTTATkUN4nIcFJb3wRTmn8fLLg4m0\r\nhtvTw05Koxm+ycYM1d3SD5yCHmKVym9OgpCwjPOsonlnxUkL3MwhQlQrao8s\r\nP8exEKOMkpAjEaBksPIpQTGAJz33389Bt5wLuHpnO+Pu1aPU/EapmhD4bTNV\r\nFWedkFy2PBFq0mEyoiZwHs77AEuJc4YIHrql94eThgXJ2TfTsEWbzmJSGycI\r\n+LAFKTUQTf7jKVv6KqMLCDfczRDiQLJnLO8PzwQIKsaqa7dinCpZNFD/83MA\r\nNfUg4qbid0w4r6RuudrKmpTnZr+38nZ7cpi18V+bzfo4U91+PdgTVYKr98P5\r\n836mhAoWmAqGHqKjCN0hKp/hZu/2P88KHvvVKIrPqXSZ5xEfeZPxmD40821V\r\nVA5w2xrsMPQhCUuuRWi0dI+UsUAqc2+UIGU=\r\n=usC7\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.2-beta.0": {"name": "json-schema-to-ts", "version": "2.5.2-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "d81fa528e7e2e0a7b0ecce95786ef3fd4c6f49f3", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.2-beta.0.tgz", "fileCount": 73, "integrity": "sha512-JKFVbkbwr01xNShIWB1/3r6CcTnGEcCnFAnrDK/DXEi6gMIv53WgctwwKDpCWUeCoAF3gRdCEPn2NhQz9/XtlA==", "signatures": [{"sig": "MEUCIQDXjpW543Lt3OOIEDR0XPLW9dFL5+cmpAsNZLtq62DNFgIgf8hYpAFrsktBkcv0N5ezc1p40l3jB5JHOsNLAaGWAaI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij4/oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo22Q//ePijziTlPGCKFrpWzekIVEl9XQmjW59svbbBHHMXnaZiFnhS\r\n26QU0wv465bO6pkCXD7OZbhfbTyKoeswFb/N4qYCKez8k5Fwug0KA60oQgKV\r\nfvAtqKxOoqn0tmCyEUPnVaClk7IYx9f2C2qfnA0zviCC/f66QS+WHfdaK+Pm\r\nIAUVcE4V/dliv9g/whn9ezsrruMBj5EXIQqY7sKVmBLP03vh2MMyUwFxjKHW\r\n6O5Ljr/pQUUXXrxhponvZtzLs6oE6CVODb1R9JaMec2xeh+lDFej/5+A3HZG\r\nE5e60dNLxw0cIZUCEWxehLLtDZ5sQXODPgRLtO/thMmY93943AulifVWdImV\r\noMxNqbtmTDyC9UDASavEi2y8+Lk5bHUwEbMoiV1itcAGUn/iiAItET+ZlnrT\r\nibbnpz0tNbzLBR/obB6hSnG8YMy8LkgQDw/Sk+Js8vGx8i6yryV/dUbG9l5t\r\n/3fr9z1mg4ZL/UdWfX8vTSWpRrDCdey+N/YgscMyuBEaYGzA0/AW/J33nbwZ\r\nXm96v6nuSi7H7rC25zUVrxLgmYlWrJXxl3+pkrgW0HRh3Yg/r/QBhAjYPN/U\r\nt7/4V/L//j2WJKtaGGpXipWqSKF1c2VFXNzhdgWq2jKYk8F32di7Z55VdVyN\r\nWILoZfbVaIR+tukTTYClheAKdI8Or97dwvs=\r\n=LT2w\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.2-beta.1": {"name": "json-schema-to-ts", "version": "2.5.2-beta.1", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "e9600b43035a53a0058f01dd52e9088c75c6de20", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.2-beta.1.tgz", "fileCount": 73, "integrity": "sha512-OWE2RYVaXV9ZNQMSloAaNMtc7zD1rdkH7Ae8zCVFpKJXhaGeUEeY5dvNYUI9Cf0yJhmW+F4yUsw7mVN15+1NBg==", "signatures": [{"sig": "MEQCIGTRsBr/onc9gyAeyHUD1X19puFUtv8yhNnRzBgFSeJ+AiBk+gQ9xL2HtgeJoINYwy/YGkk6SeqL0IUK9/tLqD3RZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49876, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJij/WSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtZg//TKZBCml2ElQPKlJ8kLPqbI2x/ha1WX4E/7zRGGTUK5hc/BxS\r\nNbvzYG3fOirGkA9eVairAhh11SPzrmNTF1ZpvQb1vtfq5PKEh6FrPr4/TRlD\r\nyT7Gd9GC/r0ez6TuMIKFMxHciR5oLPqtXSeYeHgMR2ePwIvukDTHDbGi3Mio\r\nTLHnNHuBMiWqyo/hTOIsgIWYCan4mzbjHXImFLiolamPmROjMXF+Gfi0oxLj\r\nf4klJtucgwb6d6GIVA1P0Vr1MzOD/jCPu5Nv/tGjGWLUEmJ8oeZNXGwgkOOo\r\nFlJLwB7ffhQ7usXIr5FjtigdKhY1SUwW6dCqSbJ4YPGnhE6ueUyuhc7koouG\r\nXPTYU4J2l8yukCIaOtLak1pAw3hs6BiD0X958t0tGLjrSy+C1P9eLO1YNgoZ\r\nEgymEwy42lVXmE1+LuQCna63lQ4r4QHeWDuQcPKI3NJ0wbWtfG8qqA4tJUT+\r\nUC9llibGcCuvvLKWX5/xmrJizVU33ikTvs9A4ori8iRqKCLkPcz20fOjT6MK\r\nBnbXaipvrjo/V7qgptht3spn2YJogNrAnRd6McNmZuyHFghg44sKFJnbkktH\r\nk4PdBpw0992RtTq4vGkP0NPNylcxzHbqZt3lBwLFp925zClMJ4atdtq5yx/y\r\npuiHQt1OyNVL1BD7GoQvCdLzZhc7aHXbApA=\r\n=Mz0l\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.2": {"name": "json-schema-to-ts", "version": "2.5.2", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "51332d25d9f11c35aee2e6b8ad6c6235ddaece12", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.2.tgz", "fileCount": 73, "integrity": "sha512-cpk2KMwC9sD1D4Rxz8jPhUbeIsbZ1VYik6BUkQ9/cBAovUByWSBekpy0m6alk3Cw6jj8/8gXLGCbaXRy1dYgKA==", "signatures": [{"sig": "MEQCIBgoE778gevA/3S/ioVWDZxu6aTcFNVF2zv2YS3EWqtvAiAkSm1IITIoT/ihE6jBWNRbtSZnJ8yqoo1uHmzePwYU/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 49869, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikIEwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1pxAAobphV+3mdKeM5KcKvAOiUcgxpeaD8c0k8fLi7UEMRfCRVtyy\r\nHOa2dDsKlYJFnwOi5qQTe45o8FejRTniqsXQDa8HttefR+1a5JMLH+wOwAI8\r\nrp90fJzeH3SKngGOlfE/gMt0fivuJRafIgvey/sUbmqwUqKEsKW5TgTXg2j9\r\n/0Ks2AQcXMNk0NzzpUQ3otBmmx0zsaFQ+4c+4cC/7926LenCgGInTHJicgSW\r\nS0ual2pcEHM11NXgbYFlTf8ys4JvEVEy6HoG/izX8efhH2omzeTDfuehQrB3\r\nwRc3mcfC7YN7gbdJ7CgqYNqdc6aUfOrtBHuTRwvizbyIxf5ukZbi7sjSf4pR\r\nKN7QO8Q96x0uLnAr2TIv5FdMnLJ1ALrTX/Vxgnk0mKyBEFVaS1so9K62J5Fh\r\nNjgYsqtrMGjV7I/kg/e82EJZWDAJE8MSAW9qs1UgJH3nOGpPda8hC+ycRAsC\r\nSEG/NMFNF8EXxb0Fie29cSQ7CodqM5E5uwlKm/joR+4s91mWMPK+tR1GjVmq\r\nwRM2lyD/+/A79k7Qq5IdcidY5i9G/AppOA2LAd5TUHXzB6HRBkK+xNHpEFpo\r\n6UE/uvfZMfKwOtcovBnetRU8Ql1xZU11jmbC0EXHDh23IPAs7Gw2EXl+8ATM\r\n7150K6g5VV02S+nmN8uSp2s89qgGcw+KKp4=\r\n=AXGA\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.3": {"name": "json-schema-to-ts", "version": "2.5.3", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "10a1ad27a3cc6117ae9c652cc583a9e0ed10f0c8", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.3.tgz", "fileCount": 73, "integrity": "sha512-2vABI+1IZNkChaPfLu7PG192ZY9gvRY00RbuN3VGlNNZkvYRpIECdBZPBVMe41r3wX0sl9emjRyhHT3gTm7HIg==", "signatures": [{"sig": "MEYCIQC63vuX2/c381/5uAtC42LN97PyqpO3OvQxM0QlKXsIBQIhAIaj0TJXZnzrCARL3osq7T15gRUSokdaptSnun0sa6vi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51683, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJilpq9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrM1A//YPs0X/gcZJzSWt4FEGLJ+OQyjKDFZfaVwFldqiVaqzzflO67\r\ncxOnus09iOQrQIIdTQizwgR9+LnU6uii5RRrPVOXJGT9NpIoNnJiNHtqroeB\r\nq36RXoUnSHsLefLLt46juWba1dRCWyxPx5YEkzQcDxK96lFuudaaR4EW6h9x\r\n48XQ885qrirmGSvuhIPEJ2LlLbb/cQZMb+FN0DMDMAl/Htq+QUB66aqyTi4e\r\nRLZkGBYNm71DPC7mbHPCVZjuXSt/p7zww7nqa7VYgRV7hndyZny0RnXcmMp+\r\n9a/zR1xOe0pze4HJU+j2AOyaAJlJqGh/L96tn8NnxLioYvNCcMGobWXrKx9J\r\nG5Cr/Zpt4WAyXqCPMP9g/wHhef16dTtcGub6Asbgdd01DV39pHdXqCmc0YSs\r\nVmUEcCfollPPzLzwkjW65XQ/JQzHmsRz4hmW9bKgZkSfIFgP75WhMOyBD+vo\r\nq9msH9zkyqr5mQHbTQkTrLGfrRad3EQrkj701nm7bwjPI9MFTWRj4F/nC+tF\r\nWXupGDEBnsrriF1Ux11nAitv9JgySUlJ2arYVSdwGcNKZof/FK35M0mVe2NE\r\nqqMwIUsg+nBZVH9alh85/hkyUqRnfuFU94hYvo1wj1pb9deo8N4418kgqdZM\r\nmpkmffrnTDCnIpWSBGtxDWVletdSHLSsG04=\r\n=wHkK\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.4": {"name": "json-schema-to-ts", "version": "2.5.4", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "64008cf5e203284289922bd622bff82043a1a4ed", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.4.tgz", "fileCount": 73, "integrity": "sha512-wlaYrGg+aYq0aEjSDY3cAFNzJVD2GvdrVIlvMdrbOLwkaMarXBiX+k0qm5Myb2aI3xjvdqsZoGs63JPS/M8+dg==", "signatures": [{"sig": "MEQCIAFiHBFZDZLfGDQydOKJxYSgGMpt+ZT5BAfjeJTILXfWAiADu7Rj4x67Z1ZNPN4MXqeqSKROc/WOsajthsKIW8FB0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiwCF/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmofSg//c0i/1p5IbDGb20BxRN1zidJKDrC42/JAb85Xh13S1U+Hl4pE\r\nN7hMgILFXOrKj2FeAKncwx/3zil6+JaHgP5lOpPp1eWua4eH1dghxeygk5/G\r\nigmPnWRTthaLDmL/AljC3Qe7l6lQY35E2chAx4UG0f9Z5bijbE4jtRylvv+O\r\nnOEbR/ELiQ0R3PmSoDw5btzDaRHfuhKqaZDN0vUGMWVkaQ2bDNGW/BK/KJjm\r\npZq/OIgLYMzoNv6hKJRwc825cvUZF9iLYyPx6dc2ldmCA4NofozKVA88EzhF\r\nKAoKPcNDt5tDMjBtMKyg/q+Sa36CtzTuGzugPOtyGK+d9afN/eYK5W5wOG7c\r\nnGrNiYooNAnN2H9NAM+LL83yxieyMDth1rShTJEHuO23WhDLYfPcDy/U/3L7\r\nNX3lkCbi0WELLGmtdw3LsFPEOjUgiO3XhEno2jvIzhUqE050mpQPHgEUG+Fk\r\nQkk+5ho1kN4PjIIAkC4FeCTGiYkyhYptb8JYltYU3dgMtV9uoxF24HK6HGFg\r\n7oPkk+Hi7ua+IEv7pfpF8bzexAoAdVXxG1sqt/GhlycbgjxkMGiofTXTgytx\r\nODHbcVxvhB9PBG+/ZILemx4/udnW4Byny+T+3H3p/Fhrp2TeOouOTCzgI5L2\r\n4c+142mT+8DX8mP7R5kMQWeK9LhPDgA88Do=\r\n=GeN6\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.5.5": {"name": "json-schema-to-ts", "version": "2.5.5", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "09022355466dc07451b0d1235d4056ae67bceaa1", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.5.5.tgz", "fileCount": 73, "integrity": "sha512-GFD5t0fUnX/B0gE9xbHjxv2BwFXRJND2+OKoLoMElJ3XRJ7dOBlLT7KXpg96aETeZ0RJbAZOfqHALBf5k4aIIA==", "signatures": [{"sig": "MEUCIQDSpytk6tYwoDf+Zf7I9S3hVpMiFHFiEkSU63Ee8TrERgIgBmnFG9jV3c41pIjs3VSLbFLGACQnkStai1tidUSJCec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 51746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi4EXwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqosw/+IwTJAWjTfH67wNBPtHvetDt1PCOdMm9ZGbfNNpm50jlqYeKC\r\nUcxpmXl+krfSafdXgb21mguBsN0i9v0BOuPOzwrrdcFlSgducOIn+hivkJ/F\r\nZTcOatXcrqXy4LNh6AMAFjL0BkL2huKU8K6Q9z7XTYstxm6YSGdQlwu4Q1W2\r\nMNDDPz8Zkp+EHjjICErYRV1fqHvtII1wLwg6AOZzDJyB4oM7nCd6WEyFEgqz\r\nzhiY+D5/+Q413uKDtLBj8eJG3htYZv4F7xSf2Z1j418XwSSpPK4s0dPUdw6W\r\nC5AkxNcyrpksMAMVZPd3BpuIai330O8jQ0egSGZ/NnKgObLWD238Ma5MHv3N\r\n9fT+XgPtH/y/QN5Z3RkW+bny1AfnaNDty/5isiGxxCpYhz0cF9mEK7yYJbfK\r\nO98EyQCwv/cV0uQOZCLX+Pi/sjOAqlq7GS13mE/TPKipZAJ9NkyY4cDMclet\r\nlJehHAxjWG/4hsUbijGqDZjFlSrp3tU8qxaz9XwgjocusZZo3R4Qb1u3TltG\r\nxAbSSJQ0QkrHZnzfqVy2bEsMuxtGw4SxV7+ZlKfHRoAM4Hp3WuRYwdczJf30\r\nJ+Tp+4HzlaGE19HKVQNs+X/LOuHPsSJnSSRWA+YoHUaXVP7oAFyncmr94C16\r\nfBteoM30d3hf6hBtKteWkqqChmkto/EWGG4=\r\n=UBIW\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0-beta.0": {"name": "json-schema-to-ts", "version": "2.6.0-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "b60c372ab59fe844eb238983c791a7dd9139d868", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.0-beta.0.tgz", "fileCount": 81, "integrity": "sha512-6ucjrAIs/lk7fatpxnk+1wwNQeM3PAByoIjbMezKv8i4r8MER7GdOzTO1mm/5XmkTj2tP9tzR3whLo3vocVVqw==", "signatures": [{"sig": "MEUCIQDYTgDQwFWDGcWWHyg85Gb/+Fm6OQAZCXlHN2lwaKFkTwIgKAxIFn8UREf3cGTLCCEtPVj/5ELgzivVjc3TrslKQF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaOETACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUew/+LpAzk8Pnvb6RRSOhL+XagzfmkSMh9zfJ+HerBZQfWeX3ynKu\r\nCW6QxnN4fK96ctVJszVTirse5P5gYF7XqPqYUBcGV8UxxKVstEIjK+iDS2Du\r\nZVTuG0HTdTGqGHcQ1+74Uylh1P4e4mLbH6hCXO3dZAHoYltil2oC07S22fvs\r\nduYO+D6KQ8KFhtJmeO0tvbVs04AT3v9fkMDs/MkXeJVpLd443d9ul+CwHEuB\r\ntZ6t6J9kwkn9EQJMZ8ciOjjDa38quiqC0k3aEBIE2XzYF8aYNo1ioEIT3f8p\r\n6s879QeHGMhzza6P4fO9G0Iuw4EIwIDwhpRJ3SPk7OUZD+SiF/+1poq9DZMK\r\nECRNf7eNsUvSGyAJIFhi8zjx4JouuCAa2IuRIJaKtBXB6Segh6E0lwgHdQfM\r\neJipqv68cnvqFeWt5KKX2Jb64qiksyyzI9rXbrp9+9zl7mM2EcTGcFDwvaMB\r\nnTAZndCnKLD/xmoMRBBz+BoVp20ms/CKXzL5Vr+p72tmv4wochtofKmhSOwi\r\nkbS6og9HfQZ2INLlKSFQ5LMDaH5VTJv+CDsjOdwteqXYreH5A1xwhOaTSAW6\r\npjM/h+L9FGxJCAL4Z/9PZqfswlcm/vnq7NgDE7SZcO3dW/nbMhL/P3pVHL2W\r\nTeUMqijZRPB3QA7CQSA6AstOKcna7vK6SG0=\r\n=ity+\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.0": {"name": "json-schema-to-ts", "version": "2.6.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.1.1", "tslib": "^2.4.0", "rollup": "^2.67.3", "babel-jest": "^27.5.1", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2"}, "dist": {"shasum": "17b1492571509ed0a6ea18dd06f26761a47920fe", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.0.tgz", "fileCount": 81, "integrity": "sha512-j6x2ZFCN3exmGAaGusxZYB2mvgpy8I8UkkNmN1H6zlq+3mdBwSQMIsB5iAltGlBLWG4aPTYqeIkit4okTI0s7g==", "signatures": [{"sig": "MEYCIQCq4LNY+cSKUP+dsUHi9kLuVhuo+tdmAKSQbxKzJKEtlwIhAMkKg2NrzwXUeJHjJSaRzkG5hYuIdGs9v9qhK0hwSlFe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 53933, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaOTGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgUQ//UYJWrVLVx1CY1Q75c0hkPhKYUo52uR958qTz2e5lxh5DeNZb\r\nxTsIiftsV1aphK2Tz6gZ6GvPYJDxvU+n66OEzTQp0tFZnJz6Fw7oHz9ZeCUb\r\nukDS86fO5koGuJKzPeqFBi4c5O/CT4P+ddmZ9L8FZ837DMwRsizu8/gnRJIl\r\nOLsX4KnyXp69zhEFenzkYObhbxdy8yjezWQ5pkieYgGDWa7eZUTExVjIeEZw\r\nsY+bD7OKHXc9RI0YTqKgnfgTpzxoUeLNjPyEX9DJrHglgRBjA8UHvB/tozHH\r\n3Wx4RoHpBBE+uGOG600x9DgCqcjo5g7tL372BuB2VL0RS9EQ0m7lsyV0Z6Xm\r\nrgUqyg/jtph3/txBcXiKovh3ksat6o04kU9XinhqNifeEnAKf66Yy58L4xwI\r\nkvuzO6KABkWmI5Jcrm54Vsk6ZReoK3yWV+xZGLvVbOEh7AQFoqj2nupng3rG\r\nUNvIDQlLxgmvBNuFib8qCF4YOIpGivInhiFrIeBkzwlEFa0bt/Q9/fpY6X0R\r\n8gZ/93YOGI29NnZgf7itiemAhlO2lLdfcqN7GOExe7gJrPNEcr/oGZShcnj9\r\nxo8iIzeluKWe+Gb+HRZmIK3xm+McQf0lMfG+TpIC94xVrLC2vxZRcVLuzfcV\r\nUcjb0oL1+8GO9cQhK560xwtX/xIvkpxo51Y=\r\n=gyu3\r\n-----END PGP SIGNATURE-----\r\n"}}, "2.6.1-beta.0": {"name": "json-schema-to-ts", "version": "2.6.1-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "fc82947837dfc9e130736bfe798fc9091bf37139", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.1-beta.0.tgz", "fileCount": 237, "integrity": "sha512-QPOsR69a3yI2WbHNCT1Rfd9MQyslheAVT+i786qMKN40+zPl94PMPoXjQ3tW1gMeeZaOCGPiGWAcgCyIznoasQ==", "signatures": [{"sig": "MEUCIHFq67Vc4HhLSPEbq/wWY0Z5iSIDXAEMPZbyZK5xOZrQAiEA/afmf5Y2MdgCLg02EiFPJ/xXHZ35SAoN0dlPosVfpQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97049, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjeg2JACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTZhAAhy+sKu9AgZgDgmB16UPHr7a/esZCxJRtTVub7aAKmSU69gA8\r\nyR2ny+Of2/PiYaFwp7GXC3w+ZdvceBdiQrnBpcEXL49wVT1wpVwyw62cF1q9\r\nhvBrUXSisW/vRSFTxHXsoRjhLA+ejJM7aIp39UKAsPTT3RyJrDg+dIKC4DbH\r\nE09v121DWNxglWH0NeNl+Znxz/VPo90rSRLHoce/Kwr2o1tsJI+JNLEt9nML\r\nTYNBB6vtTXtyBtuvmCSk3V7Rfuh7oXqbFDRov9XGOdfO1BZZHkHzQv23+CMP\r\nKutgdrQm8k4Cm5xZQglCq13VquDuZNFm6cAxddEkwgtqXT9Y2o5Cz4dF0yWX\r\nfb6004O+/6Pu1EQRX0TkDz5roPVnjiFap43QknbVaEOM6zsKPaLUa6jVqAZa\r\nLZiTionoYmF1c9cbglMMGVbBRvJ8p/Tx8iqSB0cveNEAT89GDjUk/m5/zUzf\r\n6T8UqPvoAXCjR1tQScwEP6Cj9Bgp5XyqunG3MXp3cLETw1LdpUzOoEpDj5o6\r\nO2BTebkhKeL8RCtUoSKLDKr6xl1xYWduL/sURip3u1p8Ly6BiJybXqzXv3eD\r\nYwa51jyGKskOnbBAYHxkOXd+Q38mqOYuyhB+9vJKY0QLSJWGxDDJDvL43VvC\r\n8SloGwLQ2uxhEKshmlDGYBwdU8tAzrgmgZw=\r\n=y/0U\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^16.10.0"}}, "2.6.1": {"name": "json-schema-to-ts", "version": "2.6.1", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "50327ece68abbaa74e7e07fe620f26b9f9214f4d", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.1.tgz", "fileCount": 237, "integrity": "sha512-SQ/L0BBDxYzd7AiF7WK5oKTF/3Y007XRH6Ss4ZQUfnBEtsbLmtG0GDiPw831eNzcF5WYqdUtbxS1NSb8hBu6Tg==", "signatures": [{"sig": "MEYCIQDxCT2HwrLidcrfMisGnL/3RU8rQ09EzU43+0o9MXsmbAIhAPI6k3iv3bAgvdf9fdjOGja8lVl1U059o4Cd96Z/Ggjo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97042, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjeiQtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqveQ//QSxENIFSbhnr/JDa95LagR201JSHXl8fKl/X8WjSvxhUL6aX\r\nJOEAl7IbrwVLuljDaxsVXcXiJnhgveEPuZduJ36UUR5F3qjobiF+c109h8W8\r\n+zg7T5IlwoD837NRzAvCDW7PjxNWGrbrWQkOtv+tHakZ8I9vyuRvbZ7cmhV7\r\nK7XPbSU5LznJXeAzGSX09ukkQTXjJGT5cN3MUEIU+TklcMVWVEEgRdrDynBS\r\ncM39+XzQ0TdHl5mDixUtQr6+ocCwS+62FFSq+FfCAjWpVl79VuqxUQ8sTV3L\r\nyrnpoVokbv6ZVw00xiFnZ1XlL0bU9m9cv6SKCYE0zMvD/ZkEcx6lYSxO+qrB\r\nt53npGUOWI/zkermTt10sPw58FoYXwKoUUp7DwEB5HLKnm/Yr9/fpoAFBv8U\r\n+xv3rU5NFC/eNZrNWyIpbw3x5spQuhJNpp7I6tGWp3nWI3fBivjuGmzGLf+x\r\nBQ3lqcB+A2+TpebhHWDa282+8UFUd3jbiDi9BV6pUL7lkUC2+G4sBbx1wfnk\r\nF7F6XtbWOrGzermDYVsGcNYNEYNjGZUyme3bYZKlw+uF2YucvZb5FZNEMqU6\r\ntyXNJuNKtEydehEjbyvJQWovSJVj5clard2L+6CZoW6DhPx9NXCCoQ5MPwXj\r\nNjBO7DaHNz1/ZV3gAIJRYqoyb93GLgVa+2M=\r\n=H8fZ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^16.10.0"}}, "2.6.2-beta.0": {"name": "json-schema-to-ts", "version": "2.6.2-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "b65c2ea30251f3b2762eead3f4e0c25e10ce4a68", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.2-beta.0.tgz", "fileCount": 237, "integrity": "sha512-nDuBy41XMVDxXNMs9TAycGwQIf7/wN3vwOB/sTk/hWqJdwdWQU7FRTeKf8qEZ4MbfkSVF4x+UQo7arDrgnmDHA==", "signatures": [{"sig": "MEUCIBr5Ouj0GnVVLEy1rsPft+YcIhaWxHSHNSLfyouAE2ygAiEApMdhIK/MM22Ep7tkI1/qBoO1vJjqIhlIlHX91/ROvps=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjhfy8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpmBA/+O0bh1AERjx3ptU1YzKlPsMBKrwArfAh/8fil4xshF1B/C9dM\r\nEcm7SJZ66/b3/Y2h/eMIbRhwlyGz3F9OVsZh5OvvJZ+PiTyhSPva0W0UP2EJ\r\nxCIJIGvxcpwslk3n+FAVBJK/xMM+HMgW+EBpJfytBBlvaxB3gRzvn/+h9iht\r\nV65+ovqnVRSa5/vopcMNGQnXXALuw/XoUYYO4Bk9dzwVDMYqf16TEV3TjE6Y\r\np6AftFmRCh8VhOls1Bbt7cuDHfD3jz3I8uTs4YoXfilEnN3nKy5COUevUa+2\r\nci/haiSUkmxJUDGJ390JaKb/iPhB38M9cVw+dSAYuLC23VcmMHecjTq82LVI\r\nQkDH9f+IhXI4l1MY6mEofeGXBkWkVsjd4gvHaaK56CHyd21Y4ALxLi92HITh\r\n2gx5Ttq/NygN9xNKy6So9RurNxDG+Rth5j8DlSEt/7/0pAiO0Yo5js5A8sdz\r\nmEnUmczCehFCMfx7bizJFTYySuxbH/BP5yMD3mqDULMMV3Mja807Fz54PKp4\r\niwru/9KzqGai9jNjZzzp2jeDVYL904CR3xAtfWnGoPoMrJ2TqvlVJ/n2YMOK\r\nf+GJB/sHQBZEntp6OEGwpU/QaVbZPf8WRspabjDBDKVXlrrNwl4xeT683B9Z\r\nWKtmyzfGSv3CnFO/P6eqkMTuoMN1oxqWDKg=\r\n=I5l7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.6.2-beta.1": {"name": "json-schema-to-ts", "version": "2.6.2-beta.1", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "7fb521104fefa301024b1313082ecf8408c5eb19", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.2-beta.1.tgz", "fileCount": 237, "integrity": "sha512-qPVE7rdszEBuPqY37N1T1KBn9FBURIJ6NQP3NvzJIb2DgKR2ojTjFdl0gjioxPUpr9CXz9Cx/s9DtDW3HPysEw==", "signatures": [{"sig": "MEUCIAdkO1zekQdyNOsOooCAH97sBjerHtXl4Gj9TiKLlfNoAiEA3g4136VX61wLUx4mdkTHIFi5WhfWMHyf2YBXqZCkR3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97045, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiKLeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5RA//YBLcGrs/26FoktMyFdeG58XmMU5vciL65FEydZ1fi2E0ROPW\r\nxeZL9RU9c78d8z3YTEHZ+24cSYCvkW0aF4MwC6+/GlYjrMD4p54NPFWH+h40\r\n6hOA+QZBTMXu0keNHRmlm6C80YOjz4msX0nxnq0gcOJVd6MVfesouXl/WIs+\r\nXkNjUgThUJH1lf8y+0hG1OzvZ0flKYG08lKyHZBvjIoqJwGdNjEPF/HotT4L\r\n/onvD2MGVZ6Afsk7mjB/9irM2CkbNVBou/BoIwcxDJCjaJ74pBiy9c/r2vnf\r\nwPZuyM/lZ+SNaU6AG/TZ+a5PMKw3ZwGznfE4j+WOQs+tzRfmm2VZcna68TMt\r\n3Cdp307YYPgraMV8Z/0Z/lPUXQG5N0ZBbEDmxhYX7ovKmWMhJVRkfB2gRigd\r\nef0EKdyig5ZZ6Z9VIh4rELXZT/FWW3q6b6LdB15+Y8I99pjN+aVLM/zIV+xX\r\nc45EValYmorEvLpAe6A7QLd1jFFHri90sRp5kXnqSYfwNEszEFkn5ZVfMcHk\r\nekpFJ+58L/s/pMCBUc+NIfX7WeADRTkBa8iDoYr4XZJvs3G6ez/3oL+NNww2\r\nKg2HKlQX67uEeLlz6aIhpqyQHFN0uHsWUY04YDBGmhL/3qi/mONyvXmYItRH\r\n/N6WwHTgszg1XjRiEApQ0tJ7V4IwrPvlopY=\r\n=PuHH\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.6.2": {"name": "json-schema-to-ts", "version": "2.6.2", "dependencies": {"ts-algebra": "^1.1.1", "ts-toolbelt": "^9.6.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "15d79875d3277fa2ec17854d97e83502801b3335", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.6.2.tgz", "fileCount": 237, "integrity": "sha512-RrcvhZUcTAtfMVSvHIq3h/tELToha68V/1kGeQ2ggBv/4Bv31Zjbqis+b+Hiwibj6GO5WLA9PE4X93C8VTJ1TA==", "signatures": [{"sig": "MEYCIQCK4qANNZcfhpeM7iNGcWueq5rYQVrJ6rFPstTKuXvDSAIhAKym4OpsrB3juA7gL0gVmn7HGt/FjFRAfr8sk47FrExE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjiPN3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOfg//SjXNUDzjoHhlp+5rJ/44OJGfaVwqXFYhLXdRLrJmYIvoIHYu\r\nLiK4haIDiM00uX1hoEMb9VLwEs5VFf7Cgq25rbxMrdjb9api+8mbgPSHkcpA\r\nXTHLfsCtWBUtG9AaVjFlSJtnyQRCHnxjb1ZP3ZdnOw1PInjSD2nYEb+NmDtw\r\n6ICXneFUlb3nhLF/n/blaoTrRaZnR6AZ6O0i+EdUFDLa18sRckIOqNn2hsxS\r\n4XwxQKJC71ovJmyQeeWMcC9P07ks2TotY9cHMVi79/DuYvNgbA42sI3YaCha\r\ndl0tCMHcv4v2UPk0UN7Byq03i80ECojjmUdA0vD327Skomz+lI04/alxLdi8\r\n+sxvOoh4IsT34axPeM6FxUEnAe7hVdEQgnf7ukCPVO/zt/kA0PGaRWYu5pnF\r\n2lyl24P/Tt/3L23dpZ2UZJd+UzKVlqpSc9rgpwFnSMBE5bPDuSoh9qGolCEh\r\nF5fpFDGkAOMvY82tzjGuCvqT/nBgz1wqD5J/w2vQy7TCpBwp4nEOzU+v5kMw\r\n8GrunEUsbVdjCBKUzQ0oihECy2zOz8g0owu7qzsdTvtBt5TDKzZ5Fe5JGZvT\r\nhEXtI8IBg8MU9LBqWsgRwFcWBAazCxAx8FiiLrfl5UccDhoxNH0zYOXB/jNp\r\nDCYrc5Yk6WUwSlr7YCd3JUwqshHpI7UNI10=\r\n=VouP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.0-beta.0": {"name": "json-schema-to-ts", "version": "2.7.0-beta.0", "dependencies": {"ts-algebra": "^1.1.1", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "03bebacfe5bcd2d1806a37f17875fb6f79b9525d", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.0-beta.0.tgz", "fileCount": 267, "integrity": "sha512-fIpLpK5ZrumSPPNDYWPDwc+SB6P4xUcdX5qBz10GEw/f6yLHPAUnwE5kEjQxfOE2fbwppcaL9MNj2rFfaDdfkg==", "signatures": [{"sig": "MEYCIQC0M3+3Vys0JDyMbOnJ33WxBgMC7toDIcSUzf3sgEnRgAIhAIJyEiPL27v+lq1dCnNqKzQjPHuxfNQLPlDxXxm9TxLS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97065, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjyIPmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoONg/+PHswHUW7KgQuNagRoFs7KQ8OK42PRM8CDdjK+mE33XfZjk3u\r\nVwD9CNcf6zu/RgqKeW4d4T+WGKwNdCx7MqMk8E3Ozf+2s8Ia7UQaEm2IWRb6\r\n3J4RU5g9HQlnVq907yO0ieUIsaCqj/AR7BwTyTTZe/OdqU6W7VVBWb/kY6E6\r\nH1XFDYHh0rvvijZmj8V/IWIp/YJBJHqA2yiJlrw+VLJxAdkIGgxZ1ZIt/sOG\r\nlLOEOwdghtDxjwfo3Ml6VZp961TIIvRochviNXII5P0iXf6Bp+FlHuYLkXrZ\r\nbjifxIEX629WNikBidJQRR9fnHDzT8XHMto+XTg5a/AmGjP2fCeuBUhBAMUF\r\nuuxW4WF91zv2tfYG5yMK6JnQ4R5HWCGJjwdh4Pis1YBxnvCVfMoL0NJ1GJD0\r\nCvc5GUe0DDIJVhoYIGZHAhMmYv2FwTEGPW3ngQQ+r2VJ6f36v38e6gGZyrWA\r\nJ92O999IyhEbRibSTDf1jYFtPgEzSiGkImcMx3UvmUws+BC4C1Zu4orm8H/4\r\nN+uCjsq32xHOZKW+ZCTa7sduTMKgm6okhNElGyym6CKlpWlKCRALTvqLRbwC\r\ngOh8eVK+l8uEzDIJ7AGM4ib0YPtYB+Y/PuHsZVwIfy5lrfevGTHFryn/f+9v\r\nMLXAm7CjxL+CbXhoSgy20GNeXZsbmQyuY4I=\r\n=BEhn\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.0-beta.1": {"name": "json-schema-to-ts", "version": "2.7.0-beta.1", "dependencies": {"ts-algebra": "1.2.0-beta.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "cf523d342e8b061d3e67823a1c573086bf99c6ec", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.0-beta.1.tgz", "fileCount": 267, "integrity": "sha512-V35NHnvQkCqAuipQ8rjZLNx8D2ZfQxyPVipWytXiIQXgZkOo+u8Z9NtClkmssGIOn1PXgwKqr8dlC3EY/aG5og==", "signatures": [{"sig": "MEYCIQDvZ2sYNZdAAlrzqyxfoRCrBBZBPvL8sLoXNPF28cUCswIhAJjHFRa/ZzL0ZjJJ1ZxADoJzXNPJw28udpZPHqYufkBt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97071, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjy9RRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXPg/+NUCuHKXvrp1NdOfs+//UZjh05i98BXFykynt5QrzpN/ul1bw\r\n/oKiOWtfKN5KLAZp5jjOkqogjm9+2Qrvf+SrPfN7/WVSJJjJnuqoCliHDkbD\r\nAVw9EXBEDpZqZy31Ho/cV+C9KmKp8UrDjU1luaZTVEr4CaMWInkJZhGgI+dB\r\nEj6xSQaZXy2cq6HS1aCIpMCQ5yj/FfOJrZWQn9YpPUp+oFTCYeZTEuJ+hAtS\r\nSP6f0N+2uDLz9gf7w9C4rcur/hk4lSlJxJohfkBQos+tHAUupYV3NlDk/zqP\r\nb3SH/yL4vj7kjm8vgqk0NYnluZ3aDZFQgtMJO35/0NYVvk9pmAIgv2rWLP3c\r\nZJHMtfQwVcv9qKFMaX3xqUjs17UkCGmUPbCCcwsmX7X63JfpNDB2KtxYdaZs\r\nv8RfZpjHAu7AXIRKmkMaqe+dQewHbdIq1GQhj6UpwkPqSPfEpOQIpvwjQHb/\r\nCbnrRBwsd/xYCehX9J2wvqhfwkHYAm4OvXlPqe/OZi9giLOWcc4gv2ljjnrn\r\nHclNRa4pCwbeJAzxKNPPUTY2Z+0tvWGb8UdKQitCS1Acrgal/pBHYTVykIsH\r\n74RiWpHrSBBfW6kmyaqfgWzYwt8Vs2rvy7skTX1PyshLPuJBJhNmA3Ns94LF\r\ni7dmEe4kcjvD6k+dUA0EYIpkv4xft6IidbY=\r\n=Bh6E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.0-beta.2": {"name": "json-schema-to-ts", "version": "2.7.0-beta.2", "dependencies": {"ts-algebra": "^1.1.1", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "83bec5509f74de7b92744de98226a1e45ea2e840", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.0-beta.2.tgz", "fileCount": 279, "integrity": "sha512-jGsWKNo9Rol3zIfZ97vMllUOyrarAK+MypVQjklfzH5zHiQ3q4F2E7WSOXsC/8X8E5Yg6IhZC7/6m1zL3j2xOw==", "signatures": [{"sig": "MEQCIFNRdzK4kVMjbAMaja0bg/MX0wvbx/yXsBG/DEOS+etIAiBwI+jMFlffXmpzcOPv34hy7eUgaXqoUIHnKBTCrVQbFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98378, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9pb6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqWEw/+MEMdopCbcI/oi89Ek3RZ/rC+f+rSJgGkpcnqpF8M2MvLqrxa\r\nouBCZpvIeyUsA7GO0Wrh7FPsjkikdKzQGP75kkoEtPLwZ8mM8OS94h6JWEDk\r\nD3MFDZrRXb2XvvLPe0+/ee3CqNcTkCuKrLNipCc+B6M6zzORfJUhG5TP3JaI\r\nOGpRBfKWRgluJPAO52vhsXuo06EmSCekjtWkE5aet5Nb07r1fTjWWYtxBGwG\r\ncGi7JBpQARDDkw77BVwS0geGKOwQwnsi+eiGQgD7J2IeiRq8mW6ecA+FWEqn\r\ngWpfYfQLKmLyJ/zi9AA2B1swGnNdEahQVR1WWH/ruRTsqB+5T8cRoCc8s3SQ\r\nKoWrOhZwAIsthGf7dghuHrkWJRZH1QLTgPy4+YRS1w3Xgfc0GJFxKSdMzheu\r\n1U+G8kv57266aCVp/WO1G01IINU/WpiMBizxS3A3w1lhH3vXGxUG8BPMDwsA\r\nkXLYU3D/dqXZoSkX0AVGf2prV4+E20YJBjdc2DaH8hLzlOzTldNq+3zY8EHH\r\nIZ03x9bqBJHjICNmt8JwM0WMbMSI0cwdHk2fVoOIH1knvuYf03ZSbxXno+iL\r\nf47UWZ8p94HK8r8oTYOMUDGLX4QIX22GfduFrk1bJNlJbg+dH8WHsczen+1i\r\npqvi7D1vX5A/FcnByLhIJ/vpCXuKbcKDbCo=\r\n=Jq/V\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.0": {"name": "json-schema-to-ts", "version": "2.7.0", "dependencies": {"ts-algebra": "^1.1.1", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "647be17cfb86c03b3cd3592ce8d82d7d1ab0cd51", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.0.tgz", "fileCount": 279, "integrity": "sha512-68zZJkbDrK3wcxxXvj0AXi0B0Vha10QNdb45iRwU3IkWFY5wcjSnClZY3DaBTBejRo9p2++26njm7DTLpRKNZw==", "signatures": [{"sig": "MEQCIBIIskgQ20iVqlxviYYpZ2x7Bw9RdCF1Xxe6wsDalowfAiAE+WYQK/MTqIBXfTFmKLz6vIzCtmJ5W3BigTx+VvMUCA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9puWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoUCA/+K1ecUz0k2pKBiL8Wx0zo+lCMQ9F3+FXxEmZuHCzXVJ0Iri0H\r\n6vgzSyvHFPJdVFe0oSahUNrKzgZS+yMT93nQvSUlG1W9XowBB8k/GB079t41\r\nGyCtpBmJu3gtNyj85YpZtvLMQ4A2Ha81z80E5bncndi3EP0EEv7qwjisbGPx\r\n42Py6B3YZPDu+iQ+Cg6JtwrtOkbaG8sdX2rqZ3IAGzQhffbeXOvcloTdzzTJ\r\n+S6TX3/ajZsrt+rgSlKiuhOnchkrNZyg3bt6BPGcHCzGSBmleJcxsSun8p0r\r\nxRIwS1N2IOQj7tgw48/fw0isFQdo1YirNcfJDO7NiszJ78c/QhVh1vUYhFse\r\npfPE0SjyILIMLtTrb7Db/DT7HBLuwqa7AYL9ksMyafMSoU06ccPHp2hNetAO\r\nRLZ1OvRcb0cnLmQyv5ywyOhyCsfQrfUqHdi/75OiFrNEIL+gAUkAKkSzfLz1\r\ny1il1JbnhgQ634lxR6fMQvXAIyGCkX+42hhoo+k6ESrr71Hrr1s6tZJ1dktS\r\n6+IV9Xi6Yn3qs+d76EVI4ix3TESxQM07dxu0+u4BuEwcZy7ORUd+IWd0SgAJ\r\nFxFmx1KEQbaSP9+g5cDPLIYOanomqftEZ7ScANQ7E30xiwHcykhXl9S8wxb1\r\nf1xsZxYjZxO2CXof/cmaU1qHToD27PTCt38=\r\n=pK5p\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.1": {"name": "json-schema-to-ts", "version": "2.7.1", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "87f322463b179373addbf08037baa98ef4a7be8f", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.1.tgz", "fileCount": 279, "integrity": "sha512-jLp6dB56OSR5civIILztmX1ZY+1z5o3mpICNTa9Tg4fh1fxi4So3uRFA/Oa6K3SVrwfBHbAmj63Bkx6FB6DtJg==", "signatures": [{"sig": "MEUCIHe4ZTP894OEHwLTW+SDUD2l81jatcB0kFVnyKXSREokAiEAxoxANSYNcm3DONRk3RKzIx1QRCOgecX9sqCn3R5R5Xw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98371, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9qCiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmNw//XCZAa8vvX5wIIXAFQROAxd3cj2S1/JtuBl+fxHZHndWV9Hxk\r\nzv4ONXzq/r7ZJiteVmn8PhPS1EZzk4JQooGztx/IYefG4P+kYr02is3WgAlv\r\ni19j42SZhLMA4vdpSMptbvXD1EVcbEkp6qXsWn5V57g8aJJvoBlXhzHIbvGm\r\nOaxTrJ5aH9I1SC2jnXdSsccZ+fbsIM5+smGLk+6qBhQsamY4zD29vilvItBk\r\nOMu2fyPbjOl6qeZcZhF1Z+faTgBgVWoU9o751PxcBWAtH+qwydvU/F4yfDku\r\n80yn52KodT/afrBbg8uUaCwkhga1p7kGjF9I94nPxOWHGPPZBdPbcdZNZYnk\r\nTmCXcctPcuEy3iTCQ3hasq5YOiUwqgZbZk8yhCxGouaqcKfKdyTKw6Y7NtMn\r\nT/4NKBF2UfOGFSSeCzmW6ANTpMOTWWiaHgEZhikEkkcQV7PqoqVzvTO1eN4f\r\nXEvGCmyRQTHfyqjs5ttP3OB1RE0kmuAK0oCcmbn13qBoE2OZ1UzBVO6aX4uu\r\nFNIgqb6RiiJsemglCx89EjGQsld25Rccy0jNlHszlodbWYFkxG0F+2XlOld4\r\nqIWqLqdpz3aNFtwvHqlVCWHGzrhUJQOCMTf4/QWvqU+AZDjvU5j9exU76G+L\r\nLId4BUvqXAi082QcGOpSBna9K/HjQ58sl9E=\r\n=eO3K\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.2-beta.0": {"name": "json-schema-to-ts", "version": "2.7.2-beta.0", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "f0e2cff6d66c73d4c0d8fba950653f234f1f14d1", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.2-beta.0.tgz", "fileCount": 279, "integrity": "sha512-N2zDzGsPY1JjkryxSOTtxFCa3WAeBnRBRnQOQDA5IcjfaCB5tjzGd3OpDVveQcrPWOvcJHzg7cSBIA/J0UO/rw==", "signatures": [{"sig": "MEUCIFOuMyLJAQVpFEaNshW7mFUtU3KfLCv+qGmqqIKHex/zAiEAjUTPS002giOZKjkAE3R0QsT03w9UKas70jPJmzcgfEE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9rK4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmocjQ//R9J8xYxdEk+raL6xxivDL0Eka3NC4r1nYOXxOWK8WwHRPAue\r\ngr6nTZmi3tGRq9R2WvnrzR/0R/hqqD2uIxuA++vf0TGBHPTJ7h6L3FCYgGi7\r\nbPvXcil/A4iufrttVm3+LFNLmXLIm0ta2yHFj4xY7MBu0LCyth5goMhnNoxd\r\n2rO776HITuyRP2zLhJM7QqlLahbiiCyg4IT/2vItF0XVcQyxfWC85T1Aw90b\r\nosKg6+emun6p9UzEMK728BZrCeM8AiFdjLcE6ogyIJh0kJ536vyB5K0CRsg6\r\nihud15N9x591JmLyXstZYxrRxqlTL76kPDAMEALZxa40DJZpfjFz/F88QyUO\r\nmckffugVq6+rGOTj0WolmUibfTnB2g6yrwPbjYOPFTF/7q3/KcIjP3+UgeDS\r\nS4xKLOzmN4r0MtR08I2XEHToKmSe8Ua1//xktI90ROvY3Cxj/K7BLNbJvW2h\r\n0L0x5uhwRfv0/WpYzinPVi7xIyI8ln+8FTijaYs3wKxg8t0plMi1XZqAUqnM\r\nYDEVuQQky24dya0evBAgMxNR+nj6kxlxWUWrgT4RYa2zTuF/G2WPXKAPCKBh\r\nMvkJl1WQ7LlFz8E/RnyudvYUIfGFqWj7FIsPu6zZ+WzSBumLUPr2k48OFipO\r\nH91BDRx8R8wrQkfEJfrXz6jYu2Ovc+3Thbo=\r\n=XE8b\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.7.2": {"name": "json-schema-to-ts", "version": "2.7.2", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "e8df41d7153e5517f0e68dbe57be12bb3609d6d5", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.7.2.tgz", "fileCount": 279, "integrity": "sha512-R1JfqKqbBR4qE8UyBR56Ms30LL62/nlhoz+1UkfI/VE7p54Awu919FZ6ZUPG8zIa3XB65usPJgr1ONVncUGSaQ==", "signatures": [{"sig": "MEYCIQCPC5u+vxEXjUq+Lbdi/itdS4z1MEn2l5fuvlkcsrDEKAIhALDhkAxaNOx6LDl7XPXrFwTlop68r3c0PjfFmtMSXFil", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98537, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9rNdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmppYQ//QSSRw0sr7WaAV4Q5Zwjv90on1DlQ+ehwPGNfbICJaCagl+o1\r\nb+a1I0GS4XhVrjv+Hu75PTNyWM9xavfk8qNg4mATA0+mgv8e804+FvBa5cyI\r\n4qPKLb+TGKHiQx9T0B1cj4+GTCwYB9Y8SsNhOzUDITyLSCZyyKfUFIPcPt8E\r\nFwgK4ekFu506KxN15nSq9SZDxPi+U0je7FR8KMfHPEwX/9gtd8TZp2PT3pAa\r\nvtcqo97Ax6ZJyKyYiPwxjnD2v2VURlqXgLJk7nVWBfbZUTP3gPNhhzMJPuFG\r\nHADk2Zx7pvJr19qZTTfHuzj9GOSYV8iTu1Qt82bpZfhnmbuvB97BSNK+Wde6\r\nz3rAH8b9Fcj3e9jnOKJrWkNWZeRxuO+9MOr+UlqQHsz8nsm8wH+Y7ZMggDSF\r\njImSFaDK99SCiu38a7gdAV1h1Sge7ldfqElFPhMvzTAz5Yq5Dz0v6Ex5l00W\r\nlPigXoJRQCdcr+4Id3rSXud+AghqpAVxtRgDLPQRHL28SoYjU07ouBEZmvSe\r\nxzfeEgwzHlrmpShGDENjQzkZQGz+J8rhmqEufXig4tLXSJxhlIcSpBoyc7NT\r\nBjLAyumLp62ReCxZ0nrvpC2DvxP6BGcenwc9fwhSje2xfllvDNuuTGyOGhvj\r\nW3hBV/fPeQSi0b9DPyjY4vVtcYtm7pAG99A=\r\n=y2Ug\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.8.0-beta.0": {"name": "json-schema-to-ts", "version": "2.8.0-beta.0", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "01991dabb983550ab3b679303ad964fcf40067f7", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.8.0-beta.0.tgz", "fileCount": 291, "integrity": "sha512-t6xj/KIdBtqckJXpGarvbcZLnK/3wxA7nkxPrAaXynigWUVJ4emZ5i4+n6TjLgZzryeVVpEUgruzFONiHObn3A==", "signatures": [{"sig": "MEUCIBnOVOuecqTPdoraRi0jk+YpDyP7A6W9VWfj3w/WqoGEAiEAwRP27YMpRemePenzQ3zBJVK6xCMHMrD6tsXbJTNHUe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 111444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPc8+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJ5g//bUBcHkjTPN25Y97oXO6ob2FrXIS6M3AR2Ix0pq+9yuPOYUo8\r\nXDjbgLEUI/lAqHANXlmXrP80r8CbgWOmG7KLhUashXKo6ue4M/IpzqY8U0qe\r\n749KRwNxtRJ20ZJnSEm7qgragMJW54bFvsvlw9CGBkvxf5E0Xqn7KA4Fev+0\r\nnN8Ov4GzyIOtdOITcpA4K/3ipNKEBZE0+fWqL1e9rl3iEzA9kJdjrv9TJ/29\r\nmRi+NITjwgIP8qdnkOaqfFnB9hhtdP0N3EFjdXhYccKpuZ0XnSgH6vR1gNjZ\r\nS34GWvExFo1sMQFsFnhavPUm10JTCGSvsFOk9prPQYxQX6GfqQrzEDPRNG2L\r\ntJfiHLgughog6nEL80Rz3vEX2FUNsSMRP1fcX8LZkHcvpE3iwmMoUcjHu0y3\r\nlKF5zudSd/U2hVMUJGHzfidr0+wLyEAbXJrWFq0KmFNl4hoL9kP5k+OnDIyV\r\nIWnRta95AcKhh85YwnckbD5ySYc1pZgbxBl40b0VS4UyCe2c68s7BuCkHj8x\r\nOelYSFNNcecOQyNXoPM5DA5+FpIobSjKguhX1alPMRuvUZWuaTCriXvIh2Zg\r\nc6wTvqaLpbV46aCYn3kBsAoAuWz6MKNa0C8PAKYmRrnFsycjg5/EremvUW0r\r\n8k9FA4fSQciFslO869dP+C0ur30x/bEFi+U=\r\n=pcZO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.8.0": {"name": "json-schema-to-ts", "version": "2.8.0", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "4e31a6ac98fd7ceae2a7ad065fa73977f0588ddb", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.8.0.tgz", "fileCount": 295, "integrity": "sha512-fNAnQ6N0a/0rJlqkzKBdb+5EnMgcKTlrAwMLg3adlKRTT1uNNT4ziqwBzMhj4vp768pO9PoQzZcUyv3QWmD0pQ==", "signatures": [{"sig": "MEUCIGC2pZ40Cv0rvKpisEnP7S9H74h1i6Oxr/pwAlfflVNBAiEAmcIbqKp2xMt+tAdqQY2d9a9U7od6pDLZgzg0vwNN0Jg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkPdgRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKKg//czlP/0RKXKiPaG4q5LRUAnKZsG/GHuCMFsppDHoD6cP0yeXW\r\nJ/qvbUkf9b2uL4QLSe/3jzSxGbDhTTlMjFqtJffou+tdRqb9Lhe4bj+RYthZ\r\nX0lk8Kaahcr+WubJOtj19e8raAFoyA5xoXmSowpLa54k6qAgOQf5J0ZTGSM/\r\nyPdtBn9akE2mxZulZ85MOeTdEilyDtwJ02RPRwqVu9DlWHR6KYV0gNEvevOw\r\nch50r5JmQh9IFycXqJtEVMGdnp1auxX6iCE98qCSu8re0gkcGmdzPun4c09Z\r\nudRMxkZ7eK6zrq+SKCtove93SKOIkrFzrqXU6OnUW/igD1th1CyHYpJsMk7g\r\nuy2aeFIfwY+kRLNtKmiLbE2Zd6DgUr1nl+hGsi3eCXEngSgsFS9b9iqNTXPg\r\nUT4D80hr+5I9Hw1lL+PXILmvc34rclohuImVdPjBw0zbK5V+7vFhWLNpwYjw\r\neNKoMOZTMMDDOfhmTCrLJEsvHazqksIUq0IgzrYzXwgTHInEw1Pvy0Yg9iiz\r\neppr/6AqHz9+fnP3muTrtnW1h6gdLmPTQflHXaa0HLlDmdUyWLKN3ukV9e6n\r\nPG+fo4RjvhRoQlTA6F8ETodYaEUpxWi7q/CIo303wJ3c5ny3MGGHJLndP/Ap\r\ng7PWriPzRK1gJ898q76y6yYblfGU6bBnBIY=\r\n=7XIs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=16"}}, "2.8.1": {"name": "json-schema-to-ts", "version": "2.8.1", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "4b19495cf274a87800527368d72787bc32ae3725", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.8.1.tgz", "fileCount": 295, "integrity": "sha512-cvyrXyE6g43GfJFA/B5VEBxWvutEze8iHVf9UTC0Pg26VcvWoYCWfF6X3484cm9xaX0pUJGP/wnHnU/IVZIHgg==", "signatures": [{"sig": "MEYCIQCS5c93e3fiQS+dGbs4xS2V7JSEzQjWxcqu9CDNkP1uBQIhAMcelHg6EE7qCfVFzsDL09R/VoRfiGxWeEPUleIAwUJO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116445}, "engines": {"node": ">=16"}}, "2.8.2": {"name": "json-schema-to-ts", "version": "2.8.2", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "233b810b73f01e0ab93ad06ddccb1c2b98f23b8d", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.8.2.tgz", "fileCount": 289, "integrity": "sha512-qyITu0FeIGLDMBaaAaMSWXSwpcgnuR5YEhNdLkD4nhCDrpjCKCJzO9pup99giDbzpGf5eX2o9OxCOe/29SaSbQ==", "signatures": [{"sig": "MEUCIB62RRyJ54C3ogwCyVfFq9w5Qkf+Z69MCGGnMSzWefEFAiEA/QWCn50ipWj2GHq3O1oNyQ/ynAdhblnUFQuTA3y5Xpo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117714}, "engines": {"node": ">=16"}}, "2.9.0": {"name": "json-schema-to-ts", "version": "2.9.0", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "7c688194bcd391b1bcca84c723dae23797ac4787", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.9.0.tgz", "fileCount": 289, "integrity": "sha512-QyIjLEDtlRVfgoOAhjNj/RLYWnuDLUIFFcvb4vnmMOzRjHRF/VcjOZX7LfxCEaKzkJLZ8/ZStaU3EGo4O2V+lw==", "signatures": [{"sig": "MEQCIBhPO8ieWFijNwHF+WTNNO28OrlCwSP9NMgB/sffMyJ1AiBZVjNjwpfqPZnpqe8AVKBkY4IosezqoZT4BVYSxhdHHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 116935}, "engines": {"node": ">=16"}}, "2.9.1": {"name": "json-schema-to-ts", "version": "2.9.1", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "0e055b787587477abdb7e880c874efad3dba7779", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.9.1.tgz", "fileCount": 289, "integrity": "sha512-8MNpRGERlCUWYeJwsWkMrJ0MWzBz49dfqpG+n9viiIlP4othaahbiaNQZuBzmPxRLUhOv1QJMCzW5WE8nHFGIQ==", "signatures": [{"sig": "MEQCIE1suhQP1MbPnYsHJAWgqe/1uLLhw1OqJKvX1FJdsxPTAiAY0DsQRdgvVDX0rNC3GwVB09rB4tp7sPF7ccDrZ7EF9w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 117803}, "engines": {"node": ">=16"}}, "2.9.2": {"name": "json-schema-to-ts", "version": "2.9.2", "dependencies": {"ts-algebra": "^1.2.0", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^2.7.1", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "ts-toolbelt": "^9.6.0", "ttypescript": "^1.5.13", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^5.42.1", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^5.42.1", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^3.4.0"}, "dist": {"shasum": "a054bc6410f13c7a2fc51aeabac52292e885b98d", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.9.2.tgz", "fileCount": 283, "integrity": "sha512-h9WqLkTVpBbiaPb5OmeUpz/FBLS/kvIJw4oRCPiEisIu2WjMh+aai0QIY2LoOhRFx5r92taGLcerIrzxKBAP6g==", "signatures": [{"sig": "MEUCIQDKTCenkNsu2s07i4Gxu1KIKQ9E/EbT3FUOKrBH1j2ecwIgR5uXCVpjKAIG/Y2GNBboBV4XJZpZqcY19eMmmMR4rsg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 123660}, "engines": {"node": ">=16"}}, "2.12.0": {"name": "json-schema-to-ts", "version": "2.12.0", "dependencies": {"ts-algebra": "^1.2.2", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^3.1.0", "tsc-alias": "^1.8.8", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@types/node": "^20.5.7", "ts-toolbelt": "^9.6.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^5.0.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^6.13.2", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0"}, "dist": {"shasum": "fd8bc79cebadc7bee5be1e32b289f4f7e1c2e4ae", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-2.12.0.tgz", "fileCount": 267, "integrity": "sha512-uTde38yBm5lzJSRPWRaasxZo72pb+JGE4iUksNdNfAkFaLhV4N9akeBxPPUpZy5onINt9Zo0oTLrAoEXyZESiQ==", "signatures": [{"sig": "MEUCIEbILF13rjx5bW3G1UNGcJuDYqMJgEFtCP8IP1CBMk4fAiEAwmGGlFPMq8Q2C86SrOHU0SxawSVgj2X5N/2fdI/wYL8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 113322}, "engines": {"node": ">=16"}}, "3.0.0": {"name": "json-schema-to-ts", "version": "3.0.0", "dependencies": {"ts-algebra": "^1.2.2", "@babel/runtime": "^7.18.3", "@types/json-schema": "^7.0.9"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^3.1.0", "tsc-alias": "^1.8.8", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@types/node": "^20.5.7", "ts-toolbelt": "^9.6.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^5.0.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^6.13.2", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0"}, "dist": {"shasum": "f97eaa14a412bbf7c3cde523703d93907386a439", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-3.0.0.tgz", "fileCount": 267, "integrity": "sha512-2adDesYifYEXYxNySx3gG0RR69rDWIjqAFzK/JPXdOvjHLZ/UP6d2rkpy6a+AxyhtRp2SvFPZ4+EW36jBinUbA==", "signatures": [{"sig": "MEUCIQCNayNHT39a0FaWK1pGHHOyf08xC/gp0yE4+UlrFyLY4gIgE5AhmyOjuO/BUB7rzDkxa3L8OZwkfwP65vGUmUi70Vo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114693}, "engines": {"node": ">=16"}}, "3.0.1": {"name": "json-schema-to-ts", "version": "3.0.1", "dependencies": {"ts-algebra": "^1.2.2", "@babel/runtime": "^7.18.3"}, "devDependencies": {"ajv": "^8.10.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^3.1.0", "tsc-alias": "^1.8.8", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@types/node": "^20.5.7", "ts-toolbelt": "^9.6.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^5.0.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^6.13.2", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0"}, "dist": {"shasum": "bd2760144e4a44fc8a4ffedc96291bf6c76f7921", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-3.0.1.tgz", "fileCount": 267, "integrity": "sha512-ANphQxnKbzLWPeYDmdoci8C9g9ttpfMx8etTlJJ8UCEmNXH9jxGkn3AAbMe+lR4N5OG/01nYxPrDyugLdsRt+A==", "signatures": [{"sig": "MEYCIQC8yBqk/0YdQs58Pi4jsQ6HXq1LCekXgUD0J8EFw1BFxQIhAI7+4QpSwvFWOmjz6101HKBAA8w75120Sl/Q7YtS2O8G", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119376}, "engines": {"node": ">=16"}}, "3.1.0": {"name": "json-schema-to-ts", "version": "3.1.0", "dependencies": {"ts-algebra": "^2.0.0", "@babel/runtime": "^7.18.3"}, "devDependencies": {"ajv": "^8.13.0", "jest": "^27.5.1", "eslint": "^8.27.0", "rollup": "^2.67.3", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "prettier": "^3.1.0", "tsc-alias": "^1.8.8", "@babel/cli": "^7.17.6", "typescript": "^4.5.5", "@babel/core": "^7.17.5", "@types/jest": "^27.4.0", "@types/node": "^20.5.7", "ts-toolbelt": "^9.6.0", "@babel/preset-env": "^7.16.11", "rollup-plugin-dts": "4.1.0", "ts-unused-exports": "^8.0.0", "dependency-cruiser": "^11.18.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-import": "^2.26.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^5.0.1", "@babel/preset-typescript": "^7.16.7", "rollup-plugin-import-map": "^2.2.2", "@rollup/plugin-typescript": "^8.3.2", "@typescript-eslint/parser": "^6.13.2", "eslint-plugin-prefer-arrow": "^1.2.3", "@zerollup/ts-transform-paths": "^1.7.18", "babel-plugin-module-resolver": "^4.1.0", "eslint-plugin-unused-imports": "^2.0.0", "rollup-plugin-typescript-paths": "^1.4.0", "@babel/plugin-transform-runtime": "^7.17.0", "@typescript-eslint/eslint-plugin": "^6.13.2", "eslint-import-resolver-typescript": "^3.5.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0"}, "dist": {"shasum": "96303370c2bf9da23aa0e86841548de6c70077b4", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-3.1.0.tgz", "fileCount": 267, "integrity": "sha512-UeVN/ery4/JeXI8h4rM8yZPxsH+KqPi/84qFxHfTGHZnWnK9D0UU9ZGYO+6XAaJLqCWMiks+ARuFOKAiSxJCHA==", "signatures": [{"sig": "MEYCIQCh38SxuXEOrMUgp0RiDoYMYbudKpHtn6iIZlL8UvU0PwIhALtf/5A1RuYkk9B6be4qfSKxyQ1xCHfnH4SiDRLHTF6k", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 121095}, "engines": {"node": ">=16"}}, "3.1.1": {"name": "json-schema-to-ts", "version": "3.1.1", "dependencies": {"@babel/runtime": "^7.18.3", "ts-algebra": "^2.0.0"}, "devDependencies": {"@babel/cli": "^7.17.6", "@babel/core": "^7.17.5", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@babel/preset-typescript": "^7.16.7", "@rollup/plugin-typescript": "^8.3.2", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/jest": "^27.4.0", "@types/node": "^20.5.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@zerollup/ts-transform-paths": "^1.7.18", "ajv": "^8.13.0", "babel-plugin-module-resolver": "^4.1.0", "dependency-cruiser": "^11.18.0", "eslint": "^8.27.0", "eslint-config-prettier": "^8.5.0", "eslint-import-resolver-typescript": "^3.5.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.4", "eslint-plugin-jsdoc": "^46.4.6", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-unused-imports": "^2.0.0", "jest": "^27.5.1", "prettier": "^3.1.0", "rollup": "^2.67.3", "rollup-plugin-dts": "4.1.0", "rollup-plugin-import-map": "^2.2.2", "rollup-plugin-typescript-paths": "^1.4.0", "ts-jest": "^28.0.2", "ts-node": "^10.9.1", "ts-toolbelt": "^9.6.0", "ts-unused-exports": "^8.0.0", "tsc-alias": "^1.8.8", "typescript": "^4.5.5"}, "dist": {"integrity": "sha512-+DWg8jCJG2TEnpy7kOm/7/AxaYoaRbjVB4LFZLySZlWn8exGs3A4OLJR966cVvU26N7X9TWxl+Jsw7dzAqKT6g==", "shasum": "81f3acaf5a34736492f6f5f51870ef9ece1ca853", "tarball": "https://registry.npmjs.org/json-schema-to-ts/-/json-schema-to-ts-3.1.1.tgz", "fileCount": 267, "unpackedSize": 121555, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICKcSaUbmqiKzzEqWlbHOx6lodG0qR6Ay+upVFPqx3HvAiBFL8JcaF0CGbSO0OVC+FhN51DxSb7m40EfCxghiLwg9w=="}]}, "engines": {"node": ">=16"}}}, "modified": "2024-08-29T16:56:23.298Z"}