{"_id": "@types/react-router-dom", "_rev": "629-736e1f740ed068a24f1b7642dee2af1a", "name": "@types/react-router-dom", "dist-tags": {"ts2.2": "4.0.4", "ts2.3": "4.2.2", "ts2.4": "4.2.3", "ts2.5": "4.2.3", "ts2.6": "4.2.6", "ts2.7": "4.2.6", "ts2.8": "5.1.5", "ts2.9": "5.1.5", "ts3.0": "5.1.5", "ts3.1": "5.1.5", "ts3.2": "5.1.6", "ts3.3": "5.1.7", "ts3.4": "5.1.7", "ts3.5": "5.1.7", "ts3.6": "5.1.8", "ts3.7": "5.3.2", "latest": "5.3.3", "ts3.8": "5.3.3", "ts3.9": "5.3.3", "ts4.0": "5.3.3", "ts4.1": "5.3.3", "ts4.2": "5.3.3", "ts4.3": "5.3.3", "ts4.4": "5.3.3", "ts4.5": "5.3.3", "ts4.6": "5.3.3", "ts4.7": "5.3.3", "ts4.8": "5.3.3", "ts4.9": "5.3.3", "ts5.0": "5.3.3", "ts5.1": "5.3.3"}, "versions": {"4.0.0": {"name": "@types/react-router-dom", "version": "4.0.0", "license": "MIT", "_id": "@types/react-router-dom@4.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "a3c26001ac47a174b1f039111db22215c03a3cfa", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.0.tgz", "integrity": "sha512-Y4Xwjc+TFHLqXIszpfZDRJyNabGkPMI9r7P7pHKN+OuG79C+Kr5Z22C1SqinX3tCKXjLn7V1wp5VTNg5DsGMDg==", "signatures": [{"sig": "MEUCIDMWAKNxB8ATz8UMu0JRkIX827Z8xcN0/mc6FWFWOtc4AiEAoroerlcOHFrIjZ2LkNRe5Kqtz84pMB8nWWaYJzT6ovo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.0.tgz_1490142489502_0.23233371693640947", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "415503abce196f861ed8196a966f0acb0f2b6f15c17cb82aae605f1bfd77fcb0"}, "4.0.1": {"name": "@types/react-router-dom", "version": "4.0.1", "license": "MIT", "_id": "@types/react-router-dom@4.0.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "5a8dd901607f26b936482ba73b47bcd68b5bd98a", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.1.tgz", "integrity": "sha512-6c4U0QsNhVtxBlKyQ0lNjfI5663X8XM8yV4nFlkx0augs9fiHMOdu8ayX2kjDbBvdcVrII1toNssYCCZ0GCQcg==", "signatures": [{"sig": "MEQCIBOaexPyF9BJl5Bk2Onu7fef3tmKFG82Lq/rN22Fae5iAiA6G4orzUZICGW2X5FJVCdVhrWvMolH0Nf9lqlMe/g+7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.1.tgz_1490312644916_0.48784023616462946", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "3aaf9f6b2e98a9cb105abf1785c018bf27f420c3756fe4cbf0e7a87682858f93"}, "4.0.2": {"name": "@types/react-router-dom", "version": "4.0.2", "license": "MIT", "_id": "@types/react-router-dom@4.0.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "4b5a3a9a29a05553e55c40e270f0716c7de3c9aa", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.2.tgz", "integrity": "sha512-jV9JBKJHLeMPsU6U9nRcoPjar7a8rKhCYiF3PcNnP90uzrtEgOsakcv1vm+tS6dBT7skwqqv43rvgdeSHyHHjQ==", "signatures": [{"sig": "MEQCIFcEDw0LUdxC+/d0n2oc2XQht3sx1GMxX4SUoXR9slDKAiAevIAHEOhDPOjvQ8AwkUUghqODd7Kd6NIBCjYS19M74Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.2.tgz_1490739104101_0.29600418475456536", "host": "packages-18-east.internal.npmjs.com"}, "typesPublisherContentHash": "6ca71834e3a49db1fd87ce22bad10a34230fbd360def1f916a9c3a73912a3bac"}, "4.0.3": {"name": "@types/react-router-dom", "version": "4.0.3", "license": "MIT", "_id": "@types/react-router-dom@4.0.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "7073af160d7ed3c30f210a3b55c3fd24e9b5ddfb", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.3.tgz", "integrity": "sha512-FJ5/7H7XxxQ7U2Pj1GL3QJyKq6kNvglHUCiFn82p2NArkdDF0Mob4oMRYTo/uSHtwZ+7HUY9Jhr81HG6u8sL4g==", "signatures": [{"sig": "MEQCIFYQghItFaX7/ijf2bfgYGgo7VQsju8X8qs/vA8cA3QgAiAu6MEXo2D5zZTAQDjGyLaLyyndmUK212/+d+Ys0R1tLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.3.tgz_1490909483653_0.5144960593897849", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "1e42b06be0ac907332e448c3faca31e08c871c3c826b9fb68ed792f753f6907f"}, "4.0.4": {"name": "@types/react-router-dom", "version": "4.0.4", "license": "MIT", "_id": "@types/react-router-dom@4.0.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "49f4a7037958f743e764b807fcbc8062fe46e493", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.4.tgz", "integrity": "sha512-zEJbDVdik4QGG54YXsJ+b00IiptGHOPi9aJrxZAoYSkcZaW5camVQyggOpTI6W2F1RwsAD5qmn3gDZeIC9je2A==", "signatures": [{"sig": "MEUCIQCZCWsRjk/ugEvilBlqeyhfE+nXg36/rzKDvxkz+9W0HAIgeocCMP5XZSzld0yFs5aNyoR+F/a8VBHphvK9gGzwWNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.4.tgz_1492528397209_0.6661159184295684", "host": "packages-12-west.internal.npmjs.com"}, "typesPublisherContentHash": "a09df3bfbaec097fb7f91c4866c593a399acdb880eefb9a6b557e13e46cd39d1"}, "4.0.5": {"name": "@types/react-router-dom", "version": "4.0.5", "license": "MIT", "_id": "@types/react-router-dom@4.0.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "638f89ca9053a0f3937d1604f2ed63dac6172ece", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.5.tgz", "integrity": "sha512-0AHAokPyW7L7M+brcaMTnFewce0tCaqG+C4KtUyizlk5tBToBEZ/bpjMDL5hbQ96Nc/wl5Ri8DJBijUmf82fdQ==", "signatures": [{"sig": "MEYCIQCcwoD2pPyhR8ZpgQOFZK1sxfcgyLu/Hhh8Q7HcZ0yj/AIhAO4oJyuIRCebzUQ1y+bUxKHaMiPbZPBcrVudDA89Vkia", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.5.tgz_1498239455183_0.7550721298903227", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "95b78936f9026242ae473761e67bea8779ef109e654dd43cdcb5c162bdda59db"}, "4.0.6": {"name": "@types/react-router-dom", "version": "4.0.6", "license": "MIT", "_id": "@types/react-router-dom@4.0.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "e3bd43e4dcae8aed421f53b02425cc83242f3983", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.6.tgz", "integrity": "sha512-cvmg0t5YxE2cIXPpFp4gI6avY/rZTO46cIsTTe1crl582uO9ZnXvbJYwpgdxvpRCGv7klmIJdzP4JMQ817ZDug==", "signatures": [{"sig": "MEYCIQD6zZCuskSmDWJysmnwGKuuThhu82IE9teGa3LBgeErsgIhAKN6zZJ3GOZcTl6upYb4abujxHdnzAP8CwjB45hvu3iK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.6.tgz_1499460827859_0.7381868441589177", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "fdb1358da888c508aa843c3321ff70b2e39eca17d9e8fb66f6d080ab7f972b23"}, "4.0.7": {"name": "@types/react-router-dom", "version": "4.0.7", "license": "MIT", "_id": "@types/react-router-dom@4.0.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>"}], "dist": {"shasum": "65b192f7a5dbd1c05a8d04cc5863c9775705a435", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.7.tgz", "integrity": "sha512-U0MRf2YP/Bf23jKc+9rh9Ugn2JuAGUFfgMGnaHlL5iX7m8VvE5Mt+vuHikR10nysduEUg1Etc8xwBcLEh7E3rw==", "signatures": [{"sig": "MEUCIQC/46o0uU01LsRydoVuWbStZMKq0EH1msqH8ejkkFjw0wIgYWhvZ00MY2jtx45RkGBxoTmiB3lT6Ly0301z4tdRsYY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "peerDependencies": {}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.7.tgz_1499781727872_0.692630500998348", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7271a4eef8515401943d23b46bda0fe40b33fe68e9fffaf175692d6f12bc43ed"}, "4.0.8": {"name": "@types/react-router-dom", "version": "4.0.8", "license": "MIT", "_id": "@types/react-router-dom@4.0.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "a9731bf35d053cdd27053baacc19e505d74514f3", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.0.8.tgz", "integrity": "sha512-+Ipm4f9eNhzu4PKoQIqrj+VMiYWLFb1UXWOpx5z1CmSoVIdA0x703aUuRPncC1o9KKcuwr3bj1tEIzg1I/vhAg==", "signatures": [{"sig": "MEUCIAP50nxS4s4aAo2F+7otneYDSs4LbDhirN6jl+UTpal1AiEA8cQ+k6v/ghb3nwCvWBwEiW2vluqKfWH+wQINSAais8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.0.8.tgz_1506384780719_0.20925608393736184", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "63b289cd1b9dcfe982d808f0bb20f9c6073cfafbcae1cd9d241565a6c1a9f68d"}, "4.2.0": {"name": "@types/react-router-dom", "version": "4.2.0", "license": "MIT", "_id": "@types/react-router-dom@4.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "0f2f11d1553bd5aafbd8f613317e8dc9291d7a34", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.0.tgz", "integrity": "sha512-q4Wed56xsMSYOYa8IXW/SHJ6GqKE1nGSvcm8ZNzesQPIObyoFhLDUwzF88aJTVLDvlW0+YB4sf5E0H7AZY5LHA==", "signatures": [{"sig": "MEUCIHTDBThFx7Lrl1gOJ1GajxfEvlxfeLD9Qi1LQIDghoDrAiEAqSF0ecpRK4GaIsuTK8xH1otcgkrgAV9Bu4RF96lpJjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.2.0.tgz_1509151022951_0.4841445554047823", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "cb1cd23de33bf181815f7050390600a6f149a131859dad26a1916efe1c1a83dc"}, "4.2.1": {"name": "@types/react-router-dom", "version": "4.2.1", "license": "MIT", "_id": "@types/react-router-dom@4.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "c5583550d7246dea60612cc55a523cf980d56df1", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.1.tgz", "integrity": "sha512-+iCFvtJ/X7LuBNHphm8CUDPg/J8y8iSM/XfYiIquEIUOyC+xXMfKLQFf+1zNpN8iHdB+ISuCRGq/UqOd5LVjsA==", "signatures": [{"sig": "MEUCICJQQOUtDMzR/3C7ROpB/Oru5aXgYJ5zIhZ2kW7MY/yBAiEAmVXGUIgwBB3l35rgwDLH14Isg+LJvKjL0WRj7Mi6apE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.2.1.tgz_1510221565068_0.5830972418189049", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8917fb767669ffcd4384de16d5100bc6d21b0b71736097a7bfb9b666127ed88e"}, "4.2.2": {"name": "@types/react-router-dom", "version": "4.2.2", "license": "MIT", "_id": "@types/react-router-dom@4.2.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "4d6d71ab98560efde309c6469e1023a70dfa5ea7", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.2.tgz", "integrity": "sha512-SkTG6CVbpIZOasb2NQm613RJ0T+WHMPc7+P9KT1V7WE3ukSdDO1318w5dD2znScLTT5nnFlLwbQzJiKkDXXc9w==", "signatures": [{"sig": "MEUCIQCGr35vtIlvfsuHZ09KxWuIe+5SuO6W1noWTGB52Yk9TQIgIBvwdf3eAP6RVuDrRGhv1WtEo/k0yv8gvbv/y0zaabY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "typeScriptVersion": "2.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.2.2.tgz_1511290958057_0.035951157566159964", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "f17c68b43ecff988141600c8a8468f989b6d7f9d755e9b32bb0586c954d0f6f1"}, "4.2.3": {"name": "@types/react-router-dom", "version": "4.2.3", "license": "MIT", "_id": "@types/react-router-dom@4.2.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "06e0b67ff536adc0681dffdbe592ae91fb85887d", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.3.tgz", "integrity": "sha512-q11w7Hw3mM/5C6wwKjres3476IKA7wDbpEjsSl1FsAJ8I5K7zlZmt1NAOvbKBZT4cJ4GQEZPIJWaelsLWd/muw==", "signatures": [{"sig": "MEUCIQDLb8S98hFYAa+E+nils5W/iQQaWIthMggW3oChhsOiowIgWNuHgvkuw+EBbQZ9cTiZXTmxJHnuJlV6BLo4AArt55E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "typeScriptVersion": "2.4", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom-4.2.3.tgz_1511804937793_0.6417496439535171", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "04aa94f4e7804bd22b455f2ef2c3679c095bd735e7cad38f4dfa14ce2b2e4e6a"}, "4.2.4": {"name": "@types/react-router-dom", "version": "4.2.4", "license": "MIT", "_id": "@types/react-router-dom@4.2.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "02c43274129ffe0ac42b348f7c8c4a9a4e127c8a", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.4.tgz", "fileCount": 4, "integrity": "sha512-m90mTx1kWJ75H6WALbJtcxvbbQkt4BrlUDFrrAy7HKlAO6Vf6yRuc41m85PbIAABrJZnrMDXp1wwnR5ios8u4Q==", "signatures": [{"sig": "MEUCIQCexctAaSOP591KQruuPrV9dlWDwrt9Cv2sGry2r/YvmAIgWBoH85KXGL9TUEADlkB2qIlKZk4jlAQ+OJW4F7NW2Bk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4383}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.2.4_1518469661043_0.10498903854628794", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "26dd9118a17b3136133a0f83a5444fb670cb033cb471e42923336b821c87c2ec"}, "4.2.5": {"name": "@types/react-router-dom", "version": "4.2.5", "license": "MIT", "_id": "@types/react-router-dom@4.2.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "a6fc921f1008567eae35244e30f34f5f7655d5f0", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.5.tgz", "fileCount": 4, "integrity": "sha512-e03TTlYW8cEIJw8bRPmfpVvXN8BpeusjOypxNwlbovPGltR7WwQzZBlutsJC6XQxhhHd7g62Qkyt1oBCcosjkQ==", "signatures": [{"sig": "MEYCIQCdhtV1Z0LgjUd/eAjLRM0ZaQCK3nCPHpkCqoRw6Igw+QIhANAdZu7Q+T/E1SGzWNCPFsUWn4WK/Pw/ExQcO0+q2eOE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4402}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.2.5_1521156019712_0.2016249803367931", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8b5bddf5bac8e06f9918a31f544fb37bdcf5ce9fb7c20ecde982b2364211c00c"}, "4.2.6": {"name": "@types/react-router-dom", "version": "4.2.6", "license": "MIT", "_id": "@types/react-router-dom@4.2.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}], "dist": {"shasum": "9f7eb3c0e6661a9607d878ff8675cc4ea95cd276", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.6.tgz", "fileCount": 4, "integrity": "sha512-K7SdbkF8xgecp2WCeXw51IMySYvQ1EuVPKfjU1fymyTSX9bZk5Qx8T5cipwtAY8Zhb/4GIjhYKm0ZGVEbCKEzQ==", "signatures": [{"sig": "MEYCIQDzipRwh8KZ9DueCjNwrBJ0bu8rPFm3beHdXVYuTCREqQIhAOWqI0XM6595eg22Zsv+8VFMEgWWdsbYPJUnOUtoXB9t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4723}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.6", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.2.6_1522176909297_0.2595211148382157", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "54dcff8613f62c94657e6bcca01cd59907c07d75c2097ea9c78574325398798d"}, "4.2.7": {"name": "@types/react-router-dom", "version": "4.2.7", "license": "MIT", "_id": "@types/react-router-dom@4.2.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}], "dist": {"shasum": "9d36bfe175f916dd8d7b6b0237feed6cce376b4c", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.2.7.tgz", "fileCount": 4, "integrity": "sha512-6sIP3dIj6xquvcAuYDaxpbeLjr9954OuhCXnniMhnDgykAw2tVji9b0jKHofPJGUoHEMBsWzO83tjnk7vfzozA==", "signatures": [{"sig": "MEUCICjZYhvoKqTjAycii72YCXjkAomdjjMI6Tx8IpOct4vOAiEAwEiBUw7JI6zbATH/qSDAhbnoIySk+bA6aVv1w0zBg0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4715, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEIsZCRA9TVsSAnZWagAADVAP/A3neOQINYhO3e6h6zc3\nAeX8VqKdP08SnLgCw0hrC7gNYsDdicFX2Uad5FGjTV0w724Rb+f6ytLAaWcW\nEQn594GsL/2+9cyU8LrAyC/afVA5Ybu0dnwACMn+OwKScAci3CgGjXOr077x\n6EaG4HNs4M4GsR8fQRoEeJ7HP202hjVNDEjAclW9IrpyiEYPIfMEzIM28FSu\ncbb10109SLAJ9dbAZ9dEJAr4QibJi0Cg+F3b2GDyOd7/n78fecSnOgluMFnw\nmxR91t3q/8yZQPoPS4Hn4cVgs7enZiQUhJ5JzO53okrnMLM+hLdPw4Kc7g/1\nBKz5EDtngMZM4Gr64vS1Qlv0HSIjCVxwdqp8rrW0IqTxDeOAAxyUf8AbZQ2I\nA4pJHzxR+MQZyHn6rY/uMF+8msk+5M6ebWd7bt8ga8RDwRuxjJzLx9Dgy7w/\nK0XmYCcfx67BI1uzrG85up9GrkNBlsf2F90kBiLvWQ0WgFyGEm9XD8vW0KED\nMLNBp72WAv1o6pEEIlEDOzS6RIJRt6B+I6hNORUP3wugEI9How+gjPXsM3J3\ncyygCBrbAtSiuGmf31iJ6bc7ScvXvN7Yfo1aCUwXjOhH8bQdGdteQAvXWdPW\nuVOySE50OFQbzgoUJ/z/I9bMfGElLoZxaCrSdNdeYym4xGt5L7WaVA4iHJb9\nS0x6\r\n=D6HE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.2.7_1527810841300_0.06299562369171396", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "169cb9f5e4de325ca0f1035c24d526ebedc78272c93c61d7ea6d01b5d67d78e4"}, "4.3.0": {"name": "@types/react-router-dom", "version": "4.3.0", "license": "MIT", "_id": "@types/react-router-dom@4.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}], "dist": {"shasum": "c91796d02deb3a5b24bc1c5db4a255df0d18b8b5", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.0.tgz", "fileCount": 4, "integrity": "sha512-vaq+nVUGyMnIhAl+svKHmfqhCoCNe5lf1UWmvVcsBlAcX0kznF25711DNFQ7B8/QZ+ZF1zzGq1dIi9bzxIu85w==", "signatures": [{"sig": "MEYCIQC2JFOQEj+e+/O28IQBknK+I4FHr0d/tbaCgvxy1YGVJwIhAMXjczsmB8ylLTXlIQ8vj/64lMQuxdEY1O35llXZH7D2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbUpYMCRA9TVsSAnZWagAAMlUQAI+p2CD59HOh3Ubwz34R\nPqkeb7dc0+ergznEEEY0ugNmqdvQJXQY6CZTxUnzhF8YW9rtNP9OYNSQNyLF\nM0McuKt9g9GfaBDrdgH+6o+ah5B9OOJBTmHQkMfsdFilck2Ho7Wuy1LyT8wt\nbuozabx2FYZG3wD5zhp/UwYWoz9hgMpti9xnpbCEeq/dnl+MvoE6OEgJEBlo\nayW6yCKR+bye6eqjdgjsSuCq4c9333MObNhBHi3nE/KFqMXppaW3nrPjZFqV\ne6GxCBeWVkOv6QWLxteK4fLBlMp9Z5Z/86lwj4tkj13/vHVvZZuFDsbnACIX\nwJTbSArxrmgzNLfd9YRt75aSp7EVOxnNqEtFJhR84vKfv3ynE8dMnbVqNGue\nulBZOKYA9DYLzZnXRVJFKdsJn2wGPJtG98ona+Upt1jyIKuG/hRpHLmR6z7+\nHCtpTzUZmsqt0QU/fZYxV3jh4+6TRZF09IfaxIY2AIsSyG+vZbhJIT3ieIx3\netZAMTn2kuLqKoCNtDWlyciUzdrVFImrcT8tFJRdE9dA0TxVFQfLGoBBxI+j\nVWYMYJxn3uMBAiNl7kDT5ZlD6Xz64VYccUE833iNBcT5PWDrSGMrNe46/P4M\ndfUAHkmYhtAUkF1vYcKaLf5w0Oz6Qg4Yu5mP7gZCOkdDXRyLacV/MZTItbQy\n6IUq\r\n=BeRA\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.0_1532139020761_0.5407125197375859", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a9be44524dbc1ddd0e4476900969c5bfb3fdc87f1c81a6462509082bf5d5a711"}, "4.3.1": {"name": "@types/react-router-dom", "version": "4.3.1", "license": "MIT", "_id": "@types/react-router-dom@4.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "71fe2918f8f60474a891520def40a63997dafe04", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.1.tgz", "fileCount": 4, "integrity": "sha512-GbztJAScOmQ/7RsQfO4cd55RuH1W4g6V1gDW3j4riLlt+8yxYLqqsiMzmyuXBLzdFmDtX/uU2Bpcm0cmudv44A==", "signatures": [{"sig": "MEUCIFvFZsj8nW1cA9RSpeF2pNdnth3Te8qA8xboQ8mS2OYbAiEAvAz9wyS6qrbl10Ut6YeTRB34IsZ5UEky9GEpU+zOTZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5134, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmvcKCRA9TVsSAnZWagAAKr4P/i5nRt2OfFM4aoQaGaUf\ni3v5ClczcqPl6y6VqTqouPMeDb98JXo6merBbMT/1f5Zo00m1Cif62CiC9iC\nDtr202s0eNgs62SiwhCZAR7ltNhH0NCy0TUnRIY4s4wWaf/xc4QXeSM4pMcZ\nm19DapSrASa3nxTIh8LQUl0kGGoJ4J/XioumvBu0Eb7A52WPnIg/HYHdggKA\nfgxVx/LWe/RhmyzNu+1SLGq7qSK4/i9uK854KjGmws0cQyweXhhCUjRk4efb\n/JaOsj47Eh8hNe2N80rgst0e2l93VczMOQdFqi+ZbkmqW3sy9bQswunJ7Z4F\nY6RoFFlq/KoidA6KhtvwgCPneQ0ij01BTyHqLDZ/qRb9u0DxWkd7u8b7+7zG\nErf4MFpK+2BOYcedB9uqLbEzrj4LPAmk4AYVZy0C5sCLWvN9+FZpB8huiwNi\nL+/dad2OBeoe1gGmWalwGdSQIwBVmjtuBOSrYLyyIhoAX2uLLUeY8yDo3GUT\nH2WxeoXbfKKKGtDnWmQdeJ0w8i+oyLBP2r9YDBphSNoH7VPV213+oM0qRvKZ\n0LC3bHaMZMZAkQ6zRqzM7zwcV0Ik+vGhkoK+jKwWt05Ewx5HeXxu7jLpBTZC\nheWB2oBDe+V6UFdvcF4GLaKXFdIFRxPsoGCml6PV+ZrPWQbM+O1MXIq14Bd9\netBC\r\n=NEHf\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.1_1536882441624_0.2326724374162339", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "96f932703c2f426e329e0b5e08e4de1f5e96c79a8de06f06edd169939a1de459"}, "4.3.2": {"name": "@types/react-router-dom", "version": "4.3.2", "license": "MIT", "_id": "@types/react-router-dom@4.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "52c17c3682597638f31c17c42620403dc5c2a3f5", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.2.tgz", "fileCount": 4, "integrity": "sha512-biesHodFxPgDxku2m08XwPeAfUYBcxAnrQG7pwFikuA3L2e3u2OKAb+Sb16bJuU3L5CTHd+Ivap+ke4mmGsHqQ==", "signatures": [{"sig": "MEQCIHXerJKP/QllUP1WmCfmzgY/s58c4gSF09VGKqV3wiKDAiBEiBP5378Ui6VlhXNt8PWbPxq6KhfMmekLuGlD/Lp5yA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcr9VuCRA9TVsSAnZWagAABfMQAIpPxOP7n3HqYvIaR4kt\nOndELzwhLM7rlJ+vfHEyNmoVaQ6B/KdftkZU2Co5xsVc+9nj2LEy7jzHH/oO\n+AYU/Ecnf8Mp5jSWW24AskyQYZ8KKGvVHva7+7Y74pUuUVnwudSOCAXs1Q54\n+SBMz4jCl0cAairP0lGiDpxMg30ONjgDtfZn7IQSXyvughKSLzGkExNSyejt\neCK84TCKcvWLGkOO/DnNogdGAXSs2F56xJd5cy4FHbWKpYAHTJKKWc4bOHni\nz/UelFUCKXLs98Eqkjq2ufToyq70LKSTD64yeU4EUDzGj1TxYw+BJ8jjVxnj\n9P/EbaEnXXlW0LUg5TKSMSldihBGSegTnyKnpnW7Qb7FkDQJ52Z3rDzusOs9\nMThqsnuBJswcy7TfQnOCyZVA8Bzi5M4mkx2IX6JdnnG7nKNMzJzjR2631Nqv\n/8fO+jHZGSnXLBxT/Q9BC5x/O84gGyaUGfoKyjJfKaI+qDSDgFOVrRuhHDEn\nGcEXM29pQxdT21AtWjD+DneobjRIU/4ihrOafMP3GvtuzbJ7iqE9OW8SvZ59\nMf0Izo+7TEiRcJbwiufWIprS4j9YikVtvrwT4lbJ7Hcupcc/jClmfUvzNREQ\nOZuIYJ/d0hQKHMR3aykDPJD1dXOSdEVRvUMmAA/ZCEhEWMoZ2tBef8J9UIDI\ngW3g\r\n=4S8h\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.2_1555027302590_0.7641410428654365", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85ab60dbe44c59194a61cea0998c568147b10f8f2fa61af49aa592f7a7ff0298"}, "4.3.3": {"name": "@types/react-router-dom", "version": "4.3.3", "license": "MIT", "_id": "@types/react-router-dom@4.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/tkrotoff", "name": "<PERSON><PERSON>", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "7837e3e9fefbc84a8f6c8a51dca004f4e83e94e3", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.3.tgz", "fileCount": 4, "integrity": "sha512-xj0DmFjgvAqRfh/kJPO7apD5G30yPQe+8slu/dugioQOkdKpyzc4Fgk4hoTelm6CSHz7pI2PPsW5+Y6GRBF2zw==", "signatures": [{"sig": "MEUCIQCkU+166O7s4OqlhofsqshOTZVWb974PLUReqLE992ucQIgK4yFJiC4LyM2zmQJbs7i9Ep7QNS/eZwPNc44Vl9KwOQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcz/d0CRA9TVsSAnZWagAAVskP/3wdDy56BFBDfE8zLPjB\nQDi3oXLWokUQLaikI1/l8ts1MESor5Scnj5I6gJlym9olXGqH/Tp5phJ9GjN\nYAACW3ppTgkI6TXX03dQ4PySPpAGHnUiq06sJLa4xHYHpBge9o2r6lnIro3B\n11WJWTvnmc0ePapJC92uTew9dPvEXSM1V5ESoe/f+yCU2ZmgHi3Jx2PnBgbS\nJePAAwfFrJd4M5LCnRqauBkidPV/i714F4ozXriun7KmWaXqRAsYO/lKpLAn\ncsGcZNYt2zoov3sI8sEdjm1UjyyRW9QnyZjRNF2npBbSLWv/hsuX8IwzMDfe\nMELFJg5UsReYb37+eL8aMHoMcPAZm417I0i/GfTgdORk2ivLVezN0y4oM24c\nN/sd5WPsMcfrNes47EeT6PVWDG1XLIrkfNIBLWBBvyV9QFAnRT7NvxM/193R\n+R1JfNqKKrSjRBAu8lgJXzHEv2N6tv9e4GRhLS3y4/g1nq23xJhbe4IV+zJY\nEj536A0+PE2zlJPHq2WczKwXc6SwhUdey7R7UM06T19+F00WKpgTaG01X/9f\nLo2RqaTx5/VT24rCH1J0A+7DS4BZR7LbtvYVkjOZTbmKBIlfS5/uPJPbQzy2\na4nHxhKzjFJN90NboczjWofXqtFqjWrxCWINuW8RGIGAcQD0EzghPFrVVvv/\nbWe4\r\n=Ewp3\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.3_1557133171715_0.0739565601152743", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e606974ac46204b80093b2615916ac3f81fd0fbafaeb810d7175fb57cb4234ed"}, "4.3.4": {"name": "@types/react-router-dom", "version": "4.3.4", "license": "MIT", "_id": "@types/react-router-dom@4.3.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "63a7a8558129d2f4ff76e4bdd099bf4b98e25a0d", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.4.tgz", "fileCount": 4, "integrity": "sha512-xrwaWHpnxKk/TTRe7pmoGy3E4SyF/ojFqNfFJacw7OLdfLXRvGfk4r/XePVaZNVfeJzL8fcnNilPN7xOdJ/vGw==", "signatures": [{"sig": "MEUCIAhWHBeqSS0mu07iRORC2S9gZgJV8fJhXoTresljdYK8AiEA4h9OXMnIjehm8WPiNifZSxGSfr8hD8NAP24l8jnaBtk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5183, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAqM+CRA9TVsSAnZWagAAjRgP/2urmj8a+8eREozPTC40\nwMjePhkHQG6Bs7M+09xTM9oq+zLrjeaFP68KUzKeq2wUEMLwg7ScOKdZKgZG\nTLwcAZjJ7KrIhdyqsMP4pPlm2vQmfsEBJB12rDn0NalhOgI8RHFOjpxGewKi\naQtFzriGKRxLOD/+3fWTqzSY/PayyhiI/PkFmnsQQaQ5DV5n7HVjMblu17P3\nYE9PhmEsqboEe5rJpZevw8tkXFAe2Fwv6jf1JmRubvems6GLhjGILua1N+CS\nukWAJNoMA/jPu9LXirYKeGtnVywkR/jzgLgxjPzKmTRY8qryeT0OZiANTg31\nAQ4kAIgd4UzuzjI1EcwD3jVpoCWhZqpXhTDIj84EoFbPF2KBn9T9hCQVWEg0\nsngPlJZNO9YpteRbsbHH90BeSF/myv1JYQHgHMiHRg9W/cSaJ03YdmwkhNNk\nuemYaoIRv9VFFjA57zOKEwXIwLhKSwqeNTVnLGhv9boy9Q624oL6nXamJw/X\nGmP9QhxJIHoMmaFmKIMsrc5uJgUATL7+BuxowX0/2ugl506c/uZt2EP7KZjU\nAQ9dLStNsAnSNLiTAcYWKn8M1ELct53Wt2ULXxkddgOgbNlj5CCTNXGQUbSl\nHAijw19juxd/wa1TkyVFdTGlANXQSq3sZzZPuhZ5OZOsOupUip5Xie/hddWw\nuS71\r\n=rJpw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.4_1560453949830_0.7432295414983605", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8bcd05e97e453dd5fb743b89668b34f203a83ed4dff8ab38b12d4965cb2ef43d"}, "4.3.5": {"name": "@types/react-router-dom", "version": "4.3.5", "license": "MIT", "_id": "@types/react-router-dom@4.3.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "72f229967690c890d00f96e6b85e9ee5780db31f", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-4.3.5.tgz", "fileCount": 4, "integrity": "sha512-eFajSUASYbPHg2BDM1G8Btx+YqGgvROPIg6sBhl3O4kbDdYXdFdfrgQFf/pcBuQVObjfT9AL/dd15jilR5DIEA==", "signatures": [{"sig": "MEYCIQCRKA54oxp+XMfWZyT2vSqFELa5CfiGTFLpVjcckNKfxQIhAM/KqE1rlIsKhA3XdgxI1iCTcRMaZJpYm8frcpWWvjxR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5562, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdXhqcCRA9TVsSAnZWagAAxNUP/RCPJ9psS7G1j6cVld6J\nka+Sr4oSCx0cpegqQkCtpCqHs8//fTCQ1UdjBOCzH6rB4e96NKn0MjaGY2Cy\nilc4xcjeoPNKdQcAyQdDHk2bGDC2S28zSiK+8e1HhAlN3flCGJWqmeY/wy+b\naiKPjgtAYI15qslBiBogUQMsqYW0t7Fq2cQJU0vqmo2LLsfzE0vgyE+p+xpK\nOGrW1zZSKPi3zDKzX6wWwboREzgjzE7SImC+yvnWwsf+R5+8IknRmXkrTz0t\nhZR4OAEbiq4mn7Bq/zvsWdyGNLk/jJhRrwVEVV4VsU7ohXEOdS0Z2zEXVKHq\nL1RFRpUAt5Cj0unW2DrO+gQFFPP8sa4z3eWaIGuolTqLP958KMSBkwM4NkgN\n5siPbLg1DrzH/0fuPsoBLwR96ij0vxHsG2IEODlOGBcKIQVSfsZ2BCsB/Kn3\nwSFn1NAXzdtbLDgqNhZQZvtl/BAJNFqvGSpUQPQ5RxKzwj1hNIt1kfuPFhzP\nKXbpMkRitJce5Wg7HhZq2xOR22+XuKCFIjpKSYe7XlteEBNQImFW302asPT/\n6QxhvyaITXBY158bUfsGAtHCbx4cvHWXObDVS+cfTXj2LPSM9PSH4L+K7SCL\n5DkmeNmyU6ZqdFAG5QgOy8pJm5hf0oicnnchNAub02ZwNp5oEGC8RgM27D2K\ne7t4\r\n=BiZc\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_4.3.5_1566448283898_0.5928711046606996", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "92ef7ed68051a719ada317bfe27b9e2b2038454faeed45558228df99215563ae"}, "5.1.0": {"name": "@types/react-router-dom", "version": "5.1.0", "license": "MIT", "_id": "@types/react-router-dom@5.1.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8baa84a7fa8c8e7797fb3650ca51f93038cb4caf", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.0.tgz", "fileCount": 4, "integrity": "sha512-YCh8r71pL5p8qDwQf59IU13hFy/41fDQG/GeOI3y+xmD4o0w3vEPxE8uBe+dvOgMoDl0W1WUZsWH0pxc1mcZyQ==", "signatures": [{"sig": "MEYCIQCOinFSYuEQi5zFBr6pyuq+ZAO9AZpeFwiebIONTw22FAIhAI4mZhBel1q49+FBG2zLKY9B20zPEktYlMc96lku5c5n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi1COCRA9TVsSAnZWagAAOn8P/jBqwRn+Vce+w7/ukAjP\nlHlGpKkrkphG3T5+f6+iEQC70nJEWOEQw6AyDVFK1YsXya4HoKoooCtPYVKc\ngqlmnFPWfTCwUrD2p7wxfgMRnlEfrs1BdqfXdozw3exOp8qcRFvqCr4P8MnC\ndxzflt9G27nKGm/nWNXsl/87xl9Mf/GX3xui7+xHCD9waLOvQyXCz+e5l78n\nxxEbEvSITi7gttPeZTNT420BdWjAHfsplof92O3jrXUmIg2cnQRsfYGXC/eE\nIiWoY8c8psONxHsKpa6eTEekVQqBAb/puA/bELimFdu9fGNLEdbcDPUQs/V7\nULcKYRtY0vM7wTK8M7KS4AeU+/pgR0v3AhzGAZM5Id9Zqqnpmf2xlBeeYffY\naYBd8CfUA2JK/lpOhh1DhcYfU2tSqjuPD+s5c2IUOgYdmKcIERdIRLZmEoVq\nNWVT2i45uPdOcC5usSmbyRIiXHl7ryxxQw1VAY2aQ6KKk0GadW2dQHXdwZX2\nNsmQeAHRX/TmqZW66bBmIulY4JsX8wOmVvaQTsb3Nhhn/+TQMZkyINhbKpoQ\nAzk27zLJGkxuW1X7Bkr3xaTrjb7qC7pvNeeji0xZy94FIKYpz/+FgkfgyMJQ\nRFpnaesWO4PeY3RliTYtsflV5yIyDZBSVYE3v6wy5MJ82/xVLdgwbh4GxjXx\nz499\r\n=x7EM\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.0_1569411214205_0.6815624498371466", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b784beeadd3f2a149dce53dde4e556f9fc9939cfdd73a8672a4964b5fc0da18b"}, "5.1.1": {"name": "@types/react-router-dom", "version": "5.1.1", "license": "MIT", "_id": "@types/react-router-dom@5.1.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "77a2dae8ba6ff1df51d27517f7646127a51a1a3e", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.1.tgz", "fileCount": 4, "integrity": "sha512-yXqWGaehta/cdmjvEQfCbHFX6l1c7QHuE5n2OfhcJ33ufbt55xhAKqQ0BmT24YM3s7OKwrrUUgY3FaSzO7be3Q==", "signatures": [{"sig": "MEQCICkfJdjFCweRdgzwDruZ9ZR6lYyiDfMyFdsCZCc6mQ4oAiAcQaH7+gVTfPPNb39tn01NmzpR0jorc9laJoC04CO4+Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6053, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJduR13CRA9TVsSAnZWagAAHlEP/iGWmQNgy3eSMszvWaUr\nRk/E8tHd2n86pyTbUzMAdEwb/KZVpJBSFRrTC1GqQf2MCNvdG4yR0UxPYLdC\n7pTGJ95O/+rADhQO7lYuvu+OH4dnZo8DeYOJkeDyKTNX1QXDI1LQ+Xbszm/U\niD+BqI9lZp3AgWczULAX7qHRr61IGyUfxHHGQTjJ4gLPqc121LA3/qgpcOgZ\nX1m2UBQx8Q3MAIo23AVj4TNvQoFRZSXFomKhQsVpHNcmB3rVtxpDynoyoSIr\nYvZEDxnQEsXN9nu2VgKBvEupd7f9L9+Yyx16klz9c8OybY8+Af9NGALBaF8g\nGCHAP151qNb+/3SO/2vj20I8rv6xVjfno6YkhetJitxuU0S+cLRCWqFC3EcL\nkacXqsM4ove39vvb3947KphZocNXQQpcylewtXUAL+fIqBBc1T1vWNxAOT6n\nllHPgPg79Rgme4mMZ5mMVI0ULzd7lW6tEGQ1Lmon3nPR2QBBboi00nd+0CPg\nBJfUPB6nnHwJdb8oCGFVTgZqXbR0oYzUz1n1cjNNSpH9Qwwjr/rHKzi/5LmI\nELC79iVMIUL1ayls52+szg+YvtivKc9X6A9ba1czyvg699PtGGqeyxz09nUn\ntXOlr4/MlMgYr8kbaGH+TqdhatfC01KtaslLSQL1PdtgfJPyV/vcZtxUsZVc\njV68\r\n=5+d6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.1_1572412790566_0.927106818354785", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e89b23e78325c6ca30e19e737bb68e31d286b4f8c9be945123dcf9e8ee2c1a94"}, "5.1.2": {"name": "@types/react-router-dom", "version": "5.1.2", "license": "MIT", "_id": "@types/react-router-dom@5.1.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "853f229f1f297513c0be84f7c914a08b778cfdf5", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.2.tgz", "fileCount": 4, "integrity": "sha512-kRx8hoBflE4Dp7uus+j/0uMHR5uGTAvQtc4A3vOTWKS+epe0leCuxEx7HNT7XGUd1lH53/moWM51MV2YUyhzAg==", "signatures": [{"sig": "MEYCIQCcquRtmVYXpyxOvo/TBrReLj+Fo0KIJ+lQO6TD5LK9twIhALceYWQzxGcEcEc1o2dO/b6k5zpzlb5Kg8AoPzFpBbS2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5978, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdvFp2CRA9TVsSAnZWagAAvOkP/1BiAXQQ273iKcrvf6fm\nJ2Hvyq6qHSzK4khHq2elrwRv+d6MoXgajO+EfnNEameqa8U14dA3gmzN2i6S\nPCVCcm7qalsvIixVocQORRHs8xlhgo7KqND9G9v0E86VYMmI5Oek1hmac4cM\nfW9XVFPTs9yA7E8UOo2awMFoKL2Sx1v67HdVwYJDSSoypZAmzWM+fvddc1gp\nQfCVXOI9iHN06iQkp/gVcm7d7UeRX68duy45uDqoxKHZFUglf4h7LhZAwlL/\neB34axR/GfOHdgLcqZQKzWuxncGkkifBpdnJ5vL9/k19bpM3dJiVpr68Bpsk\nlE3htFa+a1K1NP1V+iifnASvkodx1CXizK6uVZblRUsPpwrGmAF9zxyiGOY/\njUzDH7gzEJRDKileGrvsOp/z7teKcsq0ZIYsp5pzvIRGrC1fsFWN5y3EVYtC\nuuLbxrV6JXiqlI5eyPOschHK9VGU2WfHpxw2FMbGSGWGyg93RkuCCt8MAOML\n9w7z+lLQ/g1OFZUczTPvvJLFXMGf8LX7mPyj1U8zymyWVTVJm0W9RnCWqqm8\nHx8YuTbDVXbKFnaWH9iLueNuM2ekKxiOasBJeM/hdPhOs1Dh4L/ai2zinxHy\n+YG/ssZWz4bL6fYqwnQBmOBflKJz9+fWalHNzrOQWaubjFK4BSGYY899MjYB\nmDcU\r\n=LMWu\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.2_1572625013560_0.9314048167675029", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "903494b5cf2f23e5f9244ed166202fe4aa3ef419ce873ca7f269b5a1c6007bb5"}, "5.1.3": {"name": "@types/react-router-dom", "version": "5.1.3", "license": "MIT", "_id": "@types/react-router-dom@5.1.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "b5d28e7850bd274d944c0fbbe5d57e6b30d71196", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.3.tgz", "fileCount": 4, "integrity": "sha512-pCq7AkOvjE65jkGS5fQwQhvUp4+4PVD9g39gXLZViP2UqFiFzsEpB3PKf0O6mdbKsewSK8N14/eegisa/0CwnA==", "signatures": [{"sig": "MEUCIEa3PSOvAoXNYkZOvGrofaProKK6PXGAbivv5KyNCi/EAiEAka/TpxKMsJEQ/mLSOL053/dzJZpXRiIDC1nXt0xNQT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd3CtPCRA9TVsSAnZWagAAAWwP/3otLU8UL8C73qdRyHyi\nd2cPDZIQCt3sWkYkLG3JASLReRTZvA2Ov1PaXUMay3gvCjgI1m6hsnY7BBuA\nGUxmlwod6aSYf9JYx0gGUrd3KE01EA3z1OmviTxPT6waxH0ohFTvDLRfDhDq\nVBx6dnsPHF/tnsBx0QIsW+XkutqxFfY5Vo4Kk6EA7kLv7qFUMWLevKzIfCoT\n/fwv2Vid44UiHlIW3KPB6Aqo8i/uIlM0XNE9LwbctpEA4tEs4++i5tK84xK+\ne+1NxTV/BAAcfJTx/Msxo5Hk4IMxr/4JZBTuN7WPzV9uoZcYYktdgHeeqwfT\nYnesbn/FqR5vH+8JCdqBIAjIpDtCn9nt3RmlJak/9YhdoC4d9QlJ9Fud8Dvz\ncYFw1QVwL0OkWNamOBoDF5JgifuAQBtttpRMhqbzFB6YwbTHueinFijNesC/\nB0QI71w4J/OzUtrRXgnvRrLr3LnLIlIrIP34WkUDwxxVzNoddTf5yVmo7BJj\nF69EzIqbakkAN2LphzrlSH5Tt2CPM9/Y8+Af3givvPEiaMDpvckp7KRbR5/h\nfnybIR+dz8hKn9ht4smHdYlwve8/rzYfPs91iwKXKgzrpMpe3pT1bJQPNCDZ\nuI/8JbcLl+5c+AJzVacxBeOOb4Q2Gc+W8FGtfnYdhSFTbr+qZaO97Ep9sheR\nBteh\r\n=E2zq\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.3_1574710095171_0.9954133859451195", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a8cd983eadb2aa881411e63315a53905cf3fcdec87c15cbe0c8711fc4a0f1e89"}, "5.1.4": {"name": "@types/react-router-dom", "version": "5.1.4", "license": "MIT", "_id": "@types/react-router-dom@5.1.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "8d3e0306df74af301cc896309e7d4758f1a4bf71", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.4.tgz", "fileCount": 4, "integrity": "sha512-LO0z5qqSfWdYtCNsRm8/OMnnkv52hwADJKrAfpKIyfHclORllcgAMGypEA7ajHm38+jOonKLx8nPygNAAZGxtg==", "signatures": [{"sig": "MEQCIEAZxvr4axPDFc6mKRvhMc77LizU5M7K3OfOXFnejD44AiB3QCVwtYOzNMbOxWxLfVkpdLxwTcinecvxKM3V+CEgWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJekS18CRA9TVsSAnZWagAAxPgP/3oyDpBakR2uKXxTFqA1\nOQJbrDUX7Yhoqr62+1S328T3dF8bp8jbJd/h9tn8OZ+PFzEyGjOEiOO1EhFM\n66ATeg5hPOkoJwrPo64IbNwf8xu07ImuXKcnCHOlOayqm7GBaMp8YT3MLq1T\nPlWiGj7L61TIBbichoJX252jjeTI9ot4fITIFQC/F9RK5YNE606NcCUxrt9q\nVU/qdN7EENSSisgn5HWQf80gEIk2seFACbVsjZuJi1TPoo+4NBPF1iUF2bwn\neklEGIoeX2RvBlFYX5Xyx+15RTIaKYCjb4jRVZV8ENl4raO29k4/BJP6Exnh\n997O0eKuRIwEB904GbRHGQ2vwooZN+HoN/vIIsa6SpkCvDH2FunVXfa0Jy9T\nQxJdUSd69NmPc1ETiNFKX2coKBzxwXDMau0MNjGM6LWPWzj6Xm7CbaG4vjhi\nkGq71eQKhSxyWen0hu6VypyFRXzjhqxmCKl/CC/8TJYA2nxI4p0CrI3UhJaP\nayUyjC2Gv6Ml44uAe5XClQzuq/CVlyUy1Wg++2P7kSPw1VL4C0fb7Gshjax1\nsDCvV0VGy39CE5p82daX1NyfHPXV/yZ/OXDhNDk6wF3N72L04ihlyDLqCwE8\n7CsaJ2ZoJWkqu83xLXsVMoIxcfkcrMPFyfx+jkrr5HNtgmqV308eEoy/wHW2\n2UI9\r\n=UBFF\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.4_1586572668298_0.6770016231457141", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d838118da18811914d2e3472b1ece5f9566f021d2de3b81ff5813ca4ac1eb410"}, "5.1.5": {"name": "@types/react-router-dom", "version": "5.1.5", "license": "MIT", "_id": "@types/react-router-dom@5.1.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "7c334a2ea785dbad2b2dcdd83d2cf3d9973da090", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.5.tgz", "fileCount": 4, "integrity": "sha512-ArBM4B1g3BWLGbaGvwBGO75GNFbLDUthrDojV2vHLih/Tq8M+tgvY1DSwkuNrPSwdp/GUL93WSEpTZs8nVyJLw==", "signatures": [{"sig": "MEUCIQDEWjjrajoLqXiDk0yEfnsH41RRzOLY+9+jTgBmbTsD5wIgKzHtgyfb9kdgxuCIawMCkcsi26jtCokzGEJ0tvPuDe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepcLDCRA9TVsSAnZWagAAzkIP/RNRJKbr5rePRMekh3hT\nuU0kbQN3NC3aowRXsLpbRzZ+V4YvxiXT6nxa0b2uZCTIg3yAEJ9/7mPbuzYs\nCoKslpv/pUVpecI6EiOsi/1ZPDQ/8nc0nzoZtJUYqdvzr284aXUKSS61u7o2\nIWSCdZpIvAr3pfx2zLmCTJfM8iDLBvErkcc1TtWZVXJicvU1QnzhPttATKD4\nvqeY+zHEGBlFmQKh4v6XbYy4oaJnT408GPAIfeeHhVYnvXs+ASx7G9mFO6T7\nIfWT6UYQah/k45WUrqg6XRQYE5RtCVDifsbf61YIXn2VZUKKyf9oqjFkprhP\nOqrcn40rBu8xWhxWamtmc17BaMvzgZrNT7+2CX6iUQodwUF4u2irFnA/Y6pS\ns6CnN112q4gjPRXz2/spCN+gNdlMA4g21fCfT8FwO/blIr1x9woXz5Lgge8H\n/f+V3ZjfQgovjRqXPhVesOcuJbP2UUier/tQccSejdtDxjaeXN7kOA8La7lr\n+wBiNUK6M3u6aZ4yUuRjSd7kk6LIFfA1T0doOzDXQnV87NkBIvM0R8tRUh/A\nrFA6zljsUwzHJHrJYyUtN/EfpA7oq+R7DeUIzyskpCMY+5oBUZ6qCyJGn28K\nW12RCmV3SgH/ru0BGMWNLGZf/BsaVBkb/s3dLnyHYeYzsW2UuZJmEiBspRQH\neGbr\r\n=8Z6s\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.5_1587921603156_0.9449623529727709", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "db19b62f12be68f935a34ddaf0ef22d129e734ba5e711b9869ed2a395c65a52f"}, "5.1.6": {"name": "@types/react-router-dom", "version": "5.1.6", "license": "MIT", "_id": "@types/react-router-dom@5.1.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "07b14e7ab1893a837c8565634960dc398564b1fb", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.6.tgz", "fileCount": 4, "integrity": "sha512-gjrxYqxz37zWEdMVvQtWPFMFj1dRDb4TGOcgyOfSXTrEXdF92L00WE3C471O3TV/RF1oskcStkXsOU0Ete4s/g==", "signatures": [{"sig": "MEUCIQDLgN/NGbajmdduXznYTeIvISt5V815D5LcFdeyX3XpfgIgEjkWpVgRzYld1jJGdquXT3Nsd10IS47D+jYPIE/nL9o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJffIyRCRA9TVsSAnZWagAAP3IP/1OyNt+kkoM4nMTe25yz\nygru1czFtf/zvo8O7w5NwgAOhhWe4D04pqV6PLXx9XSZt6RpctfDFOgwvgk7\nYx+8QsoKSUDELO7GFQ4GT/Nx1tssz4IBBb2p7vQxxLlyCSYs/G66keod9ZtU\n+ztMt0/NW45rGFMdQZw3atnAnPUUQk7SOFs3C5kJeRGEPZf5uyZH690597w/\nhFZQ64uf5nzgoUT7E1yUeVI9Z40gD2nEsWolwJzGlhVfP7jIdjMM6XyWWXZ/\nh8pF9DYH1MOhXAvXKNF1KIRWfH97lg17xDHQlMF9fLZAwh+sMMaSljCK3D1h\ne6oczDk94ehyffKd2zGYfTecfYimD3mG6uHzEjze2qkV/9f6S3q/YGpWMx5i\nVC7JgRqrveAn1TFvpmyu9fADmP5N9mOIif0O0ZtMZdg/5jxa2Q/O9rvXndlI\nkxu/1MUS2wX5//CWqX+p5/PK9/2P44t1KmS0Nv5bpykZGtkNrKojqSd8psqZ\n7yhCoZ4vwWbUMvozltuVYP6TD8QGTaVUbvKCk5pl+4nY5FOgHpaM6UHTSVdv\ncqWZ4X8Fser9j54jAt9SQE7jy5BQU0bJiT2htsyos9leg62y+NjGPJW3dj5M\nlTzGj3fW6xDaZnLyA0zzn1uN20jk6epp2QjhPMo6lmAr6vum2E9gu5iKLwZz\nlUL1\r\n=ysvT\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.6_1601997968463_0.2572452682131672", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2791a6cf56eeaaf5ab5683fa38f7948190889da47cc8af3ddc0520c4a0611c35"}, "5.1.7": {"name": "@types/react-router-dom", "version": "5.1.7", "license": "MIT", "_id": "@types/react-router-dom@5.1.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "dist": {"shasum": "a126d9ea76079ffbbdb0d9225073eb5797ab7271", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.7.tgz", "fileCount": 4, "integrity": "sha512-D5mHD6TbdV/DNHYsnwBTv+y73ei+mMjrkGrla86HthE4/PVvL1J94Bu3qABU+COXzpL23T1EZapVVpwHuBXiUg==", "signatures": [{"sig": "MEYCIQC60AO1tJIbmj/lMr8jj9V/Bcrx0q8jPX4YXhk4jCuuOgIhANMIgt/AVLr/NNyT+VMe/MdCg59PFYvAgeHM5NOByLQk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7KquCRA9TVsSAnZWagAArL0P+waZfjkj31uGpNvvWuMG\nJXb/XpyvO0ZI5vLXaiINEchQlQ7jVMRIrPbfCJF6FvlhwEvDIc5ZWPQlrRd4\nRh79OnqVg8PhfmiXLkl8Yxei9qE9iTsGTMgr2TdgpOvJjl6EJxZN3g2n0zhH\nuhzcCmp1SceADnHe6mZAMQrnUHZ+/9OEEGiXAxiOsyLt3ilQvbHZI1CK8bcz\n9NiaB4B1tcf8lIP9L7VRBFD43w9ByRp/JVeUXOvU+5dpD9ORwlL9FDchAMMO\n/QsdgXwx9IVXvczALtDQ5x/eFZ0bXSaQVxYRajX8s+x/OUYu8Cdl4lp+Sikk\ndyWh9ArB2BcG4+Aq2n0H500PWrj0kOiCUn/iB2wUw50YLbv7NX1Qd4vENNYX\nWbLyZQ3brC0swE1wXnKPX2zOToviZC/RV/Ot5ujaI7nrUxvb4s52hzofPl2x\nF1oetxZhZfmsYj/h0MZNkCxU3KM3FjpVHuhYzQkuma6lEcgeEg0rxgLP7eZh\n2roH4XRVVz3GMqdi4MAC3NLnCar48OiLb6loYoDImTCc92LcVr6Mh4wJPBi8\nds/u+36DfjJTNHQWICwocN18NeJDqzcYJSbq35EMZYXO+oGLVVReglFbibxh\n2KdCCV93zRikOAugdjhRwgERaYmDVAa3Vgd7WcVjTUca3aaQqS2Re+X7AWn/\nb6MX\r\n=Oozk\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.7_1609345710277_0.5914752511349344", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e54e31ee8134fdfa63840e27dd3661d10834f166d942c6c9862533b51fc42542"}, "5.1.8": {"name": "@types/react-router-dom", "version": "5.1.8", "license": "MIT", "_id": "@types/react-router-dom@5.1.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "bf3e1c8149b3d62eaa206d58599de82df0241192", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.8.tgz", "fileCount": 4, "integrity": "sha512-03xHyncBzG0PmDmf8pf3rehtjY0NpUj7TIN46FrT5n1ZWHPZvXz32gUyNboJ+xsL8cpg8bQVLcllptcQHvocrw==", "signatures": [{"sig": "MEUCIFVfDrM4kOHVjts98PJpf0AVibaCZNVjjqHKE66Kfo4rAiEAgtd57M7UN1J6iIbnaTT+JyWOdw9k5owkH0KuVBE1XN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7041, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5elHCRA9TVsSAnZWagAAAlsQAKExtd1nFzr1dZR+H/0Q\nSwgxTAn6b5UN78JgDjPHUQg14b1u0R+5LZjHrvoqNnr1wqQDyoIshnyyUuDu\n8cZphh26CeFXv49gt8J7mGx8XGCpogvyjt4o7YDnedusXuIPeqZPvM5X/k3G\nUt0QqROeF+kfOHO7vug35K9D9RX4VTUc+jPToV4VRuHgQOsG848y9eVpZefp\nFOpfbvsn7Svzun0m6V0uc4iVB829ORdiNsCIMPuG6heogwQHCJv8Xpuiehng\njiYwQh76VRcU2mEeS6T7G8OAQihRIifo6reZIJpatt5atfz2+IVGy1WwjHhP\nX86psYJ/TvGPnx3rzupDG88B9nNEblVQls4yzdw104NEmFxxuthEsMmvT/9f\nhh8TKXwVGFawjixBO/KQl1hO/03KNmGTxHJmg2JXcdkciKmCHtTi6w+XcErf\n/QXkvvT4MM9VpNXiF2YeDJgCWY1qFyC9PvZdAEVJmBtzXKuCX5LbwUt7MptH\n6Z6frocXpH/DIsC2hpAIeGr/E3nNwqUIAkX0LiTxVl3nhpsL+smrdWNdl1Ir\nJQbv5U1TGq5N9v5O85dONfrkPcXECF5ErVz2DkoDfA/325CYpLWvQDbpk0xa\n50SwECe1tySSLYBdrqemZTrEzw+CPNqxFnK2dC26u9D4Oj5A098XPVxGxKK5\nGTiN\r\n=nZS/\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.8_1625680198635_0.02132089011060878", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a9c4ee6f58ce26a4d68b5b190543904889265b7ea7713a642bf99d73a2c07e9a"}, "5.1.9": {"name": "@types/react-router-dom", "version": "5.1.9", "license": "MIT", "_id": "@types/react-router-dom@5.1.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "e8a8f687351ecc8c68bb4161d7e4b9df4994416e", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.1.9.tgz", "fileCount": 4, "integrity": "sha512-Go0vxZSigXTyXx8xPkGiBrrc3YbBs82KE14WENMLS6TSUKcRFSmYVbL19zFOnNFqJhqrPqEs2h5eUpJhSRrwZw==", "signatures": [{"sig": "MEUCIQD3H5hMjFEiO68Zy8Ju47CVKElz11PIE/U9xJmSr37BNgIgdYQ5p2RA4sOqBSusVF51naqmKrbcLiX2t/M/fieykQU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7222, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLJ7CRA9TVsSAnZWagAA16UP+wbN7trqFSzCDJv56ce0\n2BW2+Ut4sCw7PsnNrfBhwl3urBZLhbVWhW099Y0MwS+3qmlr84W9ru+kNB3c\nkSGIwWzF87DPko6r1rvgCsBOLTKQQxR6EP6kLKQ811GP5udBe1yqEuoa5f44\n4UpuOBVIPlmN4kvSbSc1TVCIi5WQ1QmIbmmXpV3+YFyb4a+H9IDhONVONTTs\n6EU0nfsvnwF6qUDoj1RBYXGF7V1+qA/UhPkigQoL/l8OLeYe7dLHuuL5EkuB\nmSXi3w8tXxTf8gAHogF2rLR76I5fDmk4Y7pqNc/EjiyNc1wZWho0lO8EFyxx\nbG7tE1cK6V2e80i/CuwhoSCUuEgYnJyN1k+VdU1qkkyZ3ezjEHKV5y9BVO2i\nokctE8i0MjpApKW6lHkrasrR5oxCPHjvjchvFdbKT/tT5er534Br0cONbYp7\nXb0wW5mPojzcODWtrEd6vBalq8ByNY0lmRbkPMNx2qJsQETFvXISGPnqzguZ\n48YPDx51Z+1ki/bMoOACoeR7SLVKzRMAb1Rwoh1p/+jXTZWy/cpHBt2ZmALQ\nAVmhgBl8jLxOE+grq+vadDff393PMamyVOCPdizuGPzU85BmgRjbnjMZz6eY\nC15jZpkJLlMDVkW0/Oh0kHD4cfvvEKxjB6sGbFfpLPq7q7/T7UU6oTWp6j0w\nWbd5\r\n=7sYh\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for React Router", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.1.9_1631629947748_0.38036289462291073", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5e0901cfbc4350ef57cf07c56c5c4ac2290dcbb8f803f384ea37ca3ee01a8e0b"}, "5.3.0": {"name": "@types/react-router-dom", "version": "5.3.0", "license": "MIT", "_id": "@types/react-router-dom@5.3.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "8c4e0aa0ccaf638ba965829ad29a10ac3cbe2212", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.3.0.tgz", "fileCount": 4, "integrity": "sha512-svUzpEpKDwK8nmfV2vpZNSsiijFNKY8+gUqGqvGGOVrXvX58k1JIJubZa5igkwacbq/0umphO5SsQn/BQsnKpw==", "signatures": [{"sig": "MEUCIQCUMJxiQXCoHOTz32wunYcujz4kmLSpZ+aY7ciRyN/e1AIgQ2xyZPEycxGu5aSpcc/IbIQ9xHTlgEYHFOSpRpuKd8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7270}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for react-router-dom", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.3.0_1632249374468_0.836996474486023", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "610e4ddcb9983d1756d1f7e0a4c49073e3c8faf8f93d66e718e4fca0c386401b"}, "5.3.1": {"name": "@types/react-router-dom", "version": "5.3.1", "license": "MIT", "_id": "@types/react-router-dom@5.3.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "76700ccce6529413ec723024b71f01fc77a4a980", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.3.1.tgz", "fileCount": 4, "integrity": "sha512-UvyRy73318QI83haXlaMwmklHHzV9hjl3u71MmM6wYNu0hOVk9NLTa0vGukf8zXUqnwz4O06ig876YSPpeK28A==", "signatures": [{"sig": "MEQCIB+rhinksZdyQIJC4f9kMSynJToh19sdLLfaQOwF/7WjAiBYGnTYNopSHrg6OOZE6p7iRx08hKnX5HRzD6aNpBFFWw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7334}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for react-router-dom", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.3.1_1633552352240_0.6591163365673585", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "159033756d1b914396a736bccf53a612e0146c6a68eaa26f30e37118d5e49441"}, "5.3.2": {"name": "@types/react-router-dom", "version": "5.3.2", "license": "MIT", "_id": "@types/react-router-dom@5.3.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}, {"url": "https://github.com/1pete", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "1pete"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "ebd8e145cf056db5c66eb1dac63c72f52e8542ee", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.3.2.tgz", "fileCount": 4, "integrity": "sha512-ELEYRUie2czuJzaZ5+ziIp9Hhw+juEw8b7C11YNA4QdLCVbQ3qLi2l4aq8XnlqM7V31LZX8dxUuFUCrzHm6sqQ==", "signatures": [{"sig": "MEUCIFlAoDjWiOZUQn7l+HhNYt3oFDm+/B8Md2P0D8GnFRycAiEA6T90IkIzvPLz4YbmOHQYHhZuM20cvQs6hwxqzkThg44=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7632}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for react-router-dom", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "*", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.7", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.3.2_1635204921103_0.9157515630848518", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "17f47f1dd8c259d2b3625f57cd50c94a9e9213d44883fcc1e66a210ad93e1cd4"}, "5.3.3": {"name": "@types/react-router-dom", "version": "5.3.3", "license": "MIT", "_id": "@types/react-router-dom@5.3.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}, {"url": "https://github.com/1pete", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "1pete"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "dist": {"shasum": "e9d6b4a66fcdbd651a5f106c2656a30088cc1e83", "tarball": "https://registry.npmjs.org/@types/react-router-dom/-/react-router-dom-5.3.3.tgz", "fileCount": 4, "integrity": "sha512-kpqnYK4wcdm5UaWI3fLcELopqLrHgLqNsdpHauzlQktfkHL3npOSwtj1Uz9oKBAzs7lFtVkV8j83voAz2D8fhw==", "signatures": [{"sig": "MEYCIQCyVCN9VgB2GALwXaSep5O3a43yjQqynON8dxVLGPOMYQIhAJF0LI2p8KlTLcg9nDTZW817SFadNI7YBFkpQELNW/V6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zlfCRA9TVsSAnZWagAAPO8P/iRsAyxn3sArosxuj2SQ\nC+FPOAi/X1fA0nZ1dgSsXvIKpVJ08XPGNhFm/HDsTGKlkJYIVnneLCtkEOFK\n1NTfB38mKN3lm2QDfOQZr61RnD2+LtOttd7gY4NPylEXZmbfZeZp+U3nr8/J\nMyiZZ6FX+dheZ8QnACf0lcLLmz3Sdz/S7Ov97M3bjDqwQCuLzmYJ+IeEqUm7\nGArphBJmCUPG8YnRiEMYXL/g7Ipn0SQ9W/VAbK6W66hSiuptb5oDwc9YG6Fw\nSZWUyLXN68eJPqvqyZxGJu6JlHpioMBvYKy6/vm66ozOsrzRib0Cqed2BU1n\nW0xGTHbqnrJS2gibxvj0P9ZS43dxWd21DEoTPSv+YrC87Bc8P1y685E2dgVo\np/4Y+c53F+VAdpyiEebOLqS/U8nbiUBSjhJdsTvUAjQ/TSIKWr4j1P2faAuV\nBXFnptg35pNZV9U9tElA7zo/1PE4sjF+oS6P5E2T0oJHHdlX50NC4e4UZ/BR\n+12gw660CLV4nLT8GoBWZlWbGdcJ/TpCp7vpsLaAAxFfpig1TmPsUlYVANeD\nXL2Q1GESeehm0mYe5O2NAA6ZtjTUUcJSaFYlJO0BSf+D4jkK9tBiFVzQp/9u\nJjt7Q1bwh/Lh/+PcIbuw3yuPq2mRp6nRtkC35jJvgVYVE+XzoTquw7DVSZKV\npC4F\r\n=SUUE\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for react-router-dom", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-dom_5.3.3_1642543455015_0.8660940030742972", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "e0755682335fcda35af55012c81f34c3082bd681570954c4a515a7ada37f06af"}}, "time": {"created": "2017-03-22T00:28:09.725Z", "modified": "2024-07-19T07:41:39.807Z", "4.0.0": "2017-03-22T00:28:09.725Z", "4.0.1": "2017-03-23T23:44:05.145Z", "4.0.2": "2017-03-28T22:11:45.818Z", "4.0.3": "2017-03-30T21:31:24.185Z", "4.0.4": "2017-04-18T15:13:17.454Z", "4.0.5": "2017-06-23T17:37:35.268Z", "4.0.6": "2017-07-07T20:53:48.108Z", "4.0.7": "2017-07-11T14:02:10.873Z", "4.0.8": "2017-09-26T00:13:00.817Z", "4.2.0": "2017-10-28T00:37:03.019Z", "4.2.1": "2017-11-09T09:59:25.727Z", "4.2.2": "2017-11-21T19:02:38.176Z", "4.2.3": "2017-11-27T17:48:57.853Z", "4.2.4": "2018-02-12T21:07:41.086Z", "4.2.5": "2018-03-15T23:20:19.881Z", "4.2.6": "2018-03-27T18:55:09.384Z", "4.2.7": "2018-05-31T23:54:01.403Z", "4.3.0": "2018-07-21T02:10:20.824Z", "4.3.1": "2018-09-13T23:47:21.791Z", "4.3.2": "2019-04-12T00:01:42.703Z", "4.3.3": "2019-05-06T08:59:31.816Z", "4.3.4": "2019-06-13T19:25:49.971Z", "4.3.5": "2019-08-22T04:31:24.021Z", "5.1.0": "2019-09-25T11:33:34.376Z", "5.1.1": "2019-10-30T05:19:50.698Z", "5.1.2": "2019-11-01T16:16:53.690Z", "5.1.3": "2019-11-25T19:28:15.319Z", "5.1.4": "2020-04-11T02:37:48.435Z", "5.1.5": "2020-04-26T17:20:03.289Z", "5.1.6": "2020-10-06T15:26:08.570Z", "5.1.7": "2020-12-30T16:28:30.402Z", "5.1.8": "2021-07-07T17:49:58.771Z", "5.1.9": "2021-09-14T14:32:27.896Z", "5.3.0": "2021-09-21T18:36:14.667Z", "5.3.1": "2021-10-06T20:32:32.407Z", "5.3.2": "2021-10-25T23:35:21.255Z", "5.3.3": "2022-01-18T22:04:15.185Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-dom"}, "description": "TypeScript definitions for react-router-dom", "contributors": [{"url": "https://github.com/huy-nguyen", "name": "<PERSON><PERSON>", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/p-jackson", "name": "<PERSON>", "githubUsername": "p-jackson"}, {"url": "https://github.com/johnnyreilly", "name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}, {"url": "https://github.com/danielnixon", "name": "<PERSON>", "githubUsername": "danie<PERSON><PERSON><PERSON>"}, {"url": "https://github.com/ynotdraw", "name": "<PERSON>", "githubUsername": "ynotdraw"}, {"url": "https://github.com/1pete", "name": "<PERSON><PERSON><PERSON>", "githubUsername": "1pete"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "# Installation\r\n> `npm install --save @types/react-router-dom`\r\n\r\n# Summary\r\nThis package contains type definitions for react-router-dom (https://github.com/ReactTraining/react-router).\r\n\r\n# Details\r\nFiles were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-dom.\r\n\r\n### Additional Details\r\n * Last updated: <PERSON><PERSON>, 18 Jan 2022 22:01:52 GMT\r\n * Dependencies: [@types/react-router](https://npmjs.com/package/@types/react-router), [@types/react](https://npmjs.com/package/@types/react), [@types/history](https://npmjs.com/package/@types/history)\r\n * Global values: none\r\n\r\n# Credits\r\nThese definitions were written by [<PERSON><PERSON>](https://github.com/huy-nguyen), [<PERSON>](https://github.com/p-jackson), [<PERSON>](https://github.com/johnny<PERSON>illy), [<PERSON>](https://github.com/eps1lon), [<PERSON>](https://github.com/danielnixon), [<PERSON>](https://github.com/ynotdraw), and [<PERSON><PERSON><PERSON>](https://github.com/1pete).\r\n", "readmeFilename": "", "users": {"nisimjoseph": true}}