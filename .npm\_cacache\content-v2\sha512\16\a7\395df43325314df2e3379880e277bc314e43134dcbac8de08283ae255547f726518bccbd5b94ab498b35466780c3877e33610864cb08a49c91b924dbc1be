{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/auth-construct", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.0.0-test-20240603184635", "0.0.0-test-20240603204424", "0.0.0-test-20240726185600", "0.0.0-test-20240730222729", "0.0.0-test-20240731233924", "0.0.0-test-20240807183453", "0.0.0-test-20240813193219", "0.0.0-test-20240814184052", "0.0.0-test-20240814224750", "0.0.0-test-20240815164159", "0.0.0-test-20240815174752", "0.0.0-test-20240815221003", "0.0.0-test-20240816172322", "0.0.0-test-20240823000521", "0.0.0-test-20240828172042", "0.0.0-test-20240916205421", "0.0.0-test-20241007235332", "0.0.0-test-20241021185231", "0.0.0-test-20241023202556", "0.0.0-test-20241023204349", "0.0.0-test-20241023205154", "0.0.0-test-20241023235026", "0.0.0-test-20241030224754", "0.0.0-test-20241115171053", "0.0.0-test-20241119003939", "0.0.0-test-20241204204357", "0.0.0-test-20241206232144", "0.0.0-test-20250121165746", "0.0.0-test-20250121170912", "0.0.0-test-20250214001309", "0.0.0-test-20250218000414", "0.0.0-test-20250218005144", "0.0.0-test-20250307211308", "0.0.0-test-20250319201152", "0.0.0-test-20250320182439", "0.0.0-test-20250327200620", "0.0.0-test-20250404000352", "0.0.0-test-20250404003334", "0.0.0-test-20250407234958", "0.0.0-test-20250416182614", "0.7.0", "0.7.1", "1.0.0", "1.1.0", "1.1.1", "1.1.2", "1.1.3", "1.1.4", "1.1.5", "1.1.6", "1.2.0", "1.2.1", "1.2.2", "1.3.0", "1.3.1", "1.3.2", "1.4.0", "1.5.0", "1.5.1", "1.6.0", "1.6.1", "1.7.0", "1.8.0", "1.8.1"], "vulnerableVersions": [], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "<0.0.0-0", "id": "bTtpgwdkMJEcvgDhtHBiNtR+ZyT6fQUnl28qgfJ2A+ULsZn2ZhfhwApB335pr7lmnMEfH711hth3M4TxlkPyEg=="}