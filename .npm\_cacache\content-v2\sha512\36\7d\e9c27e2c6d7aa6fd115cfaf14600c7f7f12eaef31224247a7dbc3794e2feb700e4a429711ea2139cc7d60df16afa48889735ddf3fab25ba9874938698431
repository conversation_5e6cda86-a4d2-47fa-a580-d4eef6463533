{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-auth-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.1.1-amplify-export2.0", "0.1.1-beta.0", "0.1.1-ext10.0", "0.1.1-ext11.0", "0.1.1-ext7.0", "0.1.1-ext8.0", "0.1.1-ext9.0", "0.1.1-graphql-vnext-dev-preview.4", "0.2.0-auth-dir-v-next.0", "0.2.0-auth-dir-v-next.1", "0.2.0-auth-dir-v-next.2", "0.2.0-auth-dir-v-next.3", "0.2.0-auth-dir-v-next.4", "0.2.0-auth-dir-v-next.5", "0.2.0-auth-dir-v-next.6", "0.2.0-auth-dir-v-next.7", "0.2.0-ext12.0", "0.2.0-ext14.0", "0.2.0-ext15.0", "0.2.0-ext16.0", "0.2.0-ext17.0", "0.2.0-ext18.0", "0.2.0-ext19.0", "0.2.0-ext20.0", "0.2.0-ext21.0", "0.2.0-graphql-vnext-dev-preview.0", "0.2.0-graphql-vnext-dev-preview.1", "0.2.0-graphql-vnext-dev-preview.2", "0.2.0-graphql-vnext-dev-preview.3", "0.2.0-graphql-vnext-dev-preview.5", "0.2.0-graphql-vnext-dev-preview.7", "0.2.0-graphql-vnext-dev-preview.8", "0.2.0", "0.2.1-beta.0", "0.2.1-graphql-vnext-dev-preview.0", "0.2.1-graphql-vnext-dev-preview.9", "0.2.1-graphql-vnext-dev-preview.10", "0.2.1-graphql-vnext-dev-preview.11", "0.3.0-beta.0", "0.3.0-beta.1", "0.4.1-graphql-vnext-dev-preview.12", "0.4.1", "0.4.2-geo.0", "0.4.2", "0.4.3-beta.0", "0.4.3", "0.4.4-beta.0", "0.4.4", "0.4.5", "0.4.6", "0.4.7-apiext5.0", "0.4.7-apiext6.0", "0.4.8-beta.0", "0.5.0-apiext1.0", "0.5.0-apiext2.0", "0.5.0-apiext3.0", "0.5.0-apiext4.0", "0.5.0-gql-ext1.0", "0.5.0-gql-ext2.0", "0.5.0", "0.5.1", "0.5.2-beta.0", "0.5.2", "0.5.3-beta.1", "0.5.3", "0.5.4-beta.2", "0.5.4", "0.5.5-beta.1", "0.5.5", "0.5.6-beta.5", "0.5.6-beta.6", "0.5.6", "0.5.7", "0.5.8-beta.7", "0.5.8-geo.0", "0.5.8-sub-identity-claim.1", "0.5.8", "0.5.9-beta.1", "0.5.9-mapsto.0", "0.5.9-mapsto2.0", "0.5.9", "0.5.10-beta.1", "0.5.10", "0.5.11-beta.1", "0.5.11", "0.5.12-beta.0", "0.5.12-codegen-ui-q1-release.0", "0.5.12-mapsto3.0", "0.5.12", "0.6.0-beta.0", "0.6.2-beta.0", "0.7.0-beta.0", "0.7.0", "0.7.1-init-w-override.0", "0.7.1", "0.7.4-beta.0", "0.7.5-beta.0", "0.7.5", "0.7.6-beta.0", "0.7.6-codegen-ui-q1-release.0", "0.7.6", "0.7.7-beta.0", "0.7.7", "0.7.8-beta.0", "0.7.8-uibuilder-wip.0", "0.7.8-uibuilder-wip-2.0", "0.7.8", "0.7.9-beta.0", "0.7.9", "0.7.10-beta.1", "0.7.10-npm-pkg-cli.0", "0.7.10", "0.7.11-alpha.11", "0.7.11-beta.1.0", "0.7.11-beta.2.0", "0.7.11-npm-pkg-cli.0", "0.7.11-pkg-npm-install.0", "0.7.11", "0.7.12-apiSplit.0", "0.7.12-beta.1", "0.7.12-binary-compression.0", "0.7.12-binary-compression-2.0", "0.7.12-optimize-pipeline.0", "0.7.12-optimize-pipeline-1.0", "0.7.12-optimize-pipeline-2.0", "0.7.12", "0.7.13-alpha.19", "0.7.13-alpha.23", "0.7.13-alpha.25", "0.8.0-beta.1", "0.8.0-beta.2", "0.8.0-beta.3", "0.8.0-beta.4", "0.8.0", "0.8.1-alpha.18", "0.8.1-alpha.26", "0.8.1-alpha.27", "0.8.1", "0.8.2-alpha.0", "0.8.2-alpha.1", "0.8.2-alpha.2", "0.9.0-beta.2", "0.9.0-beta.3", "0.9.0-category-split-test.0", "0.9.0-category-split-test.2", "0.9.0-category-split-test.3", "0.9.0", "0.9.1-alpha.38", "0.9.1-alpha.40", "0.9.1-alpha.5135", "0.9.1", "0.9.2-test-api-package-migration.0", "0.9.2", "0.9.3-sub-username-identity-claim.1", "0.9.3", "0.9.4", "0.10.0-sub-username-identity-claim.2", "0.10.0", "0.10.1-alpha.10", "0.10.1-alpha.14", "0.10.1-alpha.18", "0.10.1-alpha.21", "0.10.1-alpha.23", "0.10.1-alpha.24", "0.10.1-alpha.25", "0.11.0", "0.11.1", "0.12.0", "0.12.1-alpha.19", "0.12.1", "0.12.2", "0.12.3-alpha.0", "0.12.3", "0.12.4-alpha.0", "0.12.4-alpha.1", "0.12.4-alpha.2", "0.12.4", "0.12.5-alpha.17", "0.12.5-alpha.21", "0.12.5", "0.12.6-alpha.0", "0.12.6-alpha.1", "0.12.6-alpha.2", "0.12.6", "0.12.7", "1.0.0-ic-changes.1", "1.0.0-ic-changes.3", "1.0.1", "1.0.2", "1.0.3-alpha.7", "1.0.3-alpha.20", "1.0.3", "1.0.4", "1.1.0-rtf-release-phase-1.0", "1.1.0", "1.1.1-405patch1.0", "1.1.1-alpha.9", "1.1.1", "1.1.2-alpha.1", "1.1.2-alpha.7", "1.1.2", "1.1.3-alpha.0", "1.1.3-delta-table-improvements.0", "1.1.3", "1.1.4-alpha.4", "1.1.4", "1.1.5-rds-support.0", "1.2.0", "1.2.1-alpha.1", "1.2.1-alpha.3", "1.2.1-alpha.13", "1.2.1-alpha.14", "1.2.1-rds-v2.0", "1.2.1", "1.2.2", "1.2.3-alpha.3", "1.2.3-alpha.7", "1.2.3-alpha.18", "1.2.3-circular-dep-fix.0", "1.2.3-circular-dep-fix.1", "1.2.3-circular-dep-fix.2", "1.2.3-upgrade-graphql15.0", "1.2.4", "1.2.5-rds-support.0", "1.2.5-rds-support-preview1.0.0", "1.2.5-upgrade-graphql15-2.1", "1.2.5", "1.2.6-rdsv2preview.0", "1.2.6", "1.2.7", "1.2.8-alhotpatchfeb.0", "1.2.8-alpha.31", "1.2.8-alpha.32", "1.2.8", "1.2.9-alpha.0", "1.2.9-alpha.74", "1.2.9-alpha.75", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0-cdkv2.3", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-ownerfield-pk-fix.0", "2.1.2", "2.1.3-5.2.0-ownerfield-pk-fix.0", "2.1.3-alpha.3", "2.1.3-alpha.9", "2.1.3-ownerfield-pk-fix.0", "2.1.3", "2.1.4-sync-fix.0", "2.1.4", "2.1.5", "2.1.6-alpha.1", "2.1.6-alpha.2", "2.1.6-alpha.3", "2.1.6-alpha.6", "2.1.6-alpha.7", "2.1.6-alpha.9", "2.1.6-alpha.11", "2.1.6", "2.1.7-agqlac.0", "2.1.7-agqlac.1", "2.1.7-alpha.2", "2.1.7-alpha.5", "2.1.7-alpha.6", "2.1.7-alpha.9", "2.1.7-alpha.10", "2.1.7-alpha.14", "2.1.7-alpha.17", "2.1.7-cb-test-beta.0", "2.1.7-transformer-without-feature-flags.0", "2.1.7-with-standalone-transformer.0", "2.1.7", "2.1.8-agqlac.0", "2.1.8-agqlac.1", "2.1.8-agqlac.2", "2.1.8-alpha.0", "2.1.8-alpha.18", "2.1.8-cb-test-beta-3.0", "2.1.8-cb-test-beta-4.0", "2.1.8-cb-test-beta-5.0", "2.1.8-cb-test-prod-1.0", "2.1.8-cb-test-prod-2.0", "2.1.8-rds.0", "2.1.8-rds.3", "2.1.8", "2.1.9-agqlac.0", "2.1.10", "2.1.11", "2.1.12", "2.1.13-alpha.7", "2.1.13-test-tag-1.0", "2.1.13", "2.1.14-no-internal-synth.0", "2.1.14-rds.0", "3.1.0", "3.1.1-rds-1.0", "3.1.1", "3.1.2", "3.1.3-construct-uses-jsii.0", "3.1.3-jsii-build.0", "3.1.3-jsii-build.1", "3.1.3-rds-2.0", "3.1.3", "3.1.4", "3.1.5", "3.1.6", "3.1.7", "3.1.8", "3.1.9-amplify-table-preview.0", "3.1.9-amplify-table-preview.1", "3.1.9", "3.1.10", "3.1.11-construct-publish-test.0", "3.2.0-nov-14-cut.0", "3.2.0-nov-14-cut-1.0", "3.2.0-rds-3.0", "3.2.0-rds-4.0", "3.2.0-rds-5.0", "3.2.0", "3.2.1", "3.2.2", "3.2.3", "3.3.0", "3.3.1-alpha.1", "3.3.1", "3.3.2-ecs-tagging-permissions.0", "3.3.2", "3.3.3", "3.3.4", "3.3.5", "3.3.6-secrets-manager.0", "3.3.6", "3.3.7-rds-5.0", "3.4.0-implicit-fields.0", "3.4.0", "3.4.1", "3.4.2-cors-rule.0", "3.4.2-fix-publish-tag.0", "3.4.2-iam-auth.0", "3.4.2", "3.4.3-data-schema-generator.0", "3.4.3-gen2-release.0", "3.4.3-gen2-release.1", "3.4.3-gen2-release-0410.0", "3.4.3-sql-gen2.0", "3.4.3-test-binary-size.0", "3.4.3-z-data-schema-generator.0", "3.4.3", "3.4.4", "3.5.0-0411-gen2.0", "3.5.0-gen2-release.0", "3.5.0-gen2-release.1", "3.5.0-gen2-release-0416.0", "3.5.0-gen2-release-0418.0", "3.5.0-gen2-release-0418-2.0", "3.5.0-gen2-release-0423.0", "3.5.0-iam-auth-with-identityPool-provider-1.0", "3.5.0-sql-gen2-1.0", "3.5.0", "3.5.1-cdk-upgrade-2.129.0.0", "3.5.1", "3.5.2-acdk-upgrade-2-129.0", "3.5.2", "3.5.3", "3.5.4-fix-sub-owner.0", "3.5.4", "3.5.5", "3.6.0", "3.6.1", "3.6.2", "3.6.3-gen2-migration.0", "3.6.3", "3.6.4", "3.6.5-gen2-migration-0809.0", "3.6.5", "3.6.6-api-stable-tag-1.0", "3.6.6-api-stable-tag-2.0", "3.6.6-gen1-migration-0924.0", "3.6.6-gen1-migration-0924-2.0", "3.6.6-raven.0", "3.6.6-raven.1", "3.6.6-raven.2", "3.6.6-raven.3", "3.6.6-raven.4", "3.6.6-sandbox-hotswap.0", "3.6.6-stable-tag-10.0", "3.6.6-stable-tag-2.0", "3.6.6-stable-tag-3.0", "3.6.6-stable-tag-4.0", "3.6.6-stable-tag-5.0", "3.6.6-stable-tag-6.0", "3.6.6-stable-tag-7.0", "3.6.6-stable-tag-8.0", "3.6.6-stable-tag-9.0", "3.6.6-test-stable-tag-release.0", "3.6.6", "3.6.7-gen1-migration-1218.0", "3.6.7-gen1-migration-1218-2.0", "3.6.7", "3.6.8-gen1-type-ext.0", "3.6.8", "3.6.9-gen1-migration-0211.0", "3.6.9-gen1-migration-0214.0", "3.6.9", "3.6.10", "3.6.11-gen1-migrations-0304.0", "3.6.11", "3.6.12", "4.0.0", "4.0.1", "4.1.0", "4.1.1-async-lambda.0", "4.1.1", "4.1.2-ai.0", "4.1.2-ai.1", "4.1.2-gen2-migration-0930.0", "4.1.2", "4.1.3", "4.1.4-gen2-migration-1015.0", "4.1.4", "4.1.5-ai-streaming.0", "4.1.5", "4.1.6-ai-next.0", "4.1.6-ai-streaming.0", "4.1.6", "4.1.7-ai-next.0", "4.1.7", "4.1.8-ai-next.0", "4.1.8", "4.1.9", "4.1.10", "4.1.11", "4.1.12-gen2-migration-0205.0", "4.1.12", "4.2.0", "4.2.1", "4.2.2-grant-stream-read.0", "4.2.2", "4.2.3"], "vulnerableVersions": ["1.2.9-alpha.0", "1.2.9-alpha.74", "1.2.9-alpha.75", "2.1.0-beta.0", "2.1.0-beta.1", "2.1.0-beta.2", "2.1.0-beta.3", "2.1.0-beta.4", "2.1.0-beta.5", "2.1.0-beta.6", "2.1.0-cdkv2.0", "2.1.0-cdkv2.1", "2.1.0-cdkv2.2", "2.1.0-cdkv2.3", "2.1.0", "2.1.1-alpha.5", "2.1.1-alpha.51", "2.1.1", "2.1.2-alpha.3", "2.1.2-alpha.9", "2.1.2-alpha.13", "2.1.2-ownerfield-pk-fix.0", "2.1.2", "2.1.3-5.2.0-ownerfield-pk-fix.0", "2.1.3-alpha.3", "2.1.3-alpha.9", "2.1.3-ownerfield-pk-fix.0", "2.1.3", "2.1.4-sync-fix.0", "2.1.4", "2.1.5", "2.1.6-alpha.1", "2.1.6-alpha.2", "2.1.6-alpha.3", "2.1.6-alpha.6", "2.1.6-alpha.7", "2.1.6-alpha.9", "2.1.6-alpha.11", "2.1.6", "2.1.7-agqlac.0", "2.1.7-agqlac.1", "2.1.7-alpha.2", "2.1.7-alpha.5", "2.1.7-alpha.6", "2.1.7-alpha.9", "2.1.7-alpha.10", "2.1.7-alpha.14", "2.1.7-alpha.17", "2.1.7-cb-test-beta.0", "2.1.7-transformer-without-feature-flags.0", "2.1.7-with-standalone-transformer.0", "2.1.7", "2.1.8-agqlac.0", "2.1.8-agqlac.1", "2.1.8-agqlac.2", "2.1.8-alpha.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "1.2.9-alpha.0 - 2.1.8-alpha.0", "id": "mamPqXwyGPQi4M/dmTRvkjuSM1r/nvaEGgxzkllsgDg5p3NiI4teXbGKvr2gsxQhp8MSndqsZbsiWFFCbVx9Mg=="}