{"_id": "@types/react-router", "_rev": "983-3cc524815523cc533690ff93ccafab57", "name": "@types/react-router", "description": "TypeScript definitions for React Router", "dist-tags": {"latest": "5.1.20", "ts2.0": "2.0.45", "ts2.1": "3.0.8", "ts2.2": "4.0.11", "ts2.3": "4.0.18", "ts2.4": "4.0.21", "ts2.5": "4.0.21", "ts2.6": "4.0.25", "ts2.7": "4.0.25", "ts2.8": "5.1.7", "ts2.9": "5.1.7", "ts3.0": "5.1.8", "ts3.1": "5.1.8", "ts3.2": "5.1.8", "ts3.3": "5.1.11", "ts3.4": "5.1.12", "ts3.5": "5.1.14", "ts3.6": "5.1.16", "ts3.7": "5.1.17", "ts3.8": "5.1.18", "ts3.9": "5.1.18", "ts4.0": "5.1.18", "ts4.1": "5.1.19", "ts4.2": "5.1.20", "ts4.3": "5.1.20", "ts4.4": "5.1.20", "ts4.5": "5.1.20", "ts4.6": "5.1.20", "ts4.7": "5.1.20", "ts4.8": "5.1.20", "ts4.9": "5.1.20", "ts5.0": "5.1.20", "ts5.1": "5.1.20"}, "versions": {"2.0.13-alpha": {"name": "@types/react-router", "version": "2.0.13-alpha", "description": "Type definitions for react-router v2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"react": "*", "./lib/Router": "*", "./lib/Link": "*", "./lib/IndexLink": "*", "./lib/IndexRedirect": "*", "./lib/IndexRoute": "*", "./lib/Redirect": "*", "./lib/Route": "*", "./lib/History": "*", "./lib/Lifecycle": "*", "./lib/RouteContext": "*", "./lib/browserHistory": "*", "./lib/hashHistory": "*", "./lib/useRoutes": "*", "./lib/RouteUtils": "*", "./lib/PatternUtils": "*", "./lib/RouterContext": "*", "./lib/PropTypes": "*", "./lib/match": "*", "./lib/useRouterHistory": "*", "./lib/createMemoryHistory": "*"}, "_id": "@types/react-router@2.0.13-alpha", "_shasum": "5f2ee702998e00564d4105566edfff783aecdd69", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "5f2ee702998e00564d4105566edfff783aecdd69", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.13-alpha.tgz", "integrity": "sha512-Jx4AJ4OaBAoaDSMFi9Acyh1TVWvwcf3/N+Ot4M5U/4CIAu/f5SzbuFd1G0+0XIG3LxbJoddWQ+Khfon7D+M9+A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCVd+GguTubHqoWfeGmgH4WPRwvmrnOIK30NvIKjffubgIgG8+l5R6PLUZfN8P42LHHeTPiJvDdjh25Gln8v3Bgp7s="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.13-alpha.tgz_1463510729632_0.4438646901398897"}, "directories": {}}, "2.0.14-alpha": {"name": "@types/react-router", "version": "2.0.14-alpha", "description": "Type definitions for react-router v2.0.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"react": "*", "./lib/Router": "*", "./lib/Link": "*", "./lib/IndexLink": "*", "./lib/IndexRedirect": "*", "./lib/IndexRoute": "*", "./lib/Redirect": "*", "./lib/Route": "*", "./lib/History": "*", "./lib/Lifecycle": "*", "./lib/RouteContext": "*", "./lib/browserHistory": "*", "./lib/hashHistory": "*", "./lib/useRoutes": "*", "./lib/RouteUtils": "*", "./lib/PatternUtils": "*", "./lib/RouterContext": "*", "./lib/PropTypes": "*", "./lib/match": "*", "./lib/useRouterHistory": "*", "./lib/createMemoryHistory": "*"}, "_id": "@types/react-router@2.0.14-alpha", "_shasum": "d68c92268fbde05f3ca5db2f4ee4ddbaf6a33686", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "d68c92268fbde05f3ca5db2f4ee4ddbaf6a33686", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.14-alpha.tgz", "integrity": "sha512-3JANF8b/7Xu0Z48Li2JGdOyqiJiQ2f5HO2kcTQqhW6oCI8oCFH9G+FTNd7G/lR+LG32wgwEN9RBbynJm4AfQUg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpK/4klJlyiIeOK66/8wVv+BgUSQCEJGbObE8BwizcegIhALbgFkqvVY7jL1X6xaGEh3Oaqj9JRA+biYX87J1pLfDV"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.14-alpha.tgz_1463695841875_0.367854917421937"}, "directories": {}}, "2.0.19-alpha": {"name": "@types/react-router", "version": "2.0.19-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "*"}, "_id": "@types/react-router@2.0.19-alpha", "_shasum": "314e58c4c94d5e20a2846f20bdbaa33bdeceadc4", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "314e58c4c94d5e20a2846f20bdbaa33bdeceadc4", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.19-alpha.tgz", "integrity": "sha512-6qdXdHAlMToxnR5jIGOnGeboeOLfrnUXGr6drAhyJtvG4NlSulyXe0u6Oj6bg8fmRs70VG4f56DO50LUu/2I/w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIARiBT/d7XwQJ8i6O+kxRboh/E3tvGhH7k+fRw+29NN7AiAP3vtgaRFzTAY8Ahxki04CWE5YC0OB0sFh0y7KVJk57g=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.19-alpha.tgz_1463776353035_0.883600126253441"}, "directories": {}}, "2.0.20-alpha": {"name": "@types/react-router", "version": "2.0.20-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "*"}, "_id": "@types/react-router@2.0.20-alpha", "_shasum": "bf42a18b4e78195dc66d0f3d5c35f005530e9ba1", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.8.2", "_nodeVersion": "5.5.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "bf42a18b4e78195dc66d0f3d5c35f005530e9ba1", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.20-alpha.tgz", "integrity": "sha512-uAEXREgZBxJdYzRMmK7RpgmWd6gTAHZ6gmSRog/jDcN/ss2eHbVydYloMbmcyf7x05c4BdHLCYgCZjv+lNbcFA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6JG4XPhn00OEtQSF+wWoYYTFUci99hJcl2FOcZ/dBWQIhAMAKMtFPjKIyj/V5r06ts9F9HzkEKiaq9gYGvPQj0jaF"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.20-alpha.tgz_1464155306777_0.809395634336397"}, "directories": {}}, "2.0.21-alpha": {"name": "@types/react-router", "version": "2.0.21-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.*"}, "_id": "@types/react-router@2.0.21-alpha", "_shasum": "492914b073a43c5fa314b2a8b40948ffc52c1a12", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.9.5", "_nodeVersion": "6.2.2", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "492914b073a43c5fa314b2a8b40948ffc52c1a12", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.21-alpha.tgz", "integrity": "sha512-7FrGCAa/4sVI5zuj7CkfYzX7OlbAqvWnK+0Wsyutsemaxf+jRkw5NTv5Wlop14dcleOtj0SC7jWdhN38dkDcpA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBj+G398e7JKp8bIAO0YbwWWc6av6xMQpPotnNfFSFLUAiAgMXCFFkbVF+JkER1LbvPHONljGYJUquMgCctBoX0p/A=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.21-alpha.tgz_1467404720078_0.12574587319977582"}, "directories": {}}, "2.0.22-alpha": {"name": "@types/react-router", "version": "2.0.22-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.23-alpha"}, "_id": "@types/react-router@2.0.22-alpha", "_shasum": "705159ed94e4a5c4bd407440132f99944a8984cd", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "705159ed94e4a5c4bd407440132f99944a8984cd", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.22-alpha.tgz", "integrity": "sha512-0KjpiGKVP3S/1AOAgMeICm+mdUhMhh+eCiClmAxijLPIiRUxenP4hKxYlz1SCittMb1+uqLGcOLXIC3J0dKVjQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDRg4YsGezfCVvqMqElsK7LL7Pdpdzk3VHYP287sJD60AIhAOmJQH81bhRaVJXIuLrgwJAIl64Hl3d1o7rb/o9FqcAE"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.22-alpha.tgz_1467416911213_0.2125289358664304"}, "directories": {}}, "2.0.23-alpha": {"name": "@types/react-router", "version": "2.0.23-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.24-alpha"}, "_id": "@types/react-router@2.0.23-alpha", "_shasum": "0339abc4c5fb7868ceb460957f4de7a9c255b89e", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0339abc4c5fb7868ceb460957f4de7a9c255b89e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.23-alpha.tgz", "integrity": "sha512-R<PERSON>bsCB1JpJ1wBABS56uIC24pyPAdJYV1X7Zz6Lt1dXhxA5nS52oG+UCoIgqnh/myq/0kVyIn62iQT8KxT68bzA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXxawEYNggVvBtd+lu4kiroVVG8olM0Xn+1PdyfT6ilQIhAKEqJA67VVbPxyrefJrhNcDuH3rBkPjC67wJNdGXnOmu"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.23-alpha.tgz_1467429570758_0.543013452552259"}, "directories": {}}, "2.0.24-alpha": {"name": "@types/react-router", "version": "2.0.24-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.25-alpha"}, "_id": "@types/react-router@2.0.24-alpha", "_shasum": "20fcdc210f8bf113416ade4fdd66f86dc929ecb8", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.7.2", "_nodeVersion": "6.2.1", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "20fcdc210f8bf113416ade4fdd66f86dc929ecb8", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.24-alpha.tgz", "integrity": "sha512-yhQMxbBjSPi+HtT/LkZpTGbUZdlaBUJD14rpZCzqSATeKFxUYP2PvNRhUSlE7/u4+AXm3yPBe0lZVecnBhJoRg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD0DELdXNuT81cJxJqIH0odxod7P4NF2Eub2o0cL/BqagIhAJjNre8MIsw9etY3LuWIo7spEjAuWEt2F2JpkTuRLEfB"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.24-alpha.tgz_1467594638732_0.855070301797241"}, "directories": {}}, "2.0.25-alpha": {"name": "@types/react-router", "version": "2.0.25-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.26-alpha"}, "_id": "@types/react-router@2.0.25-alpha", "_shasum": "d00c2eb318f03eda47a84c8ac8e4317919b0f2e9", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "d00c2eb318f03eda47a84c8ac8e4317919b0f2e9", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.25-alpha.tgz", "integrity": "sha512-VStW3yzlKa+Kmcm3LhSPjVH76zBwbXO1SGts9UdDu/uBvzThfgN/00yHhbPv3R8Js6YFmOGtivcoDCwDm6VYLw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQClvpUPcyUHYsXK+zze3CGBzsdfag5ePJ7pMIZVGNHycwIhAKpp6tREEhBnBKEi+0aQUweWbgOiWXuKHFfV0Z5LAOlv"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.25-alpha.tgz_1468012538394_0.40160577022470534"}, "directories": {}}, "2.0.26-alpha": {"name": "@types/react-router", "version": "2.0.26-alpha", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.26-alpha"}, "_id": "@types/react-router@2.0.26-alpha", "_shasum": "1a8b9fc6e81b4722542c32c4a53b02cb8cad9e7c", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "1a8b9fc6e81b4722542c32c4a53b02cb8cad9e7c", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.26-alpha.tgz", "integrity": "sha512-xJKiU44t7rpL9zs/UAG7ioWWdWFitDwA5MJOBf8HwbSqJOg50UapPK8wXXFuxHMA5CPVe6kWWiF16D0qm3e13w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDFdMA0aGoNppWR4DvI4LQFblvKSQFUFwYDYFv2m5RCxwIgeRNP6SNTtSKtnAEuHC0A9NFEpFtN+PS8wEovmrfRlLM="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.26-alpha.tgz_1468276193566_0.35785077908076346"}, "directories": {}}, "2.0.27": {"name": "@types/react-router", "version": "2.0.27", "description": "TypeScript definitions for react-router v2.0.0", "main": "", "scripts": {}, "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "license": "MIT", "typings": "index.d.ts", "dependencies": {"@types/react": "0.14.*"}, "_id": "@types/react-router@2.0.27", "_shasum": "79b753587576c5c35260fbd56a5b7840e7ed1a8d", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "79b753587576c5c35260fbd56a5b7840e7ed1a8d", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.27.tgz", "integrity": "sha512-Rsz3x99vW+vw1jxeE/Ld/MkyvAYjeBuz5gOsErqF59aN22fVocPxckCh8Fa1t9AXrm/+zkUAlZ5RY+oj10WogQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC1Gpp7v78qh1CiaJPElOO5gc4SwbW0B05ka/DQWf+LvQIhAMyXKuYEwK00RqOMLaBd+1szbmAj3j7pQo9RrEZkgTby"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.27.tgz_1468511546141_0.5977273879107088"}, "directories": {}}, "2.0.28": {"name": "@types/react-router", "version": "2.0.28", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "^0.14.26", "@types/history": "3.0.0"}, "typings": "index.d.ts", "_id": "@types/react-router@2.0.28", "_shasum": "c92366bb553598f6322092db6de12e9046c83169", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "c92366bb553598f6322092db6de12e9046c83169", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.28.tgz", "integrity": "sha512-AeHf0eq1zLInDbk4UOW7BjvlL9IznTytoVEyc2WUthTcAY/Xate8Ri1cQc70sxqf92Y8jzHlzrplQDe9SSU54A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCy9ckERiIQsgBVa/7FGbW587M9lLF6rkxGz4YQsRqYFAIgelhvybiFiCik7bsf+uxlH+9JQBwNxFz0gyCBzsJN0LY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.28.tgz_1468934065825_0.5770422811619937"}, "directories": {}}, "2.0.29": {"name": "@types/react-router", "version": "2.0.29", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": {"name": "<PERSON>", "email": "https://github.com/sergey-butur<PERSON>in"}, "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "2.0.*", "@types/react": "0.14.*"}, "typings": "index.d.ts", "_id": "@types/react-router@2.0.29", "_shasum": "0179bb272602fea98f6c2f33b3cfcd3287dc5c7e", "_from": "output\\react-router", "_resolved": "file:output\\react-router", "_npmVersion": "3.10.3", "_nodeVersion": "6.3.0", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "dist": {"shasum": "0179bb272602fea98f6c2f33b3cfcd3287dc5c7e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.29.tgz", "integrity": "sha512-gi/ihPLl+cP/DIaAEh9NlUxAqWE/nWBG8U+HBoh/W+ZosJLdG66rFcRu5z6i0admo0DPsJ6feJAuDLRGwsRrmw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF8mf4e5JoFGNbpbqsLjFAn4fr9xA7eQut58PxkMxTBRAiBHbkYwPFFyXe4aYkaSqZG9JP1QmInJZwpdP37lMfA6hw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.29.tgz_1469026285276_0.6229053458664566"}, "directories": {}}, "2.0.30": {"name": "@types/react-router", "version": "2.0.30", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "typings": "react-router.d.ts", "_id": "@types/react-router@2.0.30", "dist": {"shasum": "853ac27fe1a5abff3a9ee6ecf4f349ef1c0daabb", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.30.tgz", "integrity": "sha512-e2fsMtapRNY2vS39f9Wx+ftlKI6NceLcaaLApkgALIsiPytcQSdxHTSzZDTTjX6OB/4T9vkdaUNCAPVRp9y4cg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCSShmzJdcfl9trOsc5AsEUEZ90Nbn9b246rD1sZE5jGgIhAOAGxfBAaV8KWF/ktHi3UwBUdc6kXiLteGYnYD4Zq1w9"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.30.tgz_1470154062675_0.16459569637663662"}, "directories": {}}, "2.0.31": {"name": "@types/react-router", "version": "2.0.31", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "0.14.*"}, "typings": "react-router.d.ts", "_id": "@types/react-router@2.0.31", "dist": {"shasum": "c42c279d8bae24d559443d6c340a30ed83066bb9", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.31.tgz", "integrity": "sha512-4oT125Q4IPPDjdOfz/gK52Mo+T+BB1NJMv10q3+nrwY7GJpHO0rdUm6hEYkoU/rC2EzXHyobAYkSopq1s1MpsQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGSgKysMOltewvcKCEEadBpa/sv7/KQ1digfUcwKyfXvAiA7qys8ijYTlynBj2MbDq3nxF0YG+cxjOTgC8bSRs94sg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.31.tgz_1471465909591_0.9628918736707419"}, "directories": {}}, "2.0.32": {"name": "@types/react-router", "version": "2.0.32", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "0.14.*"}, "typings": "react-router.d.ts", "_id": "@types/react-router@2.0.32", "dist": {"shasum": "1817def216622bdc1436465e4e27584d9107229f", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.32.tgz", "integrity": "sha512-Ci0+wlFhXGV04iISMGc/zow2o4X3NODQRwSxsVvLQX8ZV45aQNi7LxnXEn3AzNlmgkHNnuR2aHorPlu0hGvVSg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICB2W3wFHVEqlBq8xrTL/T51hytXgJQfvb3GG7P12RanAiEA33R7rZLmU34NBa03sBkkgcif3/UvX0Lgv9OgEPucIcQ="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.32.tgz_1471528437316_0.8615551241673529"}, "directories": {}}, "2.0.33": {"name": "@types/react-router", "version": "2.0.33", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "2.0.*", "@types/react": "0.14.*"}, "typings": "index.d.ts", "_id": "@types/react-router@2.0.33", "dist": {"shasum": "c9a61be3a5cf3f6f8a8c3114eee6c7c38aa76c9e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.33.tgz", "integrity": "sha512-lgDjSx3Ct6L75x7PT6o0TrbUbrjqvel04aFYw6gqAh2Pu28dnnL8QYyzj5lSuUeoUikoMw+0llLkS79XB6YgJQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCQgN6uxkC4Oqv8jGU51dmzImsIOkwjaLVzWcSycRiH1AIgLBED3pI8DADNYiwmA9hrilGiWj3Kcdj1tg6dJ237ubg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.33.tgz_1472070151019_0.1400911114178598"}, "directories": {}}, "2.0.34": {"name": "@types/react-router", "version": "2.0.34", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "a66ad61d9528cebfc98e368d5ae452254982865a754575c3d5061685605a979f", "_id": "@types/react-router@2.0.34", "dist": {"shasum": "aa6d35eba7f89f4bc7248d809d866bb1c74d7616", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.34.tgz", "integrity": "sha512-QVO/gkLLHI4snfDWsMkPP/ar4ZEiYYP+kn8LpwyJCXJ1f5zxD9HE+Llw6+zUzLIeYH0JhbA8yQ8tRGixOJJWlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEx4VKdtcyY5ZrlNIOaG99imY7S9nv1IEGRMbffIqlz3AiBzq4fGEgE0YHUB2tYD5dyr+zH4KM1dUvuq5YyUFJRWmA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.34.tgz_1474308466497_0.19730229047127068"}, "directories": {}}, "2.0.35": {"name": "@types/react-router", "version": "2.0.35", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "0550ff753b029af606599d4788bc857390b28cb35fa152657e314f06f5a5a944", "_id": "@types/react-router@2.0.35", "dist": {"shasum": "fd5a51a2674a74076ea9344babe34e48e0c25276", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.35.tgz", "integrity": "sha512-dUSTZO3AOHWrDYw4L0D6t4RujlO7ZMVotgMuGoOV1GrPp2SO70GLyPsrH4TfFiIAFRziDt+WVrd0s/tJoGIklQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICYitZGOjCBXRJCAI2sz92slwcQ82aTV99n8DRMcgZpNAiEAoMtiqWgW1rPgZW5d4QDhEd0Ugp4/nFA837vbwttbh6Y="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.35.tgz_1474314947637_0.7073185732588172"}, "directories": {}}, "2.0.36": {"name": "@types/react-router", "version": "2.0.36", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typings": "index.d.ts", "typesPublisherContentHash": "7b9597493e4e472cb4440a97638a5a287e190faa6ab1752708a6356259cb0911", "_id": "@types/react-router@2.0.36", "dist": {"shasum": "9600a04a9e7850c80207d2b9aeab38ffcb5ee182", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.36.tgz", "integrity": "sha512-ePJPbnXnQtks5moqSOqMsSF/Qnnzi6Cho4gjcS9NoFQ+DZmNHONqDddmv7NbXkK91RflwsxZ31UQ1Fd0RLoP2A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDstWX+SX/CDptT4KpZwMUN/GnlA3y81vauaRajkiL9mAiBZtAkNJeREvJiosE0+U9X/4n4rxoDMDUD18Bok/tJ4Cg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.36.tgz_1474489700632_0.16565620270557702"}, "directories": {}}, "2.0.37": {"name": "@types/react-router", "version": "2.0.37", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "35f1901c556408f2d36fa26ce4b58879f8fc5cdef778ba0b1b2d065ea763496e", "_id": "@types/react-router@2.0.37", "dist": {"shasum": "24f8def34b11ce57594b05c4eac6741f358b977f", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.37.tgz", "integrity": "sha512-YZ4xfR2mD60K5F32vlyzKuqCs1gzIgEVZGf1lkO8rsbtNJwqX/4BlP+A6w9AIxIjbWC6iznzEMSZfLDjITRiSw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEwKMJqir1KikqE39zVJzAWgsltNRqWzFN+iWLIsao6bAiAtcq0NudDVpNc/UDLVEn6xdkKkx2Vion6uHpmxkOFiPw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.37.tgz_1475508292604_0.681729375384748"}, "directories": {}}, "2.0.38": {"name": "@types/react-router", "version": "2.0.38", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "35f1901c556408f2d36fa26ce4b58879f8fc5cdef778ba0b1b2d065ea763496e", "_id": "@types/react-router@2.0.38", "dist": {"shasum": "24a4612cfd97f6ddbf3b32e0c2c9b942899aa36d", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.38.tgz", "integrity": "sha512-w9qAkMNw6U3BSpjqOu+iS3oHEqF8WhhZ2/HgTqqHhZArUCVjIdNgUHzTEmTSnRnuiABux8AvDY1zqU/dtL3TBQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGVVTXD5Edekpp2pZyzQ2omgF4L9YeW4ni+IjMDg5uktAiBGCT80mf91IDl2OHOEN+RLz3f0TDr+JmPtY39F6ebAWw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-16-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.38.tgz_1476387506775_0.3982971135992557"}, "directories": {}}, "2.0.39": {"name": "@types/react-router", "version": "2.0.39", "description": "TypeScript definitions for react-router v2.0.0", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "b8a3e469b66769544e77c04cb560b5ee975689bda16c2b88d2f6f427b2a9aa14", "_id": "@types/react-router@2.0.39", "dist": {"shasum": "b0ae3c3dd98670c010a2b62b3c1ef6a57dda804a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.39.tgz", "integrity": "sha512-GKXuokLQrfe4djXDLYqXFwxE0aNRtziZmMpDY+EiodYneuaX6BwrDMYhCmRpjpYlFQ3+ZDpGlW8ra0RMO7EkqA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDp4XGscN+R4hO/XTLRCOYgmaP/nXgNWrrfw//FJi9YmQIgB69ccWfCaakULIeU85Eafc2EFbneQzXgV2Ipl8hHiVk="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.39.tgz_1479152243221_0.2544992270413786"}, "directories": {}}, "2.0.40": {"name": "@types/react-router", "version": "2.0.40", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typings": "index.d.ts", "typesPublisherContentHash": "c18a28bf03aab22204f412a70a4d28a5895f2406ef522fa6a5e3704c710a405a", "_id": "@types/react-router@2.0.40", "dist": {"shasum": "ffc3cbaa93182e5af6c82d08294c90d9ff0ec846", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.40.tgz", "integrity": "sha512-M8Lg0YEdujtsQp2BfLHKA9jMlD2FnEsVCd4CDeTwP3rkcXdqaPEZ30ci4JZ+gnI+rXG59yyekPgleHxxmihOog==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOHpGhM2rbX6L3sH5q8OUcNbpDhX7xDIYleEVqk+0zzwIhAN4Pd7yy5AMHGVpyhAetplwVGAaBZbFOc4iRlcoPfTT7"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.40.tgz_1480350658619_0.9774607282597572"}, "directories": {}}, "2.0.41": {"name": "@types/react-router", "version": "2.0.41", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "42e63d91e456da7c4f2cd4c90ba8f9fd4a53d475c62610b1baccb0daa86cf0c9", "_id": "@types/react-router@2.0.41", "dist": {"shasum": "4c2510dfa2d85651af2126aed681c70dbe2f0303", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.41.tgz", "integrity": "sha512-MUSS0I5FIxG0kkxKDbAoW6WyeCI3g4BwkuUvtyX1g3qDvGVgUkVHF4w4exnWHRmEzVoNUZFD1/Wxh6ebrR2jOw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCgcmLsC8+fRTc5/yeV4PjF70EURrAZMl617d4xLMX1lgIgI9hjrO9EYGEYPdPGrA5bCZcC68wIa5GsyEII+IezBCM="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.41.tgz_1480360715767_0.54022097424604"}, "directories": {}}, "2.0.42": {"name": "@types/react-router", "version": "2.0.42", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c18a28bf03aab22204f412a70a4d28a5895f2406ef522fa6a5e3704c710a405a", "typeScriptVersion": "2.0", "_id": "@types/react-router@2.0.42", "dist": {"shasum": "15f25a45974de070a5d0fd3b9207dd940b04c81f", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.42.tgz", "integrity": "sha512-LMJgyxNd/1KDnUF6nZknK9AitUOR2neN7rM//Vr5eYB3IUsEk9PKDmSrTeJPyKD3riUKZRGE5lqGXriTm0kXGQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDnoQp1f8A5xObeQmMTu+uzX3hFk5Gi6CzM3IOjVRhqvQIhAI5lsteXzX4zmmH/TaMg3xgevir22tTPT+Zlf0ihljAa"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.42.tgz_1484074459914_0.23463419801555574"}, "directories": {}}, "2.0.43": {"name": "@types/react-router", "version": "2.0.43", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "4abbc8d1bfc9b72cc0177c7c9b2019c376ff4b3383551d77d92aee65e9bec162", "typeScriptVersion": "2.0", "_id": "@types/react-router@2.0.43", "dist": {"shasum": "b00c20eece6a6b7e12477095df24158ae6a92305", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.43.tgz", "integrity": "sha512-v/qO/J6a/KB4xkaTjUm8xMJ7rKMYctqgY2IV3K5oZKSnW18j6f+2yTlTHm0Zd6R35pd7j5BFSQFtN+/PKMXWTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBRlgsTm4PC6SaO5ejquoKCy0E/fz8/iExgOYPuVsfGOAiEAk7utdZOANN+saqPNE/hbSUoBZ26Q9BWt36vnitv7JxI="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.43.tgz_1484243331837_0.08345445548184216"}, "directories": {}}, "2.0.44": {"name": "@types/react-router", "version": "2.0.44", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "66848dc678b1a87a6e5bdcfd6a721ffd591e817c66523ebcdae1f7a75e696a5a", "typeScriptVersion": "2.0", "_id": "@types/react-router@2.0.44", "dist": {"shasum": "d44515f8f25e9d0f6d2a8f6ff3b3eb022432c69c", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.44.tgz", "integrity": "sha512-qy8/zxZOM1QlVFIiX+nqa6B/E6fF3B53VxR7e8KlmDyK81vLrOD+GTVMm/MtsUHOiYI683dEysURxDK4aCZclA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDoHlre9Whyzj5zt+5Sfqi2ciIT5/kRfaWdrmBWj/DI9wIhAP5BZcKSCNRJnfOTMaikFOGqMnucVozCKPnBu7DlQX65"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.44.tgz_1484243690381_0.032252457458525896"}, "directories": {}}, "2.0.45": {"name": "@types/react-router", "version": "2.0.45", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "88a78523a007e63ba73d062fdab2f42de0d6e14fd47d079fcc5ce581a208fd83", "typeScriptVersion": "2.0", "_id": "@types/react-router@2.0.45", "dist": {"shasum": "cd859cb3dc4eeeef0dcf4817fc4ef3be13255be0", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.45.tgz", "integrity": "sha512-nu+GcA3IxRxqY8mz+PGJlTWqjt4KvyXwWTyv6j7CPfLkdGCPZbboDtFc28rZdSBwXh6Grmruuej9G9o0js53Gw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF2YUcx9igk7EVCwR6vy4XvACxzvm4Wpc2yarRyPm0fTAiA10VAEFWni7CjiqakppNeYePLzGUR5ksot9dBdph5B5g=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.45.tgz_1484672381752_0.4606283251196146"}, "directories": {}}, "2.0.46": {"name": "@types/react-router", "version": "2.0.46", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "eb08fdf20159266c05a284b1a0ccc1e9e67c9ab8f98024b9a53ee2ef40276af3", "typeScriptVersion": "2.1", "_id": "@types/react-router@2.0.46", "dist": {"shasum": "01ed3e2cc443046bc6c8b6daa6deee3bcd370532", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.46.tgz", "integrity": "sha512-CcntX6RvzYQURB3ZwucVR0ujQKK0ySKJpcYg/JkUgHRJLM9CN0+pP4ZBFD5/tcyhCwZf+AGpY/qYrz4EvxCr9A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBxN0YOR/TQnQs4xJ1soi2HIY3KJX2CWn+O+lRK9tXnvAiAz0Y+JuniSezruI1eClqMDYAOB2XTu+odbM4S5g2TMaQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.46.tgz_1485207326587_0.12409293069504201"}, "directories": {}}, "3.0.0": {"name": "@types/react-router", "version": "3.0.0", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>, <PERSON> <https://github.com/johnnyreilly>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "4c6bf2c167666ab9d0861fc7584e9b73a54b63a685d563839411261716d9f7f7", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.0", "dist": {"shasum": "59168a10f9aa38fe88d6b66e1e0e4426798b7941", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.0.tgz", "integrity": "sha512-kCoEx25w3y6/6eXJnTYyXenxu6m05JxSPG0+VfIn0Zq4bZKAAOCxWRFeC2bEgdssbVbk81Z0puYfxeylBcdQTw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDAuUCNMtTZImKRnI+vc6daZMM0938kSnJUnFKh+x4XCAiEA0+LlKDky7GRj+lcHDOojLZXvfj8eH1RHH9Z+Ka8qgfY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-3.0.0.tgz_1485554566438_0.3188353609293699"}, "directories": {}}, "2.0.47": {"name": "@types/react-router", "version": "2.0.47", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "82995dac4e2be2931d25077a1683833b30c1d3dc952d30b1ff890c070efad58e", "typeScriptVersion": "2.1", "_id": "@types/react-router@2.0.47", "dist": {"shasum": "a24f87913320fd59e6dfebcaa660a023155735f8", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.47.tgz", "integrity": "sha512-8LSi0pIQ6K7YgJPicQqFQu7neKDbwc8hqoKHs50zKrhxLziQxVRZBxhnzUHVRJGqgtaWvDACDCuQlnLxBI2Wkw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL9zW5lPjXljt7y8M/vpZpIixlQJMjfYctL1gYeIyGWgIgGRTxRgfMPbREbvzsXY3JK4j+3nclGen0TSWLkUtjD3E="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-2.0.47.tgz_1486500333828_0.9461738700047135"}, "directories": {}}, "3.0.1": {"name": "@types/react-router", "version": "3.0.1", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>, <PERSON> <https://github.com/johnnyreilly>, <PERSON><PERSON> <https://github.com/LKay>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "9253665c3779d0edbaf997a34d691229e77f214410fac8578d2de88ea183f651", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.1", "dist": {"shasum": "7054bc6e160965dfbb61b55d0d68b53b79910ea4", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.1.tgz", "integrity": "sha512-j3jzowNvgBAlGfmcod6bw6wHke3/Ft9K5FYMHNbIPDX+2ENkayynl8S7axWjMdF2NJfVMMdY+5QtB/42EhsJxA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDw25QFFtqAieQXZvSCd6h1oTavBFsxAWMG8v9H8gKtwgIgDbi1LR/6wpzVQFvbsUMzTR6g9Qw9xqqyOo3SQep8Mu8="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.1.tgz_1486500831713_0.5535924839787185"}, "directories": {}}, "3.0.2": {"name": "@types/react-router", "version": "3.0.2", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>, <PERSON> <https://github.com/johnnyreilly>, <PERSON><PERSON> <https://github.com/LKay>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "3372800c3375d18444cdf8b161ebff138a59d0f179cef4a93f46cd6372828e3d", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.2", "dist": {"shasum": "f3f71ae8222e6b5e79499007fd6ed552dfeaae5b", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.2.tgz", "integrity": "sha512-VlCiDb5DGDqyFSl49pPAtHsqZfFLoTlzN0sLlLN/mJ0WMEqpm7+aQgykP5Vi3fGsoHfJ4q02jgILUBhfC8r3FA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDoq0mNKhTirP7waBBbc28dicJJqFkcEhZnaK1YIkrFGAiEAoGupnm2Chy7uNrcoFC5xD6l4v2BT/Nq8p698+jD6HvY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-3.0.2.tgz_1486589487817_0.6810639032628387"}, "directories": {}}, "3.0.3": {"name": "@types/react-router", "version": "3.0.3", "description": "TypeScript definitions for react-router", "license": "MIT", "author": "<PERSON> <https://github.com/sergey-butur<PERSON><PERSON>>, <PERSON><PERSON> <https://github.com/mrk21>, <PERSON><PERSON><PERSON><PERSON> <https://github.com/vasek17>, <PERSON> <https://github.com/ngbrown>, <PERSON> <https://github.com/awendland>, <PERSON><PERSON><PERSON> <https://github.com/<PERSON><PERSON><PERSON>>, <PERSON> <https://github.com/johnnyreilly>, <PERSON><PERSON> <https://github.com/LKay>", "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "cd9a508da180b03d15d51ab4c3d26e8ce1de6a40682e6785a543bce84475a71d", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.3", "dist": {"shasum": "78644c9a0dd7e04f7101b9a03d63361e69587030", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.3.tgz", "integrity": "sha512-fQ8pj/heqPL/PaPIZhcdjZvAs2Jdc9fdJa68BKPzG/TJbMqiyJ5ScMYHjtHGGQ1Gmw0w6a5NJLx3XlgLnn+mFQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC6dmnF9rdbgluyKYlq0K2kz8CTFNpESH/jm/k0HWUs4QIhAKY1YO7cbf5NB9rNKYa/xFemEVz/Icp3JgY9Pfda/8wF"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.3.tgz_1486731774798_0.5706389495171607"}, "directories": {}}, "3.0.4": {"name": "@types/react-router", "version": "3.0.4", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "fd458eb9fd64a7144be1270dcc2fd3fab930ee740739478939018df5cdb37f8f", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.4", "dist": {"shasum": "48acd0334b80302feb6983dff0f96445eaa89cc8", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.4.tgz", "integrity": "sha512-kYAsj7AMtuj4FFjr9AEsr439ceEodt8ukm0TVthqOVfiIwKXpzwdQKLuJCnzDaT3U31oC0DuQaNcNrZsS9AE6A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHDVdEQqP+3kLVEnGxXF7+6lUoS7j+6VPJS1tl+mRJgmAiEA7+0qnEXRzWVc65lgPBE+89ejO96SQkAFl9FjDfdQKyk="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-3.0.4.tgz_1487577487793_0.2744333522859961"}, "directories": {}}, "2.0.48": {"name": "@types/react-router", "version": "2.0.48", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "f77b92c5d8ebb55f49818adb6d2918864334f57e9b32abb555fdb090b1e0da8a", "typeScriptVersion": "2.1", "_id": "@types/react-router@2.0.48", "dist": {"shasum": "f9a2fff72abb3204a42d726f2fa5a373fa424d10", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.48.tgz", "integrity": "sha512-dHk+o7xUUT/oyqeOkA99yZAwCyovKK2SmqRNLAcTmjYVpAywWU89KGLr1rO7klnZb1E3Q9fO2xuMMWVoHJcNzg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDSJoOI5yBO6Dqzb6xuOImsoQ6tmHTDM54fb3Jxnvh2aQIgH0O2lG5DO3RdHkvXVa1bc33YcY4GSmbxK1hxO9Vi6EI="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.48.tgz_1487863480763_0.002841137582436204"}, "directories": {}}, "3.0.5": {"name": "@types/react-router", "version": "3.0.5", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "82d07ff9917a023ec4ea6223b9cbfaeee4d545cd5f20baa74e54d54b1f9ce85b", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.5", "dist": {"shasum": "ef5eea33f067d877586d06c464a5f864860be09d", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.5.tgz", "integrity": "sha512-7jX3EngMZqN06Dmy2FBwxLXNooMkMR2FkMMeVccTJNKE+M5Iwaj9ziRmEJ2+vgQNbLkqRxfoMhhFmPIYwRGNNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDij9K2lgnioGiOvfVgDcc2MVbWIYdDoZsqch+D1Oq42gIhAKnNHUSmlFueIYY05A2mHF2Hcw9FG+6AIK7wp96AgFe3"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.5.tgz_1487875204991_0.6019898743834347"}, "directories": {}}, "2.0.49": {"name": "@types/react-router", "version": "2.0.49", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "bd3a373e6e8b370f53f17d272f8d53ef77982eafb79ffab33b464e50684155db", "typeScriptVersion": "2.1", "_id": "@types/react-router@2.0.49", "dist": {"shasum": "96f8ad51f07a5890ab35fd55f05170efd132552a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.49.tgz", "integrity": "sha512-LAieSIJ9LtiM9fK6Lv4UAf537te8zgpYhBGdNf4Iv9Ifzg9gRu3N7b3V5cPNWDlqjz4VTF8LXu8oxblKdDLJEw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCEdeUKjIDXbtB0PX9N7jgX16rakezB4eV3wRFac9meyAIhAMvtZ1DxIEqS8rNPnPREZw0TMghbyZk/V4Nqg2qEDqs2"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-2.0.49.tgz_1488291926596_0.5171037234831601"}, "directories": {}}, "3.0.6": {"name": "@types/react-router", "version": "3.0.6", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "f46f73f267d8d9243aefdd5f569caccc95056497c36a9f4338d8e356f7301384", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.6", "dist": {"shasum": "8e127d4b8bd9d816bd252b42abbbc95911e492f3", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.6.tgz", "integrity": "sha512-GpIo1hWH5WANzBETvfPsJI9cTct0d1gXU82bxsqSXcUnK1canEZjMeexwe+r/bJyRVk6Wtmtr8gKk6N45+PU1w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCLRi/O9jcWXehzHY2FTssjFPb5FTVZmzVDwyz9r8OC1QIhAM+zQFeQIUd/H2W7dcdq2Ig9zM72Nw9CWoYqYCg+DT9i"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.6.tgz_1488406982104_0.3732422860339284"}, "directories": {}}, "3.0.7": {"name": "@types/react-router", "version": "3.0.7", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "1771b8c9e1d8a651f364da62b7889c603fe67ee081b69bcbb46a42ac14c7e689", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.7", "dist": {"shasum": "f2f70df67a3a381228cf686b75cf27fc22663951", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.7.tgz", "integrity": "sha512-N2w2Ei7nvLIbUQmmcDnCDVG2rsnQErgeNY7iFJErhTJrEVtT38tAGKslsUmuLnrECrXg5iKyRw9WBqdwOUGu1A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDXOfCbbjoq5dAdesqxschafB0j1C23Jzmh2V2RxCDq1gIhANAg9ywWZNKRAqT2VFoD7iIAyQ5tPN9iOWKSPLtfUO2x"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-3.0.7.tgz_1489191340591_0.5201125657185912"}, "directories": {}}, "3.0.8": {"name": "@types/react-router", "version": "3.0.8", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "e959f72e4437df529fe5cbedeceed7e1b88cdd98bbbe784844b0733f0c131153", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.8", "dist": {"shasum": "cec0f2b90b7cd5305a0481a8b565115284796cb8", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.8.tgz", "integrity": "sha512-anaioZI/MqPS2FxqpHnNyZvr9DKK1CXhCBHVZmvXDjpzoa0/FJ5EgJ6Er7Uh23kJP59woRqrqFyLWYVwXvPIeg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXk4OGrhDl911MuEIWuyTnGnvuUjtWO6W6NC5RzUZ2MAiA9MQL+9JUXmCPw+BRxEgguc9+bW2cTx4c27v9Ny6vDLw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.8.tgz_1489426436311_0.705800951924175"}, "directories": {}}, "3.0.9": {"name": "@types/react-router", "version": "3.0.9", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "1513830f92329d45cb693859697c6bf6d7ee3b7a644fa717404ca6957316b1e6", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.9", "dist": {"shasum": "2233125611e085a3c00776f1bf9aa469c8115ec4", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.9.tgz", "integrity": "sha512-LBPF4kddlscqSBQeT6S7VHdwvfnyeF9kOCXvJhTpd3WUInnF9n/BTYGstQRhc9BM8cQtWcmULL9P1j2FYBuK4w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBsBVhplyiZFvW9drHkynDh7GUDYNhlL9bMWYcS3yBLQAiAnkK8gxc/YahtUaHRD4nNehpoIvJD+/J8H2WK1TOKBhg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.9.tgz_1490142496676_0.43996839923784137"}, "directories": {}}, "4.0.0": {"name": "@types/react-router", "version": "4.0.0", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "c4f49a3591e9f01195e2152d04453fda5d543bf2d48e2843d79e2f1c380ad7ef", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.0", "dist": {"shasum": "9303bc1c07a3562848bd1498252fc54edcc0cb40", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.0.tgz", "integrity": "sha512-zShPRnX3G3INevUqGDso9uRYzddZc7TI7XwP1LzoKaIQAF6uhvu9ZpzcWud8k6x3mDhjAweWwzUhX21fIHn79w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIE29cKloyQGXBUxsB2MOsUb4j4p3Tx3L+Yup1et8eQZ3AiBmgfAGRzzKmI0V6CiZnYxUqNwr6szu/kOIpCfWKZR/dQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-4.0.0.tgz_1490142845658_0.5139047272969037"}, "directories": {}}, "4.0.1": {"name": "@types/react-router", "version": "4.0.1", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "dd537af5f987c8962723023c004660e685842e5c69ed3ff63cefb006bd3178bd", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.1", "dist": {"shasum": "07423db947a99ef4d09c40db15d6d101d891dcdb", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.1.tgz", "integrity": "sha512-nlqAcZMbHnVUZhzrkZ6SnMDGcPyYVc2vSQsOa1iX85j3DgBMfqNTokmcaKHouzIl/eihf+Sw73v4FsHsrqpmKg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAmYCkDhCzkeZUQc72C0XfHS6NTpcPPQtvkCdXj4FbrQAiEAndT+LBgRONe6LHRcleVJ1EXrgus7Y9THFxrvs5j59N4="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.1.tgz_1490312646091_0.16491696028970182"}, "directories": {}}, "4.0.2": {"name": "@types/react-router", "version": "4.0.2", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "7d031b25cdefd971070231448546af069783b5942bdbe265e926f1a75900da1a", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.2", "dist": {"shasum": "b72e2cf277d65e7ebbc89cfe1512e1301d9aa655", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.2.tgz", "integrity": "sha512-divHHHcs8xOa/Kjs39QqNQmehUkqj9V7HouhWl2pNKWmb/8bn/RUPyYNUNwf90YKJd+23NKohqkUTkCq0Kz8cw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIF0x38P2YwYi5g+tSLQRp7W6o0Nh1gd0dNbOKEnZnNKKAiB3R9hB1HAXjePEOKAVIXHY1KcZzfmLuiAYlVn2x2omQA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.2.tgz_1490631635679_0.2624896513298154"}, "directories": {}}, "4.0.3": {"name": "@types/react-router", "version": "4.0.3", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "92d9ccf0f6470270df8085c9ecf4329dced5f7fde7a1bd736ca3c7bc1e51671e", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.3", "dist": {"shasum": "322a265196ecce50bc02985a253f9a4881ba44af", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.3.tgz", "integrity": "sha512-pDwid2vzj9vEXVsQ6bP93BxWwTunqVKM6W67Taxi0bAse2/dKDTsCLXdodiOcgzNeCxTZVlAorJV8peTiUP0xw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDbKGsvpJye69R3YSnk0f3V7w0dLx9tlabKfJInKwTDRAiA9sS160kn5eqlZxpFtNARbRc4rkXRfH+WV4YUZ0F81og=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.3.tgz_1490739106750_0.2972762444987893"}, "directories": {}}, "3.0.10": {"name": "@types/react-router", "version": "3.0.10", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "891b631de4a5cedfe10c28f75a549907895b83ed2dc55611f39e020c1f0b4e35", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.10", "dist": {"shasum": "81c4b1b000a53e69056cd559acdd1c5b57dbb649", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.10.tgz", "integrity": "sha512-nN5X2TnT//RRiKeRBZh5Jd7ZoYVT+/2gAQuJZeNCrN1gFAlkpas+7OxpqiysfP5PhjBcBhiZIhSXHfJzrXh73g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBKroVb5Ko/1TyVO4BwmEJT/TbyAqKWIX/ZRayFw+2FtAiEA0DffOc93nxQTrV4LUUDfD0o41aoTmfPkHrnPYY+DbGI="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.10.tgz_1490909485063_0.2405731815379113"}, "directories": {}}, "4.0.4": {"name": "@types/react-router", "version": "4.0.4", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "94a22c525497e9547218df85997f9e3e58de7c194ab13e0f6f9cae0f78191a00", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.4", "dist": {"shasum": "288455d9e20fb475dc39700b1dbf20b7fea1a2ac", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.4.tgz", "integrity": "sha512-Yw6ahJf/PWcQzTkFwV3zP51gjQFRjo+4ShhGB2zHkwR29ZysKAUnHT10t+i+QhkL8UD6pAIr6IJRiuuFKSJdPA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEemadcJXeKbypnvRKY8ZrgezrJWOb77nT1JV4Qx7VO+AiEA7zWsKkTtx9itB+k/ZUN13G4h+m0qM8KSpe3s+pPFb8c="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.4.tgz_1491509391555_0.8174302540719509"}, "directories": {}}, "3.0.11": {"name": "@types/react-router", "version": "3.0.11", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "80c43efcec611e1d88b4106179e6c0a74839623ea7fe33eb61bd6088ba2b8310", "typeScriptVersion": "2.1", "_id": "@types/react-router@3.0.11", "dist": {"shasum": "fbb1965e5cc1786d2cbb0f1fa49d4856e2e579f6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.11.tgz", "integrity": "sha512-lt9l0UkDJx68I1WBgKWK+j4gyvM40r71zfnGFnaFvRX0K5K237bYF9ztYvBTKTQtPWp3ORif4UQuwX/bHaSL3g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDnpfchYo+0lDEgYX86VIr3iEbaflIVCbTX8YYIWMkI3gIgb4iwb7Omjx79xsqt6xYAJkeOwhw6uZr6/twNw9O/K6s="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-3.0.11.tgz_1492451856705_0.40671046916395426"}, "directories": {}}, "4.0.5": {"name": "@types/react-router", "version": "4.0.5", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "03931b56d19aff5c20daba328a63e688691b02bc5dae3c33bdb19c76bb5cd854", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.5", "dist": {"shasum": "ad64f1064ff428b0ad67f5e87c5cbc2c46920e26", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.5.tgz", "integrity": "sha512-AJa+lnmp2jMiPknKFYBBcX2dVIH7ve3shb0Pb2JqBSvft+ytMw5jgahU1SIycjmU6oJKHcUO3gkR0KZUO10hiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGEr6+5Dvvrdi1wR/Y7t/Gp6kaHeu814fbEW3WGZmtp7AiAoYSO7wuIsbeQUIgc4eKcTrMv7cvpMCE0iqyg5o65Vmg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.5.tgz_1493402310035_0.5084201442077756"}, "directories": {}}, "4.0.6": {"name": "@types/react-router", "version": "4.0.6", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "b1ead99baf535a157f44809207d6b78ad77a00daedb570d32e5772e5df90e0db", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.6", "dist": {"shasum": "df5770535d07a1e27d4071a246b5b91c79f2555e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.6.tgz", "integrity": "sha512-0cs+3JIXoXeNJPjdLpJRWeIrPsVRdK71hqmEpfSeOxidhBQVEISqSAIBcUeQDBTlFetnZ48PzfRY7hzKq8v8lQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBnyXYeMsZM4rpqBCQJZjXC+N9IkHezYsiclx8Vn+QDOAiBOSQdNT1a44BCXLZyIka/qBO58oLMbffDPQqSI7zLonw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-4.0.6.tgz_1493672430005_0.3807739627081901"}, "directories": {}}, "4.0.7": {"name": "@types/react-router", "version": "4.0.7", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "d0da9597e6450d6b5eb07d5e836eac5b5a719102fd87f5408d18863ef11ad5e5", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.7", "dist": {"shasum": "2edba09b46e4979193482771ee5669e0e1fa0480", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.7.tgz", "integrity": "sha512-bYCnc/MNbFGO3WvyUkbd1roJA9elqtIMghI+GrIkWat/QhCho1qktSwHvXY5HNNg406AUyVytTWtDaGYmRNaDQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFbG5T3VuRJ+BE+8cktsXEwIkWLG6D9c/J8107WgfoKWAiEAnQE1EhF6kGiDX0fqWKEOBNqeDSX7tH0wFCwYLI683Uc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.7.tgz_1493957201385_0.7257482472341508"}, "directories": {}}, "4.0.8": {"name": "@types/react-router", "version": "4.0.8", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "e983b02075a23caa84958aae60ea0ed307acd9ddecb13b0831b2c4d7f6a7eadf", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.8", "dist": {"shasum": "18257d55a5dd72defb288fb9e8354908aa98f746", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.8.tgz", "integrity": "sha512-qjw2hsHEabhQbURP/b62d3ml+LdNcI59gk2PhcAkhaNmMsc/POkjnvOv2t6J2MDrlZ70ge6kC8/nYsTPKJoAtw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCn7c1ryCuljjD47gwd6M/20Wk5UuMze0og0Vk0zFgRGwIhAOxxu4fMhTH1HYwnLirszWXJ5ewLX23Q+GcjYqIXUvxZ"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-18-east.internal.npmjs.com", "tmp": "tmp/react-router-4.0.8.tgz_1494006207934_0.355673098936677"}, "directories": {}}, "4.0.9": {"name": "@types/react-router", "version": "4.0.9", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "89887ebeb84e26d8b4a5e6197e669ce8e69bd36bc87fdd8c9e7d89176ad5cc4f", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.9", "dist": {"shasum": "ff43a8e842358891d4d0c7acf0d11023b7951de9", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.9.tgz", "integrity": "sha512-2xSyGCe6J63D83Xely0x0W4x7UUzADwjAa0yl0UnVKzrwuZHYQ70OTigr53wfb/F6coESj63Up2Z4diS2zhidw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA3waeLEJhTSGV8jx7UKI3PrSsr7x5nVp7NU8wxkzWojAiB3F4rQJctoiccuYIR+Ha8JB8CDVq5WzF6jkAFCGZVJeg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "packages-12-west.internal.npmjs.com", "tmp": "tmp/react-router-4.0.9.tgz_1494269375984_0.7114945075009018"}, "directories": {}}, "4.0.10": {"name": "@types/react-router", "version": "4.0.10", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "44ecfceab7c50826778c666340c71b669d645ef5a6a56d94255c7c5c8be8d6e8", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.10", "dist": {"shasum": "8629cd11288e0c473e8230f347a064f919bca836", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.10.tgz", "integrity": "sha512-A0k13gVxLZU+dqv+hrHkaCJYjoha5pzRnttU3GYqw1nG0oJxRHyES83ZhOQvZIYfj3LH/7Ox+pV3S10rwxnPCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIB8Fn3oU2X0LNCs/sYLKErKFwFpCwAl4NlV0gYDx/wtYAiBPhK5C89+daLwyuh/7NnPaujuxl61Bphy6LM+NLJ28yw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.10.tgz_1496170304387_0.5616901204921305"}, "directories": {}}, "4.0.11": {"name": "@types/react-router", "version": "4.0.11", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "f9c2fffc229c9f7271e1e787f6c9762d8892988f064fd75ad7d193ef498fcd77", "typeScriptVersion": "2.2", "_id": "@types/react-router@4.0.11", "dist": {"integrity": "sha512-lwa2ml/dzYlg+kAElBfBGswvVtuN9kX8H8E0ag/h0u60I8Uw7zeuTV8DN45r7/73S+jeA0uEqybA7KYe0JEDbA==", "shasum": "de631d00477081c24a4668c10287ea5f3cd51311", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.11.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7vzKyCjlppQQHkgY6dEpPJuQIlaCu38YoiaRhKesuOwIgS+rusHyUeZiQpM3Z/9dDON935QYnRsQiOmJPISijhq8="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.11.tgz_1496293267335_0.33285696944221854"}, "directories": {}}, "3.0.12": {"name": "@types/react-router", "version": "3.0.12", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "921a1a7101bb6c0eb14652ed2371c6e2c11368c3c1bf4c5b7907dddac426f2ad", "typeScriptVersion": "2.3", "_id": "@types/react-router@3.0.12", "dist": {"integrity": "sha512-JxvLCFg13nLDlmKopsgiWvHASG6iYsJekdSgzx0qJcaRJ13NkUlu7dXqvYotti6KzCUu0fC/PsDwYlokfNuKeA==", "shasum": "afa9ab477440ae9d2efd4d150b043e67ce9ae06e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDcCsBpXSpIty9Gbh0R4gFX4MXFiOZL3Mo0YWyLGzGNuAiBCiOYVjqkOSDuDosGDoNeFpQWi+GwYmAeim52yvSWA3Q=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-3.0.12.tgz_1498239455770_0.3264870513230562"}, "directories": {}}, "4.0.12": {"name": "@types/react-router", "version": "4.0.12", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "3d695ad2fbee891c11facb6489ff609640cfac633985d794aefeee4ce60cebb0", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.12", "dist": {"integrity": "sha512-vlZXTicPMGn6nqiCTfK3Qg6ix0CFHClBSQgwWvtvmlwqildfPfBpY9aPQEki/w/rrMLzAApaAUaXzt5/W+1CKQ==", "shasum": "7ccf3433741393e22ede149b5cd733abfe594ffc", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.12.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIClZ/e+p8Sr/oqoYCkBz5WPZveyzfqF6S/r22+9i9f5AAiEA8625brhd1F/sMlYUnnsxEZPGvJywMAS62jNUrCArEng="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.12.tgz_1498239487701_0.16263955319300294"}, "directories": {}}, "2.0.50": {"name": "@types/react-router", "version": "2.0.50", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "3c14a5e0f570b5d79a5d82fe4da22a9ef7be0fc0f34693526b5efe0227365c5d", "typeScriptVersion": "2.3", "_id": "@types/react-router@2.0.50", "dist": {"integrity": "sha512-zCJuwnSNXczLSIuH3Kjs4t1WPe5uTBfzeT5riy51FI7qkl0+S0yf9BqrlkAX/fF+tBeZ1r4ZYSDKCtNrAetgyg==", "shasum": "5211f718d878677ccf5834f7773ea63c24f9fc2a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.50.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCTdQAIzRuSIvZrhf9lVkZ3DNE03OYCnXXJ6SV7amFo7QIhANZ58/iW91awoX8frRzuL22/ibzMtivVRmHcw/CVz72a"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-2.0.50.tgz_1498239488920_0.8971339070703834"}, "directories": {}}, "4.0.13": {"name": "@types/react-router", "version": "4.0.13", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "6bf6e0a9740a09ed37c17e4174245ec5a0819d2b145639d5c7ef88d40a0ebeb8", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.13", "dist": {"integrity": "sha512-O5Gt6Nvv29wnoGr/pvmf2+uEYsqMcoYrGn3k6VqiXs7arUaGhDiLhwkDynTgm22i0488/zLV+/fK9QGdLHhAPg==", "shasum": "60f11e4d224ada222d488244a0cca30079abf8bb", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.13.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmyEjbWzkh42CzHSzt7TX2oWpdokvX8aitLTWebeI8PAiEAkFnZ2n38eLMxmsn8xiz2Mxo5ScNO4fKPV8/KyEKZnA4="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.13.tgz_1499460828755_0.5635234350338578"}, "directories": {}}, "4.0.14": {"name": "@types/react-router", "version": "4.0.14", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "579df3776472e541fac75337bdc38a010c1f2bcf813dd8556d5dc4c6cd124986", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.14", "dist": {"integrity": "sha512-wbcqBGFv2KcBo0fIief2MJeE5e1Ph9Z35FzWPmadNMzd5dwpFGLVxyDQiLc30u3ehNrgG7JlQcFYLe5YJnToyA==", "shasum": "dd650f40bbb20ad1fec26471707665908efa9e9f", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.14.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDaX9rEfLytJ3V7GpnsQ3P7fDiuq6MdRBsCHapEAz+lBAiBgzOvsFCDAiOG8KrPbTjQYCu7sKFfP3lwPgkOGG67+qQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.14.tgz_1499716045935_0.44163372553884983"}, "directories": {}}, "4.0.15": {"name": "@types/react-router", "version": "4.0.15", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "018cbf9c8b6c1226363ee8a44f5376eada379077d9e29e6e94b079ab5fc847e1", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.15", "dist": {"integrity": "sha512-E3rS+jFk/zMcoIv4caqfic3mcIoQImpVaC9lNEgPqZttjocgLvjOwT+peBNbUCLPBeNwaTdglZZeZJmowb28sw==", "shasum": "be8aaefc20afd968a5dbcffcf09060e6c2e8746e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.15.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEkXDpIj08gx69fAMLnwND6XYjmv/vvsF6/2b4HFLKY9AiBOsT/6q+51+oGB41hZLe7dJx40sVL3jQQUjju0Q0aTaA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.15.tgz_1502921358456_0.09887310210615396"}, "directories": {}}, "3.0.13": {"name": "@types/react-router", "version": "3.0.13", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "1bdb0101e8db8e6c1e1d0a7d95b00173cd85e74873defd766faa273a05b3a8af", "typeScriptVersion": "2.3", "_id": "@types/react-router@3.0.13", "dist": {"integrity": "sha512-yPIa3UH7C03M73Va24EMenQIHJfLmm1HrecnBbK9kdMZqkDwwCfZZe+TR0aZ2O3YBDRZtF9ahQFmXqxC3USTaA==", "shasum": "493c2f0794313a37e4698deed9b816b2ad239716", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.13.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGmElShNEk/8yI9Bf0zri/A2KCuPPVlqMODzZ24dWta3AiEA2tCK7ze7wO2yeYna7q/MeWIdqxNxihKrfP6LWkotaBY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-3.0.13.tgz_1503510889576_0.04047339619137347"}, "directories": {}}, "2.0.51": {"name": "@types/react-router", "version": "2.0.51", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "16268f91f5326c22a68fc10d071ca668f3f72780f85d2bf3eacf6facde1a3730", "typeScriptVersion": "2.3", "_id": "@types/react-router@2.0.51", "dist": {"integrity": "sha512-jKMs1EX/juWrkBvybkVEsvinuVcqaWo4HRHDDUeKVRMosOSw2kCkZK+6qzxsbf0K3ml8XuBE+bSyxpdviuWU5Q==", "shasum": "d6a65275ea42de619f2f12492aaa01a638da3059", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.51.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCWZ0WI4nMNOIXCK2ERv59lQq9bOd/yK75kd+XftDv+CAIhAKJFuGmpky8S4q2otWvvW8BFFQJcYT195+ZIsTP4tn/W"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-2.0.51.tgz_1505769901279_0.3492819769307971"}, "directories": {}}, "4.0.16": {"name": "@types/react-router", "version": "4.0.16", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "ff0953e8682202c04637c3816e07f653c5e1a6454c024b3a887c63001db88c12", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.16", "dist": {"integrity": "sha512-SPs2qmry3gnPXxRrnWFuipv5qxcISz0Msk/2qWemvDW0DSukElvCdeMc6I3AuTkeZoLxlgNm2yKRJoJEE3PxtQ==", "shasum": "5153d07cb138a30c50aaaa33c8d654e8f321ae13", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.16.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEx4eGCP4HnW/o/0Rlpna4AjGWJcu3Ls0yGQsPrTUAK4AiEAqPb9tYTUfr/VdqP6Qv5dAoy8XQMIQMVI5Ggre5cqqvw="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.16.tgz_1508515377747_0.10141173982992768"}, "directories": {}}, "4.0.17": {"name": "@types/react-router", "version": "4.0.17", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "1640219d93f0136d3b093420cec08e7a794112c032975b5cc45ebfdae8d1b7c2", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.17", "dist": {"integrity": "sha512-dtA3sA2erSfj/zqTg0OoF123DRkWeYGTNno7eSPtCP9xMdbPFqRD42etfjLi72qNOqKut9pgxgBz2ebEx83m0A==", "shasum": "1032c800ed0da188d8a3bb38fe6afa8c917865d5", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.17.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC5jObhKSiiizi+USZK2fIhxZZLLPLIuh/J2PPrqDyvlgIgJiXfgxnNb5Kicf3kmZfBQ0gZ4dXRlI2teWSRnHPD3ps="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.17.tgz_1510221569078_0.25341301830485463"}, "directories": {}}, "2.0.52": {"name": "@types/react-router", "version": "2.0.52", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "f102f46b029671c915ee50c1b5d40813baea8d1a004fae50cf147567e2bbfd87", "typeScriptVersion": "2.3", "_id": "@types/react-router@2.0.52", "dist": {"integrity": "sha512-1X+zfifopktAwcGMr3jEKq2NHov0o2W3zBjEglGmHB7B7H4D8xWRrdeGFQQBh0u4NulvQJCJC94hhfAu5ki8zQ==", "shasum": "47643833f44ee21fda49fb181d19953eaac4cd79", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.52.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDS5btF1FccaN7V3t0NZhR0/JKLUkMbP6Fcoc7dDItw3AIgKStLrd86Rd5Gxf+5HGVPH60PxoA0WlDfefLtYCdceXc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-2.0.52.tgz_1510684739381_0.23475561966188252"}, "directories": {}}, "2.0.53": {"name": "@types/react-router", "version": "2.0.53", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "e273809faa048f0a04825757cb3bf3dec121e3884047900dcd1cab1d34c42e2a", "typeScriptVersion": "2.3", "_id": "@types/react-router@2.0.53", "dist": {"integrity": "sha512-ULmEyDz09Kch3oEurLGHzGhxE1ddpp53/s0kMkOrRHwrV/EyXrHYlUXJTF28p3+86TTzT+21UF//9shhY3TCew==", "shasum": "c8beb3ad955e7e44bf2c12c24a58fd8cba845b6d", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.53.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDqpvqeolPAxTtUDMCHpn0jaNPhXTPqUPdreLH+1cbGuwIhAON3Ruqqo3Ed5MCw+eNrZSLSM40Ctf1L/1BsfdU7qPun"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-2.0.53.tgz_1510685326612_0.6049240101128817"}, "directories": {}}, "4.0.18": {"name": "@types/react-router", "version": "4.0.18", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "66165b8d82a61d0c2522b671788ac21073f5940c02f5a7f02dd9d4ed9c100559", "typeScriptVersion": "2.3", "_id": "@types/react-router@4.0.18", "dist": {"integrity": "sha512-8GN2wM+T6tWhthOsAxQ4J9q5qY/XDku9wt6j0A50NgSTtsRAnTpfNZHeVN4jpBWs+mgovuLYRN6kKFf9zMURvg==", "shasum": "a990a89e797b1c77dc8651774b95649e642fd374", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.18.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDSExNmP+YQtUoGyTxYNL0mDBmvqZsnNJpf9120j2FdWAiB0S3U639+o8/mVzoBmsslDUD5tjlLJZEhSfFudF847cw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.18.tgz_1511290960114_0.4811568143777549"}, "directories": {}}, "4.0.19": {"name": "@types/react-router", "version": "4.0.19", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "a6913504d4ecb2bbed7efb5f18bb1a8e926d2ddb49b55fa1c218f8cc0e021ff3", "typeScriptVersion": "2.4", "_id": "@types/react-router@4.0.19", "dist": {"integrity": "sha512-EBqx80mE6L4RkNlPqnjmEpdcpfP4lZelzK7dSwBcryT9sChJYUcrrkBZ6DqTjGY5RzCheRTQmPMnkNwd/9t49w==", "shasum": "4258eb59a9c3a01b5adf1bf9b14f068a7699bbb6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.19.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEb6QMYjQo61+busrI3djvclGmzxYi//VkXNStYSsW25AiEA5c3GrNmZJ+OhJ13a2FmPwNf0C2hp9m0Bs29Z+yd//xM="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.19.tgz_1511804943024_0.9159312071278691"}, "directories": {}}, "4.0.20": {"name": "@types/react-router", "version": "4.0.20", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "da88b63266a309d1e719f76e57aee615dc7e10cdfd71ac067ebf9e41e371fcac", "typeScriptVersion": "2.4", "_id": "@types/react-router@4.0.20", "dist": {"integrity": "sha512-6vIB5kSSDoFQE4lfPDD8VYvhVdx4/FtxhYcJPGA7Cz/dANsGricIbMhusmZed7WMiKJBItfmcHT8THdD2Vw4hw==", "shasum": "3404f54e44bba2239ea4320ea701d86d92f05486", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.20.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCxIteF70/SwLknRV8r7ODnlYaitG2hAkHnuemBGiJkjAIge9MNGs+Sr+yEkS6x1//YOU/lAEpaujQdai5b9D3BrrI="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.20.tgz_1513781490456_0.5715283611789346"}, "directories": {}}, "3.0.14": {"name": "@types/react-router", "version": "3.0.14", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "e27eba4eb7b5dd842d517c936e80b008827fb6d5c908b369d1764faf84de6a2e", "typeScriptVersion": "2.3", "_id": "@types/react-router@3.0.14", "dist": {"integrity": "sha512-CZMh3GkyvUpEOmDvV1s36KUkMDMDUi1yFgXjl45HxtwOdjhTI0f5iT7X0a62xanA+U0P8Yp6L7B/1lL1TA7A/Q==", "shasum": "e21b365db664f72381c27ff8dbbc3e7dc7a11659", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.14.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD6YsYEGa7y8lWcjzXkGI21ghS49yCAzHAoMPQne/sJPAIhANiRmHQa0gxYE1WIKxYPJ3K4ZWegX3RybhYh/xRYDjPK"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-3.0.14.tgz_1516818968468_0.32557154912501574"}, "directories": {}}, "4.0.21": {"name": "@types/react-router", "version": "4.0.21", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "a491cc530e356ad75551b62b62594793b63915469254eb25b875d926020b34cc", "typeScriptVersion": "2.4", "_id": "@types/react-router@4.0.21", "dist": {"integrity": "sha512-JxMAzk8cWt4v093Wjw/Fiz4wcqx1MhhLLPy01C7E7cR1h0COkEiAvmqMo7/fZy8qIPZ9VmpcaHMIkQywv8rFXg==", "shasum": "d6c7ea3b45dba02eb8f869629f3b7d7b6e9a7938", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.21.tgz", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHebnMsCbprhNPCtje3OMyfj2H0lhC888iPxl1Qskx4AAiEAx1iF32GppkorK/eZN2F+maC+d0x//yOk20mthF+N70Q="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router-4.0.21.tgz_1516824520201_0.2866075853817165"}, "directories": {}}, "3.0.15": {"name": "@types/react-router", "version": "3.0.15", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "777d194d07308858176e2a9646e2a503e6464892c7024e26ef1f53bf924471a3", "typeScriptVersion": "2.6", "_id": "@types/react-router@3.0.15", "dist": {"integrity": "sha512-wEnsWwUL5fMWO3txfkh2Js3rIObaDdEcOu6hdVRYz7YXzIG9P89jG5R8PVTiH7lXSyo6+/OamNHWPHtgkB9mhg==", "shasum": "b55b0dc5ad8f6fa66b609f0efc390b191381d082", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.15.tgz", "fileCount": 23, "unpackedSize": 16676, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCNImU2tK7Rip/NidlJJiCGVthhiRw9CPF98ZWScnrB2gIhANstL5DalwhYu9H9Gkt4bY9ZtAnTilbFW86k9C47SeD4"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.15_1518469587238_0.4826197177525766"}, "_hasShrinkwrap": false}, "2.0.54": {"name": "@types/react-router", "version": "2.0.54", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "79c5258562e1f88f3030432d10aa8966ff995607f12d5706b34b2b3aaec77a7f", "typeScriptVersion": "2.6", "_id": "@types/react-router@2.0.54", "dist": {"integrity": "sha512-MAct634qnTaaeVXg134MQIuUY+yISPqs0BzgkbTZpSvztlCxlLtK7spC8k//l8oUMFlxNt0aUuqZvTOxdXp4/A==", "shasum": "7c42f8853c23399a3ace4831b1e56c7b822cc1e9", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.54.tgz", "fileCount": 27, "unpackedSize": 20040, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCi9Q0RgCXmY6vS43M4bS6w58DC7zHRnDrb63EpEXJ2dAIgH3r0gjh2yasKqMsDjrvOdndFOIBfkc/F+64+OzyJrWc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.54_1518469611436_0.26251921121148003"}, "_hasShrinkwrap": false}, "4.0.22": {"name": "@types/react-router", "version": "4.0.22", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "8b285d6d759a790f8be3c6344227157eebdebc63bd12a052b0a938ab5efc7430", "typeScriptVersion": "2.6", "_id": "@types/react-router@4.0.22", "dist": {"integrity": "sha512-niqN3wGnnURY24PW0soSlMe9JxZU+H3d6zWOax99+Cw3QZE7mlIdbRbD68S5z+9saS694IHTRrdqXVl4P0UT6g==", "shasum": "2b97336eddfdf5886973539539b8ce7037cf629b", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.22.tgz", "fileCount": 4, "unpackedSize": 9392, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCExCYsqpe7/XdkysClXGTqzw3GgwM4a5ueKVheV3CU8QIhAOO1A2kYEaGKGpONquVsJ8ALwC6QtJ0c/rbppH7AE3ct"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.22_1518469662442_0.23212160183191277"}, "_hasShrinkwrap": false}, "4.0.23": {"name": "@types/react-router", "version": "4.0.23", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "e5c4e8b004a9e37d540dcd745c98b942e420e0811e52ee66d792ace4b18d8ce5", "typeScriptVersion": "2.6", "_id": "@types/react-router@4.0.23", "dist": {"integrity": "sha512-o1yEm2Eimw7kwzJSamvBlAPXmhH14zL+AQOCgviGthMcqFVVmhZCv63PgxMZfq+PcfMIqP1O2Wq7BU94IXyVfQ==", "shasum": "d0509dcbdb1c686aed8f3d5cb186f42e5fbe7c2a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.23.tgz", "fileCount": 4, "unpackedSize": 9641, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC8dUuJJNDpYEPj9Sk0R1oSiXKg9zRZgCQxRF6gxoy4jgIhAME99nmAF1IQXT6WK47MsvpDqqFB8lJKozI75AIHRMqM"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.23_1521156023236_0.8136904668909535"}, "_hasShrinkwrap": false}, "4.0.24": {"name": "@types/react-router", "version": "4.0.24", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}], "main": "", "repository": {"type": "git", "url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "3e0100eb567a27ff64292969a7cca33b2a763d8a8cd814517a64bffdca5f7d16", "typeScriptVersion": "2.6", "_id": "@types/react-router@4.0.24", "dist": {"integrity": "sha512-uYY+o2+d2+ZzcIkkzwkdybKnGTbH5ooBuHlkPnZMl2/YlmsVgwAwCxS8HYDle3VdnYxILZe9dUSa1mchg6RPWw==", "shasum": "e378e84186522216ae36cea12313965e190cd55f", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.24.tgz", "fileCount": 4, "unpackedSize": 9734, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3/1bCRA9TVsSAnZWagAAyyUP/A35jji/ZICPntLCHanh\nF8mEaPnPnc3JvEwasQDw2Whp4TVOi03jue9VlHwQSChuoAqmIDkEGH6ho8kK\nTmEteOQSDI9B+3sc7UhgE45bxdeH7cl9CkFpGNKDzsZDEetJjTkMrB2oenkf\n/zrMIT4cOq1DhmbiQW9gxtYOsWlG1kz3HW+G9YZg64tMSmPynRtGJinP2TPi\n4gWmwJznzYX9mouFakErRiKUZaDNH0A/lvLxne7IKzIDb81xrQHJVX2iyzhi\nNqfITXGxPiplmHylxGCeM3/hg5yoj/ZCucaMsDJb5G9B0QqzxUH0iN9PVfkm\nOf//o2zV4qjjWyN6YJg8CJ+KibQ3/dg9PQ62du9tbvU5jpwaGjZ34JBL1rYC\n5pTEyzT0BHg+SsBwUI4nr0mV6OLCQalG2/Zi0sXN5v+P+pByl2UIS6znm6Vx\n32kCxUQRujiFTLrnNxyOSVe4sTDURpgfbmNSxQsI3aKbk8qqfdOmUvzIxty4\nj/Nz4mbTOYdrbHEisEZfCpSNkAnQIR2e4eeQ85pfqBnbD4TgBZr2IsIbNGth\n+cWH4RVGSzaJYQAUovcSQOZsYLd68DO4IJj7hzbrwSPxx/sI2msSFIpieNZA\nUNnKfvDZKqtY63NpEvrTRxYku76w3CZRoJ20uckZoTtFxxcD2aZSnQOoHlUR\nf6TG\r\n=82Jf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICiq6KQQJp33qovNAV4jzMZcVCbaU3uCi3WWy0zrjTtxAiEA8l7FuAKMzTRdTffcSUEfuzZTFWYgZ+EDDcbIas4NzIo="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.24_1524628826615_0.0036069581614921464"}, "_hasShrinkwrap": false}, "4.0.25": {"name": "@types/react-router", "version": "4.0.25", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "5267544892882e99c0d4c6074f7917fa1f848b488a812863ba340ac578d5fd76", "typeScriptVersion": "2.6", "_id": "@types/react-router@4.0.25", "dist": {"integrity": "sha512-IsFvDwQy2X08g+tRMvugH1l7e0kkR+o5qEUkFfYLmjw2jGCPogY2bBuRAgZCZ5CSUswdNTkKtPUmNo+f6afyfg==", "shasum": "1e25490780b80e0d8f96bf649379cca8638c1e5a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.25.tgz", "fileCount": 4, "unpackedSize": 9992, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa8iHzCRA9TVsSAnZWagAAkggP/3hS5fNsjx3XT25GU/Hr\nhw5TUMRNQESZGE6vjP3ry/tFeAg7Rdvq8ZXvsAb+GljW2IOGqHH+EvZB5ZrU\n7G0IzrJDzC9b8Y6YIiwWp3qyt0J3WF25P0CvGa0ygb0DrNCQcmmT42dDlEPp\nNXBX0/vE7/LdsK4xmEpnII0VcxeQW1MOr4A9sLi2ErhScKk8i2tNxx72eiWx\nVPaPcrtycbEWL0ZqOcMYvj7BXPqQvtXo22c6yqKEiw5E2bGoiWuNzrlvNwDk\nUil0pIq7cAsH0B7fwNvWa1lvf77x+/K0VR4LC2Y/bqhq//CoqJSVytCp9yTV\n8OgjzKIGbFKIeheenqXv9u1A8xpDVwWvJC6FBnnnGU3BbhTRiPGfT4cfAS8O\nI8p47c/Coq+zy5PIXzUrdi+uqjm+tLBS0rHgpCJ9eKpidJnZn197jkRVt24I\nc3/7KcN8Bc7Uq887Dk/VzhHlvqnjM2b0sXbHYyk133UkMnmwT9FoX/PTdu+e\nwwpPVrWh+DkVztsPWBVFJGISJOu/U5p0w5gQzqnKkpXNwhrrO3flo6GtZZU+\nx0uqEt59s/WRgC8bZhaJsoX27sNCGd2uYAHiuiuOwB/pYaG33CtzmlHqjbOZ\noVfEe7dFbjLkTLdVZ2aA/ZL85RTZLHSr2rG+qKMzMhZe8/4gDJtW6cqWt468\n5xBb\r\n=N23e\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCuAUD9M1qWwCJ9oQLcSUCo4j2jd5uWecaayZx4ABnDLQIhANZxQ2V1mJIxX0VjpfP0SoMT/zRm+PdHBCSpxOwMn6gS"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.25_1525817841788_0.4065385014290286"}, "_hasShrinkwrap": false}, "4.0.26": {"name": "@types/react-router", "version": "4.0.26", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "c13f3a3d3c181ad1fc92fd0f795423dbee2311a78eec140dec98cb60d6743876", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.26", "dist": {"integrity": "sha512-BBL/Dk/sXAlC0Ee4zJrgYp8AsM5ubITRz8kX2a+4BBkDh9E5YE+4ZqzrS6L+ec6cXb4yRHL983fEN5AsobOIHg==", "shasum": "4489c873642baa633014294a6d0a290001ba9860", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.26.tgz", "fileCount": 4, "unpackedSize": 9900, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEIr/CRA9TVsSAnZWagAAcYQP/iD4YWpwEU1GkyrVFs/E\ndSWyrAM9hqwlOU538djvtCbuK5mM7sUS8jmwGL5EFrP2QU1OgqSm7XDHfRkP\nncp31Cgoom4mjMGA6RjFncKAVZ6XQ6yGNsKKSiiletEqrM+Qf4oh8j9VSQDe\nxGhKgo9XNBNKCIn8RR2Sz65fRZDk5EoX5Kc3GtjNJIkiObXNrfvSZ2VRO+w9\nRla1+58kzo0/Fc3wJYPTqx1Uvd4ZCj5DEArWNHLR3zOK2Ti1rBaZUQk462be\nCe7KNAAcSIRtBfXTH0Cp4vgF9AMrIsvy8A8oAFCzm5blc6Q3zCKy3/gCPh/9\nM5GgxqJuYk8e5xL4q8fyZpZYZ5ScdJwBZZAFKk0krqX36yDsgz7Qc9JdV5ga\nxJ+Qm8znXcLkkfNLxSglJRw7Qz9sAjEiiSAw/cPLn0qb4fpicC97KBGXY9vk\nUrd2VATYIotly75EmQKqSMwVYIQfqOe7yRuzApL4AJwDGJzoT8RKLqbKcTNY\n91WnWt+ab0Qg11yETWTuJI69ZOdaUxiF+ktkLMLxj71ZQ/3vOJl5efAhzXu+\n362EVHBY8Dm71c1+i23jj8B9dw0zp/e+kaQrkXt4Jc5DxzM0zBcipGEfKF5e\n6CYObTD100qUFYXL/fnezpE4II3S6xT5SeoP+JugrvZBBQwEVCDGzaJ27Fl4\nPdQ9\r\n=/ReC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAg9Cg6MLPV2S0rewZfndrwu7Q9nz2bH0qvy/02ZGPAZAiBdFMjaSPyteHlcjB3V8qDZ8+2ZiCzbkvh4k1BGA5H8PQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.26_1527810814872_0.42541333388018465"}, "_hasShrinkwrap": false}, "4.0.27": {"name": "@types/react-router", "version": "4.0.27", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "f3a808151f66a9fdbe4e25d06485736d8fb8b0906aea224778128833e9e38c12", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.27", "dist": {"integrity": "sha512-EqGMptbgv4IkwJdU/ozonsFiL1iESUXk57rA6myayd/bIgYP4/pD0cZJUpOWCSvYT7QLDBuDkrwyEgCqfMZfNg==", "shasum": "553f54df7c4b09d6046b0201ce9b91c46b2940e3", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.27.tgz", "fileCount": 4, "unpackedSize": 10266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbLD7MCRA9TVsSAnZWagAA8HwP/A+oQWmga+4YJEKpNeFF\n8uW9dJNyuiZjDAjdImu2Lni0TiCdU6F6VGZQvRrwAk0Kk1HWEP39s2Un7Mkh\nkKMNaeo8/LFDix0PfsJ+Kimp/lqBGKu9sbxsRwe//+UCHcweXXaeRUEAK8RF\nlvftvRjuluITYXA6k1j5mBj0ZJcJA9LKPwSNLfaxRJ9sdR6Yu3dpz7CFg5ra\nPe6SIR0LEh6dpLh1MGNlvLS8/ZDRT3WlX2dEM7XYkEnJYogbOYwbLqBaECQ3\nYbLJ3mCiU8b1o7yO9XOe4VhCQZL4MN+cge9wdmu/iKSEMHwrtEigPOq9Iftu\nN0iNhKbALAVFAM5rwOyuRe8mtzuF3EQ1cRASXn4eRIZLFOuIwvAWHad9nn+t\nscNWO9csQnYC6PBCtufgU5CPe5Ce+xxJNszHkUT87NZ7SzLfA0jBfSb8LrCc\n1tt/Z81FrlTmUMjYwXAjmOXg2TB4CLY5d+HCsFQOvPlBzwy5EkEAAtFUuurR\n/OC8ktxtk8ccOqBMMhegN30k7ppWqc/6JKzH4mzUCdw7K5+ezKqjlsQWd0OK\nIjKF2kA2SfDP6tHuB0RpVLkqVk24dExmndtnfUqdffql0pMJqmt6cTZ7pctt\nxrV/hRghXI67MuKyZ9JgdFQJBa47n5SeOjYwzjVGnX07CviwNkLMuwg/oX70\nHNdb\r\n=MMj/\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGbhO7X57Z0Aw+Vv+x29tDYBqKfDRSts9iJl84kkdxh8AiBX8vAr7WjIwC9Jx7JqOAzrJB5W6+Xe+AyRNdhoALwtNg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.27_1529626316592_0.8091535368860092"}, "_hasShrinkwrap": false}, "4.0.28": {"name": "@types/react-router", "version": "4.0.28", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "f3296ba12ff10598cb21d9c223a2e8dbde42af56720c859d66bdcbc7c43c2720", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.28", "dist": {"integrity": "sha512-h+AmzCSb9dlJZev4txupL6c8RXg3H9VSnvsQCn7M3VND+mVjBmV+xk1din/GG5Nc6C8TGnh9BDfjcomCU0IVyw==", "shasum": "b0445fc38613c81e92ca9ec0e08ab36697d51003", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.28.tgz", "fileCount": 4, "unpackedSize": 10372, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbOpAFCRA9TVsSAnZWagAAfBsP/2yBTv94mkvjcnnIuOZe\n0y+0/rr5FMQOwcr2zXWJziiCHdUA0ClmIBgf4o5mvTqpTEF6vNVMTfJxmvaI\nlkcD2IRsz70PMoETP1zVA+0zWWI905dSL5wW38HVju5UmejGRS2FiEt4r78+\nLC4X3Mf09sH+0GD7e0R7zS/s/ONASnIwRyhDGZpljmFaSNjbzhSMj6lHCMtT\nCu0QwAoL8sfdQnrNyuhTpVzVWwDhvfzdrEQSvr5FDa43Oxpp4fh6JEApwojs\n9y0CxIFXY6fXFGtHmrugRK47RS/bT3+opdZ4A2D9PWlojuM+R0oz3n0seuoN\njIKGrEH59Loy0KxNGf4sjRxZfgWonadNq58OKpwv9JeCidEOfAH/kks3toNi\n6K+xZH00jpZ81VraKdyBlA2L4CT4tfgIe7riMAD54GrVcVFwsRWoo7QxMJcO\nu9lmaJOMnxmegZf3ydv2IKfO3Nn/rljgkqvFgSdSKhhbTfg8s0FvK+LRlo6c\nWplDZdpC1IC2QzDh1EDNaCdcvB7qS6vIJuydFxF+VeV5oICuDMaRMkQRTXc9\nLbJ7HEqz1jjWu4d/opya7p3X4lF+evv8WgVMDTVuJ2QR/1VAO+5rXq6yOMdE\nZeDIS+bwLsamlxTWYz7CkqL5j8cEtN3l7TMYxmt0pHpR/DRQf181IKPOzp19\n5D0c\r\n=46Ee\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDSuD9tVer1aaHLw+OItiQPUbIBAnp4WQS89mRmyOMIsgIhALpwxdG+c0RhusEAsn77rD2mQDxhmmEhc14h8K3s0HXn"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.28_1530564613823_0.9551470913416247"}, "_hasShrinkwrap": false}, "4.0.29": {"name": "@types/react-router", "version": "4.0.29", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "3a26ad1d815ef5b79b08780ac7cd1494b11f7636e69c589f2049fc21cb79310b", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.29", "dist": {"integrity": "sha512-flrYL4pFHSMTuPL1AfFXRlxBQ+aJ+WxFVvc54EnKejKNQqaGpl6JF9h8rql+DTseXqdthPg+rVXYexn5EKlniA==", "shasum": "1a906dd99abf21297a5b7cf003d1fd36e7a92069", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.29.tgz", "fileCount": 4, "unpackedSize": 10421, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTON7CRA9TVsSAnZWagAA1vQP/A8okSHA+w0+BiLiw7Nl\nIQkjtuCrT7zO84wgY/LghQOtvwo1dBzf8wBhVZNuSpeYsWuHl3sgqYcesjVJ\nVHusFV6Y8unku415WLOm9qyCK3z/o/txynGcd3qSUebFrJMmUIgGQUohZrVD\nsBwGXgSYXYFMGI4kfxCVODM1cbMcCUn2/IHTp83BeRrA4ztHu1GSDuOlSeIj\nyiDxcUZRJl6a70zgkthEb4GGpTp5Yq2eSlgzVwSId6S57TKPA9KMkBNhdktN\nWW8KagJ7LG9WbrOMH9D6PiUxLntBk5QzOgCwv/jd66aFSTfRUv4Xwa4JHWlP\nF9pJhuyH08X1XR9I7rq3gXqnvnkTGWXPJlMeMER3bGEadCmmRXo5rdcCJXxy\ni040M1nnsHMK4D7XbOY7u3288qtAcjg1W3eKZXRP8d3nI1sC0xOLvmCcFieF\nQMrwQuzhshMFSkFVg+tCV+ffxpzvro2BUApfw0k6glYmWJSGZYsYeCYL8JJe\nQbAyNMgkNwTKWw6/u/oQiYVY93G+HMHKSFXEQTKvzoq4eDN8Azdn1UtX1frG\npkaehgd1cmuXGRd/yTaVWXgP23tkCw+nmnJmk6adiEhM5ViUlV+3jKnL3Qjq\nJdgmjWTMTNzYcRAlMMNi5hxuDOT578qQgE80x0tL8UhNmftrcxg1YrTAzhkl\nMnqP\r\n=ETw0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCZBXkO+whdierfb5XEmrObw/vPllfnbOKqc+kru9BPqwIhAMbVKyRskUySWGvluoteJ6wYR+ec3225MvtG9yO6++u5"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.29_1531765626939_0.33272291428943457"}, "_hasShrinkwrap": false}, "4.0.30": {"name": "@types/react-router", "version": "4.0.30", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "dfd4ad9920ba7073a5680bddb68a505ad40059ff3527e6d7aa9f479854756c47", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.30", "dist": {"integrity": "sha512-zWt5RpWy7x7semeBIHqLZ31A1Tle0jb3R/KD2wguZqO43DjP4n8kYsH7UIpve6I4hzlSObnoIieDcp5qfyKwaQ==", "shasum": "64bcd886befd1f932779b74d17954adbefb7a3a7", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.30.tgz", "fileCount": 4, "unpackedSize": 10445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX52NCRA9TVsSAnZWagAAewsP/1oryyw4CcEP3H7+HaAC\nplPcRcYHgBDYXJBR/gFaBfQhJ5smSfdmHyYev7JMaJ0csHMINhI7f5/MJ2j5\nSnz9sWQ9sr+KMG8bB3oGdzanAEWOCkePiHmATBQY37ogFh0Ml9Kda1kuSnfv\nrS7FCMdLMBr7++4g2omHuM3pEEYkzLEMrUd7pujym7IFkFDkcDymmsXjQBCc\ny+7y74LZjljuTVLX+gdu9Scp7Y9V7weq15kHJs9aSLAGmiW0STHDVA5quAPz\n8f4qOl70qMoZGfvFw5NSZg/Mx3gCTZk8QX/bSgbW32eOqDU390EpaXjcEcYT\n/NyN0hI6ORxE/0HpUVkEApccPcfBUGHRIU1AjC6dKvkcTgBrFiZseiS/mCIZ\n/r7Y7WgmQp1+VuVj7p0p39tz48acb7z6aFy4q8Z/Hmx/HKO4pyJ8szRTZYci\nuSa+4aTKxBJt2FmOqhv77Aq/gUh2DwYY2bWRqW/ZEZh9AyZlo/8KbiR7skF1\nAjJ0mj/EEP9P6tN3T5tRmhkzvTongYSrwf86E1zVsbbWG08lrjHAM2cm7SO+\neVIXvgnWrYraIDA8jdiW24bramDOKmQbwIvfC1OE5irTeT66Umjlls20BIQl\nwHmXQPBcFL1PmGnXBqrYusyNCn4aItk1DIfRZTWDabEyVxkfJ8bA+o/TWE7G\nrmig\r\n=5q3R\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCFRPUXjMNGzy26ClS0d0GizAJGUOovrNodSzoaEVZRfwIhAPVfevpeGn2Af7DbRfSl3cgtj2aaydWrWFRIXRYw5xLx"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.30_1532992909752_0.5844740272300784"}, "_hasShrinkwrap": false}, "2.0.55": {"name": "@types/react-router", "version": "2.0.55", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "f9354a94f6083a9d24e1f0c27b10c2e1d20cb08e405011dc9710764f46c7fd90", "typeScriptVersion": "2.8", "_id": "@types/react-router@2.0.55", "dist": {"integrity": "sha512-IjjwFhtgOsv3ZwPIgmny07m2fjQmNk5snWaxIpUcDiVV4iAg5yBwM7OW8ue5F7HcOPfZeL0OCIZD/kFT+s5JwA==", "shasum": "23721a0cdbddf0cec914b6ca9540df4e39f25637", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.55.tgz", "fileCount": 27, "unpackedSize": 20032, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaMxPCRA9TVsSAnZWagAAFXUQAJpyis/q7kX8HmvVmN1J\nMzr0mZ1HuisL49sbn0UWNHGxuTyZE2Hl2J3E5QJAeBzTZPhZdwvaO1UFRYTu\n5633JuFmHRFGxJE1Jg8sPCn68PNZeW3E5IogecvGV5cJACpOHjk0TZ2z/oz0\n7lVbu9d2JvbBtMnPm6omFl/NehFKVb3mbbL/BR0Umxny2tTDm+4IhcXAq4gj\neLf22E6RphhsD6vqsf3LLW58Cq8uo+XoiPRmf/rh8wC4rtUlhSSRA6Y627Of\nkulx4fnjU6bceVTrFQj9vjDmum+rNR3NUwnXFABqlJaf/FUialc6FZA7RWBt\nfOosd4xQ+4vxqGQtwOH5Yr9AQQpNfWL+MOg9m6N6axzOnXsEpz/au+3LG7L0\nrOa+g0+fSSpvztRoyh9iaQ+lLKz4QgHa8nEU2zUNoItL+HPVEfSx9WFRW2CG\n1Lx7p6Kk8W771Sja0jymkOTmm6ILbZ8Q330hEINNsk7e3KXCUwQEVLxcXqZJ\nC2nr800IKZ5AcUfv9tRn8VQky/2CxgjaU/fK2VhkRZqk46cS7vpKS1cTq0KY\nX+BuDRidOtK//vC8io/WEckiIiwZfLjUx414lJowuWGYbcaX7+uMedLsDr2u\nEwachuscLdIztMwATTminMudVP8znYMI8a6XYt5RNonNJbdkd/DxU/ftk0Ui\nol0Q\r\n=hZi6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCBPFfd6RBQybVEni2jHouLIiWf40t4OavdAcFjxKOcawIhALd0FxQkpObTdf6h8hwpxlBzsoxnWjdZWxS0DdCCY+4i"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.55_1533594702922_0.09249404511046122"}, "_hasShrinkwrap": false}, "3.0.16": {"name": "@types/react-router", "version": "3.0.16", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "41ba736d8ecb610d9d537c472f828b55d6c403b973936434bcac569b389f4a72", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.16", "dist": {"integrity": "sha512-wtuB/UYVYi21WUtRb7+5QNt7ppuogH9sL84ib7soj7dRBu4d+AI5I5fjXycC7Wwsgu+Snq2XnScAqPLf8wrDUw==", "shasum": "f61afdce806a095c1f00ec154fce5712ff50ca87", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.16.tgz", "fileCount": 23, "unpackedSize": 16668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbaMxUCRA9TVsSAnZWagAAU6UQAKH5O9NhHvFaapU3Du1o\nLIsv0SG2IskYS+poo3bXZ9JI0VBGAr2ZJyN72QmG0rgy6k02QNsLPt3MfP9r\nacdMkzFc5NdS4w2wtzPKup0caChkeg2oc4lRHeY3UP37Oy+j1SMybEusrAKc\nbSSlybXjYJHywpoEukL0sNoGw18VL+FA5vJ/G8Yu2eeaa+NQOBVisVYQmROX\n4qCkRda8qiOxXjCiERQc4tVVBZiWKRznTrJfNLQgNErIxltnbTOS+BnjFh9I\nHSG0pjEQ2ewevvPPEUlipaAhpe8+LOBqAxx6q2ZRHILn5iSBMdBdPJpAuZqG\nSHWdu6wfqwwv52oHv63tC516gzUq+jnCJcqqyAR9hmRHmdE4kgqZF9o/WvlD\nTmi+dFBKH4RNJMaOGNjlFd4vo5Qi+1bbxYpxVL9AMZxhzaotlKzEORMH/BHX\nztotMpwLzJHJdrNXyr/itHSVubQ6lCM7inpfiIsNctrepGaNq9GrVpM/ktyl\nMOrw+jQ831rpbEHnLpqZYABbe+Y/amzLYkdjtNepVlAQ/PJkOnHHkK55g+zb\n1xBrqiHl/8TD9JwsykEDs3PWQdV+XvBdRco16S6pvAwaU2f018IcNC4c5J5G\nHYRX/m609Ep2OATvppumbb+cX73Z8nljZdSpGveokdxfMv50mDq016w2fTdW\nbiUT\r\n=mfGx\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCfB47B/NoZOZ1PpByEqDTM6o+bAADi4GA+rZkby4e9NwIgfwH+Q/JtOwlTAZmK9yuCXvVm2XD0MOQtQQS4haFd1mg="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.16_1533594707983_0.8991096662522473"}, "_hasShrinkwrap": false}, "3.0.17": {"name": "@types/react-router", "version": "3.0.17", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "41ba736d8ecb610d9d537c472f828b55d6c403b973936434bcac569b389f4a72", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.17", "dist": {"integrity": "sha512-9OOloEJwFwGBnbdYdXdMWMdbTSH32ig32RT9Ne/ZiVs1bRCR3U2HT2AQZQa+LATt0kYMHha3nhddcd1NxZ4csw==", "shasum": "8a2e3ed74bbd57354a7d8f5fb3ec6a5d693817c6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.17.tgz", "fileCount": 23, "unpackedSize": 16668, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhzghCRA9TVsSAnZWagAAkqwP/iRsw5HMTyaLwHMo+UaM\nGVPpjCNpKnB8QsBVIMnITpa3CKfltaUCS/tHX355srzfrheUVmZ2Zx8slG7h\nh0dXZvUbsNq98+XU/bc2zezf66ua3IMBWeRH0uV+0lIBTgUeaFNUuwtMOLJa\n+r/NkMffw1DC6Xi+95eJJdoE+tDM74tCMselkydZFOcmL/pV4zGAzcfUkKFD\nOmTQB6Z66ZV0rWoo9zrPugtfYOpAe19uWyFtXetBlnzZ/U4zMM0+iFi0arW2\nfSnoC+RhxY+EAe7oe/BKKKwsZdTJpDi5B2tD6hum/E9nh4AcGm/Ecxtx9Dt4\nTthtbPXFObrumb++EHUVPkLwGeiezhkt/ybF4/yJScmrmQlwykq2PCy4wgkP\neJUzJCCU9TIRsL95idPcye+BBLNwnb0N77ZfdUxq1fZPRT0N/l994gkxdIxV\npmvvDc1osZm+2uFcTvCO2+AdjkuWtmO2GbwATAyz3TLCwADQYJ4iVi7v6c6D\nI8jZbewtQNYSh/9lNxAYoqrVVTt9mvCZ6+RsPkmweEtGGwVRJsrpojk/4hPL\nLM6/9i2QcIycY5IXNjPYTRDLiRvv/vY+M7KtNIoKZbdjMVXcgjbRJtaQexo6\nrcQRJ3C+L3x2hU4XN+JRpXqrmttgqCm5Pbb8SLTHutMD9UK25FaPm/tDMiEB\nppG9\r\n=jUbp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHqUD1ODBp9g7iQvMOniX8DiXeeOAuhI1iGSxO/3GjBPAiAjd9GFhR+eNZhhIn+fhXN7O2cvAlMssEl6RUx9pavJ+w=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.17_1535588384257_0.16119279652629115"}, "_hasShrinkwrap": false}, "4.0.31": {"name": "@types/react-router", "version": "4.0.31", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "f24a9eb0a9527a25b73a84ef3a69a3085563a8d01d50283e23863d84797ada9f", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.31", "dist": {"integrity": "sha512-57Tqu1EDMgDzHhmIEjjQZHrc/N7/+GGv6CtH1wRTLmMIy3UMxX69vQoeEz0AmK0/zkf5ecfEW1ZX8DLVQ6Gl7Q==", "shasum": "416bac49d746800810886c7b8582a622ed9604fc", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.31.tgz", "fileCount": 4, "unpackedSize": 10817, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbmvb6CRA9TVsSAnZWagAAdlUP/2p650DR4p/SAISPHszw\nZAyRuxDRmoHnBuhe+ZR7AshjcqmwFCxft8NgQLxmZNGFAod2NDVBPmCX8/K9\nh2QmyDy3gqaPEU5yRIREMzLdRq1PLoKEVDFzY65qzygWEXy3flNRk8zvFo5t\n4Nqvl3f3PrmaBd5EgUouJxvzR4gWoOssSv6YPrcRjW8ExqswLPb1713SAorZ\nH3Wqosymiz0mohIvOVASQWjSZdLz/nlbcnNVIOlsfLkHRPF8D1X9xYlqOJcQ\nsXYfQFqqnaCRnd287iE2XF/WbCX8q/JkgbZvGIEHbxlCczobOuOS+Cohn8WC\n04jrhGUy0HlwQDbL0FjHkJul3IZI7SCFkbnO4q55CYa+jH//4tRWsmjcgCKr\n2EeGLiiVNb/Ro2HRC7wTznzGCUElha3pMXxLE6NfllOiU+1U/e5OmKzjE3JF\nwM/vy6R5Gfx/UX30qBJzGbCvscY2+dpATohd7YcTM/KgmC0U+KBXGP0M2b+G\nPJK9Q65NYd7/hjsCjY0s9+SzzXc7yitMcSzGEG3TVyTT+bDl7yQuED63o1ya\npMEXgf+/YCwQKMw9uOO+2BKKHdh1M30t5x62N6caic7zOZorYorGH0DFdA0m\nTKv83UvoeSxIT1xud+BoiFlpQ3MsMub8Dq8RWDAihdUdWpJXrvzxcPe0Zf1w\nEekZ\r\n=yAVg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID7kABBvAE0kvEwLyXERVknEx1wnhNKhPQl8Gcri9ISdAiBFES582VCLupZ+F3UrhqClcv6mqcqhRhphXKzReEnGzg=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.31_1536882425959_0.2315426246956782"}, "_hasShrinkwrap": false}, "3.0.18": {"name": "@types/react-router", "version": "3.0.18", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "180efdfad46bbd28a4da2fc41562ec6726002c2c240babcec3a44b53bc55433a", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.18", "dist": {"integrity": "sha512-s+I5uvI6c0kMrTAooZpoYVgX4SwAHvo+II/JYbHbL8udwMtkg9d0J8R1b9LUuY/ka6rFpwGOzfbRzR2hxx7HBw==", "shasum": "1cc832cdbfed5f516f5027e8bc51b03649eaf37e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.18.tgz", "fileCount": 23, "unpackedSize": 17520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJboAIkCRA9TVsSAnZWagAAxQEP/Am3q6XdguzbdRH0o8Hc\nv8NIiaWumInwxxB/J2gIV0pZ1zCO7wO02v25fITpz2lvQOIXkYbrBn/ST6t4\n9WVF5ePwOktDCHE/Ue/29nW1TsLLMVS8gKzXyakj1vO0FEWqL6dpsPVgG9YW\nXREZpK/X4E/tv2NadL6lORZELBn0QXe1SLeK+pxYu2JGT0LYIcWfPMyhf2ER\nJ5Uud9LcTFFhiG53GJDMsFy6LrGPZuYL3JuGz6QfQw03XVUGy9ySKwkDWK7y\nLBKm7C7e5uIF51HHOjTdBD1NNnDrs45OmekDdm5GhtdzcJhEu36ZhX/oPEjh\nEmEwRURwqZidIbOPfgvy6Bbvv0RAmspo6PITq7Ogu7TgXjx4PYlsUohbuLvE\nToe3Kj/VlXr3Rf7cP3GrD1kpKRQAmFM8SIpBQ5SGsVSngiLyFwXqNG5nbdBq\ncauc17YOp39/BuRbb0pyLRIfGaHHfaBhmRPsT1CbtLvQLePKEs/VUduD+NM+\nuA/SrAk1E3BS1NLv7S5TpY2xZ9/x77Q2P0LGduuumPqbavDyYhwSwatIeYlU\n60wVaChJlVayf3bQezF+3d9zjZYvr2GGtHDklss+myyuSwjar8RAxfZUxcpZ\nrTJb6Dd5R2zG2pERmD1q0KeZNadD9Tm0SJb04TyCVhPxgUJH0vSmgbjo/aC5\nXEzZ\r\n=FAEZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCjcZB/DqCChiHmDLOfOd7tLnGZ7INdiqHS5+gr+bfqYwIgZ1vQrjKe9uxoZW3/3kYXLAJwe4EFeSU8SioL4ozUYNs="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.18_1537212962949_0.20452080746120904"}, "_hasShrinkwrap": false}, "3.0.19": {"name": "@types/react-router", "version": "3.0.19", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "8af45623d27a02b1c98247643aae6584f2848f4dfa60fb67bd4d8c7839488a5d", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.19", "dist": {"integrity": "sha512-079+T/Elvlds2Nr2CX6H17c3JUy0J95EUzjFlcF6wissyOisOL23R/Ci7prugPDVT4x2dpGwR+fxpOTitUgIZA==", "shasum": "c7fc0d1feaec5fdff513337f203301de7a27b752", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.19.tgz", "fileCount": 23, "unpackedSize": 17404, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbtQTaCRA9TVsSAnZWagAAH6kP/imNLDzlqo2kYNse0TIc\ni5PH4R6WR8DeMhXuDIEsg09HQmT21pKmBzoc1InMN9+2BCEKPNpPp7DCQwkg\nT26aLLUXuMpOgtsXCMg1oyF2psnQaGAdYkI6tLlCAZGXfXmSZmxSoI5/KjRF\nnjIMpI/11t5Ixb6a9xPIlkrGeaa++sSfwHqqcscjiYqYQHW4oaqlKciqSeb9\nq+V0coJ+lEBpFjUT3gEqBQM+QAYKNnd4bJF6DjG/yaQlm0DZpsfRHjmZmbBv\n6ucyHOoT+2Egvy70YcgymJhwiV6Cn2FffCrcrXimTwT4NSJ360d0l1xm6l7b\nzwn++iBEE3p0UWh60R6hB8jsL4RBCXvnzSbBQzZbigmy7JlY8kzR3Gn1FIrm\ne+38uABhNPe0nG/cz+67gU+uoUxX/rAG35yIgH1TwSdL3+3HZ1jRBp/ybc3b\n0sNnnT9r0jp63348JmQHhIFpYu5FHAUvReBVAxMRxrTDPo7jVILNVouubtb9\nB1rPpWhz41MOwYyW9IgwHMVvK13uUymi1MTTyKpEQw8gVHP5349C1fk3ubmN\nIn89JDh6s3IQhE2MgY3ZBtFVy81v1yX2QG8amlp4O6QD+rx0LM6UoBC0rxJ2\nFpMkCMVAeq8DTqnmJck8MwOtoC7my7KoEZVOccHMWax6bBbKOcsuXei4dpf0\nXQb8\r\n=q29m\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDALDn10bMfInuvNiUamEY9jWKkOOKRB8oWigKz9Q+W4AIgPzJdQtxpvuwx80WWZ6cYccL6NBR6DGX1/8w8kKc9ZeI="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.19_1538589913814_0.271340118116816"}, "_hasShrinkwrap": false}, "4.0.32": {"name": "@types/react-router", "version": "4.0.32", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}], "main": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "bbe324f402c0ee43bdbe2a20c21f3475b54b5c21d8750526eee6ceb32428d954", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.0.32", "dist": {"integrity": "sha512-VLQSifCIKCTpfMFrJN/nO5a45LduB6qSMkO9ASbcGdCHiDwJnrLNzk91Q895yG0qWY7RqT2jR16giBRpRG1HQw==", "shasum": "501529e3d7aa7d5c738d339367e1a7dd5338b2a7", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.0.32.tgz", "fileCount": 4, "unpackedSize": 10712, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbvoE3CRA9TVsSAnZWagAAKMcP/2OWpHW7xFb3epbOcA/L\nLofZwY5SAavWHLx+AiR8uqm1jxcpsHU3drq+y400LC9atSH5NZS/WSpusogT\nurRERamd3H789JZooeM3qTMTwW0d7rOn30mG0IqNsnwm4TLObqs2SPlEtA16\nWQ0AP1OdGuJthNUYJdKip4i7bE5FjVRV76RYsp9uwv99kLje0aqHCZsi/iR2\nDs6/HESRD9V/2HUKOHlqmOvYM5BBv3Z5QJDc8v8fmenGLc0W2Z53MLDZ7wDO\ngltDBcVDeuIXu9xQcSXWEBUiR2HU35rHkAWZsXSufuAtPlQvdJd3LPW5j9pd\nxeA1OG3rnB3aNqkPLxCxR96R9+s3pc2NpqUXFKR1B5/JcauQtsS9tZhkbi8P\n6Arb4XD/TYnIxJsLvn/ABLdygIYGMjjYVyDobXczg96Cd/NrPiN8D4V7Ww2P\nWkYXHIXSwEfwnYXhu5h78xUUTOuGYJVZO3umyi+FPyKC9OhXpmk6YBnuyHBV\nRRNfOlmLNfXjUkoHxOtI2ZbOSln+ctO9HQiBgFgn0ATxcdiL1f4C5xOZzZ+g\n1FdwEaFkvZM+dq0Z8faiDZGo7IbPe4gCDxVN8pzwHXkV0wni8aOWu6Q1rt4w\nNrtBMcQi4pG1Y0cLfdrf3O8hzXj+DOec4nMIa20nbQzmkmezueFHitqxL9hK\nhuq8\r\n=VSAw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCID+fxsh9t5E72YAbBcxCgUrx+FcN7OCsk9t7/37PmZN9AiEA2bU/WeF72w4eXu4KRBOXVsyIO5TB056PeqlKUmaNMCc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.0.32_1539211575232_0.1031566319564694"}, "_hasShrinkwrap": false}, "4.4.0": {"name": "@types/react-router", "version": "4.4.0", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "ed5e508cd681756dfc9c300f89a2fbd0bf02f680b208b95f97be2c47de32d34e", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.0", "dist": {"integrity": "sha512-b16Fpy4nsxC4Ntu72+VAl0kYd1qrT8M5aRGBJ0D4RoY5QeGaA5VguPWu1If2+g2dLNvb8A4vAMQAcDgYdQ4L9w==", "shasum": "c972570ea7e8019f047c714e4e845710786837e9", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.0.tgz", "fileCount": 4, "unpackedSize": 10984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb2b/fCRA9TVsSAnZWagAAU+sP/joBWh1v/IZp6wQtETmN\nHzrrKMCtBcBhfeb5DMF/09UwXwr551dp9IVT87Om7eyylU35VaWtKZxBstnv\nD+AGgp8yodMFe/9EH1r64tU2NMqH/+BsRxh5Hkuoa23TjkAQ1CefOT5oiTxn\nxGfpu6M7DS0k1Qg0KhXOpwrny0XfSWfkOjS58/6wqP0MsLRWxJglp0a4WcrQ\nfbl0oU4byw1uD5Fa3U1aCQ4mt5tLQHbWmeLN7UAshZ4cQWE2D29oPIpPPIp8\noFk4Mg0IZVIMGOjpDNW1+NaG7zcvtMakUdUI1y2C64dyRxIe3AvGZ6h+qJOL\nU56H+vEW2mtuxtqjqJAsGlPqTMYl6wMpR5ZMsXFymcR4eW/iIKxvP/6ZpL2X\nrval6ic5zxs+LJ7sSYzI/vUng94Pm0L0pUylgQygx4ppDMy4pVsgiBmbDKGI\nCjNdDRSMnJEz/J7fh20+xCFpVjxc8s/kZpuAje/n6E7+AVLRyJHIgpMxVMTJ\nI0fi+5hXI33uYEsspyo3CHW7wiwd8q6vFKmUZvv/0DJLN8LP56Zc3Rhuix4u\n++2kJJybDrFpxwORL92tpDzFsTA8jzxGs0yc9VkZwW4yUIZCOJFvQJLymZtb\nQfzTUDUk+kT+DKCdc1j6yJQFRD127FAIrKYFUcidKc0Gb2Bli/FTvhYRCbya\nsWbc\r\n=0xw7\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCi9SE+zFLjTD0zn974JpdnsE5E9L7b9SIUaFlFC8zXvwIhAPzABW2lBacAeglirUVhtkx+0GNGzIcHxJSALojrvSqM"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.0_1540997086782_0.9851543331409249"}, "_hasShrinkwrap": false}, "4.4.1": {"name": "@types/react-router", "version": "4.4.1", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/react": "*", "@types/history": "*"}, "typesPublisherContentHash": "4da425aeb2ec915342b05f4094fcd804e275de4508b59ee92139a0c2018e5c45", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.1", "dist": {"integrity": "sha512-CtQfdcXyMye3vflnQQ2sHU832iDJRoAr4P+7f964KlLYupXU1I5crP1+d/WnCMo6mmtjBjqQvxrtbAbodqerMA==", "shasum": "c875dfd0b6fe00efa463eb8e5de8b5f74644b3f5", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.1.tgz", "fileCount": 4, "unpackedSize": 10998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb4pWeCRA9TVsSAnZWagAAL88P/2lC1Nlv0S34oyZMepz5\n0qW9L3A24g+oRCGrdoplyNnvLJfq4QyW1QMOafX/Eoq2LL+g2cE4HAFp0xBq\n9Sv9K1+Nmk5j1dXClkr67FCCd5Vq/eQuYgSFE/pYhqp9uH4yzMwY39xbPiDt\nRAbnJQ9VB6azGedmyfV33OvKa9KxAvpXWKYX+fdhrOVrPlhVP+wuaezN9wmH\nMm3WZutlHM4QgozbtILFZLMA0kwaqJSxUYxqliRv4QvHDDMVQi4G2cb/b1OU\nqpTbBNG14NVi6Z3hJdPmbZExKnluvMcEIyYuusFGgBJ89/dg1xGMuqP+qFbG\nJRiWT/idjOg4t/hn998NWuIUHAU/vwbtDzLtKhY7RTGCFci6mL8psV55JG8j\nw+GZ+gdhvaenQ1OvJNQGTKgcE5Z8UkjTmVUpSD6k+A78RlXRkgqESDNhXTui\njJVab0r7VzRPaOW9nnoPdVbqRi3CQQdpHuf2M4IQcXStdWZShED30L3vev8v\ngC0KYrvwLJeXs4CgfP33Vo/vfLPc33o14fPz1o8xJkChNqD5j6pXD0rR5KtF\n05ippPifALpApdA9Cdbu5991PqejdBE3X9FSna61ycuoF4PGRiFjIlfo5Yg4\nmjemrDcmiKk+34bZ5Se0NPvnnxLGRQoNxtPcrI9H4i1MhGAo9U05qesg1/1T\nDzgP\r\n=JYgM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDuPzJXgkA9UGysC9wPjm+L4/xq5M4/Rl0vSk3Ndl/EAiEA5vkURO1R57ICI1PGq0uc+7z8faBkUbLl/eIpaGUY5sc="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.1_1541576093917_0.5223182876277621"}, "_hasShrinkwrap": false}, "4.4.2": {"name": "@types/react-router", "version": "4.4.2", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "722799158ba3cbff1af3883d2509c5619ea438342740189120a03315502880ad", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.2", "dist": {"integrity": "sha512-EkRC1pDzrDwZ0WRCtjuAAmBGuoKyPcnO5r7mzDnMB8E+R05VkDanAF25ONaCLd3TyM13F0bHOpKcQPWYs+gzhg==", "shasum": "3436c8f825908be26137c9340f8690ef673dbb09", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.2.tgz", "fileCount": 4, "unpackedSize": 11019, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcD2TPCRA9TVsSAnZWagAAYV8P/iN0N4NFrZdcziKqMSmk\nQjD4q55mkY4c0AqQacBcPjE9xHFLj43nWx7VYqWDeNlwmZnClodu3+aUthL4\nJPEFuBp3GY0XuE5fgA9GAKiM79EW6BF0dFZQSsFaxceSJSeXgLv+jHWLs5Pj\nN92NEH4IAXIRaZZ2WERfrfcHXgJ2j35vb0+4rx2SILDKRyMqmI5RY6QSOax4\n2AeSSS3oOFSn14V5Ok4RUqFFjzBWd1cOpaRpONd1Mb/QsoUMfQWKz51tk1/E\nT/+bbJ0lHwJjCFrFnkk/4p+y8M2ddm2rMSd1SA4QT6VK4o4SdgTu4LadpT6/\nC5RkQ2AmuVEOX0F9kaZ2P0qsWZiqrFB8G1tQpPMrp1/2mNhZv1PgIM37aA1w\nQqsF4lsrC0Inr5PxP/hSL24XY8Tzo4vqrqSCWvSgZTv7SIrMmYLaLNQH+cPL\nCif3Tu7eXTR+MJBX+merAoqbCKyv+Dht7kL9i7v1CeJ25s9zAy5scclvP/XX\njQwvv6PWYw4mw7zOXDmz/p2QiVFLkiM95pkNwJCsLJ066+USsu/DdlRFhBvB\ndE/ot+5nMJ8FlKB5NdBNDQMUX66bNsoxm87+1eHWrdjYpuBMjEaQRh/UwmRx\n+Hq1OAkgemQaMXqqh0RU+1owCJf2UlQyWggK4F/uDhZ0LfI1tNvLB8OvSfpY\nhg1S\r\n=UZIU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDV5y9BDNu2HHQGEx6VZEyX9wEGSrFLyxlqpEoDQn2BHAIge5vO1lByh+80xrwwNrzxs63WlLOkVn9a/FLTXyI++YQ="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.2_1544512717802_0.2247667728824012"}, "_hasShrinkwrap": false}, "4.4.3": {"name": "@types/react-router", "version": "4.4.3", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "1fe4b3b214111639b0c3c2ffdb6816272b94e0c269aa7564c1b32a47c72266b0", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.3", "dist": {"integrity": "sha512-8GmjakEBFNCLJbpg9jtDp1EDvFP0VkIPPKBpVwmB3Q+9whFoHu8rluMUXUE5SoGkEQvVOtgJzWmUsJojNpFMQQ==", "shasum": "ea68b4021cb576866f83365b2201411537423d50", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.3.tgz", "fileCount": 4, "unpackedSize": 11213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcEV+OCRA9TVsSAnZWagAAU84QAJjuRh7Kr+hlIJHik3gl\n+K9RXCJPZLZKHWfRdGtLeIdxARoC98OpbtOL/s3SaTZzlY/8bMeDL6JcHzcK\nCLk3JTwfE5HmmT9VYTZMpw8jkcC8L9vifWZh+XFCizBB1pQEIyow77e8k1Mv\nQn0nXd7Gl2mPYhrmMQo3YYHa+QHjFae6Yc3SjXXhOIn2XyqADH5cIv69TqYb\nVUD0fSlYDwcM0aZWc99TQy+39ELryotbDqxNktCE+OTJPDOiZfZMWtLXyzf/\nDcLiDGou0/mvbAfVL6eZpuiqHjsWXkwA4oDJDnB4KD++O9hormH7sXHxTGwU\nqd+zXXJg5yfrVCQ2RYrhnSJmBufxcyLREjz3+k35AqCn8kJiBJ2eSwWRt/aI\n7kTuy4NS3Fvxj3joJaVDORDIBSdkUZL2m8A6lldW+cCph3s3wCgTCf5Ey4yp\n1G6P1OGUCGiufw2OpDMeTpQ3BNc6maUJLyXVONq6H0usyJj480WpxMUqVfmh\nq7UOOK1ISH7yBXptw4YnNomFsuhOX3SlZh3S9wQLOpK3Omn9W0DYHFrlLecD\nIQxxSBXnnf95yfCbzmYvUB5N8sd3gG38J+QAp7+DgfGzI5dQB0hwYk71IhVT\n4qjtfsLz+viOsIgkzrcB5GcaHb5fWoL97uCxQaVeu+grE61EFN3+C3csBxqG\nJA2p\r\n=i8Vt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICe6OE+LHkXNHYHJBVt+wzzHiMxuIGQotK+N6m8JnC3hAiAwSmH1UL1pHe1ELBa92+avwPtxzgN1LPw9qspQejO3cQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.3_1544642445330_0.492521428326689"}, "_hasShrinkwrap": false}, "3.0.20": {"name": "@types/react-router", "version": "3.0.20", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "e46e7ed493bf565e66d9682fcdeef109a2593a060977bd085d5c9d40351db54f", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.20", "dist": {"integrity": "sha512-0sx2ThGYgblXPf8we/c+umFzP3RCbBp1bbFmd3pO1UaOYnTDno82iql3MQTVqB09rhopKORNfakDU/9xZ4QR6g==", "shasum": "a711682475ccef70ad9ad9e459859380221e6ee6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.20.tgz", "fileCount": 23, "unpackedSize": 17523, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLhPLCRA9TVsSAnZWagAAVkYP/0dCcAFQuzQYQyYs2vVS\n44piB/VtwaPumlw1dd/vgpP+2EE7or/i6xZOMnpJV7+6KCQpJ19kYapI9WBR\n6md5h6gvF0IZ0Cr67+D+AcydIWBGUA3gHub4Np++3e2XqA4IlP05Z5SKXUAc\nmdm8KJ1mfrj7ArpeQjidJa2e5sGBobMOvyeay6Cm6nxT+kVDuMDUGtkqEu2G\nF/vwhs69eZZTuw1SMJf/4v0TNj379Nb7wvmD8O3aCMyF7nCb5sgO5rV9hCmO\nK30ff7STq7uQsEidwbRhSOCStMtzdYL3furTDtrOqAgZ1aOw7g9uHe0RBrFO\n8o+0G8Oi3XGphGQNl8NPsHoR+qxmtTjc+eX9w1HCRl7vGFq0N2q1/CSkGeGv\ndQH55TgVFY4FtTKVQ9IuEPN7nehCFp+H0J3nSj+uf9jrxeBCEL4y8dx6HFSa\neDyrwxH0VcPrlBQTWIcOj8S0rZbXdOxHYgJrCsSsZV/9bmlK3evGuEXkf9Pr\n2+R1DLimD4ynl8lXQGYEFelVMOAB9vnXYu12zVMZ9PWIMzP2vCzzXB77yeWX\no9ACNq/GuN2NGzl6BEjV8RM5smg0rSXotl47HtmxmgA7RtcNSPjpnrPtqlXr\nROaDC0R/dbaBE9Rl+Gr2ASOEBY6m119QPO4ug/u7LwWJgPFtxKn7mREHXlrt\nBRMf\r\n=9vya\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAYLDgsKHtR3OVSwIK9uYqVPSwZgQP3Xk5ldqezXpBNFAiBxbUv7njNnhfDV7/orVq9H8zHLK/v/oxBX6v1oR+tBWA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.20_1546523595336_0.6695338229242245"}, "_hasShrinkwrap": false}, "4.4.4": {"name": "@types/react-router", "version": "4.4.4", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "9c2294d9b25e6c030e8ead7c51e37b71ac5519b0b3552a203f9acb90fe8b87f6", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.4", "dist": {"integrity": "sha512-TZVfpT6nvUv/lbho/nRtckEtgkhspOQr3qxrnpXixwgQRKKyg5PvDfNKc8Uend/p/Pi70614VCmC0NPAKWF+0g==", "shasum": "4dbd5588ea6024e0c04519bd8aabe74c0a2b77e5", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.4.tgz", "fileCount": 4, "unpackedSize": 11486, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcYz+YCRA9TVsSAnZWagAALpAQAIuTvjseEZ1Ze69bH7nT\n+E9c7MnKzoCfTlusyX5zpB9OQqAIwlnZmpcAiDNz8AlfIqsvFHFFrzZzeqIG\nGumfRXsoTcuHEyiBpofJi4rv8zRzG4xB2yqHjwyl8Kw4AL+lwPDTi8SagOuu\ngxdB6d3FVvY6lKEwPk7F90EIebVSf30RMAaI4bDTz7bZIs7Oa8rTp5+h9Qfh\ntDZD9ZReJvMPBkC9l3gSSLNMysevDX3oxmI2vuZKbdGwuezwq0I0ymShzEZQ\nprj/SWisdSnbiF9xoMjTIo0pbNo7Oyw6po1CdSJioSDuUNTJLmZmOgquArd1\n9fqocUhRYCG5KKnBDr8eiUXE5jr+tBogxuycS3xG5w90yw1yw5h63ZdooeIp\ntQW8toUZbdZoL4hKHcEV2+EfsA0vwxhhs9foJhGAd2A9n+AcHIZb+2PuN8dx\nL0MmzuElzeGT/i/y4NWkLOsuzazT6r4y7Wr1dqPMMZbxmM+IU1T1PIJo+DYy\nejTpefC38k3IuAQh/TghBQxIsI5OIkxO1lW/SNUG7YxX5Mcimt/LgXz8Opwe\nX3fI11wSFV/9K9w1jFSdfMhjrsoubXp5Xy5oqu/ya6aDcO4wC6/BTyb4EWn+\n1TIthWKAUQatAqDuDcN+3kRBDGRic4hvcfG7SAUm8CPTq44zie6h9z2G+9HW\nhCt9\r\n=Xc80\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGriS/0fPIugxVDaxfcS0itLeJxyfGebvp6jYPHDYe2gAiAo3L6XL5osdtzYgXEKoPcMaDu+aFa9b0vPlw1O6RHvAw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.4_1550008215468_0.7862564036565312"}, "_hasShrinkwrap": false}, "2.0.56": {"name": "@types/react-router", "version": "2.0.56", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "bd9ea5355c461e7f6fb4b0d91b4b35fef42c7d79a1b3e84e5f1696fb878956ba", "typeScriptVersion": "2.8", "_id": "@types/react-router@2.0.56", "dist": {"integrity": "sha512-tfPTDxygZgL00RhvdGnogh/OSGOvk2TggKNasR/1DyocyS78uQHtd9+y7UNMvzhlTOVbv/G0IV8KPCP5Vs1QPA==", "shasum": "675ad9f091e33709ba628607062da279354a7220", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.56.tgz", "fileCount": 27, "unpackedSize": 20081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZJobCRA9TVsSAnZWagAAOaUP+gMQdaTrUZAvIvSKk4al\nRSAPRR6TU75lc1T9QO1ATQPf5IXZPl3ayglWRqdsi/DWhN5hbmrW3tknxXT+\n8fdEf8oreZhmsRnhpuMRxz/kir+ms+NLOAXS6PcdrzJ+VLfHrUwvFNbHqfAI\nuaeI9B5YYmRtykCxk/DJbvdYp5H6Wu1ERLgBmX84JzxWiox+sSf5W54oC51I\nO6g+bSzvh8ZNNEfIPTnktXveVKd0AJ8Un2mVvp97Zda7iDCpEXQZuaBj80we\nTDgXHqgymLpi1lPWHZmf4jQceURZuUtkqT6cbtVgTUPSp0re0Acw7y/gGhCf\ni+/dmR+qT79SqPRYWLbYRN5QNpBU4XtPVkP5mLDcYzOj/xZKGX2foXa3Cq2E\nNgwnAMe4sXDxbzTrWCGTV8vW73QJ9bxs7CFyADhm6Cd9AEseryGObO0XVwM+\n2b6/Ku9NN86CS63/j3SASi7xLruzJSrn52iMTGEaTM0kctj2Mlo5UJn1Qr14\nkS5vQz0NhnvFf0Sr8u4+enr4zaNarcK29gl+LtnF03jYfBdKNdBqHoSlSevS\nGIZLmtQLV6ICe3w303QlejFm8N6zdyOhVztV9eHS+yQV5+D3yfVOaj6Jd5xb\nHVIQGTOOTHuTj19q62yuD/CtSOiUl/DzAoEx8d8huYLRiOoH7lhlAoqmHj/S\nQUJd\r\n=O2//\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwMpijN4CJOdT1HotL2yJozfaK5jGxS6y+PhJHIK0gLQIgO8ryOSW094xmYDCjR5GFkv67SxcqX4UmLZDkAIJ0WG4="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.56_1550096922683_0.17851013057731824"}, "_hasShrinkwrap": false}, "4.4.5": {"name": "@types/react-router", "version": "4.4.5", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "9bef55d15105f34213d6f3e42bc07e772334fd485e4b0d2e9b1627ffa2bb8501", "typeScriptVersion": "2.8", "_id": "@types/react-router@4.4.5", "dist": {"integrity": "sha512-12+VOu1+xiC8RPc9yrgHCyLI79VswjtuqeS2gPrMcywH6tkc8rGIUhs4LaL3AJPqo5d+RPnfRpNKiJ7MK2Qhcg==", "shasum": "1166997dc7eef2917b5ebce890ebecb32ee5c1b3", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-4.4.5.tgz", "fileCount": 4, "unpackedSize": 11824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcgaOYCRA9TVsSAnZWagAAmSwP/Raq+KMYlas1G5fWcVF1\nR1FndcycwbQW1U6cDkbVZ7XFuYtx9600anzfIseYwcb0b4X9ZsJa4pY2m2MD\nhg/+xOikwosQrTFxrzaOZ+lrTwooYDIFt+I7Kmy0/5AJbvXkuw3+inov5iut\nEVCcNWi6FEQqsyE/cYzUT5hvLcBq7MfjmwjDPBrRfUm43L+CWBeqS61QLCjt\nKmDPRG1WrRYiXVWRbA4YSbpVrko2GwKnVmG0Znk/lB2CCbDJT0n86mYh/pm4\nVbC2SC819UYvTUYV3hy6JGW0s98AIuthYTE59Fs6V3o9L82yY21ZGfxLnbr7\nA9G3HF8F1/QxKHJ7p5L1T5bM3mXn4LvyQDKtqfyCzHPBQJcBswvVezL2XbVS\n7R8Fh2SmlqjXO7b3q+m7/lShZT3maQsQEUb2BPAzhVqGvDmTCXl7LkQOgPbB\nXCXxuNKaoJ9Y0dy+v6XDCxRoUpsayHeZEgAUeC5d0ZJGXz/wSBqr8o4wms8R\nwWFQruCnYwyvYSZCP6nM8ECMbExKyTz3Yyef956Gec+7RdPq+V62QfV/sMCV\nUPu4RjqxUq3gcQCRF/dkUDiwc2RYfP+yoCbpUdx0A9RBFBTsN9Zaccvzy8ax\n/ZN0pov9h9HZFypQ2MWtZAppv0gsoya3H0EALDZ2bU4VLh+6YbunYXqE4qpu\noEpu\r\n=mw1q\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICnqYZVGxAtoJQVJJ0dQ005aNumvHkevajWEcF3TFNHJAiEA17XfNmrJHRxw5iv7BsgxwOSUHRlp6u2edXWAAqo/v/Y="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_4.4.5_1551999896101_0.2585551384637581"}, "_hasShrinkwrap": false}, "5.0.0": {"name": "@types/react-router", "version": "5.0.0", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/neuoy", "githubUsername": "neuoy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "f9f64314da7faadf87e959e85032d8d00857eaa3f482b3c09237e7c2c780a239", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.0", "dist": {"integrity": "sha512-0JjtJMxkQSyWUHTHaD3GhKf6rcZSUFmcQob8OlPTsbnxnIg2Nh3btkss4uke5CKVRtbCMipGU7My5jtfZQC+jw==", "shasum": "22ae8f55d8af770ea1f755218936f01bfe1bfe27", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.0.tgz", "fileCount": 4, "unpackedSize": 12164, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc0g26CRA9TVsSAnZWagAArKwP/3sEsenAeBWK3IGe6t4p\nP5SJUFhCQOre5kl1V0aumrjO/MjgsKaew46/q1NRVhRpGCoTXNReHJLamUSB\ntHrj90SLb+HCdPSe7muaReE9sWJhJ7XSfR1WmdsXIHmsEMtcxlHVKrMoTGhF\nISymTC6P7mD0dhNxAxbcqBCorKrP4XaYlgqYb83OivA7pTUN/P+oVSFJDlPB\nd0/F4obn1wsitKeNQBsmlUwoce5UC5Ea+TzabGJ8+FKx3F9lRdx5AIZyJaMp\nRvl/KKKsuPoJ2ErHvA/YyGsFlMPQtdPvS21FSBsbBfgkGq4TSlrXPUwHlTIW\nu6fQl8s8/iUY1MEAJlkksrsVhfRwogrxr2l50mJHHdtWEa8kUP55I4Y+P2EQ\nSDcAWNWev9qMAB6VoRgefPbjmG52N9ohkLKKZY+vRk+8kZnHe735gl583jFl\nP2hNJ3O6ihwtymld4eIOAzVvdEEeCSMsp6OgVT9f/R7YX3XA77FjrJEVMlXw\naMJ7kysysA1ZN7qSaDStOCWA/23YW5Qa2rGCLovPhEnBoLmgfm1dnPvQdw+w\n6MkQ0BiQFELQZbmtLzqltIhY/XJHwuQXwT33smdhZ5dIG2vRmhEnZGDLHBJ6\n9UKbIFCQCIPFDt+2UuxUn6bR5YrOvdOXs7zXXVzJzMyirguBgfQrZuAV5Dtv\nWRDm\r\n=FwF+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDW+z6PS8y5F4v5FmSyjjC1xiXKR+AR5jwV2xu73o8khAiEAtgtav+x46jAnZR2IeWgvNaWAsTE5XDZwUVzZ6lhaHZA="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.0_1557269945931_0.15317456872380242"}, "_hasShrinkwrap": false}, "5.0.1": {"name": "@types/react-router", "version": "5.0.1", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tkrotoff", "githubUsername": "t<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "19d4afcfcf92b647e2a5d898766cfc86f4ed947fc6ae3bb552f0a1442d785709", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.1", "dist": {"integrity": "sha512-vOyVO0u3Cs0w6G5DzYqNVqcTsurEnDgOmmkJf2s7VwtunWzpPgI6dHsCBX68vXqeICpP6jCfojgJcHkm5BV7hQ==", "shasum": "9f4548c75755c55b0cffdd743080e5afa87da6dd", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.1.tgz", "fileCount": 4, "unpackedSize": 11922, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc4t6kCRA9TVsSAnZWagAATJIP/1/J2MVw7Vpgm+T6WxtW\niIRSzZUVvLsW9zQB3pKfYFdObpbuwy6P7pxdgLjK7YIVoqBXosTJUQaLZvFf\nX6NVCHevjxI6Dstyc/O60Wke2QwmZBJdFL+dCuSg4+c2qv2RCnzntD7ZBfZW\n1KKHzfbRlBd+c8thhFmVGOKIBgqiz1YiDXy5PVs5fTRRiVVzA+ZNX5lEtI/R\na6ds+zZ5SQ0LlReQigpbDpuGsZ3mR+c5XGuozeQfUYyK6AJmyCOF6CCULc9c\n/FgBSMbWYTNqZ1rEJQ0uLjFijyGO3CVGXk9b0SN5s4WH8pTPUP0KZW+yVAu7\nUbvFhFAhR1JsnVVB63gdZmkPQf8rsLNm3XcRMs0MdqstKHeMg3aqo5lmtHqn\nPMoUJQD83+dMrkU+E4Ggn66+xfvqYsImeKZ2A+Ow5Rm6HWd9j41pWNevf9kU\nbdvk+ebSrvQoTzvw8iy89pB8oWYzGjBs9LFy+4ofd2aCsIHhRCRIsqgza41V\nUf0hGqDncKL9qJuQCtMeo3hHR9/kDslwF5j9C3MOliCQ6fOaAd8NAr2nWuzE\ndX8aiDmi5G6O4coqjP1FbIjkBkRuoyQGpTKDNsY0Lhv1xVf7ESSPNWsiNa0G\nG3DQ0zUTynKNN1f1XqRRTCStJqPwUhAJrd1p4BuyPTLIgy8C3AtrgWZREWnv\n0no5\r\n=cy/5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIADUA339L1jrVJhi0B2OHvAT/w1U6lQRu0ScbUd/hRKSAiEAk/VMS5/Gu3a6uAWsQdu29TRY4re5YyoCN+65VgoxASU="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.1_1558372002551_0.7175928588380176"}, "_hasShrinkwrap": false}, "5.0.2": {"name": "@types/react-router", "version": "5.0.2", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "c3a2c384a6913d72cbd3fd9a47b1d2b5b13380f6c1c19d16ba4e311cbb77ceb6", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.2", "dist": {"integrity": "sha512-sdMN284GEOcqDEMS/hE/XD06Abw2fws30+xkZf3C9cSRcWopiv/HDTmunYI7DKLYKVRaWFkq1lkuJ6qeYu0E7A==", "shasum": "619850cf28245d97bfa205f1fa7136451ba384bc", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.2.tgz", "fileCount": 4, "unpackedSize": 11662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdAqMrCRA9TVsSAnZWagAAYD0P/RH5L/YUW7YFjKv8VfJn\nY6lZmVMpbaLpvhNuLDKybHup1J+HECWonU36PlLE69whuN0sdbpE7HrbSGuk\nK4GeDA/5OLrWwlVgossSriZGbO+7QdeKLmalJFDtYfxcKrbNMWAkczBtF0uk\nFhGDPNw1qrTQEjzvCqRqalDZEs0BjUFiW9Oi+xWRwvlFldQGtle/Ho8sWe/Q\n6LEFoc9Zso/Pr8RbNPc3rP0lL7W64DXSYbn5KhUTLqxxdZ5PBXIVR5Es15T7\nAvgtGnXD2b3Ub6y98gf7H5ELiGqNFDJ30jxXD6uK6uXOOqG+MpQUV2Akmh1Y\ntTduii1d4G/cXFXc2rQo/oPMcNBLXwKvR1w7TtiNTyMHKGG4t9w3nANQ+IN0\nrjZA4XUW3CfJ2ZNa5a3aLlZeL3KoSo02qVSRw3nazj8EtGYdlWOaf6NMhqJV\nsj0oSU/DA1V9UEY7usZyhmVrnu4erfZxYW1KqxdiQ7pF6Wydi8Oym7X3vlUd\nTqGpPDhNECemz8LEaRQp/C1tKj2jbXSEuS9SoMAYuUvEGMsHScUmzIbB59WL\nTnFHGg8ZNsXtEYvAVkSx2yQM9Pz21q9MRAvyBpvJyyAkPcfquwaT/ui18vVV\n+emYknB3KYrH0THFIC6HESQzAnpUOMRraR5Ahm5NfRMUgAQwXwg1ZNZi58lf\nck9l\r\n=F156\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDVHIFtvQA5ix3nvftJphTQMiaJQGbtyRl4+iwzqU6QxAIgPT/Eu5dtxTh2Eyz/+1g4KbfBnN4YmDJeQSzk/IlfZh8="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.2_1560453930731_0.5682373527803843"}, "_hasShrinkwrap": false}, "5.0.3": {"name": "@types/react-router", "version": "5.0.3", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "19f730605bd4be1fb2e9ee4458985cd6db2abfe6901d0b544759380e00994d01", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.3", "dist": {"integrity": "sha512-j2Gge5cvxca+5lK9wxovmGPgpVJMwjyu5lTA/Cd6fLGoPq7FXcUE1jFkEdxeyqGGz8VfHYSHCn5Lcn24BzaNKA==", "shasum": "855a1606e62de3f4d69ea34fb3c0e50e98e964d5", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.3.tgz", "fileCount": 4, "unpackedSize": 12013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdG4q8CRA9TVsSAnZWagAAUUYP/2YYMeZ27XNp2xfLa+XP\nrP5ibl/3KEMSrO++/KuNy70YbwKR6K4RSdUUVYunh09eUE4xe67ybqFuumiB\nO/edoSOWnj2k2hDJJu2uhFgsWKY059LiENi3w1AANYxr4DSBxUYWGic9Hyoj\nOr4t+vzGFzINZBcOEk2mj1+Dl7wUITsK/+oVtc0BBWhuXXJn2R50ngngFb4W\nKMrvJx5wJFJH62HM2X1qJ5fdiM0mi0U6Wv4+iPauxvEvFhGYRraCwDq2qYqo\n4IBIFzW8hTPv8QorW8ohtxr7AElWEvPbRcqDhL9a0invMLRkvNPi1B9v6nc0\ntqQqpXMb1SfMe2mAMALOjoOxf8eQkCnNoTRyKN7LsefFRgaJfymJSWdtrNV+\npNZ1VY+AFkfuFGX9C7i+eQ+R0cnRpYiSmlELH16fovIH++kwNEEshT5c8Ctx\nv+KuhlMQWAzsgf/hO7Or9m4yuVJvbKnqU5ew4e4mKaVh+cTZ9DrGvoEUpgTY\nndhSZH1IhLGZqmceJHXg4M2VCdCpTNH9c0c72tnUJBcz5k7FFAhnXcxp36rr\ndrfjGt26FzfSKagtVOUSCx8TvqaYuCQru2W8Kc50d3Psg74h4pqacSan7oBr\no9OV+XvoNuRM/9i/gqfUSrZ5zQf0Gtnhn1xs1OGUF2jLh3tODgeGHxpPVOXO\nN5+h\r\n=8yl9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDWdNxF+2pK6IdtXTqY2UITIXOmmW12+Fhrb2Tr3Kx2CAIhAL9L0Hw3drvX3OkouIElOe0Keq3+jIKt/j0UEOnaaz3o"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.3_1562086075452_0.3283585220560945"}, "_hasShrinkwrap": false}, "5.0.4": {"name": "@types/react-router", "version": "5.0.4", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "7adeb1a2ba1ff5a750d5af555e47ab279ea084e010cd11192aac3dd010bb36c9", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.4", "dist": {"integrity": "sha512-A9Hdz78Qq9jzZuE0ncninYJLT8lloFwaMZC/hdTi+CIc6yOqKLUSaqidhCNcDgeEKBep9RHjpuHGvEPT+D3/5Q==", "shasum": "27398c8898bcf5e3c7d1b04abb178f6c203722cb", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.4.tgz", "fileCount": 4, "unpackedSize": 12002, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgRmwCRA9TVsSAnZWagAApQ0P/09YzxK2TtPUQaH/zbU4\nhxSxMdK6lTUbtWMms1XHP+E4mw77pZ0mAyP5q/9Lv62K5XLbJc1TOPL6UoRS\nzo8W9PebiPvOcad7pOzq/wbuWr10b1uWuIVrEbL+RKGpCcjvKIWlx3Rozi/v\ngEYqowrul9JtXRK+Nk6r7bOGrWneJqP21RZzISNDx+JmZG6UWLhWChUMkatT\npPrm7uANfEXzMxG2vAFHhaqh1C4WH3VLay+2Vw2trEETqG7j9SZuj+E2gr7u\nuVgssfRAcrytH4RedN15xGvL21jDDaM30JC16F9AYv67qNq4jaocDM7u7YWp\nq68ESQLqjrGMNkGBFTjyLTl2WEWo17SwvDH3CkKJUGt0hNsoaknTpxFG3ecB\npmaXRD0HWGRX1PrTEChwDeVer0O0TyDoOEoP85APFzvgOvWIjqR2+WDeICec\npKjoFl/z42bqArCI4sqaCFU6LwUJ/U7d75dRumPJ/4IcN8bPU4EM2XLcoYE3\nnTXHnsCkH7OsKI3HhfOICIAg/EhP8qx+ltMvygII3bOh3e+5xofML7qiOArO\nN+rU3orC5jD42yjy0w5wqo7a3rTLy9KzufvqNl2WjtsD4rsm+I2sRmYLVq7F\n0TamOl9Wl/wwSwaAzOUEqCj4WSdYXWzXmeMaohJqeuS+4XyX4ElJrtQmrXQF\nwbaL\r\n=UcU4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIE1NIpRAC89bdUJDOBKK+ueaFgMpblzyEubCCdPxX0DRAiEAlq2Eq+ocNU0Er+fkY/iwlFUtexa8SarE1SwoCEJSYkY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.4_1568741807577_0.7777308603787427"}, "_hasShrinkwrap": false}, "5.0.5": {"name": "@types/react-router", "version": "5.0.5", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "19f730605bd4be1fb2e9ee4458985cd6db2abfe6901d0b544759380e00994d01", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.0.5", "dist": {"integrity": "sha512-FX/K5Vnm9q9O3zTC0v3XF08q24ccPz24E/nVDxWSzcBW/Wuxq7lCD6cc5qr1PNOz8OdaDA4Jedjs2evf4jw/aQ==", "shasum": "f28f5e0b65e2a9870e0f08f57e4f50361f8ad1a6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.0.5.tgz", "fileCount": 4, "unpackedSize": 12013, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdgnDjCRA9TVsSAnZWagAAvBAP/2UkJT4r+SHor5XDThUI\nMc7aOp+HF9LOEDtGrboDVHm1JCzpNOMS4UnsLRiTb1C9Aw5yDkj6Gkab5oRr\njEejyqvOQgeT4I7i226NB8kEXrx9NHE9yIjPb/d+KFBxP1tg0Iw5+P8AFUQ/\no21K2iZP3WV3lBezLVPDAFWXqzWS3M2KjUFH5o5gfQ2P76NwfEpTg1Ec+xiL\nXF3X00GwK1LT0iTtMhQ57JNSAOkIdyTvpsbeqhtL3QnGiZM/81HU2DD1oocI\n0vUTZSr0Z4CLLgdcZeZBRWlJFhwaanH6mlafuPORXHPlbd2N+4PfBWmJnp+N\nL+GTGg+NguqUp4qAo/CVRTTZtRGhiPvnKvU+Szf1bQG4fHEjpxzIbVtO8+iY\nHJ90xfW/ExIoFWVZ7Eonu4pHP8ldSWSx0kYvFQSPHeR3Zp5QB/QavVjsVkhC\na1D6DC9DROhEhKK04OXSMyMT+Bo2ckqOhXfLTtf5Z/75jmqvPVkVVLSZTsc8\nc4zuq1nbFYy1eR93K/i+LbHBTz9EryDWue8X03MTQai3TI5gKZX9sMKIYqxo\nD4xEopz1/jWHbIFS8tiTDWy160/Sa16PA5ZKTwab/UmQlMIKEFldRLlPfzWz\n+/GEeJAzocvEg4NFfoxdXTo/Z6gchhuTdlnVP3t3/IUdiYUcUpT93xB3GXu8\ngjq0\r\n=iO7D\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGl5XGUCCGucpz3lH5y11Uq2oiM8RZrWGL33RoqMEziHAiAMRX1OjD40GzlqT04CFAhT9ObM+DzBaYBhLg+dAPmFzQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.0.5_1568829666463_0.29795841601647766"}, "_hasShrinkwrap": false}, "5.1.0": {"name": "@types/react-router", "version": "5.1.0", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "72210d358ba3f72f8ee4b3a6863acf9f22a132f902b1704569716897c580fe96", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.0", "dist": {"integrity": "sha512-XqxaIqG+LJTh9wsGpZCVQNOAQyEjXcfYRqoIXEFqxc49BKnmvJ5FLylsNUUCTckSffD468cOn4NJvxcWuLwiDw==", "shasum": "ebb47bb7fdc75741286b5fb5a82ba5b4d62518c8", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.0.tgz", "fileCount": 4, "unpackedSize": 12444, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdi1B7CRA9TVsSAnZWagAAm5cP/2B4oMl9Ahx6f7tP+QHQ\nfUVheInoqbwgVHBexG/58j5Pk2G8v5GAPuG5kGTbpFd3J9ykgZXhdRrRrobU\nGvrhGdJG4OtzufZyOe9qjqaQ7XgvS4tE1kOmLAPedzwPrPBk5l8971tCCFRp\nMYBY/7P3689+o+IQM2lqOZSqDmu9oBKOpjZtVJL78lRmFc30hFwzZmQlpEhK\nOzgTvfwbp8tUWbfvegZYpwQpovBlKeqMyqgFcKSJMa1PDNMN67l27+Uz4wi1\nKkuB98IDUS7mct0S3ylOpXWW2KKJwpfU1CfWQLqLNTYQvXj1QZ8sQlmpdAIp\nG51xQcdcegGppQkCQUWMSB1F3zbxid2ykZfdOvHBWunqQjMnRT8sI1MgufEp\nJ9e+M9ilc7wiTHh2Q/AMxDNScXRkuH1DiE11mNGbKuWdYueXKkg16LXdRKYh\nnf2ZqHo4Bg3slft9DsUsEOlu8KhcRCbdyrzQWQMNG3UMy4hpwvkGko930aiH\ntdNAUlxNSQPJ0fHJHMjsQVJXG/IFr4otht53uPcNveUsEfXEzDOPgOJ6sx8l\nM4FGgUK/QwSnLXo3KQLzhjHIRDCiayfubA/2ykK2dYNjtMfebm3bEijh3wN9\nbwsinUHFhxY5OcHGRkRSqqZsTb5mL3FDJXobI6EGxtUA0n7AQA+Nkdm5B92f\nxf8L\r\n=Dln9\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBYebKXJP9YISL7Cb+XO61I7BxHMEfxSoKYsYrXF9VirAiBKEgwrDFnkBT7G0ma64KShs2YQMX6bUIwebKYGyeH8ZA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.0_1569411195182_0.6939948257726261"}, "_hasShrinkwrap": false}, "5.1.1": {"name": "@types/react-router", "version": "5.1.1", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "239f0247e320616764131b19a6d4bac637c8ad2c2cde028ed4d89821c92d3e46", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.1", "dist": {"integrity": "sha512-S7SlFAPb7ZKr6HHMW0kLHGcz8pyJSL0UdM+JtlWthDqKUWwr7E6oPXuHgkofDI8dKCm16slg8K8VCf5pZJquaA==", "shasum": "e0b827556abc70da3473d05daf074c839d6852aa", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.1.tgz", "fileCount": 4, "unpackedSize": 12445, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdjp5CCRA9TVsSAnZWagAAL/IP/iR191UJ6a6mH94z/ljh\nItjb9pBZhu2U2QSRAvF/ifnlg6Z7ocO4FCMxHZYtPsXdgMaUtG8kPM8qXzw+\nd+DFL9h7UD2GVTNHFZr27obww8MN9eIykSaahQ+48VCRPYy/fpCVLH8rVTat\nm3W8Z0J0w9KskVBwlFtpsLW9eoqIxuT6W88nwpMdtiVWK9tvbKqJlnuNCO2b\nbeP0Hd9btzdNVUpDJM2UGG3I2QHZ59MKQ4PdWDq9focWWfudi9ScxbEG3z22\n0lxLkUp55rMLV2ZaFp5RTuGYZ0U/m9RtMchgSO0pbViewR/zbfLPGZRDziqY\nz27/Re/+CJVc5pus3Q1X2eRIpyViamxSLi9hV6qE8ku0cXi0wLJkHh8zBNHM\n6DbF5ogPbbkLBflQehsnAtyDaFgwEmeHHsIJScCzpHCYeRdRadK8lO4ae8Sy\nYfBqkh3PDOuXY6gtWowfCFDOxDDogSEwuJWrZd/8QoLywetsbRamMgmi7xFZ\nGjzMRnga2wXmG/3sHb/d01QU3WKp4dy2NTKUKjKNsmTdbDwFCpEF673x7UTb\nJddc89cgfLPLdG1uU4CXZBFJVzWblGhS/1oqCJd2iorH3di2yBL41/dlQEkr\nlB/i4Fvbpjkes3YXKvjWvaovb6Zof91+ysI0oU3M9rkG6WC2P/ek1sNxntGQ\njnBi\r\n=xsQT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCSeq61o/VPGD7e3Gz2iQzCXDcZNNSDyBuKoKfVq+tMBAIgfI+C/mmaXdue/YTpG9YVbrIYlR0tJRmWB9Dd/zi1eCY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.1_1569627714213_0.6470416279472324"}, "_hasShrinkwrap": false}, "5.1.2": {"name": "@types/react-router", "version": "5.1.2", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "ffc4540c9d656c39e8c0efc302379d3f906098c746a2f80d752d8011fd82d2d1", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.2", "dist": {"integrity": "sha512-euC3SiwDg3NcjFdNmFL8uVuAFTpZJm0WMFUw+4eXMUnxa7M9RGFEG0szt0z+/Zgk4G2k9JBFhaEnY64RBiFmuw==", "shasum": "41e5e6aa333a7b9a2bfdac753c04e1ca4b3e0d21", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.2.tgz", "fileCount": 4, "unpackedSize": 12467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpNkLCRA9TVsSAnZWagAAvbwP/359e4XeYLEiUEkuB+nS\nZOE4zM5HxPENxQAiBlJUAUO/+K+Qun4jpIFnMSFrnHT3wextg8uD+Dpq/Gvp\n25ElZH4BAUFmZAMKDSJE7Wre7E3AAz9GdMwK9TnM5eWNRB89FOtA27ehGpiX\nIYiMDfiLhAkRcPHUalB/HgMja4c7/mUYk/WpiTQPeOYXN1TKL+1rogrIoLWQ\nOAjYgVbBletz9vPkYMvGwITO3wItp6x1j8EBq1+J9lwGR7fgkLXRcQpK5O6J\n3iLFNix25/7AQKcL2+WKjb+dJ671jUCQuBamQCU4xrGC620SGhBDVeSOoOqF\nVKJ1LUoXKhmFoCk+vYzQPEyVnzv4mR4SA3mbabp/yApFxViNE8e0CNmTqr/N\njszC/YZWLqyMqrAunOQM9mI3lD+1VMfetKoJlyXphCPv6Hk//mmQQ3Nf1Dix\nkIiAt+/0oUIpROxEIvkPnhCDa2Cm3tyM5qrqCZE68CV+FCdlfgMrpls/axWC\nfUdzy7rJvQdMTTTj2YJteUgg+KHNTd5PskUMbfLOcPpSugJlJRU5uv4oB3o8\nPjwsdq2sJNT8Q9nPm3KP2qQWDMyNe4HOnDMNZ8GwUyMrseSMYro0oDM6svQK\n2fl7aWKI8cBeU0JXUGJqnirH1GtTeFEc4XzaOlB9ArvRZA7bpm2sj571D2vU\n+xbY\r\n=wuuw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHXl8kKK0OCb82XqZYef/r6bNOwE7oo2IzKhWU4xA0gjAiAEn0cY3Ew8EzYV7Q1dkiuDETj8HfvOd+8bXXjhDLNBCw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.2_1571084554885_0.5933019165548521"}, "_hasShrinkwrap": false}, "5.1.3": {"name": "@types/react-router", "version": "5.1.3", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "0560c725519dc7a39bc11e35123d942735504575351fbfbca2addeba33f6aba3", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.3", "dist": {"integrity": "sha512-0gGhmerBqN8CzlnDmSgGNun3tuZFXerUclWkqEhozdLaJtfcJRUTGkKaEKk+/MpHd1KDS1+o2zb/3PkBUiv2qQ==", "shasum": "7c7ca717399af64d8733d8cb338dd43641b96f2d", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.3.tgz", "fileCount": 4, "unpackedSize": 12809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdzI0GCRA9TVsSAnZWagAAq2gP/i82l9ZOm/3mFIt53ube\n6OYXkUfP8uBoJbRFX1QvJ4ASzTcfhfRtVSr9zWuhtxuXNTuGNlxEbA7+XnbS\nb1F1r1cqjDDdL/Y/RkDu1sR2vAlGAaY3QIui2wiP8xoYMg20l+vBdeqcSKAK\nxwWUX6moWBni/AH8Xhms2BqfmtnTLgHEUuoouciNxJik4PjRFNVJqzJu/zIr\n8b12ofCLPfRhiSgpxRViTCzaO+qD8O5in+92u0BviHfHPw2v/Ym6yb5lnW2e\n/uAl6Q3i+MVKAeqTTNurqr8TemIW2HumurqqqTU6qI9+B10VFF4PIVUOCiu4\nyvswPItF12pExXBkHKmdwKQwJ/B/Zz/8U3XcDH+2qi1/Wjz7ETObIAdaXevf\n4EKXqobQcY0JQTaR1RbYy/KwSGB/SOw4J73LjdydsRqf0wOsJyVpw2+dME7J\nkQ0OocYz/hNu74l8VXjZ6XlRhtclFWOt3gTZK7KyqjojwfBpE6NLstr3SqSq\nn6Rn8K3q4IkT43+mkIZ+iwIj3cDdQ8CsxPUsj0NvefRwXeVYTgs2b+UPtJJ2\nDYmjES+TF9C5AUOl5XtzbX7UoteqicH1RJlt5rmQJ1+VdhIfRJsX+qBzm+Yu\nKlsvVpLaT0wx6Oy8jJXWlVTpTFWdV5DKuDqfu/qRyWv7Wg2Ds51Kf8lqCYo8\nGQ0e\r\n=R+gL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH/Y91bWYSTdpyec6RavlYXdUQaHvEJxzJTxJgL1ScxAAiAcPyUX4M2EPW4SQHZohGfydtreg741CtH8ppOnaGVpog=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.3_1573686533957_0.5443576511714627"}, "_hasShrinkwrap": false}, "5.1.4": {"name": "@types/react-router", "version": "5.1.4", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "979e8d526ea80925376075e71fa2b48a6cc9113ddd0ce86334b3551f93291207", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.4", "dist": {"integrity": "sha512-PZtnBuyfL07sqCJvGg3z+0+kt6fobc/xmle08jBiezLS8FrmGeiGkJnuxL/8Zgy9L83ypUhniV5atZn/L8n9MQ==", "shasum": "7d70bd905543cb6bcbdcc6bd98902332054f31a6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.4.tgz", "fileCount": 4, "unpackedSize": 12812, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeFQTWCRA9TVsSAnZWagAAeWoP/1X9pKtWH8u6Jge/1TWf\nEb2Unxvp5rFZXCmS1eZwrjzPqQ+HydwMLETAGUPIlwzzV2ZLuEM2UYllbjUl\nmnN+9ACj8DdWPk3JZhSsZlsBc3Jlk/pL7HCnKyOBdPYG3QvLyHJyI/wpZ2U1\nMbeDfUw7v0cvmHhtpl1LLtt7COdg2x9bHL0Ja0eM8R5apYnx7wBbufWHZqC5\n/xD3lUBab3nUAcn5eLgP6xmjhslTkPSudXK6kGh9ea4EKC4nSagzgKEGsMjW\noadFHxeqYtbvucnpAl6d9p13AstIz8RC8R5q55msByQ7FLuTtnhKLk4+CKK4\nXuOqdOjfO556kZlzlxmkMtEjSKFwt6gWd26+5igsMoqeoLE09ax52iqxHHY7\nLBSCqsZh/dgzlGgSFE9ccXtxJYkLankCg0Sd6Bv6/KJJ3vEMe7CyX4liMKFZ\nMoSNWsC09QAYc9EWQHEX914I45a8p1wSHRuA8hkzHo1euXSsQiV3y6mw20gE\n2dnjzDFqQrDjqCjTqfLq8FZRuEr8nLqr+TgOHyZHHRQ5WHZQFdBebUiMgPWQ\nlQvd83b5YDjnSxY7/L9tK1JiMV/LnezCZWzE57mXTD47Dzdm3Ci5/9g843aP\nTdK5IotldCL9tADEIK6maYj6j4e8cdUsI3vEdf3E1Va8HrERFlUpbn+rG12I\nMVB5\r\n=wMGI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC2T95lLMcwU0qwupUa3cSRLD3e9WYO0D84pa5RXOaNCwIhAPbnjmdcghqlrQs8ZB9G7qkGVaPTQI74MkemZg5B8AA7"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.4_1578435797787_0.27521535405099606"}, "_hasShrinkwrap": false}, "5.1.5": {"name": "@types/react-router", "version": "5.1.5", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "0327e56382d7b8ec57ea1275cf88f63b068ec69a45cdf3e825368238b7e73868", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.5", "dist": {"integrity": "sha512-RZPdCtZympi6X7EkGyaU7ISiAujDYTWgqMF9owE3P6efITw27IWQykcti0BvA5h4Mu1LLl5rxrpO3r8kHyUZ/Q==", "shasum": "7b2f9b7cc3d350e92664c4e38c0ef529286fe628", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.5.tgz", "fileCount": 4, "unpackedSize": 13133, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeg9QWCRA9TVsSAnZWagAASsQP/RM4dUnvodHSfPOqcsk0\ntsY9bGk3WS87bu26oRGRKt1fPPtDwO61ys30Y2ZaFx2szK4ESfngzjty/QtK\nbL/hzxJ+Gm9BjP9Bxg3g0Dq7b7TCvpp+5fMpQkDH7ombbV0MbYDUr8DXjnch\nn8bSz4yXu0GDwBfhCvhDIM3i02T4NdgTGDCJ+qPyUAtCkZmPvCZu5mk/4pVp\nZL+2dviCiWORynQqM0FDdPP2COO55A6Ca7Lg6KZZ9QvwbpAR01EraNTQiGj3\niTsmc5WNzpHNRLAI473h2l5ZdUDBKRJPvOOQMMGgP3fV7i3feANht64OTXyP\n2UDhGtUCc3MuwcuPBhHLmac+LoaISHp2N08rrCltH235i0iXLiokTmhOxsIS\nIYSXwz84OtfOSKNxMwIOwP+57uH0GogmBSqgSq2BY2UW8uC63gVohdKDVB25\nxjZE311sDZmbCZWRCTM6uG/H8P/uqOxZWlk5dsYHpQCQdLuKAW5Xxn4HxFvC\nNlYpPuv8b8Ra+/ydp7ByilIcoWqs4SKajzjwiSD+L1CUGCuPFmQ1qNYq3PES\n+VPX52Bn0vUDrZx1cVSO5HNHh1bg6Iqds6xLuXO67/ma/yP5BCoKBUJAiSGc\niv3uopMfdZo2IOw9lU3rzacnud/awLp+36AqgUOgP6vZPyEKHlPzPyGVT9q5\nQcbz\r\n=6RqR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICeq1/OOKn9aTG1iK+7XTH7YxA29I9zPOYGlVWfR9+GTAiAMrXOT0PikUQNc9oapLk83kUOhMiO0/BHzrmOykeYFvw=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.5_1585697814125_0.05447094352659154"}, "_hasShrinkwrap": false}, "3.0.21": {"name": "@types/react-router", "version": "3.0.21", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "c5e6c646f7e783486a4f0dbd0a27401f9806b6fbca8a23822083db6e92371950", "typeScriptVersion": "2.8", "_id": "@types/react-router@3.0.21", "dist": {"integrity": "sha512-WIZgUjVjhXXF1YUQKynIAsMqY+h/fwOeccLIcsgFrtFoWZtiJ5Aal4ih+OX9AqOJyASKM/UAAySLcORGCszmiA==", "shasum": "3914da8a680ba136adf602b04178fc37b4c99323", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.21.tgz", "fileCount": 23, "unpackedSize": 17702, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehOr4CRA9TVsSAnZWagAAMG8P/3WBbp6Mu/a8dBfzVaim\nKkbTcKEUHoa+o3yaO5k6+jnMONk0y8pKI/jANRJ1kCcEP7CjRCucVezC1u70\ngEkGJTAW4o43pS+g/xdlfmIQNrdnBJyXhahQcdMqQN/k+6F3CcAMtTUmOB5f\nHrf9TzO8zlDSCSmrscwaT7tubYGr3ox9Xt2bCF1vQBoMNF2I4DSvbRvCCEkw\nHaRjOzhjwr/9u0cgg7s+NQJe6os7TEv2WJTKAt4jkg8+2FxzcUBmpyqmq3L2\ndL7cqYOAC1NLbXZ7xbSyBR0yWQw6EHnRW8NZ1CT/FBUsUsXMUTS7uC0zwvfE\ncOJsl0M5Zrd2QYFbAa59WCZyQZgfQXtuQFqc7UVKyA81wO5N6MrQXenmfzoZ\nn5aTplR8g8Pe5gWNgd8W+ovyyxzTm1sgYN8hPejxvUAf17QrWPIMAQDYGvZp\noTsLOBNfOdMU+e8Dt9YFryU1Swv5ert1ZESEeXDXEU8RsVkmkWOHwSI/Gaov\nq+IydLKtEVr4trlR/Iq5joZN9Q9Hk2Ap3kcUcV1VrQ/bEbAsqAGt9GbrEiKy\ni5E0uvMF+s2Aar6TXCA9oiDwBFIC4h+KUkBJqt2KJ8QEJLD4acsRFTKRSQKM\nTzJ2gB9VaTyJpozvG7S3DWXDwh8gc83uLUvPpo+jiMXZI/2evgTZE22Ou5bx\nxAtN\r\n=fREU\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGq9A/TpbBOzx3EmKbUR9lYP8CYw6ZSryDXnbaqzPnyAIhAMZYhWcygKjjTgcjpeY+bObl2ndfMXwliRWZ796oMmWo"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.21_1585769207581_0.45635727189934383"}, "_hasShrinkwrap": false}, "3.0.22": {"name": "@types/react-router", "version": "3.0.22", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "5152fd8183ab3a96ac22aec4d013cc0aedecce488dbd556e3f19b162ad616475", "typeScriptVersion": "3.5", "_id": "@types/react-router@3.0.22", "dist": {"integrity": "sha512-MUjQtRC4vS+rTqQKag3r0Pf6wSy0Y2h/hfk4OE03STgNmtRWGfyjckHLHgESbdqz2VnL12xS6pdncsCDiudC2g==", "shasum": "047e971f2a5ff69aef981b4ffb69fa1150674e80", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.22.tgz", "fileCount": 23, "unpackedSize": 17807, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJehhmJCRA9TVsSAnZWagAAr+YP/iBZ4KwswMc4RzMxckoL\n/1ko8631j3qaTHfXcBCoEZv+KeKirCwTJkWcr8CLpyinNzeepoR1ghRkxNGm\nC2CxYvo2oXrqtuzCOdEycZwcz8QSfhuajziaAZr1SBu78cy80xgQs+T5qnof\neJaJbG8HcYbWcaRqaHDse50mNH31tnrz+nJHQWVj+AgQ0Dy6RJzD57pe6/fR\n/RzMAzp64ATjdq9HLlZKq1LK8MYBdgEY4vGB9pG/4F7DJkz4NBD4rGHyj6mT\nM0vbs3radYmiXVJwqPdWqXKtYqyPTDJ9OA834QNzubHO8H59xmdMkRrh+hSy\nawugGuI+QdxTgt2Getj5/D5wM9OCXmX6L0G4njXw3sEFuljBVXW8brkoEMUb\nRZ9yowTJsAGQmno74m35/g8pEwxmsh6yb3RBWIxXFmCEoxP8JrHVtX7TsT8Z\naSx4WtCyqHA/zJoo0NZZgKbzjFJ8MHQWSnIPnbq7BhSBH3PsKMBKQt9KuaLA\naB9UNl+OiT3OE2G8ZL0USnFw4hMMHCEGoWcJmJ4JxG9cKrOWNLFYZVFkwE8+\nk4hmQfOMzWlSQthxNh7n+sEw0aMX7pFHjqzwLlcyq64gzij2SjTVaHuRohiD\niFql/y0CDmHjaiqGS7wsu4ToyMVcGmz5cLgrIbn2/h4qBDY4myrAY+z9UJTp\n8wh3\r\n=fqUb\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEFH6cjMNYcd6UBb+QqCFdPWY71N62JlhOtq4SnbrcuSAiBs1/tkkwa+hoT5Bf04ZxIaf7s7l2eRAlK+rirkcC5qLQ=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.22_1585846664602_0.1590176769007947"}, "_hasShrinkwrap": false}, "5.1.6": {"name": "@types/react-router", "version": "5.1.6", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "aa4902cf5fcf8add9ba07733d41e08ea65c04f7a234a04299c7808397697c493", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.6", "dist": {"integrity": "sha512-YCIwRd+zcWZZIwfeWFptiualuziG84asMa1QU2+QMLhVc9GeS5LEfZlHCi3X4GMd3Eez6UkjOwyc+uQmHkzMUw==", "shasum": "5646c6e3e0cb30dbd1fcf5f8fd4f88262ae19b25", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.6.tgz", "fileCount": 4, "unpackedSize": 13008, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepcKoCRA9TVsSAnZWagAA+54P/2mY6ViXjsNnaK5Bkv7F\nrG18WL89Q8JAFpB0qT+bYjfOT1bNVsaNShev0fUUTdrv7hJQbF3xv7xd/Nnt\nTdJUxStEBvyMnYnrSKoAmOK5omxr5lq0NuZeVFBpzddNcWzK2FAcwj4/ufGA\nfjDcCSQ5rKhrMDxH5YH4YqFAOB3ElFOOoj8Bms3R2xg6986Rbg1Zie5X1i1D\nZG/oajQ/+Fmq6HPUZKgCdER+YKxnQWX1xqo4Mdur/lHpbFcrGg2kcnsXVi9B\nVwxvW7PjVwYxpPpK3VAXU5VByL3d6YtLB6owfVwpl8t7VwcVeU3r2RdTNap4\n8bOuFUR1skTgWChKJHuBLxfh2SjoUpqIhuX2Luxlz2MuJjp5Kni/q0KmpBO5\nV3zJ7V5AqNMUYGyAw4HAxubqLG7BvZLH339OD9n2FqZArSsVyxQQ1Z2B9PNx\nfQ10uKD1gxzH873HcIymLFI9hbSD8SPPDTmFMnKvf+gdti7rg5hpGdl20hdu\nCkkcr9P+d/0jfu7Gn2lkxLobqXlFzKqO+TMGUP8ectpNslNQHLaDY7d+tNA4\nGCdVZjXZStI3A1rp9FQlFiHnRUcHxVWNkhjf5gPNlxUH2LEgW5lexiIAhfO6\nUF0veH1ZrnlYmcwut0M1M75o+E80Ft6KcdK43QNIsMQ0/xMvBIRZZP8lYIIx\nuCu7\r\n=9ibZ\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDfIIPyjs1SN/65+09+bIq5FMozUE9LWwYoTDWHGgIg2wIgZzBmMbRWRBj72VBt6lftk/eSZvVAdzcX/1KgjF2SDPY="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.6_1587921576044_0.5495045828692131"}, "_hasShrinkwrap": false}, "5.1.7": {"name": "@types/react-router", "version": "5.1.7", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pret-a-porter", "githubUsername": "pret-a-porter"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "5f3ae82f68be6e5dac8ab2338879ca84bf50a5e6a91521fb20c84ee27d66d2f1", "typeScriptVersion": "2.8", "_id": "@types/react-router@5.1.7", "dist": {"integrity": "sha512-2ouP76VQafKjtuc0ShpwUebhHwJo0G6rhahW9Pb8au3tQTjYXd2jta4wv6U2tGLR/I42yuG00+UXjNYY0dTzbg==", "shasum": "e9d12ed7dcfc79187e4d36667745b69a5aa11556", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.7.tgz", "fileCount": 4, "unpackedSize": 13026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJepchiCRA9TVsSAnZWagAAniMQAJThnVTOnfjk2MLLDqBC\n8YYzv2SVFBZGoRHe8SPullZRbFWTobHNqhHzHPMOGg2+yuaIXx2YPVHVQwLO\nbT60/FzwP1XDdkF7imgUbD5Jc2atumP5GvUkkwExKVFmEr+b0ePuIvzxShCj\nh6LFDlqCp6/kHY0T/BZ20TATxTMqd1V7w0A3fAT0DLWJBCnM75NwuyEHr46q\nMs4hr6jBYVvqNiTEqOtlap12DSJm/Ub9aGcpdFTQpTsAAGhfBcxbMoI3G+yH\nmjYYfIe/5KCCPypbzILx/W689wYLK6mV0hLn1lM0V1kZwiKJNu93Bfmkp+pD\nZrks9Vw+XDg/s9/ZJvjybcyUMjA8O7RQuinue/yJWE9XhREQc6ucQxTRK46z\niI24rZJ85SOqKuw7JNg3+cvzr8Y1fZExQIl3ciQ9WLiynm3ERMlO7yCTjoJ2\nKE1qq3pG4l4fq51unQF0Swl90aTpWF42mTKvznXReIYqUtGCIL7PhYG1Tzqa\njSu+DIORdOfSZCO24TCAwY6FhUzugysEj8WLKOPsONGfQ1iq04Izcp79LyJ1\nW9BXueA79RvqYk9uEeSjhj4QULjvbr+KhJd404jbMVKz+XoSVE41ZIUUolHD\ncX3oUen67Q6Nt6j3PqcqvtPBIR9MzUdnI1tTraLzZ5GLEpJhr7UdsEnxPhpq\nRD4d\r\n=AvDT\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDrLJpm4D4JnfuZAhah/caLNVvyxCunCSbSEX0PFSfbOAiEAlPpW9BdZ6DvMu779ZWPQUzVi4XhznUeTXbguAzNgdTE="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.7_1587923042272_0.2603614311077238"}, "_hasShrinkwrap": false}, "3.0.23": {"name": "@types/react-router", "version": "3.0.23", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "e5d5fbc83835fd5d9e368e304104091f33ec79cfefe42697bd5f58fb4d61ae4b", "typeScriptVersion": "3.5", "_id": "@types/react-router@3.0.23", "dist": {"integrity": "sha512-1JrVSk4hVdZOmrD1Me1UuxdgMxjQC8o+7b6aG90GKypo9f2gzbYaJ9w92v+8XRtsU5yVnfcPhuZLoWdZc38OsQ==", "shasum": "86612ce051244c60306724942cc0de24225a0653", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.23.tgz", "fileCount": 23, "unpackedSize": 17771, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevop7CRA9TVsSAnZWagAAOG4QAKNdzD/suGHseeRkVmxp\no8/kVV0oC62ueK8Pu4DcpV2ym2KVXnbCxXLtAFxSis2Wh/RuodgTI78u7tUi\noAHykT/KEqlYltok+l1d4WdaLRQwEPOGshlngOAMFgjtrUfR+rF3d0X/+L20\nHsYySlIt7pWhUTAcGR8EQ+2EhjhbKrBWQnD7vkJGZkRv+D/OqjST/Wtuc8xo\n7rFh7MNEObJndoUJHdyzWjLVOBO5MZon0BAXRc3GM4N4Hr8X3SFjfYeFtoSI\nB1Hot+VQqQdzTCwLEWONEy4VhMWDYic8eEDYm4WwS6ui1EiXHrY8X8SbDun0\nKPNNmRcFX/kpbomnUnlrvVC+VQMg7552wFoYHLK2XU+mSAxAP/GBTCeBDmgH\neXMN2Ofak5wJcRS3TRndC6K0M6aIg5S1GjC7dCAOlcCjEkmN1VTaxSyz12Oa\neTjXeTaxD7J+JYlxtnS7r0m+BcyoVIhwAVjNziSHM4ncfwYfg/h1PQgzzI2T\n/9c9pEEoIWuQZ68Y5A7+/H3K22zUELkhRxcvAVtYKijj7pYZVAWGW5O6lZny\nSMldnuY5YNjYRJl/vujWfLKlypvOcWtpUr2O8GVXIVaXr+uXZYoc6sRz0zDs\nhDZNu8ZXdwarg4yEkhRg84zCUG44972l3zXayqEia/kNLudjFrBNXStKQHu3\nh5lX\r\n=wZYr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCKaCYz7d02VK0Lh6k0xOBd0f+1vVFD5namvnDx1RJnvgIhAIbO4rhIUxsjv0A2NMit3aLRL8PNuL3WDcVPsEkrS+kd"}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.23_1589545594760_0.16058388873831242"}, "_hasShrinkwrap": false}, "2.0.57": {"name": "@types/react-router", "version": "2.0.57", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "310d0b8d71423fe099f3b41a14ca3e4ad1599ce4aad61594e51b6bff0ed43f05", "typeScriptVersion": "3.0", "_id": "@types/react-router@2.0.57", "dist": {"integrity": "sha512-zwPM9HkmXQfVOxwPzdvNWPfrBxnVFjqKrqJd5THhX1FKBGk1av6aD7FNdsPTD81caYyV3gWmlC2lnh5ML08qHQ==", "shasum": "f05a495b76ccf95d1e4b9d0fa14d9d1c9349f2ba", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.57.tgz", "fileCount": 27, "unpackedSize": 20212, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJevoqKCRA9TVsSAnZWagAAM4UP/iOb1FczIEkplEIcn7Lw\nZmrJKfjqhEOFc8GtDeoXeyueVHS+OgonbVjc18QQXhwVNIUXh6He4kqcOlqE\nJGabO0dcwMiMnLsdbgT0BIqTvzEFUW9Ehmdzti2USZ9U3ONDk+VWrGxOxkaR\nu/WCA1ufQFebIcd7MiCkEC2aroBF3HnNum0/7OI1Q3TUnG0Jo300ZStgIweZ\nLTjmiw2JbqgPl4yoJLRFu0J0V0QxEpMGueCly9alYtW9LZ0HSn8QDirpZfx/\noBMmoOldUiH6X3dhhHlL5s+VOIqqjyNOWO1Vs3olNderLxVCske1w7B0tsqX\nd4GZ8vaEHrvJwZoL4zsWtc6Dnrz63JdIgmz+TUr+7RIDk5mBY2yio/Uckzsx\nCgrl5r8CsFwNe4t6Pj3ujngy+4T00vcx3nkHGTJ0VLu4kDvnQ/tB6LGhgg/c\n7Oc0CsQakO1X20AiMgckMgUL9iGYXvsbe6ZIFQ5FP2pK7BPfHq2cBszfNjoN\nLgUBX3GCAnjHCoo3y9ZEIai4VHrJ9LoBeV56ondMuDO9tIrhppj1LPGLhutV\nG58jgl+GFbcnABHS9vke7Uutbho3+jwdAFxjdrGbpZuyQseBJhQMh489EeBF\nOvFbb63qSQZiYv60XKlYiOrj0dGNCoknj7WCfP9IDBzH2jX49w1Q8Bha0gel\nBH3w\r\n=6OdR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDAzE0P7gHEcumL/CSvekuOXIIMQyyg7UnAazJa9AMjNAiEApYOLOJkkbrDoqZPxeyUTS14DJvR2KfLGpJPJI2hJusM="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.57_1589545610116_0.12662138575759774"}, "_hasShrinkwrap": false}, "5.1.8": {"name": "@types/react-router", "version": "5.1.8", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "ebbcba9f704fc9b8cb4477bb7c543cc65aca22bfa45eaad517ec1ec478e5fd64", "typeScriptVersion": "3.0", "_id": "@types/react-router@5.1.8", "dist": {"integrity": "sha512-HzOyJb+wFmyEhyfp4D4NYrumi+LQgQL/68HvJO+q6XtuHSDvw6Aqov7sCAhjbNq3bUPgPqbdvjXC5HeB2oEAPg==", "shasum": "4614e5ba7559657438e17766bb95ef6ed6acc3fa", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.8.tgz", "fileCount": 4, "unpackedSize": 12742, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8E//CRA9TVsSAnZWagAAPw0QAJx4YUS4yNGk2zFe2jck\nQsvVpMEfD3MSlwrspewQrTGS2ANyGg26K0aqe3kT3uwdIvpV50jI74OpBp/V\np2SB1iSyUAuPBGPE7V0zplQlQ4a6m4jyVOYKGbZ8a1F4hfnA+7JnGPqPt61/\n4Gits87QH8aKNZ3KYW4wI2LQGT6LkbOAXyCy8tEtbBO1PrdJX8b3VFvSdwHo\nmYGnkoXCMrA8fy7O0Q0h2Enm5oG+ZIawBZHLQ2/IRAKup3T4L54OQNTpr2uZ\nC9RNBWx5Fsc9BxB259+o9rSwthxiSWvMUSSDtqTd+2xUzP+eoqrznEHXLyJX\neo0hFgCVFfxuJV7JzHvo+oKUoyB4k6aQOvUag1+X3tPDRkOvE6b27KlNYu/O\nUhX+nNoL9WPpLh4rDWZnL9L1zWQtTIW9YoISA6rge3rcWOd5sVID0HZmTc9s\nItI4qyc3v05JNry22yFT54wsSS7Hx/Fg9iYaYgvskMlbQgaykQnCCIK3TMRE\nJBkicJEbOTWb9DCu5e8iJpg6VDvQZoLCnw1tjxblL3dXUrfqoAUKF9wz3MqL\nUEp2U5vjcxtX2/ddgLwwM84R/PjfDu96YylIsy50CjltXmP/VnxPI4qSmZuy\n6MUdZM7Uh88AGnZpIMZsgSkPYT3dLjEcVBu3jBt0DconexP3/uIBNJEJH8zS\nkSop\r\n=8Jqe\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCw9dXrDocsqNKKKTZ1i7MmTZhYmBm92nn2nFMQYy7glwIgW/fkswtuS2sVPdCGj6DQPRq7GGOriAdkR63j3ZTp45I="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.8_1592807422990_0.6790126276916026"}, "_hasShrinkwrap": false}, "3.0.24": {"name": "@types/react-router", "version": "3.0.24", "description": "TypeScript definitions for react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "a1e4091a9d49ac8f4835d362bd6703ce47f58bd3a95045dcc14b226e38e8e77f", "typeScriptVersion": "3.5", "_id": "@types/react-router@3.0.24", "dist": {"integrity": "sha512-cSpMXzI0WocB5/UmySAtGlvG5w3m2mNvU6FgYFFWGqt6KywI7Ez+4Z9mEkglcAAGaP+voZjVg+BJP86bkVrSxQ==", "shasum": "f924569538ea78a0b0d70892900a0d99ed6d7354", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.24.tgz", "fileCount": 23, "unpackedSize": 17777, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfM1aVCRA9TVsSAnZWagAAJjYQAJUDnIDAMTdPuOHQU29f\n7hrXPRipr6BctEjNkv3u3q5fJqGblMiqYTEaKu8nWW5yHLqUlRxVRl5HceqL\n4+4iZIltOi1g4UC7RG8R1RxCFFDFVQnIOtP0dN4w/LYLq2p2YAvxgningWmV\nWyD30Ljq62uzLqEQGe4wVEiqMRlQZz51i7E/zNvdqNpVQiaYstyky8kF4PwK\nL07MOALQ7liMxMRHwUkr+cwPM24YBvhvqamefsOPIpHwfCIEos4F/SZGl5nx\n5lwTmaSXu8Au/e9ZynPQWkmQr6i19oO9QyBiHro8KOAs1xRUJJcD9Hdep9/a\nPTPeMWKmRQ585Qiqb9XquS0OuqKUP3kMRMHSDb/vcctlQnRUTXg3WvrnGoY7\nC0uNmRsQR+kgfHWAnfbBNQD507ShQR51JpKonkgXPWWvT3YO8CbmABe1sn/f\n4O9i3JTAdyQytD6arbJR4+BlJx6NDuX5ixSStRu2DCUdiJUCwKbW1Zs4od1O\nR2NwrvpTKzxnNmigJStqFGUGUmuohozc5+GJjzfgSEHmu7ET3E28+TTD2FrN\nqC7Tcf1MA0jsveTYESuBJGk5tCpb63z6aRKW/nYcfWazyvacTlxYjjWVaMLV\n3GzGR8s122VNnOsUBdobDDXIHwfMOmZCCPnaHWZXVfD3bXUZk5guIG6VCK5N\ntoGD\r\n=Qp3d\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHabpIU35aKQBUdMvhbx1UOElZia00oWgouUTaA8pMl2AiAdPBKzK6lH88rdzT9OjF80erZHhzUHGZ3qoRl5TkqylA=="}]}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.24_1597200021516_0.03983844170729256"}, "_hasShrinkwrap": false}, "5.1.9": {"name": "@types/react-router", "version": "5.1.9", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "ad71298302ad74741508b388cefc274d77b466f2f082a995389eccf7ca4efab1", "typeScriptVersion": "3.3", "_id": "@types/react-router@5.1.9", "dist": {"integrity": "sha512-US6C0rq2Wt/7uje1roqO0R++Sr0jqplKaBChDY5sNg5k7GC/79YFK0ZsLEdemqUjW05wq1Y/9YYEUgfNZ8TlvA==", "shasum": "92a5558f22243d45f90e7699812c342bd98ba41e", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.9.tgz", "fileCount": 6, "unpackedSize": 18107, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf6eOUCRA9TVsSAnZWagAAy7EQAJVdxefzm/leeoDgfvZt\nX0PLUkrFoboLSdcdMIT+6TfCtdQRTHpY/dgTqFXYML9oEEsOL23vmvWHAWUb\n7AXacZbMLTSEmQKTO1ByOMIIeYclwGsvtGbQv9wI0tKKZQxn+IOPW8PO7vB3\njtkQD9VxUqT1LLMg8bj+VemcLRGwaWf4fI7r7Or+fS+h8U9OfGS+q7Qky6Mh\nL6EqLxFqHN63yYbHt3N0E0dAi75ZPjYcR6kbsUDqoPfN1QV0S1VmjsZhIBJq\nwLN+75bRtOMbNFUj20hp/7F5TNrsXeYi9/CDZWxN0+lG0JLh9UD9CkdDls6D\nxn98vFoeEnnS3urSKlQFxlCKkC6kksGokratPtlvjb2kdRLPkoT83l+fi1ux\nTPU2l4c7OyjNHXQGAKkeT97nWKAkgh6cEO3HINKbhZF/ADOIhQhnPGIaHkgx\n6xvBxuImH6VOfWKbm8ExPNB/u6BTgM1qtXxfdTaSm2SWvCl+94HIoxv3oE72\nOy3bQ6KOGDpesnile1L8AoOkaIggqpMVaExkgiiXWlJEOEqxIDmY3nPOoZfR\n0dA9xZb16pcmSJ4PJesnZzaCWQ5QOuaEcaa5K0QNys+FaxCJEhbBNWeBYMxe\ntGIRn8S3x2rH43wEpku70VHXCNyiSfU2koH3jUY1c6XWZf1x/JBiQZgAK2+7\nHpD+\r\n=McIt\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4fiQzXFzWAr4gzUQtsNmIGRzEV12Lv+Fi6GUAAYnerwIhAJLiUqohdag9QOHGNu0fThVhQr+RWCEYSro7dky532uD"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.9_1609163667553_0.7711946029388888"}, "_hasShrinkwrap": false}, "5.1.10": {"name": "@types/react-router", "version": "5.1.10", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "ed4af19df45cd4192eb1947d0a3001cbc40e34c129b54bbbc04bf66b5ab9dc27", "typeScriptVersion": "3.3", "_id": "@types/react-router@5.1.10", "dist": {"integrity": "sha512-yu11Hu16CfGvvBWc7wluRlxbwfuSlY0snEntbbOTvfgMvyO6uLaEpAbnVOntr+9TNIpR++OOlPkmDcJPxOXRaQ==", "shasum": "7de7ad3df20a8adb2b97943f1871ca4d2705c42c", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.10.tgz", "fileCount": 6, "unpackedSize": 18312, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9c8eCRA9TVsSAnZWagAAcjIP/1PN5QWX+xp6p3E7Tnyq\nYuRO1xTLC2uQM8/HMrHqvuQCgSMQjJ6FO+9TXczztNgzqx4tUBSSSkKgW/bp\nz4fBPOyUy8i0psit4XS8ZaUL+SCWaicCbcMFwkcV+JDqnRqhpptKliRxudA9\nhSeryoQXh5y47lyE46rkihi1++dLMfIIC78DTjKt1FE+KRORd1tr19guwNn6\nb9eFb5xSna+8w5lKZRTKQ+kQrdIrFbtwVnfW80UcSzkYiOIwkDe23kyt8oEX\n1I5i2VMJeIRxaE/EwlQYGrmRoADL9gasb0Tf4giwNEyL1QL8lDDqrR80t/XT\nN91hDhR4VXG0ci2wDq1SXx4npRpnK4+FduMVWOhZNQT31xdJhiyduPnuD8Wb\nWcvxlaT+VwoT2bHUNEJVKWXAukmQaBwL7V9KZJHKm3aTsMLwQCTA8G4A5Twb\nM9SuzgwLF91+27tPyagHzwPadG+kNdypVXjRP8ZuE2f0aHMlDn5QpB96tt75\n2oN3mrua/2bc4GPtcrLF3ltFhd/dYmNJk02xxpQ0JQ56XalfdfVoAmV5ZjeA\nxiNiC+7DyEdmc4jXvyrr68w68rXUm0QZCOyyWaoDEue3XEvRkHh3QFvOAh5k\n5kKO0RX1MiiN6k26/q3rvxdO4/rGmPkiUhi2DYvQUjuqeueOdTTO9BtH98QS\nyKxR\r\n=9+xs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCICJafZi783FBZ2GlOzUxzAYlfaR2MrcToXb2xrJjhrSjAiEAlpkv76sqykRUjEGAOuFSzyxJBVSCl/Hv4JhG/J7ILgM="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.10_1609944861241_0.8482700005414037"}, "_hasShrinkwrap": false}, "5.1.11": {"name": "@types/react-router", "version": "5.1.11", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "961c2fa917a2e0fb12404dd928f6e7ce482dd9b54353b0fca2443ffd9151937c", "typeScriptVersion": "3.3", "_id": "@types/react-router@5.1.11", "dist": {"integrity": "sha512-ofHbZMlp0Y2baOHgsWBQ4K3AttxY61bDMkwTiBOkPg7U6C/3UwwB5WaIx28JmSVi/eX3uFEMRo61BV22fDQIvg==", "shasum": "b01ce4cb21bf7d6b32edc862fc1e2c0088044b5b", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.11.tgz", "fileCount": 6, "unpackedSize": 18326, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf+J+BCRA9TVsSAnZWagAA104P/io0vqeb8aX8UQg2pnWg\nZWoRIk8J3rRc5pKs8M6KYMEwYVQB6hw9KPPGcIFG9b+uFHX0wHrqkBbkmFVl\nkadNWaheO3VPAFI74pDsCQ1iWCkjHLBX49MxrVIugVREUmKi6cy6muFBvqER\nn1GIE/Z9JB6icZB485teytgZIU57SGuHspyh76P5DzutQUE3wuT2R/sjrpfx\nTYPXrcShBl5eDFkr1wqIqktk9o/8+T14jXUjr6+nS7K5RMBJ/9bQoJBxvlp4\nh6l4YEvR1qLeitC8ozUPuJU/M6LUWfnZ1eYmbvLkI1klxy223Jlc0T4uH5ml\nruNYZFsRiP6O44/dolcaZhP8mFjg0HoxEG3i943llzlTHM2Ct6Ay5t8I9C89\n5ghhgOpaEr+6VJLxi6GQ0uJyczYfMXOkX+qvpMA5i5j4Xtx4LRMGH1K4dyK8\ni7ZTpdrN4CjM9x27S6I52z5NprnEtnuoelD84FJHm1TicncGKwhKYm+8PCSn\ny0WYL5e/nbMW42O+aYwiJ6UHvnGvGQOC0BP1UVPW9wlHBAQ7WJlezbA4snO0\nwCOgDJ3pbpWWJPncmJFQsNjlMzKeOhZWuFq3F33xyg8w+QH6YMv8N/f3vCop\nhJu1DYEckXwtudEPvLDMSfj0YEXNmnTdxsR3yYwS57Vc85If+AHCnTBRl+1Z\n2i81\r\n=8MGw\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDtUxYyxhS4S+GYH9wFdl9uUcvds6BNAp04Wq4ZzrDcCQIhAL0zPItAgsB0+pQgAz7TkuQ//UCs/02de+VMNJu00kik"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.11_1610129281289_0.9637145289948059"}, "_hasShrinkwrap": false}, "5.1.12": {"name": "@types/react-router", "version": "5.1.12", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "cbc35e382f4f32a43190d682ad28311613bf611cf4b9553a199a6d6c6a123249", "typeScriptVersion": "3.4", "_id": "@types/react-router@5.1.12", "dist": {"integrity": "sha512-0bhXQwHYfMeJlCh7mGhc0VJTRm0Gk+Z8T00aiP4702mDUuLs9SMhnd2DitpjWFjdOecx2UXtICK14H9iMnziGA==", "shasum": "0f300e09468e7aed86e18241c90238c18c377e51", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.12.tgz", "fileCount": 6, "unpackedSize": 18728, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgPTjfCRA9TVsSAnZWagAAfrEP/2cZIHmbFIK/K6KvNQHG\npWYmdIpyTa3S4gAv+i8j8yZheQvBC7qw7IRBmJjHeCOijhcbh1FEvImbIJet\ngWUOh6aZcuoL07+dwQ6Qhpvi4FAZ7X64vqzdz86Jdb/d9mpa6kIsnTCX+9Xr\nNglAiObyFRshj+iKdTy3GUlT+4w1uNV8k0+eVJkHOtNqy+ydmDrwwYD1v2AU\nBLN+5Po2z2zKPn50LPzmYmwFUdNFM/0dSdlE0ia5F4AUblQ3WtQZpJpDL9xC\n9ASY29PSrBy1QUorJmtR1hLIpx/DJcsKJonBEntB2ON73Mt1WqzX+rSoVPTJ\nNAGFn7vFnDOvAuq+o4Wo6v57/jVfM2yPTTw54X6vF03j0m/ILQ3zTwGGPakH\nRYQQAjqR2N6bx6rwPCM+0qouBg/+G8sMnueipj14c5wfjlo+Up4rh5lVifPg\nlfqz+tQ69xwWAMSTR2zv5QWFUR2F0Fov/GH0vZ2qdLW0EEZB6C95V4CEXhi9\nkZLcK6cwy4utS+yKi6P3ov30Hx69QGStiZZGlp5ikhFJm9yNDKFW9gD2zpkC\nOBIaEX0eYrBtFpr6Bmbc4GudQWhTp3nn+mtyjzXZODWll5PYipWtRXNsEG1W\nVeIA8fPqvh6AD33ybzj0wIqKhdT2Iw/mkM905E9vd2WKvwnUjPMKYvjA+d2e\n6g6A\r\n=pHkE\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGGPdfaL5VyC3cISs6XB3KU4mha/gvwcP2+/fgDXNraaAiEA+SpIwh9ar7nml5eFwm5noyjFA/ELO+lf7QkyVkRO/PY="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.12_1614624990644_0.2437580358800333"}, "_hasShrinkwrap": false}, "5.1.13": {"name": "@types/react-router", "version": "5.1.13", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "88e493da2b191ce9370d072c9e7f544be62affdb5e5cbeec4dfd3f5d5f2bdb45", "typeScriptVersion": "3.5", "_id": "@types/react-router@5.1.13", "dist": {"integrity": "sha512-ZIuaO9Yrln54X6elg8q2Ivp6iK6p4syPsefEYAhRDAoqNh48C8VYUmB9RkXjKSQAJSJV0mbIFCX7I4vZDcHrjg==", "shasum": "051c0d229bd48ad90558a1db500708127cc512f7", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.13.tgz", "fileCount": 6, "unpackedSize": 18737, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXPYzCRA9TVsSAnZWagAASY8P/jYeopZmj9izavlRNXyw\neAbl7gcQ+e9Yc4+aywa+5W3UqTRSPpVI+iDMe5WutlGeJXDVqKJXXdtWZZEs\nU3TRr3W6XfpXH3SQBaTcuFh+Muy6nlSHOsZ5kOCsMFR96MZAktTN2hv7QXpj\nhJwNdtbd2CuwdfmSawMVLUc74OEtAXMhzSqheDhj/EhSgsHz3L2zvpPcCpuf\nJ29s4Cru+4yYxJaLOsqLPoqbUbM9hII7BpyCiDjYbXStz5KEgv+UKPxxDwWQ\nipmph496P22sgo9N12WtMNnuulDl/U4nvfzdZe2AbESrwi7CRg2W7KV3O7xz\n7qsen4jmzmkzyKKYXECujkUMw0h5mqksRk/Vx+X5PnSrFHobzrx1eDFrcXwh\ne6uc2Skheke9UHeYiW+StANrWoHuTLXn8Nr2SZSsnY5qSlmpUxU45bRmlHEj\nsUgudBCxoKh2sUn6PUql+zvsiWHGucd411jpMzZ+X6ssZ0znckfIVjljy/ol\ngyQXMkgktC8w5V8guKVTYTxVaWkSu4KH3guLwfcV2r3BRk3XwQM0kMgSJrd+\nbqD2F8+9abtHhE9a6g3bx5xj6gdH93Hzt09Bh4sDBTefoHyzjL5LVwo5PLqg\nzDmFYLkYhyCcslJco9Xw4weQB5QBgzmhZf7knhY68hWap18Vl83Z6w2H9fDt\nDD0P\r\n=d5zM\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQChGZdlNEmGpeGbcz/mLexfEKmycDEYc7Aa1eaWw4Rg6gIhAM29bKn69a7lkWuLWwSzo3Pi5g7fS6OJ0PaU+KGmtKRH"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.13_1616705074612_0.42412384097950406"}, "_hasShrinkwrap": false}, "5.1.14": {"name": "@types/react-router", "version": "5.1.14", "description": "TypeScript definitions for React Router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "6ab7104d18e82494a9b4bd9bbeb1f55796f525bde99f0f0cd57bf6afee256363", "typeScriptVersion": "3.5", "_id": "@types/react-router@5.1.14", "dist": {"integrity": "sha512-LAJpqYUaCTMT2anZheoidiIymt8MuX286zoVFPM3DVb23aQBH0mAkFvzpd4LKqiolV8bBtZWT5Qp7hClCNDENw==", "shasum": "e0442f4eb4c446541ad7435d44a97f8fe6df40da", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.14.tgz", "fileCount": 6, "unpackedSize": 19077, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgklDfCRA9TVsSAnZWagAACqsP/0fp26Pia+hMdE6GRdOM\nJEiVFPhVZns1hCnBtyZ6PHCkLyzZkeqDzdNSxTnqoojhDMpRPBwoOr3t2Fhg\n3CeoItz2pOAnbisIcFN4LmukoR26AoJZijSSj1vtSztYCpGVjo5Y5v5AaMJh\n1aq/ruYC8Mt4VEf6bgxwlarjR4MfKzO+cusXGsJ1OqFKbOdEV04XPlPtRNbV\naErZFnQi5XDkwlyG28w/yhHXZ6uKRM8WvYroxaK8pA8qFsUEyZkmh2V15t47\nqIE4/J4YGVo+rRR+oDy+W52E3v7ucCk9MRCC+m9o3NokTAvPDEDyupCDqxZS\nVLJ1OLDo7LZJI07Fsec/OClZNyewrwTQw9cdubhgo5VYVp2udLH7yWWnlxJN\ng7Y0LPZ9s43vcHONaH/lDM5A8PZijY66bkfUREDHEKLa2prUv5sDdLm7zJgl\nnOKqz0Pe2PBBrA2deVQiAORGfKx0ffktbKf+NTzJD2/ZdwJhuDjq8UmgL2DR\nrkCbGlG/g3QqgIxEMTu7fDlUp6Qs4TV1edkOHSZBC6x+4+FdLldmgHaj5fRs\n3nrtGzJMTMx8ISdMi3hxUIcZUp8S2zTVv4C5D0F/4TUkfUChvdocAeD5/JUy\n00MuOC1dOFCWoZpPrB0B2oTVDZFPrxcmxZORDyMnMdeAI4mEteTrHBAdlNJ3\n2lh2\r\n=Xbbf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFaKCCsoLNsJ8nOdWREqO/mbXeJy1JzSSBrHlfvLTFoAAiEA1AApKgqdr9qW0/SxyWJy3LXMhSfdTWBMGoiVXm3kFMA="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.14_1620201695081_0.9774091722593226"}, "_hasShrinkwrap": false}, "5.1.15": {"name": "@types/react-router", "version": "5.1.15", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "dc2545f9c18a14a72c2efb96a90c8e4c6f1dafde83cf7383274153ec4ea3fcf5", "typeScriptVersion": "3.6", "_id": "@types/react-router@5.1.15", "dist": {"integrity": "sha512-z3UlMG/x91SFEVmmvykk9FLTliDvfdIUky4k2rCfXWQ0NKbrP8o9BTCaCTPuYsB8gDkUnUmkcA2vYlm2DR+HAA==", "shasum": "c1069e0da4617fd315e381b56b18b89490e14e2a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.15.tgz", "fileCount": 6, "unpackedSize": 19299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgveB9CRA9TVsSAnZWagAA1tkP/iqwQXD5tkbt0r35VsP2\nuWIaOEYX58z4uiY86/BnESWVDHD5YY2sCi4cFHlNdnoxCJpHWM7dk61chJYC\n+mFnIvuX3aEpUe9pqwzhEd15eBggWqS8hMJZR+mnMXiXUCCJDbqP+U8kaa7V\nm83rkJKINEoH+DHkhMPJfo0fkUtECbA4WZ6AlGmCOP+ln6eHdqr6iNGXNHAP\nnmyEtQrmvM+/N0IN7wYOWQvt7+Q3WUB2EbbcKjxiovjAZeVvZC10FxAFBht8\neH+4OwYGZ/R+DqUKzttK3P4C9otG8LAqaNbcRpgfM3orMWSYrG1DwbuZuybt\namoSNcYud6brUqi7+3kIDd8MuOBoRFKaUZYXnX0mmKR7oa5ZCQgwzkGbx6XG\nyJzTvl8yrTP+p+rsk1/SIQyce7s1UJNwv3f55ylj4jxT6nrxafebtkuMa2RQ\ndfPcjONPz8tf1t2eY/QLSjcd6LM2ZF9ERyinVaL4IOVknr0zsq0TO5mUASJ4\nEHaO0zaz3tq4nSxvX7WY66PJLJHDlPukG2/+mP+4qguu8JtnxqnUpT9enuz/\nfhQR3gLN+b0n9Wsb12U7jxcD5xvMDjuV+I3eZ7ermu/XP4om/GJy7rKhARsr\niH9D3nytQ/qepOgGdLJYb9ypw5EDk9OCTDKDq9KT09D9eJInP3MqxzOB8WNs\nzIEN\r\n=eyt4\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGAJ+Vv1PZnxRuzFN7Lfh8YdOYJHjhldWXXCLQJl874hAiAOtiOwzVy9PS7K8WRk1FPt60awKmPD5Swp0/QAyBJUPw=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.15_1623056509533_0.7177541313173856"}, "_hasShrinkwrap": false}, "5.1.16": {"name": "@types/react-router", "version": "5.1.16", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "446625c9bcb8136264005b26bc1f43afe252bb5fa2145fedc3a17a71285a6bc3", "typeScriptVersion": "3.6", "_id": "@types/react-router@5.1.16", "dist": {"integrity": "sha512-8d7nR/fNSqlTFGHti0R3F9WwIertOaaA1UEB8/jr5l5mDMOs4CidEgvvYMw4ivqrBK+vtVLxyTj2P+Pr/dtgzg==", "shasum": "f3ba045fb96634e38b21531c482f9aeb37608a99", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.16.tgz", "fileCount": 6, "unpackedSize": 19651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5ekWCRA9TVsSAnZWagAAaAAP/3lsbTXyhn2mKiFiOTu7\nNxVp9MvB/1JQJz7YFV14FOirpsrM9T/rfp2mOTz7gbnBzjhVSruQb4oqQHrV\nzUrwcWsAElR4NEC2dkskdcT7CF67IiDKkML/T2cG9W01wucrZUuVe4THCjFQ\nJzQWGWgqrpptKQLyzPPs+VpPkELoNJMb0sZj3e6exz35PlWuQ59cR2HZm45c\n1fzMeGmX9wz/dmORwnykM2cHgQmIT387zq/dINOdOG8AAxNRcMU00685FU7i\n9HRufK6ybFJ9sBfkW6u3BSbazIv+h+ccb4TClAUYOU2LiRs+5kboBzz1tPra\nGZpdorGAxdbPT5zzWef6mvYXTJ1FCLNfygvACqPkLPyj4g2VPWsikk+sg1P1\nVxFhby3YLonLKQuGUaYWRy679PhmVD+lFbVeG4GqWRcUyQbvG3WxhklPpl3K\nH77oiX+2WKlXkt/GoHIw/Hw+9m9LqlWFtV1uCYRdJ0TtMUeRwuv6h2wNZMx7\ncJdgJnA00nPAwK2qnK2MiUFnFXt29lBf82elkjD8ivODshgzY/LtPdLTN++c\n7oTEVQGieL4LNQNzuIMYqkhBnjFyQL+IqlBCSMciEeNw9oHmh6c9r6KwwNcJ\nMxPGDGhL6+iusp3zJMb1QkvNJih/kewjGlhoxhWZ6p3tE+ZMh3IHHZZ5+9Vz\nV8tS\r\n=LXNS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIC2UwNKFVrw/OJNaXJ0iwhbuNcJAm9EB80RERg+kIGnDAiEAksejjxsjZtqWjN13vDW8rXkxeuJ3QAMuohG+F5WrzIU="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.16_1625680149589_0.42533651250329796"}, "_hasShrinkwrap": false}, "3.0.25": {"name": "@types/react-router", "version": "3.0.25", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "bdcca9e22643c1a6c0b9a9d4b10c52ec9ace324389d7383d2e90d6332fc348fb", "typeScriptVersion": "3.6", "_id": "@types/react-router@3.0.25", "dist": {"integrity": "sha512-8G7W+/GbSzC1tiEp6fir/X8JHy3GL561uy1lId/as9rZOGi8x0oM+TvEipzZGrW6N8ILiwTVK1A2vMYFWy4Z3A==", "shasum": "a06d5ce69481cd12a4fdfdaaf1adc7a47ea1eb19", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.25.tgz", "fileCount": 23, "unpackedSize": 18130, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5ekpCRA9TVsSAnZWagAAtS0P/RvUqyppHK6vDUxjtjkP\nhxEjbXA0BNP3UfpsQDxTKqu9v0sje+Nw7jkczLAKG6fP+QeNZ4nfBAm5omna\nP3oKNphDAgoDirSALTAQv6iq38ls6KNopAGEY7sqMYzKr56mIynHY6npy95Q\nQ9ECVBT/HG+8mx1vgCQAzJqa9SsXe2uhnRIHe3nD+lXydhWh4z4FXr+zY/NZ\n8ifuGQq+OJODL0UzYq5Ryi24nIbISTFeiEYHM/kWsG5MDhkf6dDTgxzNHIpw\nQKCbXTBNbg5KKn60FTBNqsZVoq26kpdksBgevu9fVRomsWAK10k2EE9+4729\nVZ1NSNJuHnd311wDkLepQB66P+Spi7lx5MM7bOGtcHboZmRMkmMV6/S+I+9p\n37RU/LBSAPahHGlQIWqySyEBQ3zGfCiza/YCcKcQvdpuEmA4tbzSvpWAVGiN\nVStsCubqjJHtg0JgHk5ANoUpso0JuX++gW30/qRFhAneyver+FvFUQvBH3Ce\nQ0dgUDDQ7UIqp9adq5oEp4rmVZDw2q7bCt2dNqsWT5hh4ubskDBGasnzZ/tp\nw/QVHyQhDgOxJo304AF0P/En2ENhee6xBFM99VXzxa7Mfe/IaBZAr5igeCsV\nnHMflbvVxL2h3K2CE5Dg/HhhrydNUieal3yB/loDVCnKVwnEUvs3dzAsPRrX\nSWDC\r\n=mfM+\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDUD7OKZ4yMmZSyxudXmXeT4KxZ+DmuXWEzCvMH7fpObgIgeXjQ5c7OEp78VdpDM4ef004EzBn2wZKUcnuK3mx3YcU="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.25_1625680168738_0.8506259861528012"}, "_hasShrinkwrap": false}, "2.0.58": {"name": "@types/react-router", "version": "2.0.58", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "826727a5055c411056cbeb9e0efc7a669818ad16355265519824fef519f770a0", "typeScriptVersion": "3.6", "_id": "@types/react-router@2.0.58", "dist": {"integrity": "sha512-6ssUpg300dLfv4o3m0BQ0EffN7IEbHCuBhDCyARDOgH2nwjzinSFzSMlz2G1/6wM2fw/hJniHv1tiQCozU178w==", "shasum": "6d266b3e5f402721fa847bacc7728cbffc86ae9a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.58.tgz", "fileCount": 27, "unpackedSize": 21149, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5ekyCRA9TVsSAnZWagAACgoP/0zR9/hN3q9gxHYuuLTE\nukGQYWqN/wG0cKrnUSdJELagE6/uJMsv13a+gdG6TYDXJoDuWO+vcE2kTgDw\nGHLJ4U5zObCxHp5/itDy7/Ptdg2Rh8VhOCD7mZOuI5yTPrqJ3lNU3vDLAWwC\nf2eyODpkybcl1aqlUb+ASS+j3TntZ3tVYfFX1tG729AYUI4iKVQaeM8B5iED\nnvbw0DTXfqeTdPFxO1MHXNtpzqVWT+tjIItzZXw5s51JvQ4OfXej6AS+Mdck\nlHRBm6Q6aE+kuXidu4tRfIDrKWKa6FDejJmZUVw71JE2db/ktNiwhZgjeh2q\nnCd/zeTHmYf58CaKUKYlMtdZpFhfwxnRUjGW/Jwm6H/kOnH8KDq0pdZDF6Zm\n/fF5GJLOtDsdIy4snm5d1XoRsUWLYDFnSIBUJ87YOxt4IGkXWKu6md572WWq\nN1F/9n07LmonG4W2oquNeU+6VLCLSV1XZZ5AoH5ED7GZui1HpsxlnmU9uDO8\nemr1G69fpFnNzTasJoWjpSoTt6Tvvmzs8XvdYauS3RW0RupiJIzZ6T57DgnB\nYcOMh//4+t+emC+fNL+PaapXRYFN1ZpELJAennJ9EuA8fRl3r9AWNe650ye1\n8Fcxb6hIUBoX89DAVkJy97ivtXFOyWJhNo4DbVH3YDky281mrVfgNDncLvXU\ncXbG\r\n=2mrn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCZPxKkgsRYHZD4NI5mfcK0GjzPoBDu3oevySXTAbMFVwIgLoiobxHseixnath5lOg7NRy72n/yxCHAHKjsXJVk9a8="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.58_1625680177406_0.17484898229832946"}, "_hasShrinkwrap": false}, "5.1.17": {"name": "@types/react-router", "version": "5.1.17", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "*", "@types/react": "*"}, "typesPublisherContentHash": "ed1e94ba609b2703ee57b3c7ab7e1ee7327cdac5414bb35da6dab52fabecc924", "typeScriptVersion": "3.7", "_id": "@types/react-router@5.1.17", "dist": {"integrity": "sha512-RNSXOyb3VyRs/EOGmjBhhGKTbnN6fHWvy5FNLzWfOWOGjgVUKqJZXfpKzLmgoU8h6Hj8mpALj/mbXQASOb92wQ==", "shasum": "087091006213b11042f39570e5cd414863693968", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.17.tgz", "fileCount": 6, "unpackedSize": 19843, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGVyAwYjcIlOUOusA9InBh7lwv9BaF4CpXifrA0pvkwNAiEA4VTvfswiJe1MTG96LXPJggRenQ61/n4nGJMe857FG0g="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.17_1633552336154_0.39845600105055134"}, "_hasShrinkwrap": false}, "3.0.26": {"name": "@types/react-router", "version": "3.0.26", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "1a1dafdf94e649696a573ff42f2db74a4c1b0f66b6a8343d8fb4defbe0941693", "typeScriptVersion": "3.7", "_id": "@types/react-router@3.0.26", "dist": {"integrity": "sha512-fXijsAY9jFSQbdTz8e4QCMm2s8xkd6HPnYQcg57K4H+Hy5xeB+i2zbY7BaURGewWecN56oILH93AjaYB3BfVig==", "shasum": "09406b00e1df696311ce365819b5a21efea99975", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.26.tgz", "fileCount": 23, "unpackedSize": 18227, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHfBL7NpHJALe7tVAs8U6NHbhMJ9bWTWxnb77mbfdDrUAiACV4jeUY8n6hlMU6cZrFgdK8wuIsEo/46vmpWUKtNa5Q=="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.26_1633552346563_0.4993389095217664"}, "_hasShrinkwrap": false}, "3.0.27": {"name": "@types/react-router", "version": "3.0.27", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3", "@types/react": "*"}, "typesPublisherContentHash": "93341c29e63177d96844cd3df0e3f78cd2fc423b1032fef2d2e7c6af1091ecc6", "typeScriptVersion": "3.7", "_id": "@types/react-router@3.0.27", "dist": {"integrity": "sha512-Sq4bfTZ8TuHFTHXyzA3tu/BTGm/d4B8CwtOK4qOyJNoJMkY87qMOazkBizMHNpszVR1+kYOCNLjPdh/wAD7ESw==", "shasum": "db78ffc4141e5b2d5b0e29bb5565094c05d864d3", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.27.tgz", "fileCount": 23, "unpackedSize": 18223, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXgeIxh94pIMxXU+atJHdI8GH29CGaYZf00maP/5u8PwIgc0Fxh7ZbNs/HVAhTOqf0Vdij85kNShlgHHxOtJnjdaE="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.27_1634763926236_0.6095716689769266"}, "_hasShrinkwrap": false}, "2.0.59": {"name": "@types/react-router", "version": "2.0.59", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "b401d5ac7126257560e32a496d57856403f0e13ee063ac15ab25bdb983688cf2", "typeScriptVersion": "3.7", "_id": "@types/react-router@2.0.59", "dist": {"integrity": "sha512-YdC4owfAMznDBLfME6KJlxIEk7RNuLzZcFYmqNmgdL6olKd/9oiVBfFbQvdoAySPMvDIFwEFuw+SJhDTnQdwyA==", "shasum": "1af9f0a404541c9def7fed03ec4f20c1cc6e9e58", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.59.tgz", "fileCount": 27, "unpackedSize": 21148, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4mw6v57MqxNqryitY2Br6LSrxIZjb7BBt2ILnR+D6TQIgIewxzQDUiIFj4pK7PVUc2MGx+zwxuTyBI/wMRJsmOiQ="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.59_1634763930147_0.2618675190346471"}, "_hasShrinkwrap": false}, "2.0.60": {"name": "@types/react-router", "version": "2.0.60", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^2", "@types/react": "*"}, "typesPublisherContentHash": "7d71c44e76e741c293a0c24fca7b4385b09eaa5b195e9c6ce39ae4141336f0e0", "typeScriptVersion": "3.7", "_id": "@types/react-router@2.0.60", "dist": {"integrity": "sha512-3EsuRFuL0rFcxedl5+zdVDvLlQpHE9Yptn2zeN6utS1ZihN9wiYuMg/bvN6h13QbQ6xAtuyZ16NlyN3lD/6kIg==", "shasum": "6e4289efdc7ba507ccb8d6bea09584277578f566", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.60.tgz", "fileCount": 27, "unpackedSize": 21724, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDYlqVIfe2hM7dqUWGSKyX53IDVsSOQ6a2KWaLE6wVGUQIgTLtDP8NpzYLugBLYbuzJYflh9VwbHxa06k+7iB+lqac="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.60_1634857594609_0.1017901390432987"}, "_hasShrinkwrap": false}, "5.1.18": {"name": "@types/react-router", "version": "5.1.18", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.0": {"*": ["ts4.0/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^4.7.11", "@types/react": "*"}, "typesPublisherContentHash": "29ffae2537b7f6d31a084661ddb4821ce78b2018a2f478baee639cdde3b4b03a", "typeScriptVersion": "3.8", "_id": "@types/react-router@5.1.18", "dist": {"integrity": "sha512-YYknwy0D0iOwKQgz9v8nOzt2J6l4gouBmDnWqUUznltOTaon+r8US8ky8HvN0tXvc38U9m6z/t2RsVsnd1zM0g==", "shasum": "c8851884b60bc23733500d86c1266e1cfbbd9ef3", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.18.tgz", "fileCount": 6, "unpackedSize": 19849, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zkvCRA9TVsSAnZWagAAGoAP/ixiXohwKjMHc6Oh0aDx\nmtn7bgXNyPBJJkOhIDcJS9KSEunqtzDfWbcsiJQT4JiOh3EYyzT0y+pMN4eT\nmdvmr7Od2t50DMySKow9aubSSRffZ6L7y68XFIBFHyyO04tZJH9CiJ2b5fVB\nIXsGYFjr8QxEa5bHmKsHq+hnusXEClPXhCaLp/Ho4viLgHIinAAt3bPcUQvM\ntim5Z1IbWeHiijbzFgEwp9TBeDaeM9x6YpmWNXDxO+80EIsJ0G/rcDOBzlhS\nsbAY8PESezNJLE8sJrOSeeP37/lXRVsSnA53WNsbpKKnNFRXS5zplbTfrKJI\nxp3Yd8RuIv+zqXox0Bixh1Ks6nz94YCXBau41q/km4J0uCCfl/GU9Fipg/d9\nkc4Twhi3qF7LOVOo69SslzfS1RIbLRl3SAXKUyoDFf/JPwugGtgSqe3ynfa+\nToYyhi3x/9qml1pdPKTCuxnz7bktf0t7JpBnQ9JvuQ9iN6xU47tf+DmR7/Oc\nSnSucExNGiobk9C+EzQSztK9sL/ANcnZJ5/h2QM8MYCKa+jBIUVV0oHsyliG\nA+qFD6oyrhRDjqkXrbCMvQGXITzKpCtkfOzFqoc53mZrmS8+EShgXppcX/W5\ny8EmINrDw1MZhEqQWKqks9G4X/8pQB14tirOjOzO3aIEoo3DHZswFKM2j4of\n2wRO\r\n=EDjq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDpgVFQR5WEvZUsh7mTSmELpDHLao3ORvuX4gHueB+UawIhAP6Jw5zLW+RVp2bc/wiu+lOrO2RinrKXnCFiMW6RyFTj"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.18_1642543406800_0.6429752485789988"}, "_hasShrinkwrap": false}, "3.0.28": {"name": "@types/react-router", "version": "3.0.28", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/ssorallen", "githubUsername": "s<PERSON><PERSON>n"}, {"name": "<PERSON>", "url": "https://github.com/gillchristian", "githubUsername": "gillchristian"}, {"name": "<PERSON>", "url": "https://github.com/nulladdict", "githubUsername": "nulladdict"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^3.2.5", "@types/react": "*"}, "typesPublisherContentHash": "b28e47decd3d742a0131215d4b4fe2fa9082978785f5abc9643d3f6440d03778", "typeScriptVersion": "3.8", "_id": "@types/react-router@3.0.28", "dist": {"integrity": "sha512-cf3V8fSBLAzmICC8l5qbnrJhdjF0ia8K0EZwL8IpJDBQN6ieHDdw8t1+9regufQxEVip1T4tCQRwhAmy26Zn2A==", "shasum": "ae457228e6934f6664e6b092afd7f8e1bb6eee3a", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-3.0.28.tgz", "fileCount": 23, "unpackedSize": 18227, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zk8CRA9TVsSAnZWagAA2J0QAKD2NvzH2hfi6y2thIJN\n/37x0Q4F/hUeCAu/8+0zCrsxOliOwS5yNUaA+Pq21Hyczg28wg1ck4K1mTRr\nUAcHLTXaTs4XlhgeYrACTSx/ou6uRieqyhxDZoJpvpgZcVtjev0mDXSYc56E\nfxLeKENG/mP3XNtIeNMxai5OWGlYy+0lS+jGJoAeTRElECT2enrXt+01Dilj\nrfKGeuseN6M4yi1kym6LUx0npaddwKkEEf9SPLvzKNdEbpgDJwyW7gxz0f5T\nxRsLZnJu4YyyL+qvZBcU4V7HXLoI9vYVvnNMnlLL9MF2q0N0xCkiTw5ZRhdH\nksjopYuhlYfCUlUGH9sudEBI36GJrXrPoENgryUGQZXdgjO+7TIYNyXu/ub7\nXp6ou1xkpxwHREQJQIK5RBtNdLWyO4ljqTayQCHUhc2TUkf0s3N5pPKAmFzX\nisPqa9BgjTUOmCkFusB8xtvXhZbVoDJ9zcTX809VYtM9rkYk89WaOcdbCFwo\nuINJ5iUujKDjAPvFSnLRPdVzAaE+cLapHGWdH0ZHrN4ZGy7BwhLMp+Ajcekh\nW0E2hOX5nT9miSOS5KZJPm4wpvPIY9b/t4I9p+EuPOMeLvMG8mKHyRQthNIC\nclMuwVzaNtRpO68zG9pWj2I6QUX0il2c9NJT+r2EEe3LZnBsptiiwj6pirzH\nletu\r\n=C1jg\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDr6EqAraEyFsqke7K+EdEzgXOvf8T+vRawbt04Xk+kOAiEAuAfKAfjlw+0blqXy3XdJOkdlTiIScPtVIpAQTfkj7xE="}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_3.0.28_1642543420251_0.3834807172373458"}, "_hasShrinkwrap": false}, "2.0.61": {"name": "@types/react-router", "version": "2.0.61", "description": "TypeScript definitions for react-router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^2.0.51", "@types/react": "*"}, "typesPublisherContentHash": "ec53b12546b84fed446087c131d8ef1a8594f0b15246b410f37212e0c89e66b5", "typeScriptVersion": "3.8", "_id": "@types/react-router@2.0.61", "dist": {"integrity": "sha512-vVC/ivsYmY96SVWSWHvqjiuItSmRb+hiwCQIPfsR5KzolHVkFGmKbO9h+3RNdGriXbpKM7wL/XNECPZXJSK9wg==", "shasum": "e71b4fc3573d6447f2a844140011e26cad372d27", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-2.0.61.tgz", "fileCount": 27, "unpackedSize": 21729, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zlECRA9TVsSAnZWagAAHNgP/R6TpJkqw2Ux+TZX9BJ2\nGXAqV130Y4t+KwMYckuiSbnjpVxaPtBMC5cmBvke33lhvxhC+s3D75yMYRm0\ngyZ7l0NsVHFDckgHgYXwZOhUVtJw8SauEviQwOifoCIcmj8Iv/nPfFO6tQ8q\nq2Gn+EHWwzGvNf84IhNSPqMaZATyc/e/i2uCMbhel3wAvAjXEQNh/Gbw1TqT\npppMcNVFhh7EaUQqavW6mocQLMtFYHUooE1kBKbbCq+wVw81OcDcI+eyVuPY\no8k5l+XxFqRHkEwSTJcyM3jXX63CiGZ5MfwawAew/AS9y6IXvjmqq9RYNxks\nRO3shx3CUYOsK5dFm8wWRt1VsuWHejszL4W9BcLyLyyy60qQPY7oyHJhpePR\nmiIcSfUHvpgUcZpCQZJGszPyo8ssWQyKekMkGL/kP62sh8cIdNGmFlRHOzb8\nWsipoCPKVrq0H1bxqPP1pUTb6U9NpzR2G848fhM+PeiZFUrmsuMmxO/riveM\nbMRBvpSvnS76KiiVBlUsCoM6MloVT3qMvW5C93B5cx9NWx+AdKfQPqcKo9hX\nlMPBHpbE3xMNiU4vUNBMA560+AxOj754pJqQf6FR51ve3H6QmhhzKEDIBJTw\n4FsX7wgSEJ5Xp9YVlzrOLateEVVgSNx/yxtwiB1ShfrrZJvTYmkNnjgdcZ0T\nI5p1\r\n=ybgs\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD/EpVqVRmm5Z4WUPJlA7rGlueXGzW/8u/mGe51zUNbjwIhAIekOMYGbfnvCR7zr2+dqBem2r2o5+CbJqpMDwzUtA3+"}]}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_2.0.61_1642543427869_0.5433518793067518"}, "_hasShrinkwrap": false}, "5.1.19": {"name": "@types/react-router", "version": "5.1.19", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^4.7.11", "@types/react": "*"}, "typesPublisherContentHash": "3a0f62e5e9f08512c5883b455435e78a227cd0597291f85f21bd9f4a2704cb14", "typeScriptVersion": "4.1", "_id": "@types/react-router@5.1.19", "dist": {"integrity": "sha512-Fv/5kb2STAEMT3wHzdKQK2z8xKq38EDIGVrutYLmQVVLe+4orDFquU52hQrULnEHinMKv9FSA6lf9+uNT1ITtA==", "shasum": "9b404246fba7f91474d7008a3d48c17b6e075ad6", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.19.tgz", "fileCount": 5, "unpackedSize": 14661, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDZCIvs+V+FCpBIon4axUJ1O0PE9rPMzS+xA7R7KGg+aAIgNOvvf8J+90JeWRL7zKrR7H/SFzG3l+EPq1UpixoiN8E="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIMt6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqsGxAAmxJkc8UPSCWVXPS2+n//mOXtJVrq11Q/LGfR77BNwS8ojsKo\r\nME2tPmcCGrDO2lzdgEFEAa++wnPVTIq5onZu4+AEi9OyPdcgF4Va4vsCO7Li\r\nkH054daPnCUg9tezfeRxxsAEDdB29JZ3kYfgGfDlxUh3oSfO8yvDb2fvfdUo\r\nBc1IUrR5J1drxpTa+FtLYLRqiwWLLVYNM8vrKgHRfxW++cGmvjf5Fp0YSUws\r\nCIhpUFemcz6yNBWXdIqTKm4VbHFuTbc785QApx3c0XCAxKox2hDSMqkT2xQz\r\n8Q8eWLdZfREHFhpXUwygEaRPPaw6VSWnt/xNuGpVN+Ze9XsJcS7DwF6vAuKT\r\nmNdMVYyAL9uAWALOR5UAyJZsZiznH2lF1VT8Nnvw989gaMdlKeGcJ3HKSQyH\r\n0VVNZ0BkM3hM35rDo5zuJ1v06yyCOO/RQ3X1KSiKxLbq0HGRwVSigiqSv6uT\r\nNgcE0WJlHLWe8YxIvXXwrW++Vadp0a6NMi70YkwKINw+54dz6GMf7ntjDYVd\r\nY5xuP/PKAKhfi/nBTExwgBBeM5Zeq63HgEWCugavIpB7cnAfaSKbXPiusmFV\r\nvGqygSNiH6vm9r9UOd4PP4EqENl/ukvIHfhmWBMDqWGXPqmk5UvZJIe+MEer\r\nUky86n9UGd8PhJyy/f2KVrUlcyxEcxPbeug=\r\n=m/1/\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.19_1663093626022_0.24377772133312692"}, "_hasShrinkwrap": false}, "5.1.20": {"name": "@types/react-router", "version": "5.1.20", "description": "TypeScript definitions for React Router", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "main": "", "types": "index.d.ts", "typesVersions": {"<=4.6": {"*": ["ts4.6/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "scripts": {}, "dependencies": {"@types/history": "^4.7.11", "@types/react": "*"}, "typesPublisherContentHash": "471509be13705fc944e92092c64b94ac19712efd46cd3b0bfe38faefb539955f", "typeScriptVersion": "4.2", "_id": "@types/react-router@5.1.20", "dist": {"integrity": "sha512-jGjmu/ZqS7FjSH6owMcD5qpq19+1RS9DeVRqfl1FeBMxTDQAGwlMWOcs52NDoXaNKyG3d1cYQFMs9rCrb88o9Q==", "shasum": "88eccaa122a82405ef3efbcaaa5dcdd9f021387c", "tarball": "https://registry.npmjs.org/@types/react-router/-/react-router-5.1.20.tgz", "fileCount": 7, "unpackedSize": 21708, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyv0vLCn1Y/Sno01qMf9H+PLtHzfaf4qNOMAubGV1AzwIhAKaehPO2YW9ouoIxOsaYfkQ3uY/BMYUwkoW4XgiybBdt"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjpYrYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhbQ/9H763objqGM0xilnwj+LGmxBEXP/aWWVsRBK71TARqIDRe+nm\r\n0heJBgA/M+uBjoE/YDerFuuKeJRDy8RyZlF4SKhsBJXpcbs/0XDzYiFqcYmc\r\nxnq1muYQup52Q40qUmRn3Me1CVe3WMrEvWFroX2Guzn5ZNEgaOwNEE7rY0SE\r\nW8AsDA98esnZ5owy6AnfG5s9u4JXvmrFEFzV9YMn52lyd2cAMYG/u3cC9urw\r\n3R9RCKbT/c0/5xymW1ZtZ6j7ftyZlSUj3qhnPGZBJx+RxPc/l94LtSWcFXU2\r\n/nxJ/2dMhKvK/RgWAAgV5mQKFwufdmMFknucxp9iLifsc8SmEasWDHE7Xr+t\r\n3iQNLFWCpvXEBwu+oF8xKBCB4YwoCzdUggieKyyEAKfs8e7Jq1rCKLBSZBkJ\r\nReaCmgLfzFvd4XOi8Rg80w59HCemk5hGiBZcE25eqfO287lbPfyi4M0WRup1\r\n1eG+Ahyo/Y5tU/YxkYpQ6Rnwn9uOVLxGemp9yWR61Gf/2798FmDHlW7EeOff\r\nmgZIIlo70MlTHDXd8wz4rIJHU+e6ZTog3nd+WQiFwhhNCcw1F6ROqP2RltXp\r\n3mbQc0susZ6sZyeSQYVQdaKMTePqI2od3ADanush1oQhIcmt3nQr1a8JOIQ1\r\n50xmtIf6d50YdYzyPhTLugnMsgGgy2PSXAQ=\r\n=/OXW\r\n-----END PGP SIGNATURE-----\r\n"}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "directories": {}, "maintainers": [{"name": "types", "email": "<EMAIL>"}], "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/react-router_5.1.20_1671793368774_0.6678287465667461"}, "_hasShrinkwrap": false}}, "readme": "[object Object]", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "time": {"modified": "2023-03-06T08:43:22.703Z", "created": "2016-05-17T18:45:34.270Z", "2.0.13-alpha": "2016-05-17T18:45:34.270Z", "2.0.14-alpha": "2016-05-19T22:10:45.225Z", "2.0.19-alpha": "2016-05-20T20:32:35.406Z", "2.0.20-alpha": "2016-05-25T05:48:27.231Z", "2.0.21-alpha": "2016-07-01T20:25:22.182Z", "2.0.22-alpha": "2016-07-01T23:48:33.623Z", "2.0.23-alpha": "2016-07-02T03:19:31.235Z", "2.0.24-alpha": "2016-07-04T01:10:42.331Z", "2.0.25-alpha": "2016-07-08T21:15:41.038Z", "2.0.26-alpha": "2016-07-11T22:29:54.099Z", "2.0.27": "2016-07-14T15:52:28.459Z", "2.0.28": "2016-07-19T13:14:27.657Z", "2.0.29": "2016-07-20T14:51:27.784Z", "2.0.30": "2016-08-02T16:07:44.737Z", "2.0.31": "2016-08-17T20:31:51.558Z", "2.0.32": "2016-08-18T13:53:59.131Z", "2.0.33": "2016-08-24T20:22:31.258Z", "2.0.34": "2016-09-19T18:07:46.723Z", "2.0.35": "2016-09-19T19:55:47.870Z", "2.0.36": "2016-09-21T20:28:20.854Z", "2.0.37": "2016-10-03T15:24:55.005Z", "2.0.38": "2016-10-13T19:38:28.875Z", "2.0.39": "2016-11-14T19:37:23.457Z", "2.0.40": "2016-11-28T16:31:00.621Z", "2.0.41": "2016-11-28T19:18:37.612Z", "2.0.42": "2017-01-10T18:54:21.989Z", "2.0.43": "2017-01-12T17:48:52.062Z", "2.0.44": "2017-01-12T17:54:50.600Z", "2.0.45": "2017-01-17T16:59:43.439Z", "2.0.46": "2017-01-23T21:35:28.287Z", "3.0.0": "2017-01-27T22:02:46.676Z", "2.0.47": "2017-02-07T20:45:34.406Z", "3.0.1": "2017-02-07T20:53:53.464Z", "3.0.2": "2017-02-08T21:31:28.050Z", "3.0.3": "2017-02-10T13:02:56.764Z", "3.0.4": "2017-02-20T07:58:08.010Z", "2.0.48": "2017-02-23T15:24:40.977Z", "3.0.5": "2017-02-23T18:40:06.739Z", "2.0.49": "2017-02-28T14:25:26.820Z", "3.0.6": "2017-03-01T22:23:03.915Z", "3.0.7": "2017-03-11T00:15:40.816Z", "3.0.8": "2017-03-13T17:33:58.200Z", "3.0.9": "2017-03-22T00:28:18.572Z", "4.0.0": "2017-03-22T00:34:05.907Z", "4.0.1": "2017-03-23T23:44:08.177Z", "4.0.2": "2017-03-27T16:20:37.672Z", "4.0.3": "2017-03-28T22:11:48.604Z", "3.0.10": "2017-03-30T21:31:27.045Z", "4.0.4": "2017-04-06T20:09:53.563Z", "3.0.11": "2017-04-17T17:57:38.518Z", "4.0.5": "2017-04-28T17:58:31.827Z", "4.0.6": "2017-05-01T21:00:30.261Z", "4.0.7": "2017-05-05T04:06:44.113Z", "4.0.8": "2017-05-05T17:43:29.832Z", "4.0.9": "2017-05-08T18:49:36.211Z", "4.0.10": "2017-05-30T18:51:44.465Z", "4.0.11": "2017-06-01T05:01:09.441Z", "3.0.12": "2017-06-23T17:37:35.898Z", "4.0.12": "2017-06-23T17:38:07.916Z", "2.0.50": "2017-06-23T17:38:09.004Z", "4.0.13": "2017-07-07T20:53:48.876Z", "4.0.14": "2017-07-10T19:47:26.352Z", "4.0.15": "2017-08-16T22:09:18.534Z", "3.0.13": "2017-08-23T17:54:49.642Z", "2.0.51": "2017-09-18T21:25:01.368Z", "4.0.16": "2017-10-20T16:02:57.820Z", "4.0.17": "2017-11-09T09:59:29.216Z", "2.0.52": "2017-11-14T18:38:59.459Z", "2.0.53": "2017-11-14T18:48:46.685Z", "4.0.18": "2017-11-21T19:02:40.203Z", "4.0.19": "2017-11-27T17:49:03.802Z", "4.0.20": "2017-12-20T14:51:30.528Z", "3.0.14": "2018-01-24T18:36:08.555Z", "4.0.21": "2018-01-24T20:08:40.294Z", "3.0.15": "2018-02-12T21:06:27.280Z", "2.0.54": "2018-02-12T21:06:51.514Z", "4.0.22": "2018-02-12T21:07:42.486Z", "4.0.23": "2018-03-15T23:20:23.331Z", "4.0.24": "2018-04-25T04:00:26.717Z", "4.0.25": "2018-05-08T22:17:21.878Z", "4.0.26": "2018-05-31T23:53:34.945Z", "4.0.27": "2018-06-22T00:11:56.662Z", "4.0.28": "2018-07-02T20:50:13.890Z", "4.0.29": "2018-07-16T18:27:07.032Z", "4.0.30": "2018-07-30T23:21:49.853Z", "2.0.55": "2018-08-06T22:31:43.024Z", "3.0.16": "2018-08-06T22:31:48.087Z", "3.0.17": "2018-08-30T00:19:44.405Z", "4.0.31": "2018-09-13T23:47:06.042Z", "3.0.18": "2018-09-17T19:36:03.115Z", "3.0.19": "2018-10-03T18:05:13.993Z", "4.0.32": "2018-10-10T22:46:15.375Z", "4.4.0": "2018-10-31T14:44:47.007Z", "4.4.1": "2018-11-07T07:34:54.013Z", "4.4.2": "2018-12-11T07:18:38.232Z", "4.4.3": "2018-12-12T19:20:45.500Z", "3.0.20": "2019-01-03T13:53:15.522Z", "4.4.4": "2019-02-12T21:50:15.929Z", "2.0.56": "2019-02-13T22:28:42.836Z", "4.4.5": "2019-03-07T23:04:56.270Z", "5.0.0": "2019-05-07T22:59:06.040Z", "5.0.1": "2019-05-20T17:06:42.739Z", "5.0.2": "2019-06-13T19:25:30.821Z", "5.0.3": "2019-07-02T16:47:55.619Z", "5.0.4": "2019-09-17T17:36:47.740Z", "5.0.5": "2019-09-18T18:01:06.570Z", "5.1.0": "2019-09-25T11:33:15.298Z", "5.1.1": "2019-09-27T23:41:54.408Z", "5.1.2": "2019-10-14T20:22:35.102Z", "5.1.3": "2019-11-13T23:08:54.156Z", "5.1.4": "2020-01-07T22:23:17.903Z", "5.1.5": "2020-03-31T23:36:54.263Z", "3.0.21": "2020-04-01T19:26:47.768Z", "3.0.22": "2020-04-02T16:57:44.703Z", "5.1.6": "2020-04-26T17:19:36.166Z", "5.1.7": "2020-04-26T17:44:02.480Z", "3.0.23": "2020-05-15T12:26:34.915Z", "2.0.57": "2020-05-15T12:26:50.301Z", "5.1.8": "2020-06-22T06:30:23.119Z", "3.0.24": "2020-08-12T02:40:21.645Z", "5.1.9": "2020-12-28T13:54:27.710Z", "5.1.10": "2021-01-06T14:54:21.434Z", "5.1.11": "2021-01-08T18:08:01.405Z", "5.1.12": "2021-03-01T18:56:30.878Z", "5.1.13": "2021-03-25T20:44:34.753Z", "5.1.14": "2021-05-05T08:01:35.213Z", "5.1.15": "2021-06-07T09:01:49.715Z", "5.1.16": "2021-07-07T17:49:09.732Z", "3.0.25": "2021-07-07T17:49:28.845Z", "2.0.58": "2021-07-07T17:49:37.547Z", "5.1.17": "2021-10-06T20:32:16.525Z", "3.0.26": "2021-10-06T20:32:26.704Z", "3.0.27": "2021-10-20T21:05:26.368Z", "2.0.59": "2021-10-20T21:05:30.379Z", "2.0.60": "2021-10-21T23:06:34.753Z", "5.1.18": "2022-01-18T22:03:27.359Z", "3.0.28": "2022-01-18T22:03:40.415Z", "2.0.61": "2022-01-18T22:03:48.079Z", "5.1.19": "2022-09-13T18:27:06.174Z", "5.1.20": "2022-12-23T11:02:48.930Z"}, "license": "MIT", "readmeFilename": "", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/react-router"}, "contributors": [{"name": "<PERSON>", "url": "https://github.com/sergey-butur<PERSON>in", "githubUsername": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mrk21", "githubUsername": "mrk21"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/vasek17", "githubUsername": "vasek17"}, {"name": "<PERSON>", "url": "https://github.com/ngbrown", "githubUsername": "ngbrown"}, {"name": "<PERSON>", "url": "https://github.com/awendland", "githubUsername": "awendland"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly", "githubUsername": "johnny<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LKay", "githubUsername": "LKay"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>das<PERSON>as", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/huy-nguyen", "githubUsername": "h<PERSON>-<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/grmiade", "githubUsername": "grmiade"}, {"name": "<PERSON>", "url": "https://github.com/DaIgeb", "githubUsername": "DaIgeb"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/egorshulga", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/rraina", "githubUsername": "rraina"}, {"name": "Duong Tran", "url": "https://github.com/t49tran", "githubUsername": "t49tran"}, {"name": "<PERSON>", "url": "https://github.com/8enSmith", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/wezleytsai", "githubUsername": "wezleytsai"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon", "githubUsername": "eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/HipsterBrown", "githubUsername": "HipsterBrown"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/pawfa", "githubUsername": "pawfa"}], "users": {"nisimjoseph": true}, "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router"}