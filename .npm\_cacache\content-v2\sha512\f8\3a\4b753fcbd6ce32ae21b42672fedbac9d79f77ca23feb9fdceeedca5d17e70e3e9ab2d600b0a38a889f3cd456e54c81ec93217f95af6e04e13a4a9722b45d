{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-transformer-core", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.1.0-beta.1", "0.1.0", "0.1.1-teamprv.15", "0.1.1-teamprv2.16", "0.2.1-beta.0", "0.2.1", "0.2.2-alpha.0", "0.3.0-beta.0", "0.3.0", "0.3.1-beta.0", "0.3.1", "0.3.2-beta.0", "0.3.2", "0.3.3-beta.0", "0.3.3", "0.3.4-beta.0", "0.3.4", "0.4.0-beta.0", "0.4.0-beta.1", "0.5.0", "0.5.1-alpha.35", "0.6.0-beta.0", "0.6.0", "0.6.1-authHeadlessImportTest.0", "0.6.1-beta.0", "0.6.1", "0.6.2-authHeadlessImportTest.0", "0.6.2-beta.0", "0.6.2-flutter-preview.26", "0.6.2-flutter-preview.27", "0.6.2", "0.6.3-beta.0", "0.6.3", "0.7.0-beta.0", "0.7.0-ext.0", "0.7.0", "0.7.1-beta.0", "0.7.1-geo.0", "0.7.1", "0.7.2-beta.0", "0.7.2-geo.0", "0.7.2", "0.8.0-beta.0", "0.8.0", "0.8.1-beta.0", "0.8.1-runtime-hooks.0", "0.8.1-siwa-update.0", "0.8.1-siwa-update.1", "0.8.1-siwa-update.2", "0.8.1", "0.8.2-beta.0", "0.8.2-beta.1", "0.8.2-ext.0", "0.8.2", "0.9.0-beta.0", "0.9.0-beta.1", "0.9.0-custom-iam-policies.0", "0.9.0", "0.9.1-beta.0", "0.9.1-headless-s3-not-for-production.0", "0.9.1", "0.9.2-beta.0", "0.9.2", "0.9.3-amplify-export2.0", "0.9.3-beta.0", "0.9.3-ext1.0", "0.9.3-ext10.0", "0.9.3-ext11.0", "0.9.3-ext2.0", "0.9.3-ext3.0", "0.9.3-ext4.0", "0.9.3-ext5.0", "0.9.3-ext6.0", "0.9.3-ext7.0", "0.9.3-ext8.0", "0.9.3-ext9.0", "0.9.3-extOverrides.0", "0.10.0-auth-dir-v-next.0", "0.10.0-auth-dir-v-next.1", "0.10.0-auth-dir-v-next.2", "0.10.0-auth-dir-v-next.3", "0.10.0-ext12.0", "0.10.0-ext14.0", "0.10.0-ext15.0", "0.10.0-ext16.0", "0.10.0-ext17.0", "0.10.0-ext18.0", "0.10.0-ext19.0", "0.10.0-ext20.0", "0.10.0-ext21.0", "0.10.0-graphql-vnext-dev-preview.0", "0.10.0-graphql-vnext-dev-preview.1", "0.10.0-graphql-vnext-dev-preview.2", "0.10.0-graphql-vnext-dev-preview.4", "0.10.0-graphql-vnext-dev-preview.5", "0.10.0-graphql-vnext-dev-preview.7", "0.10.0-graphql-vnext-dev-preview.8", "0.10.0", "0.10.1-beta.0", "0.11.0-beta.0", "0.11.0-beta.1", "0.11.0-graphql-vnext-dev-preview.0", "0.11.0-graphql-vnext-dev-preview.9", "0.11.0-graphql-vnext-dev-preview.10", "0.11.0-graphql-vnext-dev-preview.11", "0.12.1", "0.12.2-beta.0", "0.12.2-geo.0", "0.12.3-beta.0", "0.13.0-graphql-vnext-dev-preview.12", "0.13.0", "0.13.1", "0.13.2-beta.0", "0.13.2", "0.13.3-apiext5.0", "0.13.3-apiext6.0", "0.13.4-beta.0", "0.14.0-GqlExtensibility.0", "0.14.0-apiext1.0", "0.14.0-apiext2.0", "0.14.0-apiext3.0", "0.14.0-apiext4.0", "0.14.0-gql-ext1.0", "0.14.0-gql-ext2.0", "0.14.0", "0.15.0-beta.0", "0.15.0-beta.1", "0.15.0", "0.15.1-beta.1", "0.15.1-beta.2", "0.15.1", "0.15.2-beta.1", "0.15.2", "0.15.3-beta.5", "0.15.3-beta.6", "0.15.3", "0.15.4", "0.15.5-beta.7", "0.15.5-geo.0", "0.15.5", "0.15.6-beta.1", "0.15.6", "0.15.7-beta.0", "0.15.7-codegen-ui-q1-release.0", "0.15.7", "0.16.0-beta.0", "0.16.0-mapsto.0", "0.16.0-mapsto2.0", "0.16.0-mapsto3.0", "0.16.0", "0.16.1-beta.0", "0.16.1-codegen-ui-q1-release.0", "0.16.1", "0.16.2-beta.0", "0.16.2", "0.16.3-beta.1", "0.16.3-npm-pkg-cli.0", "0.16.3", "0.16.4-alpha.11", "0.16.4-beta.1.0", "0.16.4-beta.2.0", "0.16.4-npm-pkg-cli.0", "0.16.4-pkg-npm-install.0", "0.16.4", "0.16.5-alpha.35", "0.16.5-alpha.39", "0.16.5-alpha.41", "0.16.5-beta.1", "0.16.5-beta.2", "0.16.5-beta.3", "0.16.5-beta.4", "0.16.5-beta.6", "0.16.5-ic-changes.1", "0.16.5", "0.16.6-alpha.18", "0.16.6-alpha.26", "0.16.6-alpha.27", "0.16.6", "0.16.7-alpha.0", "0.16.7-alpha.1", "0.16.7-alpha.2", "0.17.0-beta.2", "0.17.0-beta.3", "0.17.0-category-split-test.0", "0.17.0-category-split-test.2", "0.17.0-category-split-test.3", "0.17.0", "0.17.1-alpha.38", "0.17.1-alpha.40", "0.17.1-alpha.5135", "0.17.1", "0.17.2-test-api-package-migration.0", "0.17.2", "0.17.3-sub-username-identity-claim.1", "0.17.3", "0.17.4-sub-username-identity-claim.2", "0.17.4", "0.17.5-alpha.18", "0.17.5-alpha.22", "0.17.5-alpha.26", "0.17.5-alpha.29", "0.17.5-alpha.31", "0.17.5-alpha.32", "0.17.5-alpha.33", "0.17.5", "0.17.6", "0.17.7", "0.17.8-alpha.19", "0.17.8", "0.17.9", "0.17.10-alpha.0", "0.17.10-alpha.3", "0.17.10-alpha.4", "0.17.10-alpha.5", "0.17.10-alpha.23", "0.17.10-alpha.27", "0.17.10", "0.17.11-alpha.0", "0.17.11-alpha.1", "0.17.11-alpha.2", "0.17.11", "0.17.12-alpha.7", "0.17.12-alpha.20", "0.17.12", "0.17.13-alpha.1", "0.17.13-alpha.27", "0.17.13", "0.17.14-alpha.1", "0.17.14-alpha.7", "0.17.14", "0.17.15-alpha.0", "0.17.15-alpha.12", "0.17.15-delta-table-improvements.0", "0.17.15", "0.17.16-rds-support.0", "0.18.0", "0.18.1-alpha.1", "0.18.1-alpha.3", "0.18.1-alpha.13", "0.18.1-alpha.14", "0.18.1-alpha.23", "0.18.1-alpha.27", "0.18.1-alpha.38", "0.18.1-circular-dep-fix.0", "0.18.1-circular-dep-fix.1", "0.18.1-circular-dep-fix.2", "0.18.1-upgrade-graphql15.0", "0.18.1", "0.18.2", "0.18.3-upgrade-graphql15-2.0", "0.18.3-upgrade-graphql15-2.1", "0.18.3", "0.18.4", "0.18.5-alhotpatchfeb.0", "0.18.5-alpha.34", "0.18.5-alpha.35", "0.18.5", "0.18.6-alpha.0", "0.18.6-alpha.74", "0.18.6-alpha.75", "0.19.0-rds-support.0", "0.19.0-rds-support-preview1.0.0", "0.19.0-rdsv2preview.0", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.3.0-sync-fix.0", "1.3.0", "1.3.1", "1.3.2-alpha.1", "1.3.2-alpha.2", "1.3.2-alpha.3", "1.3.2-alpha.6", "1.3.2-alpha.7", "1.3.2-alpha.9", "1.3.2-alpha.11", "1.3.2", "1.3.3-agqlac.0", "1.3.3-agqlac.1", "1.3.3-alpha.2", "1.3.3-alpha.5", "1.3.3-alpha.6", "1.3.3-alpha.9", "1.3.3-alpha.10", "1.3.3-alpha.14", "1.3.3-alpha.17", "1.3.3-cb-test-beta.0", "1.3.3-transformer-without-feature-flags.0", "1.3.3-with-standalone-transformer.0", "1.3.3", "1.3.4-agqlac.0", "1.3.4-agqlac.1", "1.3.4-alpha.0", "1.3.4-alpha.18", "1.3.4-cb-test-beta-3.0", "1.3.4-cb-test-beta-4.0", "1.3.4-cb-test-beta-5.0", "1.3.4-cb-test-prod-1.0", "1.3.4-cb-test-prod-2.0", "1.3.4", "1.3.5-agqlac.0", "1.3.5", "1.3.6", "1.3.7", "1.3.8", "1.3.9-alpha.7", "1.4.0-rds.0", "1.4.0-test-tag-1.0", "1.4.0", "1.5.0-no-internal-synth.0", "1.5.0-rds.0", "2.1.0", "2.1.1", "2.1.2", "2.2.0-rds-1.0", "2.2.0-rds-2.0", "2.2.0", "2.2.1", "2.2.2", "2.2.3", "2.2.4", "2.3.0-amplify-table-preview.0", "2.3.0-construct-publish-test.0", "2.3.0-nov-14-cut.0", "2.3.0-nov-14-cut-1.0", "2.3.0-rds-3.0", "2.3.0-rds-4.0", "2.3.0-rds-5.0", "2.3.0", "2.3.1", "2.3.2", "2.3.3", "2.4.0", "2.4.1-alpha.1", "2.4.1", "2.4.2-ecs-tagging-permissions.0", "2.4.2", "2.4.3", "2.4.4", "2.4.5-secrets-manager.0", "2.4.5", "2.4.6-rds-5.0", "2.5.0-implicit-fields.0", "2.5.0", "2.5.1-cors-rule.0", "2.5.1-fix-publish-tag.0", "2.5.1-gen2-release.0", "2.5.1-gen2-release.1", "2.5.1-iam-auth.0", "2.5.1-iam-auth-with-identityPool-provider-1.0", "2.5.1", "2.5.2-gen2-release.0", "2.5.2-gen2-release.1", "2.5.2-sql-gen2.0", "2.5.2-sql-gen2-1.0", "2.6.0-data-schema-generator.0", "2.6.0-gen2-release-0410.0", "2.6.0-test-binary-size.0", "2.6.0-z-data-schema-generator.0", "2.6.0-zz-0411-gen2.0", "2.6.0", "2.6.1-0411-gen2.0", "2.6.1-gen2-release-0416.0", "2.6.1-gen2-release-0418.0", "2.6.1-gen2-release-0418-2.0", "2.7.0-gen2-release-0423.0", "2.7.0", "2.7.1-cdk-upgrade-2.129.0.0", "2.8.0-acdk-upgrade-2-129.0", "2.8.0", "2.8.1-fix-sub-owner.0", "2.9.0", "2.9.1", "2.9.2", "2.9.3", "2.9.4-api-stable-tag-2.0", "2.9.4-gen1-type-ext.0", "2.9.4-raven.0", "2.9.4-raven.1", "2.9.4-raven.2", "2.9.4-raven.3", "2.9.4-raven.4", "2.9.4-sandbox-hotswap.0", "2.9.4", "2.10.0-gen1-migration-0211.0", "2.10.0-gen1-migration-0214.0", "2.10.0-gen1-migration-0924.0", "2.10.0-gen1-migration-0924-2.0", "2.10.0-gen1-migration-1218.0", "2.10.0-gen1-migration-1218-2.0", "2.10.0-gen2-migration.0", "2.10.0-gen2-migration-0809.0", "2.10.0", "2.10.1", "2.10.2-gen1-migrations-0304.0", "2.11.0", "2.11.1", "3.0.0", "3.0.1", "3.1.0", "3.1.1-async-lambda.0", "3.1.1", "3.1.2-ai.0", "3.1.2-ai.1", "3.1.2", "3.2.0-gen2-migration-0930.0", "3.2.0", "3.2.1", "3.2.2-ai-streaming.0", "3.2.2", "3.2.3-ai-next.0", "3.2.3-ai-streaming.0", "3.3.0-gen2-migration-1015.0", "3.3.0", "3.3.1-ai-next.0", "3.3.1", "3.3.2-ai-next.0", "3.3.2", "3.3.3", "3.3.4", "3.3.5", "3.3.6", "3.4.0", "3.4.1", "3.4.2-grant-stream-read.0", "3.4.2", "3.4.3"], "vulnerableVersions": ["0.18.6-alpha.0", "0.18.6-alpha.74", "0.18.6-alpha.75", "1.1.0-beta.0", "1.1.0-beta.1", "1.1.0-beta.2", "1.1.0-beta.3", "1.1.0-beta.4", "1.1.0-beta.5", "1.1.0-beta.6", "1.1.0-cdkv2.0", "1.1.0-cdkv2.1", "1.1.0-cdkv2.2", "1.1.0", "1.1.1-alpha.5", "1.1.1-alpha.51", "1.1.1", "1.1.2-alpha.3", "1.1.2-alpha.9", "1.1.2-alpha.13", "1.1.2-ownerfield-pk-fix.0", "1.2.0", "1.2.1-5.2.0-ownerfield-pk-fix.0", "1.2.1-alpha.3", "1.2.1-alpha.9", "1.2.1-ownerfield-pk-fix.0", "1.2.1", "1.3.0-sync-fix.0", "1.3.0", "1.3.1", "1.3.2-alpha.1", "1.3.2-alpha.2", "1.3.2-alpha.3", "1.3.2-alpha.6", "1.3.2-alpha.7", "1.3.2-alpha.9", "1.3.2-alpha.11", "1.3.2", "1.3.3-agqlac.0", "1.3.3-agqlac.1", "1.3.3-alpha.2", "1.3.3-alpha.5", "1.3.3-alpha.6", "1.3.3-alpha.9", "1.3.3-alpha.10", "1.3.3-alpha.14", "1.3.3-alpha.17", "1.3.3-cb-test-beta.0", "1.3.3-transformer-without-feature-flags.0", "1.3.3-with-standalone-transformer.0", "1.3.3", "1.3.4-agqlac.0", "1.3.4-agqlac.1", "1.3.4-alpha.0", "1.3.4-alpha.18", "1.3.4-cb-test-beta-3.0", "1.3.4-cb-test-beta-4.0", "1.3.4-cb-test-beta-5.0", "1.3.4-cb-test-prod-1.0", "1.3.4-cb-test-prod-2.0", "1.4.0-rds.0", "1.4.0-test-tag-1.0"], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "0.18.6-alpha.0 - 1.3.4-cb-test-prod-2.0 || 1.3.9-alpha.7 - 1.4.0-test-tag-1.0", "id": "UL+NKV6jfvzL7Zd4Rds/v1gFdRznsgLYSpri2hf20TmHqTPNxokIiTCOasd3q9OTVqXORcDjwERwZtEMGIKzlg=="}