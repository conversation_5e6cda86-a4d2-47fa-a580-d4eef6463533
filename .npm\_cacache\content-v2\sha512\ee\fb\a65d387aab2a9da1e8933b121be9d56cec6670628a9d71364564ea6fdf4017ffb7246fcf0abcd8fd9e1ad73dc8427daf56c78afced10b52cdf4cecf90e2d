{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.0.2-agqlac.0", "0.0.2-alpha.6671", "0.0.2-alpha.6672", "0.0.2-alpha.6676", "0.0.2-alpha.6679", "0.0.2-transformer-without-feature-flags.0", "0.0.2-with-standalone-transformer.0", "0.0.2", "0.0.3-agqlac.0", "0.0.3-agqlac.1", "0.0.3-agqlac.2", "0.0.3-alpha.0", "0.0.3-alpha.18", "0.0.3-cb-test-beta-3.0", "0.0.3-cb-test-beta-4.0", "0.0.3-cb-test-beta-5.0", "0.0.3-cb-test-prod-1.0", "0.0.3-cb-test-prod-2.0", "0.0.3-rds.0", "0.0.3-rds.3", "0.0.3", "0.0.4-agqlac.0", "0.0.5", "0.0.6", "0.0.7", "0.0.8-alpha.7", "0.1.0-agqlac.0", "0.1.0-agqlac.1", "0.1.0-test-tag-1.0", "0.1.0", "0.1.1-rds.0", "0.2.0-no-internal-synth.0", "1.1.0", "1.1.1", "1.1.2", "1.1.3-construct-uses-jsii.0", "1.1.3-jsii-build.0", "1.1.3-jsii-build.1", "1.1.3", "1.1.4", "1.2.0-rds-1.0", "1.2.0-rds-2.0", "1.2.0", "1.2.1", "1.2.2", "1.2.3", "1.2.4", "1.2.5", "1.2.6", "1.3.0-amplify-table-preview.0", "1.3.0-amplify-table-preview.1", "1.3.0-construct-publish-test.0", "1.3.0-nov-14-cut.0", "1.3.0-nov-14-cut-1.0", "1.3.0-rds-3.0", "1.3.0-rds-4.0", "1.3.0-rds-5.0", "1.3.0", "1.3.1", "1.3.2", "1.3.3", "1.3.4", "1.3.5-alpha.1", "1.3.5", "1.3.6-ecs-tagging-permissions.0", "1.3.6", "1.3.7", "1.3.8", "1.3.9", "1.3.10", "1.3.11-implicit-fields.0", "1.3.11-rds-5.0", "1.3.11", "1.3.12", "1.3.13-iam-auth.0", "1.3.13-iam-auth-with-identityPool-provider-1.0", "1.4.0-cors-rule.0", "1.4.0-fix-publish-tag.0", "1.4.0-gen2-release.0", "1.4.0-gen2-release.1", "1.4.0-secrets-manager.0", "1.4.0", "1.4.1-gen2-release.0", "1.4.1-gen2-release.1", "1.4.1-sql-gen2.0", "1.4.1-sql-gen2-1.0", "1.5.0-data-schema-generator.0", "1.5.0-gen2-release-0410.0", "1.5.0-test-binary-size.0", "1.5.0-z-data-schema-generator.0", "1.5.0", "1.5.1-0411-gen2.0", "1.5.1-gen2-release-0416.0", "1.5.1", "1.5.2-gen2-release-0418.0", "1.5.2-gen2-release-0418-2.0", "1.5.2-gen2-release-0423.0", "1.5.2", "1.5.3-cdk-upgrade-2.129.0.0", "1.5.3", "1.5.4-acdk-upgrade-2-129.0", "1.5.4", "1.5.5", "1.5.6-fix-sub-owner.0", "1.5.6", "1.5.7", "1.6.0", "1.6.1", "1.6.2", "1.6.3", "1.6.4", "1.6.5", "1.6.6-api-stable-tag-1.0", "1.6.6-api-stable-tag-2.0", "1.6.6-raven.0", "1.6.6-raven.1", "1.6.6-raven.2", "1.6.6-raven.3", "1.6.6-raven.4", "1.6.6-raven.5", "1.6.6-raven.6", "1.6.6-raven.7", "1.6.6-raven.8", "1.6.6-raven.9", "1.6.6-raven.10", "1.6.6-raven.11", "1.6.6-raven.12", "1.6.6-raven.13", "1.6.6-sandbox-hotswap.0", "1.6.6-stable-tag-10.0", "1.6.6-stable-tag-2.0", "1.6.6-stable-tag-3.0", "1.6.6-stable-tag-4.0", "1.6.6-stable-tag-5.0", "1.6.6-stable-tag-6.0", "1.6.6-stable-tag-7.0", "1.6.6-stable-tag-8.0", "1.6.6-stable-tag-9.0", "1.6.6-test-stable-tag-release.0", "1.6.6", "1.6.7", "1.6.8-gen1-type-ext.0", "1.6.8", "1.7.0-gen1-migration-0211.0", "1.7.0-gen1-migration-0214.0", "1.7.0-gen1-migration-0924.0", "1.7.0-gen1-migration-0924-2.0", "1.7.0-gen1-migration-1218.0", "1.7.0-gen1-migration-1218-2.0", "1.7.0-gen2-migration.0", "1.7.0-gen2-migration-0809.0", "1.7.0", "1.7.1", "1.7.2-gen1-migrations-0304.0", "1.8.0", "1.8.1", "2.0.0", "2.0.1", "2.1.0", "2.1.1-ai.0", "2.1.1-ai.1", "2.1.1-ai.2", "2.1.1-ai.3", "2.1.1-ai.4", "2.1.1-ai.5", "2.1.1-ai.6", "2.1.1-ai.7", "2.1.1-async-lambda.0", "2.1.1", "2.1.2", "2.1.3", "2.1.4-ai-streaming.0", "2.1.4-ai-streaming.1", "2.1.4-ai-streaming.2", "2.1.4-ai-streaming.3", "2.1.4-ai-streaming.4", "2.1.4-ai-streaming.5", "2.1.4-ai-streaming.6", "2.1.4-ai-streaming.7", "2.1.4-gen2-migration-1015.0", "2.1.4", "2.1.5-ai-streaming.0", "2.1.5", "2.1.6-ai-next.0", "2.1.6-ai-next.1", "2.1.6-ai-streaming.0", "2.1.6", "2.1.7-ai-next.0", "2.1.7-ai-next.1", "2.1.7", "2.1.8", "2.1.9-ai-next.0", "2.1.9-ai-next.1", "2.2.0-gen2-migration-0930.0", "2.2.0", "2.2.1", "2.2.2", "2.2.3-ai-next.0", "2.2.3", "2.2.4", "2.2.5", "2.2.6", "2.2.7-gen2-migration-0205.0", "2.2.7", "2.3.0", "2.3.1", "2.3.2-grant-stream-read.0", "2.3.2", "2.3.3"], "vulnerableVersions": [], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "<0.0.0-0", "id": "dGJZrsC0HAynDxp4eKeY/DTLUcsLZrH9Oa60jVFAk24AFnsAd+7i5K1/Wcjyvdnjap177FcX00Fj3tvLAxhoJg=="}