{"_id": "@types/react-router-hash-link", "_rev": "385-20659cd274493de63f512d4396eba316", "name": "@types/react-router-hash-link", "dist-tags": {"ts4.1": "1.2.1", "ts3.9": "1.2.1", "ts4.0": "1.2.1", "ts2.8": "1.2.1", "ts2.9": "1.2.1", "ts3.0": "1.2.1", "ts3.1": "1.2.1", "ts3.2": "1.2.1", "ts3.3": "1.2.1", "ts3.4": "1.2.1", "ts3.5": "1.2.1", "ts3.6": "1.2.1", "ts3.7": "1.2.1", "ts3.8": "1.2.1", "ts4.2": "2.4.5", "ts4.3": "2.4.6", "ts4.4": "2.4.6", "ts5.8": "2.4.9", "ts5.7": "2.4.9", "latest": "2.4.9", "ts4.5": "2.4.9", "ts4.6": "2.4.9", "ts4.7": "2.4.9", "ts4.8": "2.4.9", "ts4.9": "2.4.9", "ts5.0": "2.4.9", "ts5.1": "2.4.9", "ts5.2": "2.4.9", "ts5.3": "2.4.9", "ts5.4": "2.4.9", "ts5.5": "2.4.9", "ts5.6": "2.4.9", "ts5.9": "2.4.9"}, "versions": {"1.2.0": {"name": "@types/react-router-hash-link", "version": "1.2.0", "license": "MIT", "_id": "@types/react-router-hash-link@1.2.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}], "dist": {"shasum": "168c252dc5887c83c457b7982aa1fcb0daa5f023", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-1.2.0.tgz", "fileCount": 4, "integrity": "sha512-JsksU/anUPcG89tysGq+e13AfPYlNLm3WcsURi0eqeuPhHESwKabAaoVp4sN7bCS59vitILIAMuVIXJMqYiuzw==", "signatures": [{"sig": "MEQCIFuScih40gy3WPZ777yLXJAA62MKF1Uge/u2viLbbcbnAiAaJRD7WlTy8KwaaR7Xv7iZoo2RAia8JUkM/tDqI5DG2A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3281, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbdJDiCRA9TVsSAnZWagAARzEQAJls//p/+9syC5UeVvjb\nRZApisUgFzwLK64RWMKQx2+COGybv+u7HmJQVZ7agOVPyn5PqSk6PdnfiTqV\nr8RB3On0y/BByScPhyrAFeuje8NG0wCvM5+1jKFVxUcbuSwnpUgbFLC1AWjk\nkY3nf8iXqwLYB+R2+UD1RIgJ68NUieCBdBeC50XWSGcF/mS5R1EdiHcel5dS\nLSF49qmiLt77GYRq0ncEK44HX1paVyS8ESWUZLEjBKGkJByQ8OO+G1RZ73CY\nKMaMxtCt1Ckw+ytjbDFspb6wwdJQrNdXYZJuNyFId8ovMQGnf5c2P4ipW6Lg\nz7w7JX96uhVycUmODDSAVcvctN46mdPxAc/AgNdrxTbZRMaflLU9SjzKEm/A\n4htHC9/78FflXYsGBqa93qS85hWYyNIxEmTUO7o1bk4xtJulLo1L67wsCa7J\nO8p9d7naLye3IZrgW4bEsmalxEVG2yIp4DRdTgXQ2mTnUuwhEHHAxykI7EAu\nn4waD/tTdC4+oyUzbKybtXO4E0QOP+TL9lItolXNSiPZ2diaB0FPK/oFjtLP\nbLa9txfZ3m/nNR7cP/5hwqg6EoqlE7lVLIL3TMqRh59eeuDw/MIOeSo7N0MN\nWwgslI11vP3Es0fpHnV9ECsaHib0FQ45icyyiAbSWju7g0fhAINyyDZ2ERs+\n5TJx\r\n=gnMs\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_1.2.0_1534365922825_0.8362893994792064", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ed6de0cdec988fcac21bce849623cc2f83b1e80a2ed1a5018434e27ef9d7ae2c"}, "1.2.1": {"name": "@types/react-router-hash-link", "version": "1.2.1", "license": "MIT", "_id": "@types/react-router-hash-link@1.2.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}], "dist": {"shasum": "fba7dc351cef2985791023018b7a5dbd0653c843", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-1.2.1.tgz", "fileCount": 4, "integrity": "sha512-jdzPGE8jFGq7fHUpPaKrJvLW1Yhoe5MQCrmgeesC+eSLseMj3cGCTYMDA4BNWG8JQmwO8NTYt/oT3uBZ77pmBA==", "signatures": [{"sig": "MEUCIQCSQwsVpKQgfawjkLOpdrFXCVy8ZkyNowgDETW1r9v1jAIgQwT2JqmnyDfZLVxzVCWF5uLFqz2vmSv32LqisLqZhGI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3195, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcZJo5CRA9TVsSAnZWagAAVpoQAJfhST+sGVaA7WAsRG8o\nPslzZDpYuMLxcAxnSHhUbJGdkJF/XhM7dUuugcxtJrZKgMu3jI2pNFgNuE8B\nwQdRewFKK1BrBf/5zzs2vbVgi7MUusWRHn+bwDdLjh+HE0P7wL9OHH4tb5L+\nhBFSBM+y/JQgxelTPvG0wUIM7A7QjueXUj4XbT7I/JpMme0qAvpBHOXwmZYu\nqrH4Y4zNhyv6QhRvFglm5jtOrqBUzbc4qwn/BxYEkuGtgW4Q4mJpNx3cgWzQ\n4cPyv7oeTY758Zmc3nHSSDHImZZYUfACno02iczPIMFRzk3zZCsF5PeRHKU+\n+O4LGydVgCbM3l68msXe6P7UmbqcxBBtM0OseToumwFuLQlpMT/r+X4+jTt0\nTVshGiHliU5jAIp5C2Sv4Cjs2XguvKF+ePvgm9xkZ9wjMZ9tCuqlJZRB+cYT\npLMXue0/fqyaNVShOXabZwPIdLUIw55SWSdaKRuDjipxjMG5QFArHF/YKVtg\naa8bME+ti0+EZA/kA+oFfEVRqO+Tv4kSCeEl2Dgza8QV7LnQ9Ux6auB1HyFt\nwqT3Nwxsp2x3dpOdSSfT0ukXk09/M6d5zBrnOZH2sLqoa6XSKm5AHP+hXD1G\nRCu35KQ7UMpMutQJCCyscIZZPg3WezJp7nSiQjSLc5am1Fe+6dYixoXql7Hq\ny0Ni\r\n=lUdR\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_1.2.1_1550096952893_0.9868004019966012", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "ec86124bf9c59071217e169a7009ca044c8b9740ebab156cc5c6cde7bc9babdf"}, "2.0.0": {"name": "@types/react-router-hash-link", "version": "2.0.0", "license": "MIT", "_id": "@types/react-router-hash-link@2.0.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "93189b39dcc4ac3f28616867aad134bbe7a41521", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.0.0.tgz", "fileCount": 4, "integrity": "sha512-Y40Kjxu9IqpVFE7gUrRJvV23+iMkrM+gI8q4DCNfywG9Cv34lQ/rrO2uZJ6TjCF2faMy3Y9rizVue1jLFQzQYA==", "signatures": [{"sig": "MEUCIQC5hcerkdiQfPxf0GrQ6bu/CJdkkw8Cp+hu4/I2Sp+CPAIgOZ7/S7OMpAoRhfwW5WYliZYUlFc2bcn9mGxQwCo+Zsk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3713, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJglC9XCRA9TVsSAnZWagAAZc0P/354KERwwxrHqaQDoWWO\nxpEgb8pchSqNLsue0gsR+4TzqZZT3FXHPwis0Lnb7YCtiuKhK6rpCh6DVKnc\nKv6KXKyyvSMa+0IeUnzEVeJFnZ9DgQQY6i4f8MZlla0sXYD/9bfxZy5fwtKM\n1khaiBFRR88hlmaS3lg2VsD5xdt29GOCKdsrwJ232N/lS/zpP4RjFaIsNLaj\nTunIPc0Fh2mD+ezIFkdkNfO2FQVe0idaY761KEohlVAyozCSYTvMUSdk0d7a\n0rRAvqua2rrVSbwbM29zQ7gPxSL6CBs3GCzZPjtPed++FmH2yFfGg5rALPFJ\nz7VR8RGD6s/pq/kXR4F5rMlEyf8ux9x0bbJo/ee3oIfwBaXuMSD9sdgI8gHv\n/fUPAODgBXauozghIXHU4sPSF1pvAYWV4MJIycdn2pE0Ug+SlPARHx8LGifS\nKxbIxs1kAGpNZHsg0eYpYeaD2ia4zKdedCQYOEaGlZGhu+JxElsvQMsoMt/9\nRAam060E6VhpPlxP5nKn9u1ePJ/5Kjd0ibiSZRJoicqTbL3RNqHjRwf0cBrb\nK6vwCiyCJTGpI75uBzKAt8Bzd68gi1jjqWzWl9Ulz44xda4JhC8ebqD1YeVR\nKH/mKRnbYaTPM9PZ4zoKa5EB2nNRsaSKsDLMlHk74uLRUx8JEXaPztoeJTfT\nn4An\r\n=Rp5T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.0.0_1620324182896_0.6296649330318791", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "1cfd9a9d6aca4d91034d41c6214e9d56dff716a64851264b2ee1ded065af003c"}, "2.4.0": {"name": "@types/react-router-hash-link", "version": "2.4.0", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "dist": {"shasum": "8d028a841075f06a60b502eeea251eddf39fd985", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.0.tgz", "fileCount": 4, "integrity": "sha512-4IB72mosjRshMmoli6ktb0ltzEFBPWgOT/GjZwPEmcehwuacGdsTytIboPfFT9O8hgL1pgdPZMWPfMwCywjO0Q==", "signatures": [{"sig": "MEUCIBFJsUBk0UVspwoqKxfuO8MsVlo+gnT9rt8JCm1UJuAsAiEA6sakSQ38ItDIeHymwS2iLLcd0dsRyefDNZ62uq6Uu78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgo4mCCRA9TVsSAnZWagAA36sP/RWtN2lvZq2l8n36khdZ\nwVK5PThdJA33Ad5VLh5ByL4zEbN/vi7/wOCt0wgPWkH9YcDbwiaAQtpQE00v\nbTR8gQ7MCNFd+UernQaRa5m1z641op58fnNAFf+3gVaeRZqOm1sfiLZAxu+s\nYPETvZ9hv3r1Q2XSXHM36kCn4VgCEp70gMOjcrR1R5uq7kIhSg5qc0REJ7HO\nEiXGutw7T7+6FEDda1X5RlKQb6RkTYv4zb8CCqndoSaDC0aMDyB7gnIu0meP\njba7OXrZbO3nRVgrhe5k/rf70j3a1fZVsNzdqkg/RICQUFPJ611/ARPTA/9s\nTVZG0iLnkWHeyaoW9ZzisRn3FmAEW/ERr78RvOUBho5OJBCe2H8skkxuu8yZ\n730ZxrsJWES7D6PfcgOugtLim8+M5cUUrXociaLpS8O/3UYTFWVvI7AWvfVM\nIUQzZgOfYAZ+8X2QpzZTbndzE5Gd1vkT5sb2v28YS427ftM2pXHJ/XdW7w7R\n9kVhr+37Lye5om3xKMPRRjTkgUG9d5kAfpD2EDcYv0VH/zZexg4VvvU2tp8p\nwmGYZfefVEfJXPx8L5fvNvJkbWrg7/mMrEM+roc2udh+B7hsDjD+bld7sekV\n7yVm8BuUKUkIAP2IpJiUIf4+5Wv/JBQt9aImcjQ+Vi4WIGd4GVzCR+X2KVI7\nxDmh\r\n=aIu1\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.0_1621330306176_0.6702769099295323", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "773d494a9ff3c777f23f94d678315d187000b84337da9d66983ea7e304bc1919"}, "2.4.1": {"name": "@types/react-router-hash-link", "version": "2.4.1", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "9e5da328817e12f489a2005613857cbf50127e61", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.1.tgz", "fileCount": 4, "integrity": "sha512-SPVymyscQUBWMAPEpAn3I35MQXarTx0rOPmcfHl1xWYaTSDP5kxQnrFjmMxJlX5mjIPVHb3XBm8t6DTQNjkGEQ==", "signatures": [{"sig": "MEUCIQDeHbdaof5xvdUiivj9yUje7+qMUWwmlUYVFWkbFt4mqAIgQG6VIsugLXkRNhTpyHhDDo4aoDFF9dle0qihL/R6OQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5356, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5elVCRA9TVsSAnZWagAAsisP/0OM3TW4GXLmeoRjZN9K\n5jFBQmUwA0uJYqcXjuuSS9iGCI6aZ+pCoB4gYCWyCETQmUgUOPLJ4Jgu/MoO\nqSJlWWiw8RzlD9j6L+WDqc8Qpxth7hfjb02P6RyQ7BvXzw5YULnmS7gcEMk2\njqmkFMBIGD+Wj0PmwIz25LgLGanaVssQOFXjWl/8kIZh7ybbvfQOVP5xSLT8\nekIsthoOusRJzDL8dYCChYBAYPEUStGoWiKUkhbC5MXkCW2nnkShwwGUFTp+\nQbVbXU9MV89dmw2cJZVACxTZz82AF81q+T/2dqZw8lUYIJBelxaeBhKF/hv4\ndvBF204tPXAEPktsKC4UOd1MEo8zp3jy4yfjbh6mpEeM0vkU/LYB6CMZuSFP\nOeJI+hB4CZDF780pql7ZcXDGGH4kfQbXWL1ZQhsN7CDkgPWt+QtMA+e8kMil\ng/Mcr2inVA5LYNTBeg29TzJt2Sit1pswq/0gYN63MeTbVMtFe90s+MYvO/4u\n8HwbHGuwM1JZ3GJmRq18HgZI0U1PQDFHaRXiLUWWuMb04G/XsIP+pHEQzv4H\nusqtlo73YaFm5KyLB+p29AeYcrhbZNpEfIHKmGxldmpB6IJxsgQMueU2U9J9\ngcigjPJHpwUuYV/YYHFAuGOG+qIMKQcptPBrHsyBApzEXf0laJnim7RMDu7U\nUZR4\r\n=MY3A\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.1_1625680212914_0.3482775700856491", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "7e7aff224b23d816cfaf7cd9fb99089d7920c38916b9bc9ae768bea06010f517"}, "2.4.2": {"name": "@types/react-router-hash-link", "version": "2.4.2", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "536ff3dce8d846137643c71db57a3b168384dbfc", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.2.tgz", "fileCount": 4, "integrity": "sha512-DpTvUnvjTgy5VKd+qI+sa/hliGoAhmdrbIJJBPatcgKmvzYd9mzhssAalCOOOkY4anViKUAB/D6MbE11JnK8oA==", "signatures": [{"sig": "MEYCIQDZXNLEzFxnpbzevkA+LWSa2QIHqRHs8Z3acjze6pL0uAIhAJR5BkSMfwJUY7n6E6Af3jmvdVkCzH51RCOU2bW6sKh2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQLKHCRA9TVsSAnZWagAAgx4QAIVC/8+zvTjTAu3DnqrZ\nSffXmjZC108KLJ0JcEBBOIXi9wtRllZtzafgn1qlWrNuaRVpHCQI0G2D1ymu\n6tkTIEMzSAsaMaKTDaXU51VrdKpy03iC/c65UA2o+QTBjbu/wkzUatBw4pjK\nvuKG0QuW+pnoxCMvR+RGZU52r5mLmIvzVl6hERBupLHbnj1sMzb0DIc/CiRk\nGBD1JFx7VryK5SlEA/fcenyhkWD54gHnh12pi9wlmOllsC8Pvzwp421IZeVY\nLBS02/Z9TAjfzJhQqnSFjIONJzclALhTpZyEu8mV35xZGC0RrI7YBRO8ExE4\nnCDJT4RU5oNEGuvJxKTfFtkxjQB9pGb/IVO/SvTwWv2OL+JEJh/yDHK0pFLp\ndquM8rk5nZElKqyfH3+oVVSFr492+cY6r8rbAp/mMcnVi9bkUJL5OS6C5yeB\nReWGnNtkazZAQsHYofp2Hoz+F6WHtMvMJ/NWG3/Tvqn8Aib2ZCUD7M79KhvJ\niLzMIaC+Kbmv10B/M5gw1tKfBKvVhM83aMjBPnRu/gp4uAp+fGtEuUM0Cxc2\nwpJ1igyyzF9+kR4tVXnXy/JorQBbfgEahEvwbtYVU+Z4Jus6iOOpd5UEx9KB\nWYo2KszOucNM5T0dRJWChw2DNWelB4fxEMbFNqU9Xno+8cxLPP05noRfdo1u\nHQBm\r\n=yvAw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.2_1631629959578_0.19868735071519072", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "89db1703ac3230d09e3df029d0412f3b4b8ec025896a72429f86c57f866f5dbc"}, "2.4.3": {"name": "@types/react-router-hash-link", "version": "2.4.3", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "8242fc1e9fb947ff26a11be97bf429058590f284", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.3.tgz", "fileCount": 4, "integrity": "sha512-qTWXxFZbr8WFtnmAqEmgVQjjBcbx+Upv5MN63icwV5sGZ92/+tGLkxVvPxh7sftmLuQprEIvgvxWbP8GsBI5vA==", "signatures": [{"sig": "MEUCIQCb3dTpqBGzwBkmwdjIy8q66+EQfMDBM2SSPljYv8BZqAIgfAdJvMRy7g4NKAytbVuXiyOyAaIaTceFGszD0pt2bXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5688}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.3_1636135422027_0.7808085596893763", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "71bfe7f3d7646bf88080226706250ac67615b60e692f5712f7d1ba2d037e3515"}, "2.4.4": {"name": "@types/react-router-hash-link", "version": "2.4.4", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "edbbf15d65890802d45d870845df455c0cfc4afe", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.4.tgz", "fileCount": 4, "integrity": "sha512-YTP4OJouh3gsKDMxa9jKjDZdh6AT+34LGllCs5ZkDOS6Yii3L3TZeDiVmg0E8VFjFL+xsPKyyW4r2lkBDohudA==", "signatures": [{"sig": "MEUCIQDqrynvaeDIyc3E9F/X+vX7JgXvTQmbvr+uVrfj2lhhyQIgWlQOz0qFSeyBozgF+IJ6SGPjRMww6XqeANafpmzSYuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5582}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.4_1636149712302_0.1248476823576623", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4184ae0604dde54c49590f148cd142d26e02236743640f6f4aed1510444c93ef"}, "2.4.5": {"name": "@types/react-router-hash-link", "version": "2.4.5", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "41dcb55279351fedc9062115bb35db921d1d69f6", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.5.tgz", "fileCount": 4, "integrity": "sha512-YsiD8xCWtRBebzPqG6kXjDQCI35LCN9MhV/MbgYF8y0trOp7VSUNmSj8HdIGyH99WCfSOLZB2pIwUMN/IwIDQg==", "signatures": [{"sig": "MEUCIEC7G7tpH9DTnFYFZk+C87kqxE2DzWrBuZtdkScuYg90AiEA8TNieu2LIT91GZrNa/qLnnyeuFaj0499qmQ7V0o8a5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5619, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5zl2CRA9TVsSAnZWagAAiOoQAIhCgz3yp7lYj/czzm7Z\n4fpMXftRq1giI+dcPM+z/cBAMtGu2ImL7PHFkN9+Bq2gh8aVb/jbOLT2PjQP\nx4y+CjdVp2cK5W14wyg2o3yzKkNBRnDcGW5KjSrJ2kz/5IrGCWlqqFppt5VG\nvgkkQVKerGmxlmvg8sSq+s0RKbQl8MQLFS5wjgpqz6W07mV4//lpFgfB6IQU\nG/cSe4Samr0SQPkSHHsN07yU7xntB0sgnvaO+QDNZKhV2mkj4YUxOge0UONE\n3P6WqwC3NpqN/h4yP/+piygs3qNrupiJVbw3aPQx+E9EiNAREmK54iNAaPcE\n0jHzOM/KVSQGVgeVDBh1hE7xV5WivneERLg1uqPDRFocD0zun06wnfZOL2wK\nUFprD3S5rovnTjq3GF/qQATCGvxJTtDPexdm2OVmFwpvBDnxgYF5oH9JGygn\nDLymmSw984gU2u7is/RSnuBVrGXN7Sw8MI8Y56WJtuaZMtlhzWaNgooJJxpw\ntQrC/b1XX/Lr+DOUuGuin9+6CEYGEQ0vNflhJYdlFN28aP4EucZaGtTAHPMM\nJX9ms/tJ4+sQNYnlhJSIKI0wmqoRJjoTxmL+gprgh/Od/oXvoLymx+YOkckv\nQuGQdv/k4RLwaemXMpRb3/UYomq6fg++P3tsZc+gd/hHxbv0X0I2jmnOFjEl\nmeHs\r\n=w0ea\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router-dom": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.2", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.5_1642543478838_0.15541203072815168", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "a3ccca2825d9ba08655af40ecdf8fc0d3dcc6127885d42c6a1491372c7edcb68"}, "2.4.6": {"name": "@types/react-router-hash-link", "version": "2.4.6", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "6411fb185e126bb1b985c6867a0e413670082f34", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.6.tgz", "fileCount": 5, "integrity": "sha512-JOV4Q1N60tJJUPisS/u1jiXn8c4jX7ThQf8XavzZYIWOIv0RP17nbyI9YgEZh1r3APXpP9ZkU1ytrlv+1+8jcw==", "signatures": [{"sig": "MEUCIQC4w7YI8iUs74O+QdXmTXmRHY1qvPDGzXShUl3fkR+s9QIgaTsfGedrQrw99HQYMIXd++SodbhszHFgo10pRHu6KWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5624, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLc0QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCew//ZqHazoczt7v9Vwd7LVcLic0H5+PAJbDYYt4qLN5Pg7w6mPIE\r\nkJ5BK5/Q6SxsadRn3V/Gm59jcPKsH9sJoR4vESLBlusJoVmNWIWdSyDl+7PM\r\n2jlhkztllk6XvcWzckmO3UR2FR5ngtgQnuHzcejcpY/PtleXqyDwhfpbzuh+\r\n3Ur8sCL9l04fwjWqs7fG4MLIIqo1gJOQL8s92NUwsAIWeQHSV9Lr4BwdkqMd\r\nAV599sXXjPWx55h9Pnz70oHgiEPRTsvIcPd6F3dSCSl1m8RZhknUE2EN9RsM\r\nqUvK4vhwP4+D/Ln9j9LnW8hNYBUczU4v5q69DX8t4ymXpVWaTEfnN4En4nmT\r\nbDoy3kDP1xy9bKoBx7grQwH9OA4eTUt0mUdUk4F32fRFgVIDertN0odCemmf\r\n0MSPCU5WwfhuFIuCYTV7KPI0PqKCTozlPwurizGKlpjcz5Qdwo2w7PY7GVcc\r\nMnPAjvT+S5wGLw28JnPK/8Mh5Es89sVmjRjtLYzGtICmS7etH/ffmVNlCMcv\r\nba0ZWvotFkvFWJLmS1tDR4Wu4m+rkHKHwWTO6z0QzxbE0O0e7TyZdTshN7TY\r\nlnecxAdA1+VRATaRbY6kRCN2ekAisFrPWhz4h5qDvQBCTZ25HdOzVsHQkpTC\r\nb7RBQ7dp0yZRVMRYdEdCHRqyRA1vUly7ewQ=\r\n=Bcm6\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router-dom": "^5.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.6_1680723215968_0.21832622125138168", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b955970594163f961e429dcc9a7c2c5101f8fd72148d947c83fc6fb983372edb"}, "2.4.7": {"name": "@types/react-router-hash-link", "version": "2.4.7", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "4f6f5be7ed88c49ba02e1fd69718286fa99791f8", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.7.tgz", "fileCount": 5, "integrity": "sha512-pyNv43V1TiK239WIIHF6sGoEbLbwSbnsPeoF1Y2nvFPMki/6ChQMtlKxzQH8qfkztemgrSQhGyi2SJXRIo65Dg==", "signatures": [{"sig": "MEYCIQCSsT36lp3SiMPdNiIt8UVNEqjcEsn/j2IOP50A7m6AnwIhAJnxhMiCjStvDsfaC6zVqYUWZ3En4lsp5dAStec/V0Ef", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5638}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router-dom": "^5.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.7_1695804189996_0.2403520021650365", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "38d9ca1024055c7f84266a68e18d12d73acafaaf5445d31d9dadb9a37a53f5ec"}, "2.4.8": {"name": "@types/react-router-hash-link", "version": "2.4.8", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "524ed5a71a5857febfaf788182ab321d2c81b5e1", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.8.tgz", "fileCount": 5, "integrity": "sha512-6j0W3/l9MAZd3JPFv1uMEp0yNJN7DX7l9iKWR6hb8eI70pV93LwYkp2yevioSXof3DiPD2pFPuregsVvNQLMXQ==", "signatures": [{"sig": "MEUCIHI/5zIarEOkbLF8MWQGvcKAr9ra+/tCM0kja1bkx5ZLAiEA/mK/MXYMxEH3f5Ks/2TD51PWmZD0wALgcW5jI8cWc7g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router-dom": "^5.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.8_1697634412170_0.7636480024463435", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "038b6a5989e374c93366605836423195b5199daf4dc39d0dd7991c349e6fe801"}, "2.4.9": {"name": "@types/react-router-hash-link", "version": "2.4.9", "license": "MIT", "_id": "@types/react-router-hash-link@2.4.9", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "dist": {"shasum": "b9f069fb5faeba2477426b3932205d080f72ba99", "tarball": "https://registry.npmjs.org/@types/react-router-hash-link/-/react-router-hash-link-2.4.9.tgz", "fileCount": 5, "integrity": "sha512-zl/VMj+lfJZhvjOAQXIlBVPNKSK+/fRG8AUHhlP9++LhlA2ziLeTmbRxIMJI3PCiCTS+W/FosEoDRoNOGH0OzA==", "signatures": [{"sig": "MEQCIBmsWltVhdjevjX8SdJ+/TePihgaW3RsUJg+N8Rdus04AiBIMLjiiKh+SUx186HFAgNMaXupcJqAYdu4RGlp5m5hEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4866}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "directories": {}, "dependencies": {"@types/react": "*", "@types/history": "^4.7.11", "@types/react-router-dom": "^5.3.0"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/react-router-hash-link_2.4.9_1699368453064_0.276243114510782", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "bfc75020c11f0a6e4c5e97dc5208d896ec5b8536984fd8f7cf6ecacc4134d426"}}, "time": {"created": "2018-08-15T20:45:22.587Z", "modified": "2025-02-23T07:41:46.366Z", "1.2.0": "2018-08-15T20:45:22.983Z", "1.2.1": "2019-02-13T22:29:13.062Z", "2.0.0": "2021-05-06T18:03:03.099Z", "2.4.0": "2021-05-18T09:31:46.289Z", "2.4.1": "2021-07-07T17:50:13.029Z", "2.4.2": "2021-09-14T14:32:39.709Z", "2.4.3": "2021-11-05T18:03:42.167Z", "2.4.4": "2021-11-05T22:01:52.516Z", "2.4.5": "2022-01-18T22:04:38.953Z", "2.4.6": "2023-04-05T19:33:36.114Z", "2.4.7": "2023-09-27T08:43:10.174Z", "2.4.8": "2023-10-18T13:06:52.303Z", "2.4.9": "2023-11-07T14:47:33.249Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-router-hash-link", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/react-router-hash-link"}, "description": "TypeScript definitions for react-router-hash-link", "contributors": [{"url": "https://github.com/zoompie", "name": "<PERSON>", "githubUsername": "zoompie"}, {"url": "https://github.com/micha<PERSON>-vasyliv", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"url": "https://github.com/EliRobinson", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}