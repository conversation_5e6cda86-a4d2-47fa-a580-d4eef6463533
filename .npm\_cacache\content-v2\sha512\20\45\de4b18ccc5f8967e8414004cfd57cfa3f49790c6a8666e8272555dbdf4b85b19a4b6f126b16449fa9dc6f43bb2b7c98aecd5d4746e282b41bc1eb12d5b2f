{"name": "@restart/ui", "dist-tags": {"latest": "1.9.4", "next": "2.0.0-beta.3"}, "versions": {"0.0.1": {"name": "@restart/ui", "version": "0.0.1", "dependencies": {"warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1", "@4c/docusaurus-preset": "^0.2.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "81de3752ad58df904958d1e10ca5794cf07e948d", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.1.tgz", "fileCount": 147, "integrity": "sha512-aYmA3g5sQCepL20DcmtY/SbO3SA9xIWJIgoQW4T4DJgdAHGo7jlGBU3l4AVchuhwz8t26MSLq08chFGQL3mwYQ==", "signatures": [{"sig": "MEYCIQD6jpwz4kf6KV8toW2sNwI71NsVDg87p+3SRE70WopnEAIhAMTbsgGDbiQx9tN/LQ1iBrn6P//Rw00O0BfJv19Y+FD9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326838, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggbyhCRA9TVsSAnZWagAAhkkP/34cDJc3l8KaZCC8lte/\niIajR5KVGeeWACGURwNR9sa7mOVsJRr9SPtpasuLv3ca6JGVqBmB8bxpA0n+\nwdvld5fRbAr7gp4GAAfIw1Gl/RquiQFGZd/YbL0hXMlJeQvPxJmXPWFP5ger\naPmNPtQNRtF/qUG6eft5mu7GRohbuctDVwy15Nrm5565fOAJ4KiJ3ogzSITs\nBsJf3f085vHAzoLiSNb/YTIJdfXAAWrF7jIFvUAUKdQ53QmCZp/kZul67fUo\niYg9AiC4cMQqpx8+10U0m0OkKd/e9xA6BiA31OP2qrDPZEwpIilYOBq7+mZh\nOLXb21dGIV06FJVHu2VczeyKkxLyTlH1YU621Fo69DF3rYXIAr9GD3/DfgHb\nLMFZlrEfB/JFC8hp5egw6k+auRBZAIAZh0VrW8xlv2zZoypgANrB8nhllGhu\nLpohNakYx9TGGrC0ZeyReU8Kc2FZ8MDx2if1VrAQ3BUoRxwc6t+Guniic/RW\nDjaCS1zbKbVe0R4M63xuD18LCNOWGZ/g5ZL0F/R0qXwWeu+N5tfD4EtwIlJq\nX8B6g6z4hXflw84+guHBJ3N15hyuqupu2mCxnQu9zUlM3Cy49v2zsRrXS1aY\nyoM+g8VqGsbIFCdpke0SxCr0YhdF8VPEB86K437wcjWl+4a6pwUvBDnnvuaR\nrmGh\r\n=I46J\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@restart/ui", "version": "0.0.2", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1", "@4c/docusaurus-preset": "^0.2.4"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "406300022b284344a4efa79c91cf0b2651e35889", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.2.tgz", "fileCount": 147, "integrity": "sha512-1MpHtlUcxpR6arsN1D90romvclodFpwGcI3YCX/80EKjZerE4CFJyOxQmPnQYvM8o2NEQWTfy9Ij7ghEM4Mavg==", "signatures": [{"sig": "MEYCIQDN3KIP+1N94JY4iqnKv8pSdS4zXTKbhCJyDCzoJ5PqCQIhALCH56w0RQvpTqToos3AHcZpxOGphi1nFd49mX6BixsA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 327086, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJggv2YCRA9TVsSAnZWagAAJykP/i+2WefMuEpgvOh63y//\np+01fjU04DAbTpF2Wn5sSZK48Ccw235km33b1oXBRdOV1HaybQ24eB3feA2A\nY1KBRhTPs8gHIOtwV78ArMTFgRQxxL/hPH50MN7vnYAwaEHEMwFtczLtXBsJ\ntv2o+5QEohUsJbzFCpf7XOSqwhjIg4g37V3ARvdmrK5Lrw7UzCFuZMcChfBz\nY4t8JojU+/3AyJcCAHnXAkdFzGz5aE7DDSuF23IzO7h/ZpkO7aRYLbinMJLH\nXd3xhvL1VQPG5Mol4m1z7IvwC0ZK52JZyZ2m1Io3Nl/DiV+1+1ZOtLEzM3ln\nk5BoeFKxY7XypojzLJcZOK9oc0XAMevSgY19+LU+2HqfAM5yIIEcMINfqTK8\nqeZTc0EJ//aCW5olA+WHX1jWRTXMNQECFm1gzNbyz6FR10FiIK3eF5QyvG7D\nB8o7ti9PD6sp7LX3KQLJrihJbQqxCUizzwK1psgfNUxiTJm9UAvSLrTBqjjc\n7SNukuxXGmE9yw29JxmawdS3uk4IZlfznRzC4W6LfUcVvd3JjNeebPlF9E14\nLFfSXR8ysw4+4Hg6r7NMa0Vmmz3KWZX/TGtYwQ3uB1UMHc2tfdpAMDJfhC7Z\nL4jA4vYBz5gPu13j7HGwmd5TmoCtPKjnMtVWXXc02x0e/4VqLlxosP1iN7hf\nTjOH\r\n=0zE8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@restart/ui", "version": "0.0.3", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.3.26", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "0fdf323f37b88da76f0545bfade8fdaac27f949b", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.3.tgz", "fileCount": 147, "integrity": "sha512-9mmHrk267KEk8wenhs3GRi3EbgEIIhwaNKR5072k+TTJIpz9evjAv1Ga2/0p9jJcVEmIzmOswiuXR/Tk865k/Q==", "signatures": [{"sig": "MEQCIFYfHdtumzyJVthf9k5JjMfOmdYYoFFdHrnfc/F0+DcIAiB0xdWhF7Z/GBqfIQMuXIAeKrF2Au58Am6ZtsnEPHhapw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 327047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnDnYCRA9TVsSAnZWagAAAtEP/jbEPddIexSAP9ZpJUel\nYsZ672T9bW1PfrfiOgTaHlpiy7pE3TkT39EgW+abyIBG7T65rwBvQyRKSDRk\n50iCUekedNZvtIs1PlDAjg1C3Ia3JmHk35I4yz1VHEcDmL52hbW9k9JI25a6\nIbOe415PC7oftjnYrth8Wi13w/mZ+IVRn1NVWiPui3caQetSE6oKTrWmnn6s\njCESfKxZTFAAKhvfumiWkZBA2aPrQiE6pYPVCjtBd8UAY7xySpwoBU+ci9V9\nqt4CG5T+GFtUIG7Mew0xSgMDR5vt0NdLazdVWfEGKFrm2Kr1CusEI6lT++kM\no/b0JPa+fx7sFG5GrfFggPGpCsi/j2aXdjc5Ep5IAoIhyWr7neXYPErQkdVh\noOuOVtdYmHKU9QdtobFfalI6466UQLo3w9gxpzIjv/Ua8NhPYX33zggqSTcj\ngBGmN/ec63lhE0O8PnVVxVVkW5Pp5MsApU5eDbks++/m0mqA9JmSdXNHcFI3\n1gPwPDPh8PiPo5ucDELPehStS8eBXzDILSU4zmjAgJG1+wnB/tlSQruZg/mF\nWFPFwCDECxilC+Sy8qItw2wwa3KS41q/D9TOEMMqmKenNJl1IvUJx9Z0z/ck\nIH2/CCBuYTW9NIakzWjPkbCLcbU/eDANPmMjThmeGCCX29Zx2ajV5o0AjRT3\ntwC8\r\n=qIO1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@restart/ui", "version": "0.0.4", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "400d39e0edf1a0eba7af00e4d5cf62da0462a30a", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.4.tgz", "fileCount": 162, "integrity": "sha512-RnweTupjJPVXsTaEu9ZIkLs79p9nrKb1nOHNSO79YZPM0bJzbA0WdS3BiSZykTN2z1YIJCH33r5Y32Q29w93WQ==", "signatures": [{"sig": "MEQCIEi/2bqE61v71TElIsTLATPJLg83tr/iSejXyUUp4xBIAiBRvxjNkVLHhQfdPP4EqYVFp9g54Ew6xeLrwtjPzcIV4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 343851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7x2nCRA9TVsSAnZWagAAdEkP/RZO3GANiqf+OYdm2ges\n3XXet+DnxkCzxMO16YR5mvAAhNGEyAKsWIPUD7/nrIek6NrSaJf/annHfocG\nYPsCzybUfrdj9Mg8aQKq/mZNk4Pjvir0UIXSrLfGKuuHif3Hial5EQNflIP6\n++yleRwtSP42zhbbqBU54o1NUUkZXNA1fcfxSsbfEPquHeL2VGUI0TUMKHpN\n3xvJow79YwKYbAjglyg6OZgs8nsGFGPVokJ5LS30ogdbE9PP7ahsC96XqvJB\n8vck9AQMzavtWgw6v9wyL9g4lOOBQfFUgdRojFpViecCVJprC9zUqQHnoopO\nsphbHu7amyVBPcJI8x5uliYsnKne+39ty0vNEBC/lFL0CkwsqaZhjK70CtaT\nndn7fwtAA9hMsVv/5xr/9EMSHuLZzr2K9AnDnSrVVvj2lsR9cw3/SUFvgV7i\num9Mf0Uf2DMN2o+W2naQMm6C7tLbbC58TLOzcmCvk4NFoHHQCB25zcshFjZ4\nvnScFg+w/EkZzU+wQbjJMiNMXKnk39FXhVDHkRnsXuUy43kNSlnDX4tI+v2+\nP0vUQ6aq1EWiokTrB+YsmM0UpASWPeAu6vvfeb2XzGt+mEYmtYglemATet3w\nRWevY/twntdh653QsK7YHf6kGrMZ/CJ0zhCM7+HPlzlBz8tjnAYePd0irimY\n49Bm\r\n=BtAR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@restart/ui", "version": "0.0.5", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "c28c9dfd943f34781b43c313b5a2beaf20335875", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.5.tgz", "fileCount": 157, "integrity": "sha512-JahykXG9Yf77QnGdENXYl4A0k2oj+lhwYGhItlctu3duLCglxIOVHUvarlKNdLywvW+zqGukNrgdDLvThMiVng==", "signatures": [{"sig": "MEUCIQCJG4aPjSGgThM/yZ2PKaCOOXSHLbC2W5nQ7bpbqGtWVQIgZNv4cGthUUUc/LtybsOOh/rRrxeV/1xYzA61gzw/AeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg9w7fCRA9TVsSAnZWagAAUWsQAJyl4hlW6QIMJtYpmzYn\nLz6ootW4KRUvaEPTm9fCBYCuJwnZg7njyGFt6XXrUQa2Pe2PGtpTcENdAS0V\nCZFHJcccvY1HfKC/Vyoh/G6SRBeTbhRwrfUnzcfs9DAWZIk+S2sUtXp32VnO\nBBJ4IMKvF94sPPvGdQ0zGjwU3V6Iw1I+cQ6ep/2uzHri9p4NZCxD1ZY1X7iM\n5zMnNDSEUv1qYYaFDy5VG0CNbIMJXoMrofjN3GXCHZPsOay1WDeIxKvv4nAD\n2ePf7a/MLM6Oaoso4cSfkdygBoeIpet3aTezKRKtZiuQosYN2nSF/UVkUpQx\nx4rzNSEIfQqDSnx0NL9eXhLmLKfg85grqR45/DR16XrHxIf/Of65/qpNqLLh\nRuBr2FoSOJZfjMI0VtXRWnOnEClPWMq9TKpbft0OquJo2qilgMMsHtoB/duC\ne1g2SHAFgNQM+/g3Sxrx2nbcwWC8LiRhWfOrUYAiwAHMrIrAXTQx98hQcH6u\nkaDfzzZlKCHcIlnru/dB9U2YSoBuFqMI9h0Oxp7VPXN8aU38EcKEAuBi+1q3\nT5/JiRGIu4jtPVK4ZA/Pj9GYRF7d4rWUbWTVVwHCGOHDeUKXZObF4axo/iA/\nfTzw7SAKtteFADMLo888qMlrYcg580OT7ixRklvE/qHKg7chBnboqqGOcL6v\nh36d\r\n=btUk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@restart/ui", "version": "0.0.6", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "31e7ee452bdd57b1a03906dc2b4ce54968e83099", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.0.6.tgz", "fileCount": 162, "integrity": "sha512-1GjkUZ1C44FS+x2zMxns6gnqntFa9thnBIdXwlKpYc5+rjYnzY7DylDglmbbUZ8qXs4izo6awq09spSJEMJqsQ==", "signatures": [{"sig": "MEQCIBCQTuZxpaGedNYm0Abxdo2n2xSeBKZdVe6IW6y1YwdaAiArdlyDy+O74VxLZ1CryLSYxwJkhm95LkG9XrrQgbivXA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+HD1CRA9TVsSAnZWagAAuFMP/iY1pCXLnC1p5ZRfrjuq\nQAbxWvWZ0Fy+XDNufnECk9dML034ZN1w5jzO5Jn300Qb9tPrUVksGdDzmqU3\nkNIR4Gw/MyIDo6l5HxM2v632TZR7jYYAs8UsHFg66/EEceW8eH/NJK79/y0c\nc2o5dPFQ8/4+mnoK0pzEuoyl64VdlXFQYnE15trvnfw8Y9bH+di5SXacK00d\nCcU9M1W/F/AdG10OZLmL9PZOfiSf61S7Fp2ik7PxxZjXewI40fy86m7MXW7D\na37MYBNmS97HWr23v+FTwiRYm6wgIwpVa5akvOH/ssBzpPS+g8pvHz5eEYw7\nykjyjAJ3nkHegoboBJGVhuQAKi6J30fottu0BYSw2GiqT47+HHMMc5pzMhP8\nHo1+jojIBR8nRBN5PQ3u1EZzi68G9sfds46+dDRZEcIVuYnbFn2mKRmNIbwR\nsg6Jia47IzkDGapGPOoW50grD9OCb3VwdZPgZzAWbF0qd4esX2LS4Lfnx5/P\n+sLVWX4ydX9cTJjbCxN7i3gQSHCnp8xrGhFtat3DYohvuOYdV2iQEKQntjLS\nM8jLZegoOawySsL3ZXYd35iT03TKvvoq1lOPsrLPCrZhgFQwwXF5zUZyn09X\nCUQlZ3gu8+RFt2zSJQobBUtzNN41LdZsn0bvWX5js+vF5RCayYqTbourC3aJ\nxsRh\r\n=iBSk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@restart/ui", "version": "0.1.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "9e7c64ef25498af057712c168c7d2cb88045b9db", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.1.0.tgz", "fileCount": 162, "integrity": "sha512-t7ZVkvM5hxHUp/Ki+AUaie8WjoFcx4qb/iGj67IQz00C09RCW0Vv78E5oVR+AWyDfYIbtlyCA/qFsJQhkTsN9A==", "signatures": [{"sig": "MEUCIQDNna2YPYD82winRy4MJG1KC2MwWfA6kKx14FmDaKakaQIgcFRgJV0NQMkcQN+NM6y8OiupZD7XFAJz/0urYvuilns=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321496, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg+x0mCRA9TVsSAnZWagAAdSAQAIkDe1lSbo0mVXSHdm56\ncl7yWpe01Dh0e04bdemWfsZ9Kd1/J/BGN4p45X1SUGmrEiI4JpWVoZ/49pSC\nSJByuLhAV2/ZAgy5+IPcMUvHBdWAduxbEWDXB8lZP3D1m0Tlv8tel+xBOneO\nMyV1U8IWLlW3udJLaVUK/EZIlS/DYv6iXgk+Ip3Mf14OAfjwnqnFCo8+tPUJ\nCOHVuR9Y9LOYeNPFmUMRfPv3h46TENOcVYOPN9eeoFVaKjjY4mD2DXFO1CfT\nQSNbaS03beozMqoRucaZkqFHSuaCF+yPziXJE+gYIue3I3ncZggw58+ieX7X\nXqb2BRa3+YJSHxDcD2mxAV3W1ax0JU+0cmYUmsm6fvzA/GslQQYu1tSnzhtc\nzM2Fcfq/uhe810T3aabZq82wcEl6N+jBvydnFw5nTs3T+viXl6qEA6+ObUsl\nHYJsT9J73RtfOhqEb6iuE79xR2TSluJQGaqN8y5oeMJcTSTRcKAcL4VtZUPu\nntIw206sdJDP+OmD4pIMb+xsT8RBOmgw+vsiJFSA6ilf73JOoXbbdJvP4A8K\nPqjynuRoVZufeuI28AwQ/Wp7LPMDNsmqDJI1DcxRCwUOVKbDacSJGed/yDBq\nUdDiiQbaRYI/h7UlzHVFPLfy3VDIzaXyVqobT3uWnMFHp1IaYqgonvlSG285\n+MX+\r\n=PXeR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1": {"name": "@restart/ui", "version": "0.1.1", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "7b867ca5ecc1d4ee48b1c2acf44126bca65bec36", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.1.1.tgz", "fileCount": 162, "integrity": "sha512-D/EbRKjYiGogv7j4SmlZtF6DYSCb8KwQza9ajWv/8G9qY/I04wnqoqSJEH7JQwTiwUbzNG6hYQ7qMj2Nn1d8Qg==", "signatures": [{"sig": "MEUCIFacrw2bDZPGU3fI0mvr5YxbPNyBGmCPVXMdHQCzqDPlAiEAuZTRs6S36vLZlDbiQX284Du+xImS9Gqb9TvraAZakgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320700, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhAj4vCRA9TVsSAnZWagAADK8P/Ren5jfPD59vgEW8iqAk\nzz/xmQA9Lce4mO7Lv2ubt5LrSlzgpd6H24V2m2ebFSX68PMo1t8OB0fctN1q\nLwTNsCiN1jKAN7fBwA6SoXd7pcxhEtnsp4/lbzBaeqJNeG2mZcwuQvKUU4rG\nW57YHRPLAboyJkJjEhUdyufgLqSvtErEPKevrrzLDtO2uMNMNDwgp7eoT9vC\n5R48jsb28onb9sE1modgBfvlGS6rsAkB+vEzEAZQlEy8UATVR7P6STPBJgrM\noE3j0zAPGkvMHVvwL/Xfqe2IR5EJr4s0PqE4/U2y/zqxjM9BRxdU4AEWNJuY\nxap0MTEfWfozCBODT2fFxTgNI8v2d+jTcRdyCt0g/AiMtyZkYfBE3jjnIHMj\n846X61ioZY2T0uFooZYgHJ/dh+yKBA3suePwI5n8C7UVpZ9pglFB2epmnZz1\n97KaVLtkbme8JKfy1Z7OR8j0XT5X4D2vMCkW202qPVX2QidhJsZ9L4+2qx0S\nV6eiq/nAT8zaI8IThY2mrr0cCZz8Tq415D6kF6vPacyN6hH7mBHCS+08U8Bu\nAXFH06s3P31qGetlevpURrjOP73OejbxYvVhAzFSMWkpxkGSYDZ1zXobExtA\nUReufecVqXL+Xa/yiTpkeP+Rz79HTQlVpMJBPAaIXTKFPpQIOhMqoIemrHVQ\n8M+n\r\n=axoV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.0": {"name": "@restart/ui", "version": "0.2.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "83419c3d1cf4802134384a689d03cb605f8a01e1", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.0.tgz", "fileCount": 162, "integrity": "sha512-Aoc4jUQUu1KJnEwuT9JGU3+6c8zQhepIcDpcefMojZ/GT641WGN8sTDO0WMfR3AlusFD6mSwrEYVXuGVyOnwIw==", "signatures": [{"sig": "MEUCIQC4AVSZwYSL8uTr+LJ5Qsrj3yZ+cJz5PDD8P0a8XY/wNAIgBivtXjQz6Zn/Dxuc34ctlC3A1s51dnCABwu3B5HKZ1A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318382, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhDH0ICRA9TVsSAnZWagAAzJYP/RuOBS1cPs/CZVZu3zvX\nqLJJ/sswZQBXNtzvQZBfag3fPqVmhy2IYV3Kn4kR+tEfbOXj5nyGjLukBItQ\n7b/qWY19s2xcRpK2Xgb4AZZkG+IFbe9bu4GkLyrm5Hbdx/YN20JwRrNaMar5\nAHxokCT2pplW426uuxoo3TtXPLMegXTGuR6gMmVZrRhs0akUySFLVhmkZCrD\n2xYmvedp8/fc5boeUm7Qk44aUeGlTm1fK22LXOmrHylKsZaYAv7Zpf1NgKKJ\ngt/3B9YBma+DrP/6VreHPsAuqRHqgitx7oHvcGmLNKznJAkdBObr5NNfEqhU\nx1ntmog659SPsrpQCMVaO3fIJ+QjKoj6YYlMjkFa2K3ps8ut4yp1Gs9Qssdd\nA8pwgraDzdL4MOBY/9IGPbTi6oX0ktT0gGuF7lAWoKL9pbhasd6u4e9hXhkB\n8OuD37XMPPVLKdVmfhZv4ejeT8DDDn1Yi22nOPbvk/YCokAw+zFfZHuMh0XZ\nDX/LYmVGM+DyIcP1HENDGa5NXpa9rQG8S1jjaiwPpikjc+PsGhODhITZKl9O\nSNKdc6lY8TB8SyYO2yhbPeY1wydSY+N8HqKyh+6biCQ2+F9wNuJbZnntjbQd\nnbI97wUS9+3RbaiYDw61n6ymRzgMtXyNvxFl58FTQwFfRCMB55YpSlIeXe/U\nlmlq\r\n=LmEY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.1": {"name": "@restart/ui", "version": "0.2.1", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "75d533673194b5e2e8ab1682f0ed7cc4260825d2", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.1.tgz", "fileCount": 162, "integrity": "sha512-9k3hJ03uC3kjmjZ7w5qf+OjZ19WnDhIZPaL6dDk0sbZyo0rhrHM7TKYA339W1he/EINeAWe0C63TnjUS0+lSsg==", "signatures": [{"sig": "MEQCIHSgyzp8HRDZG4YZ5XEcd4LxYVp2HGbsls+HsEDvcXlmAiAvigI+xb4uIyXMcsisaLIOI/1EwXEHqwATJM8g6q9yFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318426, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEcCWCRA9TVsSAnZWagAABLMP/3FvzvO5QFIZId5mlho3\nrY5wIkl9UoMCeBQC4LkjysRjawEeBvj3waJi846Z3G9xDZfh7Y3CMNae9Bhu\nG6VUna28lo3LLReBuA3PrdnRjdVvzrJJNP/jL7lcZMqh+MEY30u37Uab+pVk\nSYfF02C2tedmaRYUiSOXaVDIPWg9doypycghMm0c2N1+BJch0Yei9RW65sf/\nlgJdWZg2tjX+ZB9ts5ZVEfJCOCBwfEtZ8P9Nmz5NiNU3RluDJletEL+MtntK\naQSJYYBeoM4THH3gCrAs8QIu3wqOzhmsij34m6hfAbMNR5IjgKpI1h1qBHUr\nEdkkxYdoMk96qIzFTUyhB81G1foQTlTRtKGbMi+Q9nft9lhNWEIdpP2JycXw\nlKt4VayebcUeia7w37LzKe5HWs5aLX8ITLthGnczhh8JEfPhLWyc/D6AAXB4\neo7y1KPA4CrYysvQ5Fm4EkYEEuEL4elPw98gOw0uK0++CSNIBpRtoVUPZAUG\n4GMLmMcS0lMFqF6ZNEd5oCPrNaEFvSkKVnT8gA6o1utEGcvnkqzCFkzbZrjV\nyDtZ32gp+DvMYfQI3N+Uuv9FiaSDb074nJPmN6YZ/pR8iSHSZPHoyldbpaXq\nlnO/K+01HN8pLkwwc1eJknfDx9ecqOkO1+e5Y+PWAhBff/S23iULQFSldFhJ\nOnNa\r\n=GXPf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.2": {"name": "@restart/ui", "version": "0.2.2", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.9.2", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "57067918a5ac5761848bb70a74aa020025cc91ce", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.2.tgz", "fileCount": 162, "integrity": "sha512-PgNkiyOaWwx8ttQ45KNABXU3780fB/UxNFxcsCpC4RRAlaByZHHbNLOKfhuFs+ZUU0uLxEH9wYQEhDAZc6ajkA==", "signatures": [{"sig": "MEYCIQCgiM2lB0hCsKo+dbPgAfGmnL5/v7v4JFBlwY/Qdf/hFAIhAM4D+JyPH67QUwtGGRcb/ctdNv+b9HqhTcwb2sxfuK7f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318753, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhFBFPCRA9TVsSAnZWagAAt/IQAJeZswzcTOvXbzYQXh7P\n33A0pzLu6tAEavkfMdPq3F+AlUCx5M4gQ9EeXkKbMoImeFoCDHo3kzZsNg8k\n3Jm1acax7BS2AC5RL5Sq8GeRv9OJSds6yo+WA+0hI0ecoj1G1g2fVf5p3Uax\nbVMRiishOu85pKZqiSLY5oVBsoKb8c0CTGlMVuekd8IFy2zlDEJ6Nm48lP8s\n9J/HVJk5QIVCWI48Y8/I6LpL2Xex95Yaww59eaxNZshY6RsN+2H/BtK7xDMm\nNEhEb3871y0Y9fRd30mUe3UUhS3Jo3Qp/SlR2kU+MKPigEHFksevRI6kAtBp\nThtwJgIlU7S9PpTXdnRIRoTcoSJeKaAw8zFWOapFsCvaEZx8L3FoG1fFfSpE\ns/opW67f9xiSBEC97qU/n3a/LpoL1tTQpb6/6QtQ3g1PWvUm6RKY6sCC/+JF\nnZ1ZZ2uedWnkljsv37ervNHhqcWXyInuuiLP55Tl3tzLqMfaoCB6xm2ZaHzE\ntj+3K88dS8ODx9XLFHQwnrvP6lgDyy/J2FjGx+deqxVA8x8tl7DaSTQEQelh\nOjbhjgHydI/lLQAQ62et4fYW6YSwGG2iz5hu0Q4W2VgQD7OFp5weSHfXP2O2\n9FQvaXobpBWdlwKhrPccEwbQglseXgxy70DvM5+IM61XSPhJuzdzI+u1HFhq\nDnj2\r\n=Db+9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.3": {"name": "@restart/ui", "version": "0.2.3", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "8b68aa2ca07af799a65b288cc3c6039915c46a4c", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.3.tgz", "fileCount": 162, "integrity": "sha512-FDhtjIR9QvUfMwvFsgVurRA1qdYxM0F0S07acywjG7gNI2YmQo78rtCYIe553V/pyBjEjaKAg3fzBFCocFTqyQ==", "signatures": [{"sig": "MEUCIQDYdFAqRF/9XWZ4cFjwlSXVscwDvDkMcZ60qAbtwgnIvwIgXkDkemdnD17GRC2m2zOm0VHQbr2fLs3tw39CyYtei/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 320515}}, "0.2.4": {"name": "@restart/ui", "version": "0.2.4", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a522ec75d1d74aa2637ded2c1b338317ff66770a", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.4.tgz", "fileCount": 167, "integrity": "sha512-KUPvgDRXaJap9uGgyqRIeOPq94GBuCGDdlcRoCjV05LSfMrDN4pdMCV9lKt+NDXu+A4QpHRpFBKSUNIyDHzeQw==", "signatures": [{"sig": "MEUCIQDO1v1QGy8RUaPQApmbRJCGzQhnMGrK73+uiwpieQTdigIgP6idNkpKXjB9ezu1JHst9X4rDC8Y4tZ9HMNpGGKr4F0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsMLwCRA9TVsSAnZWagAANdgP/2e5eqiHfVofIwGd8cJE\nVMuIzrerW+AILGSQYXExrmswU7+3S1kFjQJBJevvWMPtL4kCgrBa5z/XJWyQ\nSFWZH/TnLwIQVvvOPEvAXGxspbOJ+186uuBQ2CILhRV+q9ic+Wn/sFKl/Fdk\nVkT6oYJjq4hkuF/SEPPU3cC3VGEl7detBa6ScaMU30ccPgd/2qzQkGFHe5F8\nPbYZO2D86i21xAqiBACh2dueXDLDEC6/P4kiRbHCqKc4hl185gCsgjbcW/R4\neDEE6nkcOk4fmfqN5/1Dux7PfrkCXTHx9o0P+p6epxCpLSavCzmXeqJqB8/4\nKsdSQhOFwZ0LRYz1VjR6WJP8aoGdvzXxf6JNI78/gIErCdraXysqF6OfgB3Z\nEkSqCsnVqlpU+YbFG4M8hYVQiiOKel7XA16TA6V6PSFAR7rZhB+qrOcsjdpd\n6/p358Vy2DKGGn3jxL9O+dMCuTzmsv4UO+iJswauLq/gAUeI1X4/SbqXNZkj\n0Y+H+d5F/iemjubRpHqlF3cSoAWGlHJ7mcNMijWBZK/dwRyc2MDCj/kqGITE\naDYdn632C4lxofRa4Y8LSTiaLtZ9yaVVFj2nRBUGx3ZU4E+d99ccd6bMGQpm\nWC9MODS0Ijiw+ybagMuvl4v2mvTTXRt8Uf5O3OqeTzXX3rCr2SThe6xmJcRj\ntzKv\r\n=UKKY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.5": {"name": "@restart/ui", "version": "0.2.5", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "e94204cbc88a9d13d1d20ce9cdf4713998b7c0fa", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.5.tgz", "fileCount": 167, "integrity": "sha512-3dP8pMFickPpvAG5MVQW53HnJl0c17h7MwvI4nNy9QF66sHSYVchudlqlI8eOSaqnmc5YVjGura63vMb9LTNbQ==", "signatures": [{"sig": "MEUCIA+JQ53/PlDbyKaiz+cOMRv+XOX95Jc9J2ZyCaXzYuB1AiEA7YxUl0SrslTssBlyOS7Z+0Ow0aL9uwiAqqExtgtOV/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsiBVCRA9TVsSAnZWagAA9/IQAKGVMn6OSxfYOdohkK5s\nTJiQUs8AEjlK4CcF8AJUzXVVoOPfJvV7jzzMTvoOb4k7CL3F8GdGrjgJTMwz\n+IyuXJ7GiqJ+OXfsMp23RZtnLmbqeL2pzrN9SWsa+PoRiAmiDrcFJh4ozGKG\nrEIAXVKf3XooJJPEtc160cy/toe6/GIVu2dXZYjw4lXQm6jEawxtHzUd7Rcx\nFGrqBCa/Cwg7gcKHz656/gL6oK5shals6Ca3xcZQrmJq+y/7avbASv+UST6+\n4sHcXZvPrHcl1PApP7nFLDEMoUcXRT5D1/kRlE/dppQJxVasFTx6+Pvs5ial\nczoBS4SGBr4yLVkizMnkmyPt1GA6oC8Jfr12ZJvh6EbtWX+6fRaz39H6saxO\nQrGImLcf8QkwCPnzv2Ev5kehEwbjXWjxUx8N3SLkM6rcIB22rFs3BDX4ICfa\nMDN+4mRYmdjTrsoaZdP8Te81TIwLjTNi5R5fDai8UdOLtQZrrI3PVntlG73d\nigtO2mA0iigw7gsbVOK+JCrDwdyjsJ+qJNFr2mkBRsdyupbGaefek+z8utal\nIEnb38ydDlX1d/2uH24ZfVOL2XAFx3NJqP2FHksjyDlkZFcBvpk8HFVqlkd6\n6Wix/ZmebzluLBoo4RtJKgh5D+F9geHo3CpAJiUqirJ6P6xUn9QIniaExD/9\n5kat\r\n=+PW0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.2.6": {"name": "@restart/ui", "version": "0.2.6", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "43ecf7160c90d723310388aa9820e88b4ec1f2d1", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-0.2.6.tgz", "fileCount": 172, "integrity": "sha512-lcutEWPvsxz0uEyRxuysCbHBfXDFnMKNMNTsnuPmLFjZXgW9fVmhksS6rpFklXHMwxOM9g6hRTBq0gS3QRKgzQ==", "signatures": [{"sig": "MEUCIQDQAzzVlT0bpOfKwedrYGiIR/YpbWF+ROBfUVBCvdguFAIgG21HK7QOtF9bOCB2A1pgEyio8P4pBSW1LxjCFbwQUbY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5sSgCRA9TVsSAnZWagAAPOQP/0cg5uO2WbeZ4RsCV9nE\nz4YOlYi1zpV45m1Ib9YjVdiWv2tu/+WaUxSGSj9oU1TA6vPR9cJxOy1nw6A1\n/YX4MpYAKOvKJKtnKT2dL1N+yYlDVLcmK8jEX464EfVrBKVqwbfaX8sdvcAT\nDD+bpTViUgdmyuby8Rcog/uujeV+7w3ArCZu1sqpqXiXTRQw74l6KaspBVXa\nsydAh8kXlCXdS5I5wjhcs7ujkakdGLSIz07ihw7CoximI2Nty47RYpk8dUbX\nak9FjLl41h9Kz9pdDP1Ak4rUhs43wUvE/BuuaXYGCEqV6hs3TIolTgo8302v\nDmzcEjXQk1nzvWHW+5fy8Lk+deLrGjbhDsrmht2yNbOB18yZkeIW8MdOx4Wn\n/QkmU3UrztEjwZjNbbTWOAVoO4UpjibJNezItzRG9rbe6R64WmJP74hQn0v5\n75LypHFvBkkkeQD9vWO9AsfduzSYpMYDd4LDNFxRSsfzQgzPHVj2n9rlRl+d\nM56YSIFlJIr48U48VI17WsWUHR2Ow2rlW62is8X9kfivM4AJSSxtL27SVV6F\niHZwO40fOWICmvB98qldnH0AwIQIzypO5DX8YpXrTyGPZ/kJp5Pl8zZOU7vN\n0o00RWXSqZaT+n8t9WGe0ampP6uup1TSh/8Vz+8zdueIz4/gcVDHa3W0ly2S\n1nJf\r\n=jBUP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@restart/ui", "version": "1.0.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "2614ac6a9a03703c76bf740b79e2ea5b92c1b64b", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.0.0.tgz", "fileCount": 172, "integrity": "sha512-X0JH+diKQy9q8aIaLIDdoA1lbQDT5OcV4lP5dNS+CjCDEsN+GlcA/1aNG9kfaJvD2hB8GYQTLyjvbkEps9T/GA==", "signatures": [{"sig": "MEUCIQDPVVXr5+BCMQsUT92cmhI08voFVdaLWv9QnrjpYAOcvQIgdxAWmhHeH8d8ZtfZqnIbATQWYy5eCV3afyO+W8XnpKs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331543, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5s3MCRA9TVsSAnZWagAAF+cP/ie7eYKbZtVmtwHVQK3t\nyKvqTgrGn/ZdZOm1mYBj1MhAzIf/ex0IjGDS4cYQ4Mxp+0ob8Cg5xtk/0kKJ\n0V/tKCKPXORFYXK8VZxFvolESkQSQ/wN9zH9+xGh6TnwL+1SgZy+tFRFdhkB\nGi+/f7mYpNRYk5krmhlIcXkvhGjVgX1e7F5sOgb2hqHvj8+C+RRAePbLg72P\noWnXTwsjyWDADIluZ3wnw+qzxaMTyNEcWWEHfKxDoB8QA/w96137OKQcimpA\nMQkP1P9YcMwQ7I1vlcfIPQdA39A2VT6KvUY5vJijsM9+wJvnJu3/hgQkBS5n\nKqevCYvu3onqjCwAZJdv29dszmsBq/yVupGyV4CoV4YplZsUVCf5RuL/aiuK\nUpSywbNiQUopQ/C4qxVpiDIYIHvswjXnthQprvye5fFbolTTzirICDZsX2O8\n3Bu68omWg3usSgHaRMWdhCFXjt+JCdBIGsKOgESTggQ3EpVaeavYAaecal25\nNMB1IJWtbd/TY15KB+c929dZ1jzdzCc+IQpw7Kmh1rFcX5s2xpoND8juW0xC\nWdAYRtNJmXi2GDXFzoeY8dtDhTQdb9HmCgvIeM5k9rx3JgOpTEo86ApUfk6I\nvKbf1rBNTVcpR1ODdGJEcE/4vky/+Zb95qfy+XSCLPleXhU/UqBmmS2AXiRQ\nY/Qc\r\n=q7U8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@restart/ui", "version": "1.0.1", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "379f8340ab14adc18522731a1be6e32eaa10ef92", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.0.1.tgz", "fileCount": 172, "integrity": "sha512-hLAqltcAjQYtjGuHBHKyPpR3ScTxzdkSYNvniwBfN7rUDbYiHu/UZiI1hvV2idJeUvktRnz29l7W9BnNLHrG6Q==", "signatures": [{"sig": "MEQCIEr3pruATGxeWGkpVu1h5dt/6vkkoYWZonSFHRJn8/NUAiBXyjEKV7I/cLInPaNHDMTZ/Rcup74obL4V5yPbkEmEVw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 331899, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh92xxCRA9TVsSAnZWagAAbNoP/jhD3a1s51X3qIQm1DRN\n+D9L661fo2EGBVXS+yIDh6bt72DS/8iVR81aE9LPyySLaZU8efpEczbb2voq\nRYdhllUEL9yqoAlOvmv6lcynzdJdWPZbNc7aATBi5OD6Rkd26FB/DNuMb+s4\nHH++1AMb9TBqK3E17eJ8o6PjM/VY1nFPGnHyG6rs4j/M9ESgb2CTVjBg/uv/\nmUELTLB2jlERc3Nc7ljCB5o80Rm05ls4lVO14Ew35ILtnhqlBhSJ0VqxD6gz\n66hHotG4W4v8tD56eWQZvO5HIE+D/fVyiBAlgvDKCRSQSkMrn0ppn3KPJh0f\ndQn+BlhXaCLEVS1yiSxnE703V391d+ndeTSYuC/9OCoaL4bDd9mENzyvlJ3u\nLomiBpgQH5YRRVzhUeiV3d24PLnNa87JQwmClVhZDtrYWVUk+uK7jOZgh0Ta\nwGPiCbHECsa3s28aomLlq4HxeodDsXBGmtSd/NsQMZynaOqqoju9Hn2Uvi4O\nuH0f6Nneo7JnU3ILyQcWsNh9DphWMCYeR5MEzrTVpLAgtTtTxcQraDpXBAnH\n0MzcD0ivgEHN0VldaGnYy22aVeDRCgk159gZdtfSnvNAHqnC2hCJtvT/rUyW\nLzLbOuUl8LgwYYkPh9eOslypbLWngHczUjoXnhOIMSZplgCbhTvEHwAqJpIG\np9XB\r\n=VVPB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@restart/ui", "version": "1.0.2", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "009a06ae698d624672c5e6a776efd0e8a6017842", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.0.2.tgz", "fileCount": 172, "integrity": "sha512-vKGe0UBJLnbvNAjr8ljlDvphf2HkpjBjXsblmgKPvKdZBDn/mtAz89wmznaomIaEJ9VNoSEY0vA5T5MDi2jIcQ==", "signatures": [{"sig": "MEQCIDpT49pAX4yjjYwm0VVkQI8YpxTZrijaIk6SKfpFx7W1AiAQmGt6fMcXtFMD5YMJY1yDXW5wZdyjf8DR096+smsruw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333236, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiIaAwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcWw//eixlNdQ+VGhWYpwbo9XSr61F/NGyxOcssUoq3yBjHdbE8TUJ\r\nJL2BTqlexQGv8PNZRcsc4KcnoSRxS2N8A/IOSHuHGuLBWGrXheHkTCYmvyrp\r\nXkE3js6qiow/2jj4XjzudGnrX9ycVUtTTvEawTk++CdlYZIQcdeYybs1l6Se\r\nbwXu6uQQE1abf3cyXdueP788KANg/47695aPgIWhbraJkKKdPuAhbuMcq13o\r\nHQmDo6Gc41UXe7KttCbTxWxajJy59h4gFFf7oh8NZiRejRvjsI7AzOHPSX6Q\r\n31cWKscAELyq99637Pkwg/VBHcj23gXYYMxv2LO2QGNDbKrHae1+zFWGgTMX\r\nY4KFPTlLAF8A9kVqicCFqU3qIq0CBxrQtGBXWZztwO8HEk7V2dqQ9n6munQD\r\nK7OjT6cGAuV123iE2/t5fJfdVGmkmpQh+HcX8Dy1NJ2qNyQS/g6iS1gnSTrO\r\nWJOyogUZCOkigXGmNVV4sYMfTxj7S4d+ZuJD9DnXLOK3pQdQdMhqWTjKl/+w\r\ntjmBTOgryDypX589Goio1Mr8hM6/4/fsXvZpXjDSmGmBiJO6Rz1EQx99seFb\r\n63QuX/0CvkTEXTUX24k2pA54KIJtQ4GGmXleQ8ilDRzAHOqyKB6pRpbyayQJ\r\n3J1FCkwX19AHrh/uR/9C/tNL+4kbzYOOdNM=\r\n=s5uK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.1.0": {"name": "@restart/ui", "version": "1.1.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "prop-types": "^15.7.2", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "46d436225162b47ecccdf191cfbcf9ec3d1d5f47", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.1.0.tgz", "fileCount": 172, "integrity": "sha512-sYAO1LP78Suz5cT2VEkU4U/mvdjFXNg69QHanc5OAFTWyhCBG2lFJ9FITZ7hT8P8LPqcWXcwEGzHhuxPUDDDYQ==", "signatures": [{"sig": "MEUCIQDlDUANIVTZa7MeuXwVCkS2KupDUYX5v+VSwWkn5jbTDwIgcoTNqcxiGylDtAbUUujZiHN8fmoKaTXxOqbSfNu+HpE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334786, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMfatACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9og/7BjRIMNzLojoMu70XQMqXEgq1AMF88Fp441+fQQTQ5Lz9BVOD\r\nbGWeHqprVXO45mOzULqzCHB95LzPEQgkvclFELRtc/VVx2c+dt4CPp63xnVG\r\nnzv2W7HyTKWslfnIUX9MyHOJYxRrhFudto6BZW23FKvKTBjelX2e2gFfjpoZ\r\nqk5jrxf9epmmt7JETHneLBHYtFttufBQNpgEKjWfjeqZG/G9H1rd5MXDJpfa\r\nQmF6O9aqXvvb7jrnA6QUxoVgXLfctQE4IyZlGgLlWTGZbdE/9jNb4LkexNHC\r\nDT/u9WuZf5rQRM+NBtwHYwa3v7wHV5HS4Ue653oQiTyDa7CVJLZbmfywnlz3\r\nJ9ufG8G9wfl1pEGfCX14G5RMranvqSwhCowlKUfBhBuku70uHJNI/kTcWAap\r\nPnY1ZFgzbW1/UrJX88WzuAV5oij6qbQJKYHj3rC7qrssRctknePIqoRgGRZ7\r\nHKYWudev02Ib0FDQdH3JNDWnjXdrLkEj4MZTQJwXaGddimFi2BULH+jqBfP9\r\nniLq46WPsYqql7xNPeknzVm6bBFcGC3F6+R/91xbWZ9b/mwSJIc+1wrELYiF\r\n8x94ZcyOj1MF6vvvERXeCH0eHkZQmund9v2fq2TSrkSra3FR5pH5mGu6unZg\r\nP6aR2cHKtK6dfwG5jqEuoEbVr3J5P9tMThc=\r\n=5Qbu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.2.0": {"name": "@restart/ui", "version": "1.2.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.13.16", "@popperjs/core": "^2.10.1", "@restart/hooks": "^0.4.0", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.0.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "fb90251aa25f99b41ccedc78a91d2a15f3c5e0fb", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.2.0.tgz", "fileCount": 172, "integrity": "sha512-oIh2t3tG8drZtZ9SlaV5CY6wGsUViHk8ZajjhcI+74IQHyWy+AnxDv8rJR5wVgsgcgrPBUvGNkC1AEdcGNPaLQ==", "signatures": [{"sig": "MEUCIQCFw5Rjsu/IQkDFNWsQMc+6Z0WPPN7yOFlar++PJhg45gIgf164Ywnmsj+vOCkn+8p3vbpDfZzJRysQQjHxL+ZyCI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiPerGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnbBAAjJY+hae0g/itu2KVpEXcvxu0eEOZyjIrFZZ5GtZhB3VcZlr8\r\nnV3Mem/tDopKzODUuX/2A4Om5myiQYMwCG6IjSQLUYr8Jm52pNO0Mu6t+cnm\r\nI9RtKKAT98CicOoLrBX+lckfjmO6Sqm731D3Es42nUwOxiGXlflMQ7M+eT+v\r\nslXEaV/rPHy5BkCfNPhowInOI95dhRi+hgB2kIpcVTscLZUw6Ubi4UzVM01U\r\nTl6ym72JCw1F0QirqitCyWHKmn2jBzo8ZBYyZCWAariNLYa4SpRAOMBZHGXW\r\nOJMLc5NFUFZcREw0lAZgqezsFQHIOFJE3Ihnu+jgY64bGGlyb030Y4iJsINM\r\nIbqeTxnQbpVEtOeYna+uxgC6TRTR0GlO2yfLLRsRZuVfO7s9I4nRmLcG2Z2B\r\nzkJzBsGCVoPjanbxH03zFUNv7kvasZfCE1RQziCXEpDWylvHzpdhx63w8YvE\r\nWqc8qkKAyovbHw/kOwfjqSam1JrKsFhqWl9V5w7SdRVaMBw5r5/u1CzfqOEg\r\nfKpG9hporOmnF7EY2KuhkL8FXmtF8hd5ilB3kvWp1HEuPdZThzUHKhOGjzoG\r\nrVJ4y/M3JYcINfUPA1sNXNo2Nl/kbplYdWqHVL0yJTbiwBUWAUEtAIBEflr2\r\nUlPRFT2lb1d+cnJbVp8pwuTQwPiM0lGpyWI=\r\n=sopD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.0": {"name": "@restart/ui", "version": "1.3.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.18.3", "@popperjs/core": "^2.11.5", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.2.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "a5589aaf752bbcc0d089ae5c7c52d7984c22cb9a", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.3.0.tgz", "fileCount": 172, "integrity": "sha512-VRb330/6tDaHAHRkqe0GOawuj+hcZM7Zp5piWk/3AVwW18+0sQxGqqFeiH1ZeEMdn7w+D8bZPaY3QoLTmDKcGg==", "signatures": [{"sig": "MEYCIQC61oUFBxWvWnt7m8No8LXaoOYq66HcOQlG+0dS9TxZ4wIhALMTX9/G3Zy3Xpw0Gy0GQJfEPQ0y+eUmVs9rj0wVZh2z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336268, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJis6l4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXAxAAkk/Lkshz9W6X0RggLJtB0EU3jV0B1SYrgoaIN4c2ANCWDaeQ\r\nWYCQiO87hU9mFFWuuQ/z5ZJHXwi1OCowUlt5rcJv+yRh7QkjwvHAE/RaCg1/\r\nYPvXJEx1qzcmuC0TCGTn97ey06Nd0aRiPD7Wgm0uKamC7tqQc/LwSDt5M4K0\r\nFKNj6DEdFzCAUcEltla2dO/Pn3Yu80uObZm375rXeLkKZ9V70ObQ6hy0e<PERSON>cz\r\nleups1EkJLWIB+pSk5R27MA/ZpGL4f+2aRUptH9YtqqAQfWTVTaewCGHGuIY\r\ns9aOlopjlTynHMdGBx5p3JgjLebwhjh5C550dFVrLevikKrskUlYFqtzv5sx\r\n0MBMPZyxMYTXAb+w3I7mE9DyNjnzu455xw7xCSQWDsOJbxH0fQ01qO1QmsqM\r\nQVq5NFzUBO8Nk7qHdj5V4Yo6L/SEjBitET8TsQ1jE2gfdfxWiws8M/64hLD7\r\nYUaG2eToXAXbwlsj/TkbwrNvt76mKo790Vx6ZJ1CKxbyTgRfqCe2AiIgh4kX\r\nZ7yjNjh1l6l0zR4OpO6B2m+MG7ancNmB70SENpkknwJTaunLCgERTM0x2YKD\r\nEEDFEx6HI+bt7a6fDcvpAeEdXmRHi50enGqaq1YWRWOJXx6aZZCniKewtNax\r\n4o6eGAL+plCrgXZhxcYqPHnMJNiBur7pTLw=\r\n=vrQx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.3.1": {"name": "@restart/ui", "version": "1.3.1", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.18.3", "@popperjs/core": "^2.11.5", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.2.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ae16be26128cc205efb7e0bf93d1f34deb1fe116", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.3.1.tgz", "fileCount": 172, "integrity": "sha512-MYv<PERSON>2eeZTHu2dBJHOXKx72vxzEZeWbZx2z1QjeXq62iYjpjIyukBC2ZEy8x+sb9Gl0AiOiHkPXrl1wn95aOGQ==", "signatures": [{"sig": "MEUCIQC3HHFh91uJY/OYGdUzicttSRC4vwUeR6FhlzXqefXwYwIgBfnp06RFtouIhGbttfx8Yc+K0GR5H4gzjlesKCF56xw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2vIJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWdRAAizg9fTOI7b9/kdPCfwTjqiYGd2fXKX1aZUEF5JadQ5QD9pg6\r\nW6AURE8ITa1Fl78jcPTPIo1AfomyXIrCki6/CZK/HRkvgtSF8NZU9a8dCBiS\r\nZ5NaoY1cydXUGHnunYB7Hhb4aHE+pVyinPtqf6h3kxCmnPUlbCNB1jcO5rKH\r\nEKbo2JkuQYqkm5kU1OjjUZKXpFtYW8K8vtqTsMKZulJFs/HtqRROFkebImRY\r\n1oKGp8c+eu5y3UH0C9eB4bFl4UH1vvhcCuxuMJVsQAzxILl5mPzHrk+wVTho\r\n0UiQX/4hk4rfJa4+oAVVjzYxuVpFt7ZroACfIxawL3Cy0Iv9ty5BDnbI7njM\r\nk49mT0sCAtxDwE5QxPClpbwUqMxQqJpt05MCu4ZgLqUWe4lhP8URB6tkqRvx\r\nPvhctesMVdwUTGUuTuXLUaeBPTHzpXSF0uzCMcRVTK7PvSkAvRL5zlfhZcUQ\r\nhzuDa5r/QtPVlOuwBR3Gr/E2LCxG7m3vh8joa7grR+l51YECYdFxW5jc+5mv\r\nhwtCuoHpKVYWMsxGLdnHeXmsIgdAI1q8jbLmd1Q9WGtSlovMXtXVVoNLqVJu\r\n0R+D6TO1qXpr9OvYDzynGuinJoWaxsR6Ib+cdgD/qfZDJWXxwUZDvGxJKLUL\r\niC96dqAaOut6Udzi54NEOj5lF1Nhg1S0Jb4=\r\n=NfMu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.0": {"name": "@restart/ui", "version": "1.4.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.18.3", "@popperjs/core": "^2.11.5", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.2.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "8333294fefb045f58852d5bb34367c3323e2edb3", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.4.0.tgz", "fileCount": 172, "integrity": "sha512-5dDj5uDzUgK1iijWPRg6AnxjkHM04XhTQDJirM1h/8tIc7KyLtF9YyjcCpNEn259hPMXswpkfXKNgiag0skPFg==", "signatures": [{"sig": "MEQCIFGghXz8dSjQ/qbxZE4TDUyajJ3s2xBCgCQYFaynW+ZUAiA3fmLwOK8cUyv+92G+9N5VKhpaxu5cld07FghinT8i6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336918, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjFmIIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/YQ//TgIcfK8rk9caAjIv7lv/f1tPZOik603PvYV1YU2vUY2iTL3S\r\npR8gRV6pneW9EVS14fb6LI4Ea3KOHag4gmoRfCnIUjYdDfLFHDFB1ezs8kVY\r\nkT/TPRwvgdQ1Clc8yduk3lWEum5WsIfeNOi8quEfUpgesMh3dzdyIk4mD/9o\r\n8fuvKnui6KwOOcrtvqS0AGxbQeWBnpRmsYo+kqCLvXJJSxYnzGp/qQtHsqHT\r\nU9d5zGrkITcJCPf2R1oiHAJiVqCUQr2jNGSuMiPkeRidjp1CpDoulWJsfxG8\r\n6PXkaTilLbny1vLViq7mfkIuEj7heYDTtL159qEN2WD3qR5/V5i6ZdYKduwC\r\nx4+lpUjAAYXwOJIDU/uUNHXQgKNFYtstauEXFBIy7ow5Xd0dsNeOXj3itFC3\r\njL56Y7jIBijyatK1tuNgM20UwVM1L2mk+X8YVK1xVN5XpFptTWSRQtzxkltf\r\n4l6UaHlpuzRApozHkVWuf5v3Z9ipUqvIAiC5sQbhxsyUMoaiervHGUncrBNj\r\nwgJ64c2ogisUQ0O6pzcHGGwma/8Fh3hvRnIlaEkxjkWv3J4wH1ydA0l5CGfI\r\nVBE/+tFfybes+VxPXTpr2qQ6tqshlVU7HgN064eM80SEgbpxrqesne26VHdP\r\nOPCqRb3JVzpNlTAQqzr+OEv3XhquaqWPYWo=\r\n=cHIc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.4.1": {"name": "@restart/ui", "version": "1.4.1", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.18.3", "@popperjs/core": "^2.11.5", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.2.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "c9057915f0708c411824eeb16c03d0108c0208d2", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.4.1.tgz", "fileCount": 172, "integrity": "sha512-J7wFOx2DcmkBqCqiZgDsggLO7faiNh4Nv1/v80FmbRgP+MYpwaVDKKXLC69DA4+ejgNIsBP5ORtC74EZqO1j8A==", "signatures": [{"sig": "MEUCIQDrH7eQa9ya1nFOp+LwF80S2vQrXECTSD2dgG8VdjeMagIgOacZAlR1Nai/p7IgZ96LK29DgL2w6cRwDF5jmmwu+0Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337200, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjaCOGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhIRAAgr8x6AScAUGEymgoyYiIviVWcBG7d2g0aupEzBdq6K+R4Gua\r\n0XEhLtFBe1151DecL+GqUQSRJNfqiJrUCykYb7iHK1GDrPR+QY5pYjVkd51N\r\nIvREc3zt/0pbGm+NaC7dXAsvQ3K2bWW3huEggZ0a6XVFahNu131MgaORMfEj\r\nAu9O+51WazAAVCRlp6hnajMTs3fCSY32c5oJBTdyYlv6ANDPEg6ivcYNrd9b\r\nbVWaxaelgjT8FT2MS2Tp/7i2fwglMIZ/1K3G2Cg7HI2XDGmMaUvczKJFscaa\r\n2A8/f4CTkBMQ6BLN2M1bCewZVli9JipILZT/pgo3+SMrrBxA5Pf1r7dqPu0F\r\nP7eaXy7AeTtIowjV5H2hPxTbGdMXA/W8WdbJRj50gkgaTsfI7NHd2rLpN8aA\r\n+A0ZM9eWgc5zbGlexAZzzoDkBwDSPJgY0Qhb5p7peeR/9Cwn4feifnxDT1tb\r\nKMrAc91cYQNfILMfjPreqAuOCE74Ag2wa2y2XF8Hr0lP3Te3Npm8yMUHeh1X\r\nX1htSItSW2WqdyvPleetw+QH3sDDoRebxAsdW5yYVplkl8r/M57bqN307bWl\r\nVCtZzoqXNAci3HBE840DIUvbApY23TXXj+qXHOvzMz/FSLLHrr8rLcf9BIrT\r\nOthgRbKonEMhMpRAq/Sq64D8wKZtQLbrzjs=\r\n=fnbv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.0": {"name": "@restart/ui", "version": "1.5.0", "dependencies": {"dequal": "^2.0.2", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.18.3", "@popperjs/core": "^2.11.5", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.2.0"}, "devDependencies": {"chai": "^4.3.6", "util": "^0.12.3", "karma": "^6.4.0", "mocha": "^8.3.2", "react": "^17.0.2", "sinon": "^13.0.1", "enzyme": "^3.11.0", "eslint": "^7.24.0", "hookem": "^1.0.9", "rimraf": "^3.0.2", "rollup": "^2.75.6", "@4c/cli": "^2.2.9", "process": "^0.11.10", "webpack": "^5.73.0", "gh-pages": "^3.1.0", "prettier": "^2.7.1", "simulant": "^0.2.2", "cross-env": "^7.0.3", "react-dom": "^17.0.2", "@babel/cli": "^7.17.10", "sinon-chai": "^3.6.0", "typescript": "^4.7.4", "@4c/rollout": "^2.2.1", "@babel/core": "^7.18.5", "@types/chai": "^4.3.1", "cherry-pick": "^0.5.0", "karma-mocha": "^2.0.1", "lint-staged": "^10.5.4", "webpack-cli": "^4.1.0", "@4c/tsconfig": "^0.3.1", "@types/mocha": "^8.2.3", "@types/react": "^17.0.47", "@types/sinon": "^10.0.11", "babel-eslint": "^10.1.0", "karma-webpack": "5.0.0", "webpack-atoms": "^16.0.1", "karma-coverage": "^2.2.0", "@types/react-dom": "^17.0.17", "karma-sinon-chai": "^2.0.2", "@types/classnames": "^2.3.1", "@types/sinon-chai": "^3.2.8", "stream-browserify": "^3.0.0", "@babel/preset-react": "^7.17.12", "eslint-plugin-mocha": "^8.1.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-import": "^2.26.0", "karma-mocha-reporter": "^2.2.5", "babel-plugin-istanbul": "^6.1.1", "karma-chrome-launcher": "^3.1.1", "@testing-library/react": "^11.2.7", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^3.4.1", "karma-firefox-launcher": "^2.1.2", "karma-sourcemap-loader": "^0.3.8", "react-transition-group": "^4.4.1", "enzyme-adapter-react-16": "^1.15.6", "@babel/preset-typescript": "^7.17.12", "babel-preset-env-modules": "^1.0.1", "@typescript-eslint/parser": "^4.33.0", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^11.2.1", "@testing-library/user-event": "^13.5.0", "@types/react-transition-group": "^4.4.4", "@react-bootstrap/eslint-config": "^2.0.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "@wojtekmaj/enzyme-adapter-react-17": "^0.6.7", "eslint-config-4catalyzer-typescript": "^3.2.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ab00f8c3b30d77cd6467f0e947d2811b3348e7da", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.5.0.tgz", "fileCount": 177, "integrity": "sha512-sZDeA2c98EioJxdgMTaTDb1fjqeFM4fZ+7qQk6H4EuhnwQb4Ou1o4U6U47ehe0MJkVpPzZYX94CaRrDGjv8j2g==", "signatures": [{"sig": "MEQCICpprPp12Dj7H8x/zrAlmPpphJbO0S7Smr625lbB+pAaAiBeXK6K4KjKEKHfeWKpQBgGWs4CNyxNDwfC+DR9nyaxng==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349710, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxcLzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqdVw//Wimav7+Q/Xm5XLG6moXqNMhzwl77isR3Kz6Df2WHaZFkpFgu\r\nbCiSfVIqJunFhfR6axvyRY8FP4Te23450Bmyg9bxezt7+0ok+A/0qMqAnPqE\r\nybOx0GATylhECDH+5IVXLU7RRUTDzVbkDRaD/hMwblyBTrAiQScbBvN7BYR8\r\nM0QMBP3InETfYTXP13JMgE3YIraJWSXcDrfoULMFpnDm2G7J0jDutRkxWb/c\r\nQi4KoGazmTtRc0KJni9RasqZY7s4o3UsCGy1wrlIksJpORLrSuxuz5+yJL3F\r\nYNQPfEbls9yA9FCYs0XJ1gxn4nmD6hVleiThx+oX9aD9w7zL/XIYzLXQEQHx\r\nC8yR+5HbiqZU2Hs+Z+PHQwHWZrpH6sRAbRHceewx4tvzBpcqr7WV/b9mROhl\r\nDyDVCULQidC/uokGZkIsCeaNgiCoxF6iTGWjxAUU9sr3uKK4jDmRvvl8MF2L\r\n8mlfnWitJhBRlcwpQVhDBq6dXZjb4J6BMiTb4hXjd2nb2Qxew7O1brTuxYpI\r\nuVFyLXsvalOUI4gsQ6Y4T82Atxj1TANfY9PxjPHkDM0+YzDZHkngHu4asl31\r\nmDCCp6VFJvsCWpik5mf7DbItF2rAVWpIdWMH13yCy2SsmXjhd+md93vdzAgU\r\nxaGR5N549XpwDM8dWjQTIbyg4NrxczHYeu0=\r\n=CLuQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.1": {"name": "@restart/ui", "version": "1.5.1", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ced0d28196b02b3d6ab51e09d81d903ca33e96e0", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.5.1.tgz", "fileCount": 177, "integrity": "sha512-uPFE2ALgvtLetlzCQQz02Wa6DoVZ15WnvQmH3DwxC6Lh1BxbpmCR/rkYJKHcYvyJNWfC0AQn3R7xG4NddFiEhA==", "signatures": [{"sig": "MEUCIBm5TcEy982bz57a7n7Crt8PTSk611HKLEK+3YA0lypEAiEA8UaIfC67sNrHC8Je1tS3Qsc1GRtfe2WwP1a6bGMDNdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 345776, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxcWBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiUg/9E/VRbgvY4tZ39CDV0Zr8fBNGqPmB5ZK1BKybVnDeQdS9Sdwe\r\nBovcrl7IwK1IUEuQrS3/C/mf0HPiGXMbdjFubsSj4pKg0i4QzeB6J9+2v6ZH\r\nAHguglnv38V6iSLK3AYrA5QFjfMbzH6nDAtpg00eq1PFYT/+peDzI9vNet9S\r\nPmCffYt+3/kJGiUaWuS1WOf8ioqciugRhCCKV6mFPUC4uOmN58iXeoqsyQ7z\r\njk7vSNSJ5e58PCGAwWShBoFoaDvGmm0oECzm+qrMdVp+vmlyoWdS+2TPt9h9\r\nd2XCBMNwVNbhufSflu8561uWmZiur7tBbXaZH7Fz6qXAT/2WQywEESGaVJ9S\r\nkaH5g15g2DisqrLnyTbT/kWOwT4IetuLVDAZLCEnvp75tQyDC7H3la0PhGVc\r\nyAipS3Cw4Od0c8y/rr2Elj+ZoR2ccoZPuQRKvOu6tkWGCwEyTnmHTMpX6KZL\r\nosoapGmW4kZqJ3kmA3kUUsy10msKZbWQNo6q4E8KKOJRqfi3H6bASAT+O0JM\r\nQKf7hWoOht6OOzd00+BB7OTrDlg0gCIjVhzyr0VrYWB41P9TOlshFtx8gRG5\r\nL/Aivlx7AMQbfJlCaQiNGUH9gHKPWSTFM3LJWB4Gs8u7oUeA67n5mkcW5x6X\r\n5blxNqowUkX1PfuNyTjlN8DMYBBhdIP0ToQ=\r\n=C2bD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.2": {"name": "@restart/ui", "version": "1.5.2", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "3c4a732c5d813adb3cc94d157777e06a349f2737", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.5.2.tgz", "fileCount": 177, "integrity": "sha512-9LgjGBXo3p/hwwCxGZY2ZZIcwRy1xYl3kMzZy2NBIOhEip8iDHA3SIZ3PuK2Ta/Nl5vVMcvUmpYJdx/a0tGkdw==", "signatures": [{"sig": "MEYCIQCkvq0tROk2ytxKM6KUjpTqSNSgEUMVRSmYVo0tZaRrUQIhAJVRzZzBxCLhBRISfRPRtzsLSzPhg0T9hBZ90QKOPxLF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjypFmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqb1g/+KJqRdr+ZloGsR/G7deCwwpqrFvJBAqh3HScp0ACHyWWhYmn3\r\n5a29Rv8z0rlVgZzDUT9jc4irCwwVsN/pqCGTf3wC2kFU+UgM/lW8ENIc/PTd\r\ndd7Z2kx7EEonAk4x+lLHqnhXAfbWlhBce+fETVzJb/gmmsLv1Ji9CnyegccU\r\nQj2inza8IgmhTkDQaZRSo7+HQfoZLnBWITfPoCwTR9yH5nCOUGtu6LF21mAo\r\nzJeBQkAKX+8pW3DLbnCkPGYpw+qZz4y6JzmZOWar4hNc+bupz99VSML9QIk0\r\n92Vl5YLKuo+MBNG6HLbLtiVQa5KROqTcj1lCG/L4ZBOdKOVsRmLGXjTd2XJC\r\ntoyUFJnPjo7/cNIeVYefJ3wsJeqIxsQF/Sr6HxRB74ADz6UmBlkICBwC+c3q\r\nCFelygGYaTOCCubVjGIqdgxFI/1ASx51MKiJcQ4AuDttAyFB7Hche7L3uk7D\r\ngeVTQGCdf4oNqpNrj8HpxWRU/gShnqaDUXdqaHlJoin0m5sJ1gIE8zHQA/Nb\r\nQ0Q3y5FLpuNWRxNrM+30sTcqxLXvJ9lnjG2HR40GzazTE+f5aSqYAFE6PoXf\r\nmc5QmYXLjZKB5CJ9C6PLxV8Eo7WQdLGjBIiSj4YXbx1UQb1N/a1j0wl7Ceox\r\nzQNH5LPo1/RrcfWnb9aaUn1q6QSjVNaPDwE=\r\n=Qxaa\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.3": {"name": "@restart/ui", "version": "1.5.3", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "13145f3163f9efe48a8b56c42c16ad721ab134d5", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.5.3.tgz", "fileCount": 177, "integrity": "sha512-8Zx3HUMxOeC+ZmN7MwChkolpDQaV55prSgpNoGOw5heKnn32HgzEQ6Y0Rz0YqMBUtEKS0NsvNpsskve+O8qXAQ==", "signatures": [{"sig": "MEQCIBLtR2sK8OSQ/YrCgsGntSmXUvY21mzpc/y5FEKhfpw5AiAs2yqvn24n9tEPyTGTMhXyHda5LZKx9nW5H8wDcaRbLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj0ajvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUvw//YYewUyH2rOVRLlMNFjE5nzlRr+POSYjBvJN2ZWj0hV3k7Yzk\r\np7tjGqhd4GowUiThOLY71EKBMKLyUYYYBmKyH0CjbxHxXl0SMCqF3++FE2vB\r\nodHkXfp+hl/1o6RsOjlq5hOOQrBf5jwA6TlirmD2mBOzUQ6BmKRea5fX0rJQ\r\ngz9TUUrFKqjdzcdbYozfqnr+wxp0o2O+8nsT28w/1wectXaDsdj9dVxd54UH\r\nCwuiHIbdkGtRVyEKqsayrNQsjmWKxa9pHyfp9kNMR+NX1Tbzw+vF0Rr+yb57\r\n+O3oq/AFmJT4Ehro4CYAWMOUzsO/WaQ8vPYp77G6s8agvg9o4QW22fs9bLKx\r\no365olsAUdCO2LARY0PGi8OwMDyMpoKIoezXTg+ttk2Ri6lr2m7/bJo8D1JT\r\nvHfArUlBrUf00xmXXaRfPhrvbpRN0zBOJfgngVQks9a7XhRfWw3niHmljDam\r\npqDREMDAyBw9xpLEEENIzdADQ0Md+t6tKBNZO9y2PA5v5kqzu0YDTvJOEz9t\r\nHEfI27qDAR/eUE27Z+dFsTtdH9qMTn7i0cb5SliGGfrEbky3jJW5iImKulQQ\r\n1h7G4SWlgynWFuoyLae1ESRrkR2LqBuMMqxJkSiKf/ReljiR2EtZs7eKRjbf\r\n0Sc23tUryBY9vzMkPtA+gwqVQZNCHoQfCno=\r\n=KstS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.5.4": {"name": "@restart/ui", "version": "1.5.4", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "2537e1b7495f7ed4257dae3676c8d2b91e664e03", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.5.4.tgz", "fileCount": 177, "integrity": "sha512-ziNtXY2PrjXrRUfR1D/ry1v1i5IF+kfMcH9d1kIcU2lOELfmDsVb+fzbyEDz3yKvKuqkphTunVDuLdYRJ0YsAg==", "signatures": [{"sig": "MEUCIQD0XvdO6ZaacEg0i/0PbAVuu0vk8JpudLTYsrDn8roANQIgefR2XwS5QanDu9ytooB3XBF4Qflym1g/EmM1BeC938o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348708, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj2c07ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrS7g/9F2r8k/UX6wY8AZw54pbWDkZy8IMHc7KsIWU8BxEpAsJwmmmg\r\nPvsp2MVYoFNd2OAWF2lpELwFXrtLX6SNLGcnhKSfglfvqXyagHL5hKiqS2t8\r\n5sGmSoF8hY65XTtfOIuHE8i6o0s7KrisC3gpzUrS7sE2xxZh27elcVn76ffw\r\n+/vDL34aZEMyqJMisalOYmgXRP9BGjJPaAhbgkktI41Tu06U7Cib2ANHboJj\r\nkHqj21gGMQ+B/s0pqIq1IK/pRVuvLH32QYqheeUKhc+Nh75EKjXXJVCTWH/A\r\nUn2T3S4EIfk9bZFLbSrAHt3zs7QhpQvEmBkpc4y+QI7yHul21RRIbPlEM+iR\r\n664q1bRYpkukwnPPFsbrOR2K//t4AOTkfDYNex64x9yb4SxR9+olGn0Z5S6i\r\nhJk06hy5xjSgRfndj0TJFpIvWuSbwc02QWfE2+/ZMHMW3YVtoq4734tQ2Zf8\r\nhrkI+OgankNNn0xZsDMtfbb7EYysnVW2Z2BMyOmspmPYU3KUyLsnVLC+Si+M\r\nsGFMW9E+2eWVHmBH3b+cSU2sHT2fnxgYAUMYDk5KeLTyXzDgkC/2vSw+I5pg\r\nlCDU1DpPGV4ou5iWWsEqQAqyaw+LJVZFvrI8Q8Wa5kFqeqWFh0kjOZ1fUBmq\r\n+5DWFN8cZFI6tYJ/HyDn3O5CX01LRUSPqHU=\r\n=39CZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.0": {"name": "@restart/ui", "version": "1.6.0", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "ec39ba572a8ff1349e39e0e0248533ab7e273cc0", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.0.tgz", "fileCount": 38, "integrity": "sha512-XPk7f5xSHj3ESlLx/DFyWYgkFegSEpb9MfT5ZCJxwWCK6htBPfwIFIYQDJttAs2CKYAxJj4mDe7Pdm/YNrNHEQ==", "signatures": [{"sig": "MEYCIQCmxuftCn/9ud8CmkXi5NiPGEmq0ZdbqQvOIrfAJZvYkAIhAPSHmIO6yk/OTh9FQLyax3uRk++TZn3l3MmbZIrEu/YX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5oOrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqS3g//cNCApqKaglD1xza9SVWiQm+qqO6XWsjV/sBWUhshepCGIq/I\r\ndn5se3Z+Xpoj0Xt2uXAjtb+dfn2v/KLqFLPrPlE8FcmSAvVX/BJtni+saYPN\r\nasvhIrarMbTiBcdKnJXxyXjdZeeExblTIi6J8AHyAuZogEHdp4/rOGUfpIBu\r\nQTK/iI4ilT/IiASN9SYPUEaEMUEKxrbJGjnj7It5l1Z0oj03vmK5V3tLRnKj\r\nyVjOyKjK/pq/KcLq/waaz3AYkviMSKD8LvonrLgtenBfOsW9LeCivv4uKS5D\r\nBgBtAkaR9KHQiPDdYJakYwi5FsuRPKnqK5ZCrdrRLmXmcRi7lpAdgBPwvIrh\r\n0gtTOytOg85ltKkdi6WdQQlfjCLZW9EpOY7sRUeYIZeaOFNJamVuJpSm6kuR\r\ne9AkyYWgRDxJ3OjScTqLGGrWIi3aICdrCw89ViugJ6Sxx67IT7wbL8MWKOBG\r\nnAZ243aUe1Xli8jsptLNkOO2Aa36KErJMzafrxKZ9WNvo/nxj7YPbksHVgyA\r\nKt8EDf97vth9Vje/upfiF/xEyHSwgEyjW8XZOa+cHgBmZ1wkSsjj9Z/V/dSj\r\nyCcQzSQBFL16emDdevu6rZfjaA3+Teqd6yQK/Ito/mIXiJbw8vvJKycPfBOF\r\nk0akm7BujFg4M8Em7v3IKlaPsY+rULbDlMQ=\r\n=UmQJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.1": {"name": "@restart/ui", "version": "1.6.1", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.20.7", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.7", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.4.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "1db9c11f13bafc0546e33fe0e2ffc7de920cb7dd", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.1.tgz", "fileCount": 177, "integrity": "sha512-cMI9DdqZV5VGEyANYM4alHK9/2Lh/mKZAMydztMl6PBLm6EetFbwE2RfYqliloR+EtEULlI4TiZk/XPhQAovxw==", "signatures": [{"sig": "MEUCIAJvSSS71rA9KegDG6bjM/AfLLadvO/ck7Afa1lXA/1kAiEA8bCWVHX/XZnfpA3wv1OkK1ft+SGmNpLIQZL/iU0O22Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348988, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5owqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPag//b5nDvS01Rm7N1/7z6lq/aESLoWtfmR39sKJGe7uDo22EEq1E\r\nBCcRo93DIghS/HtAFyRe/m0i6CD/y7ytBbNbJB2m77krHnejLDv4oM0+gBqB\r\ncXZ6ol8vH+jb0LlSm3b20TxXndo7ffDdEU6TqoFVl+1Lh9YsPURlPafXigwM\r\nmTzoRKDQMC5YQNKyDlmSkJiSzJIpG6I2nYqSLYUFGkqboEG80fbyelkPCcE1\r\nDwBjQKPRU3Yq+50WQBKTyNti0I724rkr8UBd32RvbkwcwieSOicuxCA0RCi7\r\n9UhQwFplxCk6DWOySbn86jW9X5UbJ1kVdipjRPNN3e61eVn1/YrfejAPrC9G\r\nQUEah7oXl7LdSkpRxW/RgOEWDjsjZWWnd+4m6PBqCHq/1P7uSww8sERtHrZk\r\nfwmHbmFZkDuXhRjH4vLRGCLBAm8wAciAY8PNT81f84+2bLFlHQIMUQ3EIINy\r\nHT8dKmQuskfXr6IAAul49ZsyaEOZ2R59mrgWmh1wMPlbotBO93tSb/JVRwue\r\n4Jwo03B1Eo194nacNFZWSwF+b8Ewenyk6wiLEjVlBAyaYuYecsOJStUEulqP\r\nV35dFfXPKvuR4K4jbPMSTHwO5wTsHCP5nao3SY4Vsyfq6pWBF3oBp9O47hK0\r\n8rDPAJwY8Rc1WRSq7t1TwJXFqtnx3GL89lw=\r\n=olrm\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.2": {"name": "@restart/ui", "version": "1.6.2", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.0", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "e4c28c893c934abf2f61ee44999b7625e7b79855", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.2.tgz", "fileCount": 182, "integrity": "sha512-hcYs8PwpmHEtwjihLVn2Jr89yrYajfhxN5HtTq3HA9U3+feg1SC3swBM8/qibMTCFsXWToEEtzJMV+LWE+Qjpg==", "signatures": [{"sig": "MEUCIQD9rIR8dEM+i+DbIfs2mzTX/vOGt8Byk5elCSJdMEwBPgIgB+dS9xMiDuL+J3/aLpxAKEx/JPCKzOt0v+Kx5pXNJvQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFKlOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrS4A//bhaNh6fqmuZVhbzoBBg10yWhaYpwl55HTA4ncLPmoTDCw1/9\r\nKDiQkaFmmhJZznDdVsYjmDDa+PwUJYdiYsHAT44PJq6gUJIIDKgz4HL5Svjq\r\nZknPibBdqd+ZTNEnS6ftzpqkfKNBTDVE+t+TG6vViXGwZ1wlNOIUua8BVt/4\r\n/TZ/0ZJZxmA5OLNF7G9GeU9CwxJ0atUKjgrj2aT8EM687X+p9Cik0NmdBE8F\r\n96E1B1Lm66hvhzzPbP9beQ3JZZ2g/rXZr4Cg5f7KY5QhXfN6rIxOHT1D4wc+\r\nYU3YWsd6HIPXrq3OaTkv06RSlsyugPZAND47dplHLsaO9jt/TYAQHROs+Odj\r\nf6097ik9qye5dj52MtLhAiA787bNP3XuSNlRA1Bq24rTkHsl89ebaJBwt8vo\r\n3uWRtgDjSrquyv1Aqu9IhkVH4pWRyquwwvgrpGf6C2LuO9tSembAC3rH4rgw\r\n+BFeONlY1duSGkf61+ykqYSz1Cw+mR+d+RPc8RjtPESxfD8R7kzXP4Z+zhXb\r\ntY7F6/MgGdfA8KwkINGrjuz8GkR59jVIQHzXd3Se8HnNuKe4UaE8w7tEapq5\r\niIgAB9kFeR+LqckxZyVKv27HUptj1F2nlKCD7mEA/VWvkxU1R0xSdf6TpHvA\r\nCTB12+ZUbVrHgpuRbH3X7QJNecsJrsn+ylk=\r\n=o0u6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.3": {"name": "@restart/ui", "version": "1.6.3", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "8063f5b5e0131d41db2cefe148b20cb08a208e76", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.3.tgz", "fileCount": 182, "integrity": "sha512-7HM5aiSWvJBWr+FghZj/n3PSuH2kUrOPiu/D92aIv1zTL8IBwFoQ3oz/f76svoN5v2PKaP6pQbg6vTcIiSffzg==", "signatures": [{"sig": "MEUCIQC4fDUWVxJgDZzwKL+nlzAsaz/a9IvGKlWzk4fhd8vvKAIgPswAlLRZYhLJpAF9cG8JYoJYYbY6QZAdvHxgPX0oo7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkOfI1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4MQ//eZtNwBX1ITRUbA3oun+NEoAN28aXmTrhs/bi4JBwtochUY7A\r\nLuy78clGbccD044mWBzRpo3dVphU2alCTuCr/NbwgfqEkhApd7MqZQJL+HXB\r\ndc4XUFYgTFo1UyD5OfR03GzEt9APRcBlC9KGS3mmUrBd7+LFBb78OjubPXXK\r\ntlx4poBgOrVfj1V43WV1n3KS8g1myw9qYojGptRQ1inVW6vfhNxhlBFz6biJ\r\n61nVKBnxFhfo60xKv1JVBAPAeJ4PPM6nO3VYMzA+hkwwN+LBKe5XpY0+QEXJ\r\nP+4siLPDh9KM7gaS3OKIx4PHo3HVrRky5LK6BmrjsqcwCsvb3vSz9XKxqSqP\r\nISy+Y/Qpwb368SGd4x8SyRUDbhSa+cxPIlyDCcnpBnZA1STosEGFVpaOEWcb\r\nlDpIGkO/UzUmMoG3GOEG6yGQrfP10T5Zvx/ceRAuV+hDFORbKfM7RJbAwrze\r\n1IUurXsfJ73l+k8UKOchSYOfUd31IhceWo7+XyuxrbPLGMUVFvR0YynSonds\r\niAFcBkjElSmCyEuNfvMCYSo0QSJfY+s4ARvyaGzghLv32HMeaWPH31Id1ACS\r\non0S4o1Vk5FCQL508F3/nh5qzMQTo7vjLN9+kef8gyReD/fArCs+mbUiAmPG\r\nPVe8HJnOhDssPvqfHRbUtcvzRchAznko4Hg=\r\n=53hD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.6.4": {"name": "@restart/ui", "version": "1.6.4", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^7.2.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "7a3d8a1056f5df4e2a37a124abe21921806c7ee8", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.4.tgz", "fileCount": 182, "integrity": "sha512-nMetfVGmEMLqKDGqoM97nMQJt2KzPd5FJGdmmU73Wt1X+Zh5WTvWFQ5ltvb2FwrSGs4sCUtAszBMyK63/v0G3Q==", "signatures": [{"sig": "MEYCIQDFm0QjaaiTzHQZtf/qik3n7AP46c2BuKO1l296UTQDnAIhAOHeWv3ynhi/KLMUEp2RNf53k64JnPsOfwEWuRojXyGw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350501}}, "1.6.5": {"name": "@restart/ui", "version": "1.6.5", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "8bd14fdc5c8791424e9bf798343a9d422ad8b0c1", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.5.tgz", "fileCount": 182, "integrity": "sha512-kDjhH8lk+aVGc+dPb8wEBXRDx4B1WX6/pqyWi22R3Oim6KQokeLGO2g8MYzwd2/UdjsrDt+HyYFpKihLIN7+/A==", "signatures": [{"sig": "MEQCIDdJ39iHtukZ5+hZtFmWOOpcHjEjCkYeJJQM+emAqtHlAiBg++l4H3CZJbsCWVkxbXSzbJj/qcJzCivHkBULLWXXDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350501}}, "1.6.6": {"name": "@restart/ui", "version": "1.6.6", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "3481e2eaf15d7cae55bb2f518624e10d19c75800", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.6.tgz", "fileCount": 182, "integrity": "sha512-eC3puKuWE1SRYbojWHXnvCNHGgf3uzHCb6JOhnF4OXPibOIPEkR1sqDSkL643ydigxwh+ruCa1CmYHlzk7ikKA==", "signatures": [{"sig": "MEQCIHm0KROYM0ctkRh9T0YexDV11BxYjMmFXNPF/ApDqvftAiAy/rCYZO21DmhKghOM2EaMGfUdZypFrITkzikLHliR9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 351247}}, "1.6.7": {"name": "@restart/ui", "version": "1.6.7", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "f625ed67c323579e5fff756ad2700411e06adba7", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.7.tgz", "fileCount": 192, "integrity": "sha512-rrbADq+7/UIooPjnxOUOwuhBlAdWuf2X2DbVU/ygHSpxwZSo+C4gx/fRx0u1eR9bxOcH2mo/uX8/KZyw+CQxug==", "signatures": [{"sig": "MEUCIQCx66EIXDoayMXouzXPBLJ5X+Mhhni2rihd4gcmA31T2AIgBzM+VBFmvTNR2GyeZT7orB5+3KyY2BH2hMK4AucHOPw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366007}}, "1.6.8": {"name": "@restart/ui", "version": "1.6.8", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "61b73503d4690e2f0f58992d4d6ae1e89c276791", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.8.tgz", "fileCount": 192, "integrity": "sha512-6ndCv3oZ7r9vuP1Ok9KH55TM1/UkdBnP/fSraW0DFDMbPMzWKhVKeFAIEUCRCSdzayjZDcFYK6xbMlipN9dmMA==", "signatures": [{"sig": "MEQCIBlOaDOph4DivLmmOVQljBP5g3T8inxS/Sqyr1jmDN20AiBqmGj1OYoQIKq/kLQT3+AREDPXxik4wYXPaEj2D+8PMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366705}}, "1.6.9": {"name": "@restart/ui", "version": "1.6.9", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "05ec905a56486fa39b62f29c09b3917e57acd62f", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.6.9.tgz", "fileCount": 192, "integrity": "sha512-mUbygUsJcRurjZCt1f77gg4DpheD1D+Sc7J3JjAkysUj7t8m4EBJVOqWC9788Qtbc69cJ+HlJc6jBguKwS8Mcw==", "signatures": [{"sig": "MEYCIQC6GFAMfy1DSjL/3uJ5eLQvcTEzxl+xL3nGLxz4LSdRsgIhAJYH4wbfbGMHpjLNowLDqLREiAS1xZDDjIJ0i2vPsQGr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 368659}}, "1.7.0": {"name": "@restart/ui", "version": "1.7.0", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "devDependencies": {"react": "^18.3.1", "eslint": "^7.24.0", "hookem": "^1.0.9", "rimraf": "^3.0.2", "rollup": "^4.18.1", "vitest": "^2.0.2", "@4c/cli": "^4.0.4", "gh-pages": "^3.1.0", "prettier": "^2.7.1", "cross-env": "^7.0.3", "react-dom": "^18.3.1", "@babel/cli": "^7.20.7", "playwright": "^1.45.1", "typescript": "^4.7.4", "@4c/rollout": "^4.0.2", "@babel/core": "^7.20.12", "cherry-pick": "^0.5.0", "lint-staged": "^10.5.4", "@4c/tsconfig": "^0.4.1", "@types/react": "^18.3.3", "babel-eslint": "^10.1.0", "@vitest/browser": "^2.0.2", "@types/react-dom": "^18.3.0", "@types/classnames": "^2.3.1", "@babel/preset-react": "^7.18.6", "eslint-plugin-react": "^7.30.0", "@testing-library/dom": "^10.3.1", "@vitejs/plugin-react": "^4.3.1", "eslint-plugin-import": "^2.26.0", "babel-plugin-istanbul": "^6.1.1", "@testing-library/react": "^16.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^3.4.1", "react-transition-group": "^4.4.1", "@babel/preset-typescript": "^7.18.6", "babel-preset-env-modules": "^1.0.1", "@typescript-eslint/parser": "^4.33.0", "eslint-plugin-react-hooks": "^4.6.0", "@rollup/plugin-node-resolve": "^15.2.3", "@testing-library/user-event": "^14.5.2", "@types/react-transition-group": "^4.4.4", "@react-bootstrap/eslint-config": "^2.0.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "eslint-config-4catalyzer-typescript": "^3.2.1"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "347b1e31b296af0bc4548b84ffd2456b5716c078", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.7.0.tgz", "fileCount": 192, "integrity": "sha512-I2LqARoU+l83mLGaEzg/ZS7hz+LWXYnwHwfRQ7cim7N4vYOWxcl+j/rkgv4OUUYUhaDdNToOQI0wcTEalhiIEw==", "signatures": [{"sig": "MEQCIB9Z6DNgFtxUfHcNctRBzwvxMZWjjQnJjR8agj5DcPWBAiBGkhUPlPE3ptT9keLEWyjVJXrcp8UAiXQAQZKaKttJrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 360522}}, "1.8.0": {"name": "@restart/ui", "version": "1.8.0", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "3e8d80822b5fbef0576f94acda51d7da9e79e005", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.8.0.tgz", "fileCount": 192, "integrity": "sha512-xJEOXUOTmT4FngTmhdjKFRrVVF0hwCLNPdatLCHkyS4dkiSK12cEu1Y0fjxktjJrdst9jJIc5J6ihMJCoWEN/g==", "signatures": [{"sig": "MEYCIQDGnII85HBY2j8eiuqVI20rNb59Bv3YFqLWAboaNSxLqgIhAJ1ngsCGioWAqJQGGX0ibvPLggBX1XFvOeHMIZMD8ULh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 358148}}, "1.9.0": {"name": "@restart/ui", "version": "1.9.0", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.21.0", "@popperjs/core": "^2.11.6", "@restart/hooks": "^0.4.9", "@types/warning": "^3.0.0", "uncontrollable": "^8.0.1", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "11a64bae8b9babac08aafb459da635edff171985", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.0.tgz", "fileCount": 192, "integrity": "sha512-M/k/ILBXbEIFn0wSGuJYqscih7gwMcnVwv44kgHZ344sjLoO2vFK8AtkMGXx2sEANDwxEPoDhjhfIDDvrvCBCA==", "signatures": [{"sig": "MEUCIQDJyzvw7BkRXQDuhF6h0ZUHN8KNrwXLLptW0sYxMmPnagIgXD99Z2Xi1QToEWLGkoBz/SuxWfjGlxoUlhXNpwPi0bs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359498}}, "1.9.1": {"name": "@restart/ui", "version": "1.9.1", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.0", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.5.0", "@types/warning": "^3.0.3", "uncontrollable": "^8.0.4", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "f0f22d255470e02f6fee6dcc449e56f0f1b8ebc1", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.1.tgz", "fileCount": 192, "integrity": "sha512-qghR21ynHiUrpcIkKCoKYB+3rJtezY5Y7ikrwradCL+7hZHdQ2Ozc5ffxtpmpahoAGgc31gyXaSx2sXXaThmqA==", "signatures": [{"sig": "MEUCIG9Fmcq76TDBhDuQbYFguaVwQ18z2KziElUfF0EFap84AiEA6Ko3GRI3exuz7qzevuuqdrdvvxX9/HVlysdq4rQs50w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 351636}}, "1.9.2": {"name": "@restart/ui", "version": "1.9.2", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.0", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.5.0", "@types/warning": "^3.0.3", "uncontrollable": "^8.0.4", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "dad8f084e6a56f87f9799dbc248af04168ffc03b", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.2.tgz", "fileCount": 192, "integrity": "sha512-MWWqJqSyqUWWPBOOiRQrX57CBc/9CoYONg7sE+uag72GCAuYrHGU5c49vU5s4BUSBgiKNY6rL7TULqGDrouUaA==", "signatures": [{"sig": "MEQCIErXkhRNqAd1vdskgaGE/T7gqWZnJWW0MDK3rUeO8qmQAiAPzf4G9u5EOLNpcg+xkM0nOmpxqRH0SScz9oHhFE18Og==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 351672}}, "1.9.3": {"name": "@restart/ui", "version": "1.9.3", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.0", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.5.0", "@types/warning": "^3.0.3", "uncontrollable": "^8.0.4", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "32771ba81f27aa255f26387c1b9fc1a372f26638", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.3.tgz", "fileCount": 192, "integrity": "sha512-2QwCC42ISRAu7nafKeO4khG1F65Xfu2n+cwQT30Ck5bxszKDXuT2AZMDIX2auXxHRednG2ynr8ffSA1fRrkOGg==", "signatures": [{"sig": "MEUCIDx6Rjm9GWLvUU5IBdFAoNTpYW2p9buqX68oWIEmATnqAiEAtHJ5VCAW54P7GQ+xFyFys9WV5rn+5WiTUQADtr7g3es=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 351700}}, "1.9.4": {"name": "@restart/ui", "version": "1.9.4", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.0", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.5.0", "@types/warning": "^3.0.3", "uncontrollable": "^8.0.4", "@react-aria/ssr": "^3.5.0"}, "peerDependencies": {"react": ">=16.14.0", "react-dom": ">=16.14.0"}, "dist": {"shasum": "9d61f56f2647f5ab8a33d87b278b9ce183511a26", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-1.9.4.tgz", "fileCount": 192, "integrity": "sha512-N4C7haUc3vn4LTwVUPlkJN8Ach/+yIMvRuTVIhjilNHqegY60SGLrzud6errOMNJwSnmYFnt1J0H/k8FE3A4KA==", "signatures": [{"sig": "MEUCIQCqeZpE2UUBec9DjUIgT2X2iQk7tEd5aR8ojrFeLzx1zgIgPEGY8bmOE+a7iljfh82HevxsmlbfiVdyZLx/zbRuQho=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 351940}}, "2.0.0-beta.1": {"name": "@restart/ui", "version": "2.0.0-beta.1", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.7", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.6.2", "@types/warning": "^3.0.3", "uncontrollable": "^9.0.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dist": {"shasum": "1a637f8c2371c2a526068e9511144d933f14d548", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-2.0.0-beta.1.tgz", "fileCount": 77, "integrity": "sha512-khzN/YfLe1weHGJaBIZ5hjMCtLQe/4gISmSoe/zhctS6SgzIJQKzq+6KZAvulriy2hu1dDHrOFjoiOzvbSCHTQ==", "signatures": [{"sig": "MEQCIHcedRUV+YbL74afMfxbGl8dK6I/5tyU7Gayh0wbWh9vAiBgWvbJ07Qa3aADmJ447aEPbtF0ZdrHz7Ml73s8mozNuQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 186456}}, "2.0.0-beta.2": {"name": "@restart/ui", "version": "2.0.0-beta.2", "dependencies": {"dequal": "^2.0.3", "warning": "^4.0.3", "dom-helpers": "^5.2.0", "@babel/runtime": "^7.26.7", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.6.2", "@types/warning": "^3.0.3", "uncontrollable": "^9.0.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.20.0", "hookem": "^3.0.4", "rimraf": "^6.0.1", "rollup": "^4.34.6", "vitest": "^3.0.5", "@4c/cli": "^4.0.4", "globals": "^15.14.0", "gh-pages": "^3.1.0", "prettier": "^3.4.2", "cross-env": "^7.0.3", "react-dom": "^19.0.0", "@babel/cli": "^7.26.4", "@eslint/js": "^9.20.0", "playwright": "^1.50.1", "typescript": "^5.7.3", "@4c/rollout": "^4.0.2", "@babel/core": "^7.26.7", "lint-staged": "^15.4.3", "@4c/tsconfig": "^0.4.1", "@types/react": "^19.0.8", "babel-eslint": "^10.1.0", "@vitest/browser": "^3.0.5", "@types/react-dom": "^19.0.3", "typescript-eslint": "^8.23.0", "@babel/preset-react": "^7.26.3", "eslint-plugin-react": "^7.37.4", "@babel/eslint-parser": "^7.26.5", "@testing-library/dom": "^10.4.0", "@vitejs/plugin-react": "^4.3.4", "@testing-library/react": "^16.2.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "react-transition-group": "^4.4.5", "@babel/preset-typescript": "^7.26.0", "babel-preset-env-modules": "^1.0.1", "@typescript-eslint/parser": "^8.23.0", "@vitest/coverage-istanbul": "3.0.5", "eslint-plugin-react-hooks": "^5.1.0", "@rollup/plugin-node-resolve": "^16.0.0", "@testing-library/user-event": "^14.6.1", "@types/react-transition-group": "^4.4.12", "@typescript-eslint/eslint-plugin": "^8.23.0"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dist": {"shasum": "d34e6077e52543e4e25f472d64a90d459ab64596", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-2.0.0-beta.2.tgz", "fileCount": 152, "integrity": "sha512-i/X4l1LkhGv5Mm9zuBlHWTcDP5O9EQUBu2vn01svjvpVoAbCSaS+DEG1eWI+lF9Y89cu2C68hR+Teztdkw50Gg==", "signatures": [{"sig": "MEYCIQCE07cHpBf8R/S7pwyGKKI+/mC0iWBG9kUKm8V7s9C5CAIhAMOi1x1BXGQ/cKcn62K6Pm97JzyuFx3Yn8qEwIWGsyI0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 402232}}, "2.0.0-beta.3": {"name": "@restart/ui", "version": "2.0.0-beta.3", "dependencies": {"@babel/runtime": "^7.26.7", "@popperjs/core": "^2.11.8", "@restart/hooks": "^0.6.2", "@types/warning": "^3.0.3", "dequal": "^2.0.3", "dom-helpers": "^5.2.0", "uncontrollable": "^9.0.0", "warning": "^4.0.3"}, "devDependencies": {"@4c/cli": "^4.0.4", "@4c/rollout": "^4.0.2", "@4c/tsconfig": "^0.4.1", "@babel/cli": "^7.26.4", "@babel/core": "^7.26.7", "@babel/eslint-parser": "^7.26.5", "@babel/preset-react": "^7.26.3", "@babel/preset-typescript": "^7.26.0", "@eslint/js": "^9.20.0", "@rollup/plugin-node-resolve": "^16.0.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-transition-group": "^4.4.12", "@typescript-eslint/eslint-plugin": "^8.23.0", "@typescript-eslint/parser": "^8.23.0", "@vitejs/plugin-react": "^4.4.1", "@vitest/browser": "^3.1.3", "@vitest/coverage-istanbul": "3.1.3", "babel-eslint": "^10.1.0", "babel-preset-env-modules": "^1.0.1", "cross-env": "^7.0.3", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "gh-pages": "^3.1.0", "globals": "^15.14.0", "hookem": "^3.0.4", "lint-staged": "^15.4.3", "playwright": "^1.50.1", "prettier": "^3.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-transition-group": "^4.4.5", "rimraf": "^6.0.1", "rollup": "^4.34.6", "typescript": "^5.7.3", "typescript-eslint": "^8.23.0", "vitest": "^3.1.3"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}, "dist": {"integrity": "sha512-wY3TBYjxmuwMgYwjfl5up3Iu0AGgiDKB6uLSXUpecjgDXEc6Ebjl5Sv2apsJiJGRC3NHfX82REJ/Mn3Qh8nZDA==", "shasum": "8bbd4de1cc8b9deb6bd9d0f64da49fb2f8c42ff5", "tarball": "https://registry.npmjs.org/@restart/ui/-/ui-2.0.0-beta.3.tgz", "fileCount": 152, "unpackedSize": 403688, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCICuIasiusJKdu/ATZJIee5MtCntQfh5QWZdic9QcCqa1AiAdCh9MhnTHtPuRkD2V5FIgWGZL8/vjiObLsElYA9SEuw=="}]}}}, "modified": "2025-05-15T03:06:50.285Z"}