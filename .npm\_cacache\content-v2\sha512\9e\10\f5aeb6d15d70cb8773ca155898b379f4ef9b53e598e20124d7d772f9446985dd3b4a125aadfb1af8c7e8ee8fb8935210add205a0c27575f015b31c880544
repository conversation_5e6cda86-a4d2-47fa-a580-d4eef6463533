{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/ai-constructs", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.0.0-test-20240807145948", "0.0.0-test-20240807163101", "0.0.0-test-20240807225950", "0.0.0-test-20240809025127", "0.0.0-test-20240813193219", "0.0.0-test-20240814184052", "0.0.0-test-20240814224750", "0.0.0-test-20240815164159", "0.0.0-test-20240815221003", "0.0.0-test-20240816172322", "0.0.0-test-20240823000521", "0.0.0-test-20240828172042", "0.0.0-test-20240904221220", "0.0.0-test-20240905151741", "0.0.0-test-20240905215822", "0.0.0-test-20240906180649", "0.0.0-test-20240924234423", "0.0.0-test-20240925144932", "0.0.0-test-20240926024033", "0.0.0-test-20241001205419", "0.0.0-test-20241001225724", "0.0.0-test-20241010141034", "0.0.0-test-20241011172758", "0.0.0-test-20241023205154", "0.0.0-test-20241029211113", "0.0.0-test-20241030224754", "0.0.0-test-20241115171053", "0.0.0-test-20241119003939", "0.0.0-test-20241204204357", "0.0.0-test-20241206232144", "0.0.0-test-20250214001309", "0.0.0-test-20250218000414", "0.0.0-test-20250218005144", "0.0.0-test-20250307211308", "0.0.0-test-20250319201152", "0.0.0-test-20250320182439", "0.0.0-test-20250327200620", "0.0.0-test-20250404000352", "0.0.0-test-20250404003334", "0.0.0-test-20250407234958", "0.0.0-test-20250416182614", "0.1.0", "0.1.1", "0.1.2", "0.1.3", "0.1.4", "0.2.0", "0.3.0", "0.4.0", "0.5.0", "0.6.0", "0.6.1", "0.6.2", "0.7.0", "0.8.0", "0.8.1", "0.8.2", "1.0.0", "1.1.0", "1.2.0", "1.2.1", "1.2.2", "1.2.3", "1.2.4", "1.3.0", "1.4.0", "1.5.0", "1.5.1", "1.5.2", "1.5.3"], "vulnerableVersions": [], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "<0.0.0-0", "id": "HaqG0+PaMXyOL6F/JXDA2/ubcupzZITko1W7baGuPsR22Zq96NqVfecA82EVCd2+RrzmaFfyBEyOgrfzCVZLpA=="}