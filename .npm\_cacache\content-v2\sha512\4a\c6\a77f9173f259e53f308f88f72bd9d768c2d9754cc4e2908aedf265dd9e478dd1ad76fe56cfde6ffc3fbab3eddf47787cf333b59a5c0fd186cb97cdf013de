{"source": "30QgA45anDl831wGp3oPQYfnvEnABeur5zO4lbNPM1CGOQcuKB/0yD3RwI75/V5mk11iSUQ3im7Z98U6tL88dg==", "name": "@aws-amplify/graphql-conversation-transformer", "dependency": "aws-cdk-lib", "title": "Depends on vulnerable versions of aws-cdk-lib", "url": null, "severity": "moderate", "versions": ["0.1.1-raven.0", "0.1.1-raven.1", "0.1.1-raven.2", "0.1.1-raven.3", "0.1.1-raven.4", "0.1.1-raven.5", "0.1.1-raven.6", "0.1.1-raven.7", "0.1.1-raven.8", "0.1.1-raven.9", "0.1.1-raven.10", "0.1.1-raven.11", "0.2.0", "0.2.1-ai.0", "0.2.1-ai.1", "0.2.1-ai.2", "0.2.1-ai.3", "0.2.1-ai.4", "0.2.1-ai.5", "0.2.1-async-lambda.0", "0.2.1", "0.2.2-gen2-migration-0930.0", "0.2.2", "0.3.0", "0.3.1-ai-streaming.0", "0.3.1-ai-streaming.1", "0.3.1-ai-streaming.2", "0.3.1-ai-streaming.3", "0.3.1-ai-streaming.4", "0.3.1-ai-streaming.5", "0.3.1-ai-streaming.6", "0.3.1-ai-streaming.7", "0.3.1-gen2-migration-1015.0", "0.4.0", "0.5.0-ai-streaming.0", "0.5.0", "0.5.1-ai-next.0", "0.5.1-ai-next.1", "0.5.1-ai-streaming.0", "0.6.0", "0.7.0-ai-next.0", "0.7.0-ai-next.1", "0.7.0", "0.7.1", "0.7.2-ai-next.0", "0.7.2-ai-next.1", "1.1.0", "1.1.1", "1.1.2", "1.1.3-ai-next.0", "1.1.3", "1.1.4", "1.1.5", "1.1.6", "1.1.7-gen2-migration-0205.0", "1.1.7", "1.1.8", "1.1.9", "1.1.10-grant-stream-read.0", "1.1.10", "1.1.11"], "vulnerableVersions": [], "cwe": [], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:N/A:N"}, "range": "<0.0.0-0", "id": "tT/p7uNLcLyTtyGl3jYm0kb/TCEOcz6I45e67HFPKpGJ/e5rIX+tWHWAfyIswCqJ0CYwRefv6EiL/17fG+Qmgg=="}