import { useEffect, useState } from "react";
import { NavLink, useLocation } from "react-router-dom";
interface DropdownState {
  home: boolean;
  about: boolean;
  courses: boolean;
  page: boolean;
  blog: boolean;
  directors: boolean;
  staff: boolean;
  event: boolean;
  contact: boolean;
  blogPages: boolean;
  auth: boolean;
}
interface MobileNavSectionProps {
  handleSidebarClose: () => void;
}

const MobileNavSection: React.FC<MobileNavSectionProps> = ({
  handleSidebarClose,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState<DropdownState>({
    home: false,
    courses: false,
    about: false,
    blog: false,
    page: false,
    directors: false,
    staff: false,
    event: false,
    contact: false,
    blogPages: false,
    auth: false,
  });
  const handleCloseWithReset = () => {
    setIsDropdownOpen({
      home: false,
      courses: false,
      about: false,
      blog: false,
      page: false,
      directors: false,
      staff: false,
      event: false,
      contact: false,
      blogPages: false,
      auth: false,
    });
    handleSidebarClose(); // Call the original close function
  };
  // Define the function for handling dropdown toggle
  const handleDropdownToggle = (dropdownName: keyof DropdownState) => {
    setTimeout(() => {
      setIsDropdownOpen((prevState) => ({
        ...prevState,
        [dropdownName]: !prevState[dropdownName],
      }));
    }, 100);
  };
  const location = useLocation();
  const [hasScrolled, setHasScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setHasScrolled(window.scrollY > 150);
    };
    handleScroll();

    window.addEventListener("scroll", handleScroll);

    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Determine whether to show the "Home" link
  const showHome = location.pathname !== "/" || hasScrolled;
  return (
    <nav className="mean-nav">
      <ul className="justify-content-center">
        <li
          className="tl-nav-item tl-dropdown"
          style={{
            visibility: showHome ? "visible" : "hidden",
            pointerEvents: showHome ? "auto" : "none",
          }}
        >
          <NavLink
            role="button"
            to="/"
            className={isDropdownOpen.home ? "dropdown-open" : ""}
            style={{ backgroundColor: "transparent" }}
            onClick={() => {
              window.scrollTo({ top: 0, behavior: "smooth" });
              handleCloseWithReset();
            }}
          >
            Home
          </NavLink>
        </li>

        <li className="tl-nav-item tl-dropdown">
          <a
            style={{ borderTop: showHome ? "1px solid #2a3a57" : "0px" }}
            className={isDropdownOpen.courses ? "dropdown-open" : ""}
            onClick={() => handleDropdownToggle("courses")}
          >
            Courses{" "}
            <span
              className={`inner-mean-expand ${
                isDropdownOpen.courses ? "active" : ""
              }`}
              role="button"
              // onClick={() => handleDropdownToggle("courses")}
            >
              {isDropdownOpen.courses ? "-" : "+"}
            </span>
          </a>

          <ul
            className={`tl-submenu ${
              isDropdownOpen.courses ? "d-block" : "d-none"
            }`}
          >
            <li>
              <NavLink
                to="/course/engineering-classroom"
                onClick={handleCloseWithReset}
              >
                Engineering Classroom
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/course/medical-classroom"
                onClick={handleCloseWithReset}
              >
                Medical Classroom
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/course/foundation-classroom"
                onClick={handleCloseWithReset}
              >
                Foundation Classroom
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/course/IIT-JEE-repeater"
                onClick={handleCloseWithReset}
              >
                1-Year IIT-JEE Repeater Program
              </NavLink>
            </li>
            <li>
              <NavLink
                to="/course/NEET-repeater"
                onClick={handleCloseWithReset}
              >
                1-Year NEET Repeater Program
              </NavLink>
            </li>
          </ul>
        </li>

        <li className="tl-nav-item">
          <NavLink
            role="button"
            to="/our-team"
            style={{
              borderTop: showHome ? "1px solid #2a3a57" : "0px",
              backgroundColor: "transparent"
            }}
            onClick={handleCloseWithReset}
          >
            Our Team
          </NavLink>
        </li>

        {!window.location.pathname.includes("about") && (
          <li className="tl-nav-item tl-dropdown">
            <NavLink
              role="button"
              to="/about"
              className={isDropdownOpen.home ? "dropdown-open" : ""}
              style={{ backgroundColor: "transparent" }}
              onClick={handleCloseWithReset}
            >
              About Us
            </NavLink>
          </li>
        )}
      </ul>
    </nav>
  );
};

export default MobileNavSection;
