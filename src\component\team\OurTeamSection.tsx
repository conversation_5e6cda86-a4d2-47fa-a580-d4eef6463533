import React from "react";
import { Link } from "react-router-dom";
import { teacherList } from "../../data/Data";
import "./OurTeamSection.css";

const OurTeamSection = () => {
  // Filter Executive Directors and Faculty
  const executiveDirectors = teacherList.filter(teacher => 
    teacher.position.toLowerCase().includes("executive director")
  );
  
  const faculty = teacherList.filter(teacher => 
    !teacher.position.toLowerCase().includes("executive director")
  );

  const TeamMemberCard = ({ member }: { member: any }) => (
    <div className="col-lg-4 col-md-6 col-sm-6 col-12" key={member.id}>
      <Link to={`/staff/${member.slug}`} className="team-member-link">
        <div className="tl-1-teacher team-member-card">
          <img
            src={member.imgSrc}
            alt={`${member.name} - ${member.position}`}
            className="tl-1-teacher-img"
          />
          <div className="tl-1-teacher-info">
            <h5 className="tl-1-teacher-title">{member.name}</h5>
            <h6 className="tl-1-teacher-sub-title">{member.position}</h6>
            <div className="teacher-experience">
              <span className="experience-badge">{member.exp} years experience</span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );

  return (
    <section className="tl-1-teachers pt-120 pb-120">
      <div className="container">
        {/* Executive Directors Section */}
        <div className="team-section-wrapper mb-80">
          <div className="tl-1-section-heading text-center mb-60">
            <h2 className="tl-1-section-title">Executive Directors</h2>
            <p className="section-description">
              Meet our leadership team who guide ZIA Academy's vision and strategic direction
            </p>
          </div>
          <div className="row g-4 justify-content-center">
            {executiveDirectors.map((director) => (
              <TeamMemberCard key={director.id} member={director} />
            ))}
          </div>
        </div>

        {/* Faculty Section */}
        <div className="team-section-wrapper">
          <div className="tl-1-section-heading text-center mb-60">
            <h2 className="tl-1-section-title">Faculty</h2>
            <p className="section-description">
              Our experienced faculty members dedicated to student success
            </p>
          </div>
          <div className="row g-4 justify-content-center">
            {faculty.map((teacher) => (
              <TeamMemberCard key={teacher.id} member={teacher} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default OurTeamSection;
