{"name": "@aws-amplify/ai-constructs", "dist-tags": {"test": "0.0.0-test-20250416182614", "latest": "1.5.3"}, "versions": {"0.0.0-test-20240807145948": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240807145948", "dependencies": {"@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0"}, "dist": {"shasum": "9e3932c83d6a4f42a644879975cec824e255f5f9", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240807145948.tgz", "fileCount": 22, "integrity": "sha512-YedcL1WpKHnJRJ5nBecls6Sc+4M805pbPO6I5iVPfBLqfkNFCVByU0jgQKjtR+WH+0yyIkrfZOuorgtVA41tpg==", "signatures": [{"sig": "MEYCIQD4rZj2S4iyuFUL4ZqQimTEYs9n+q8KeNxNFjybQbnPngIhAPRrITiD7uHauhZ2JdUWb/dXwgqnAZ8oqQu8COI16Kbv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46695}}, "0.0.0-test-20240807163101": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240807163101", "dependencies": {"@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0"}, "dist": {"shasum": "08da4eb2ed76f1ff89b7707b3eb6c5c415bea541", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240807163101.tgz", "fileCount": 22, "integrity": "sha512-iKP5ZbXRoqEbTqjc0cUcCcbT0/azYeoyMU9j9yPCip3Nd0hKAWAZrAZqZi8pqS5RoZyVz70IYCi5NmKpUZzt+A==", "signatures": [{"sig": "MEUCIHy0z4fhptNGDNSXq5HxvE3183eB/OWba8GVQ+rfb7N4AiEAyHXNQyqLY5M+kteaxAU7ph3l6ei2eo77d8IKYJcTTPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 46695}}, "0.0.0-test-20240807225950": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240807225950", "dependencies": {"@aws-amplify/plugin-types": "^1.0.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "85fcbc2b011585ef6cde02b9033050c07788e5cf", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240807225950.tgz", "fileCount": 24, "integrity": "sha512-uOgHXnIVt5i6ScLfG90IH5mneKppGN8E1lGZ9GINOTBowjz7BrtCf+OWWylSPQKxbRmA7xhUN2KSB3UlgIlJaQ==", "signatures": [{"sig": "MEUCIAOAFODcdg/6sKCfzfQ5Y+eqdqhreHcObjKiGKzTudSzAiEA/YfXqW6IpjkDB7Ns61cUzK1rCR2pQ0A+q6JVSDjFGeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 78337}}, "0.0.0-test-20240809025127": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240809025127", "dependencies": {"@aws-amplify/plugin-types": "^1.0.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "16206c43840a852e7a856d931d61a7f35d260895", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240809025127.tgz", "fileCount": 32, "integrity": "sha512-6KoNaGWUU87ChSvcXRzClF3s1ttaVYRo6TP+2OMpxt7wu+ZN66jGOjYihvqL/9jp/otq7IAfjLLjYv3HuihD2w==", "signatures": [{"sig": "MEUCIQD4C0mSEWeuQyIZoMxuMybGiZ4WDXB4/xAAQiWMwGVgDQIgLu8K1nQQ0+AbUHxdEnNUcB2ggs8M05XFA8kDhaNUGy0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 82374}}, "0.0.0-test-20240813193219": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240813193219", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240813193219"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "bb1b0757eebded38f27939fefcd34557abdef586", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240813193219.tgz", "fileCount": 32, "integrity": "sha512-R<PERSON><PERSON>oto2Usy8PXI3+963+HpCoKkGI8x4maKVGY2T0b/c5KDMJHRABJmSHYHvo6IkapxborpstETWs9sxUYpf2+Q==", "signatures": [{"sig": "MEYCIQDMnSb2XI9gZcJNoWeCHtQRZMqKEg77UkaqK1yXdOdzpAIhAOxEhA4JRXOxA7e/8VFbmMV8Y+hhnQzRipQqUJZgIvmS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 87999}}, "0.0.0-test-20240814184052": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240814184052", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240814184052"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "2efbbdc07fae36533aee6832aad4a819eabe969f", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240814184052.tgz", "fileCount": 32, "integrity": "sha512-3nRENgl8OG3jIV1XsnYW2+LpZ8ieIvhKw1d785Eu3Nz6HBCiZfbnYfYX5qCVtbrE8b1EQiVnbvH4ffGN+ldGxw==", "signatures": [{"sig": "MEUCICx7QCbF2KRlhzt8KmiQwQ7hB1ihBxOgZi6BQnIu1+/iAiEA7hsQfJi5tbE27eg7YcexUDyHUg9pHg3SypstFyfw7UI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88312}}, "0.0.0-test-20240814224750": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240814224750", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240814224750"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "45ed6e5f0216fca1575eb86ae7725c4a194e7a82", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240814224750.tgz", "fileCount": 32, "integrity": "sha512-KtJh3SpQX+cc1L9RC7hDIxygjvkxSyL/Yu5hBetozLLuQJy0jB7qNbnczwEGA6Zp0xuvlJGbfVoe4HQ9ub7RMQ==", "signatures": [{"sig": "MEUCIAOqca4sk7TsllsUDkONEt93CRbYbEHjnh+mZ+p1nYGOAiEAglp7ny9L5xWIO8uMOG+o8jOvoA9qm8kx8Qy+vBTb9KU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88312}}, "0.0.0-test-20240815164159": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240815164159", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240815164159"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "bf21856307c17be390eb166c0fe7a89a5fe9e685", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240815164159.tgz", "fileCount": 32, "integrity": "sha512-E6NFw0rsa2EcRn9JuRIZ9ta8zeapwgDiIuJK7p9iekvHysodWiJA80R2HYimvQdHj3xqWZKStbWGd6UzXNBT3A==", "signatures": [{"sig": "MEQCIHpWGRRgNgZZRuNvXWeWkbnIX51fG06rqUZizi7N+0FnAiBLRfp6/J0gsEocl0OP64sLBk9R2TJr+pRC7vVk89qNTg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89890}}, "0.0.0-test-20240815221003": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240815221003", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240815221003"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "189faaca74bad201eb8abf93cb597b2a696494ff", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240815221003.tgz", "fileCount": 32, "integrity": "sha512-GgcPAlKCdi/4VDKxOvz1KdDVPr/aHqzsrzBp0npPrykkimH/iA1Jb24J3IKES1CsVYgCnJoiJ4HTqwbNpubVUw==", "signatures": [{"sig": "MEUCIHSZYJuCiYrffrVmGWFtfE3RlYb05SxFPXYronV79wA+AiEA+fL/mnQQFRskZ5JF8c1klpL3tQacXgmTod7Ok6W/0Rc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 89890}}, "0.0.0-test-20240816172322": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240816172322", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240816172322"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "ed87ad9140f96a0845d5d65f3701441054d7bde6", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240816172322.tgz", "fileCount": 32, "integrity": "sha512-Fabl7Doir6dNkFDQkSLbdfWXa/0Pn11JBhVt9lnAQflM689vDOmT9m9L3oD6p+r8E5Pffs5+NQEIpAdnh9uymw==", "signatures": [{"sig": "MEQCID5d03r6FFCUBIoX+pHwGIljd/8uVzJjt2epI44qNsPqAiArxSKLRvRXOk2BMqLm4C3q76FQnruwTn/L3OZOeDoVyA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90071}}, "0.0.0-test-20240823000521": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240823000521", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240823000521"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.132.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "a19fbd3ff85f9d3e0a86718ff369758ef0377534", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240823000521.tgz", "fileCount": 32, "integrity": "sha512-EATeGqo7AJlXLB5oX7I08frrSGaa9pyFIzuvhe+nCxSJJRks/BFMayxPdCBR6hSyPDqs5WUdByna5gISowgrVA==", "signatures": [{"sig": "MEQCIEhsOWH8tv2Mai7L996VxurZWegd8bVmZIjqMME3EMlLAiBLf8qEMfPmduHiE8aeKVqNKFjexeRBNjqWt6BcEaE5IQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90221}}, "0.0.0-test-20240828172042": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240828172042", "dependencies": {"@aws-amplify/plugin-types": "0.0.0-test-20240828172042"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "2d0f5223eda9311a08f63f80a3d97104fd9f7ddc", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240828172042.tgz", "fileCount": 32, "integrity": "sha512-52xy5Bxs7Xc3gpnpwsNi6y0l9o5Ap5kwG8grua6ULxzuhCzoi3tpF0gnp6DSg9xorhp0d4+4Nj2lhC230KNCvw==", "signatures": [{"sig": "MEQCIGEb5OqoHyISxb3szrsWrQlCY96EmO5B9K3CAp8ExIvHAiAW7DpmObqnJJAZIpRs0xrZ1ALGRGqmOC5qMHY7D8orpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90221}}, "0.1.0": {"name": "@aws-amplify/ai-constructs", "version": "0.1.0", "dependencies": {"@aws-amplify/plugin-types": "^1.0.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0", "@smithy/types": "^3.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "dist": {"shasum": "e5ea7c252f66a0707f3e7c42a1b5a0dd6435118e", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.1.0.tgz", "fileCount": 32, "integrity": "sha512-tTPZxtQp+w4gzx51QetuBH7RNrVr9v8dGn+w8qELiCo/jwNLTwQ0rRyrqgfT0KoMyYc4+eapR79K6pL8AAwKTg==", "signatures": [{"sig": "MEUCIQC7ZGXNoubeXLRef3nQryMIhWN1UPC5I0YxjpEXi3/1BgIgd20D9NciQvnR14Y2nQ8yQkuuLIlGsusZce7B2gwrEfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 90596}}, "0.0.0-test-20240904221220": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240904221220", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "a4e4e30b6e738bd2717ebd7e267c88ecda262b6a", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240904221220.tgz", "fileCount": 32, "integrity": "sha512-bBKiCo1StJoYVXzC0xKDbFXGPr02KmxWnEbJdYXAZwHF2n0e5TfQXAo7VNAiZp6BlfclqMA7R0VkRXnfBkOvsA==", "signatures": [{"sig": "MEUCIH5FZZOv2by9WcSs9SDugHaoQVUM5lsNeJZ/o2zjTqxnAiEA9pRv0eWi7plUTwfBDDuX6xvGA3W2x0R6jJxCUrEp47E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95256}}, "0.1.1": {"name": "@aws-amplify/ai-constructs", "version": "0.1.1", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "27bc519be9459ed1caf8068348e2b51b534a2bd1", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.1.1.tgz", "fileCount": 32, "integrity": "sha512-PKuaocx8spTylgwpZPTsOnwTmszOgKZP74ZIriy+Ua9bqj5Uv0aetpgnusTk2qqCKV5LkiFnNImiPp4s/KNx3Q==", "signatures": [{"sig": "MEUCID8MwsSRdd8H2LtH8gNilkX9NV35UBu+oD0803xiVmbGAiEA0U32no+3nk/II8Xrj3NK2JdIdpFZIlrAKQud6Y+mkJs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 95236}}, "0.0.0-test-20240905151741": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240905151741", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "e6d9c39bfea864bd3fae645687bf7d0deac865c0", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240905151741.tgz", "fileCount": 34, "integrity": "sha512-Ty0SAhF0Ca0kO2mVY6fEsxweMnNzaS51oJLcV7HHFHftFUC7udlvbUsjtNlKNYN7CamgOekjncqsAJ6I11/aZA==", "signatures": [{"sig": "MEQCIHqJZtUgrksraSVRp5ZD1lt6SP5qVBJ0DgSGEtM5korwAiBOZUIBTEbCcK9BU4XdOtth+0KOd3y+4iF8bqsYbO0NHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97769}}, "0.1.2": {"name": "@aws-amplify/ai-constructs", "version": "0.1.2", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "572f46c308edf5823861116cd9152eb429e9453f", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.1.2.tgz", "fileCount": 34, "integrity": "sha512-fMwCPTBrf80Lk3/fyIUqDH79NVbo+kdroYj3wOOIqFsDHAnrQbnV64UgBNHqv7PUcQD11V+tLbC5qEb30+5Emg==", "signatures": [{"sig": "MEYCIQCkybgGfwIo0RQ9FstHut4iNDwlP4bhPPlaZlnvRPInhwIhALCbrA8KfmBDeEUoJ5AeuqZIf6Oi2CRaFpV3GWIjWpSo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 97403}}, "0.0.0-test-20240905215822": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240905215822", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "bundleDependencies": ["@aws-sdk/client-bedrock-runtime"], "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "3b9ebeada09b17d0c301506f30537c7f1459ee85", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240905215822.tgz", "fileCount": 3460, "integrity": "sha512-LHKuiurwLMNss9Z5gfOO5shih5qYrQAAeypHOyItSdOQpH3TzDAkairw32CXzSf5PliHeb1r9Dm4EEvLvGSg+Q==", "signatures": [{"sig": "MEYCIQCUAPu7WKP9C4C8vwPE8ju678Iw4qGj80ChTkTOUa5QTgIhAKNFpsAF92M3svLNHneOq1u3/Igj+r51+MTmhjjtIbgI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5257394}}, "0.0.0-test-20240906180649": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240906180649", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "35498181f77d54e0f5d4027391c2c0e6bf2bfc08", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240906180649.tgz", "fileCount": 34, "integrity": "sha512-zlR9FME/kleiTXBTTpbzwSDOjJTB64EY445isfqFlYAd3kF6McNZtS4jpWrXxzKP8D9Ko1rIPUb2844PH0GGhw==", "signatures": [{"sig": "MEUCIBhAyyxu4NSA5kWJtsOlLIRhw+5fVaumOqEUoHp+X+MlAiEAtwMKvcytSb3xTQxW65RGuzpp1dnAcfEtR0eXmkaLiMM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98164}}, "0.1.3": {"name": "@aws-amplify/ai-constructs", "version": "0.1.3", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "dc371ff76937635042d50376c233308775495e87", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.1.3.tgz", "fileCount": 34, "integrity": "sha512-6WynWa4R6AuogXqqRncU7PXJ+ZeWEIGmJuYcFot+x+oxp3juj51oFnPwdfj65QSqNcq2TbxSUe7jE/2A0pZ5OQ==", "signatures": [{"sig": "MEUCIFG2LjAHKtrqXcoozM5vpDyauB0ZteWjkKOxu0L8v6bgAiEA+MRHA6brydWD+G8vE2aGXYjydxPoSRCwvNqjbe2YfRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 98144}}, "0.1.4": {"name": "@aws-amplify/ai-constructs", "version": "0.1.4", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "043ca7793cb4a97ad7864797bd70dbfa323329f4", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.1.4.tgz", "fileCount": 34, "integrity": "sha512-BGLBFs/pt6JrNgUo+QD0Szt/ssHMa6EyEE45yLoHemwPHRuJPpnFmxIbbxgxaqJP0mWK6QMs9Wh3IsdJ/6XhDA==", "signatures": [{"sig": "MEUCIQDWixDGzO02X6XlBNaGmNipMJGRKnAk8WsqH7az6ezYKAIgPCRiiU0gIE/wFsQPk2sofbdT3jDpGQRpAwJqlw0fzrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 99773}}, "0.0.0-test-20240924234423": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240924234423", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "b072efea6e004984391dfdd50988f2764f5c9d64", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240924234423.tgz", "fileCount": 36, "integrity": "sha512-JEc9OF5Yolyw1fz2C/DwisxfnmAlInr1wkA1MXCSss6Ev4xEeB6K4/l/ykJAW0xru60QKrwwe9DAsJ34ISwDbg==", "signatures": [{"sig": "MEUCIFXTJn7TVU2Pf9y3GR5KYR5ODEjfqi2soZ4i1NLodTMqAiEA0Ojm8MLsTGWJ7LxaiBtlRmNQhBRb9PkzNGy/mpJ1JK8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 114407}}, "0.0.0-test-20240925144932": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240925144932", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "4b7fd94e08f18f0ccc96f44b02d30bd87b470647", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240925144932.tgz", "fileCount": 38, "integrity": "sha512-F63OomSxZVtY3WY4akD2XYXKMSda+jBlC6/B3IAf9F9Vi1+0RSn/iwQnRwlIMRby3X7r9z0bVk7h0ey1AkH42g==", "signatures": [{"sig": "MEUCIQCH8i+K9KXtPFF1sMscdQJ22Uy5gZ3oaEh0E6fuSpY5GgIgdkPnem2+Qndci1xA28KHkvuUHWKtXR/WnK0qjseT9vg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118325}}, "0.0.0-test-20240926024033": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20240926024033", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "d4ca96ffe557768bf0f5edc464485bff3b65b18a", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20240926024033.tgz", "fileCount": 38, "integrity": "sha512-DSbD+UQI/F1QjbxeI8WYOlfAHH17asQiMDM/WVmAScOUlCzoWNz9M7BJR4LgfVF5lkjmS4u41YSDYra+/1Bqhg==", "signatures": [{"sig": "MEUCIQCs5uY8A+FXcykTkoW79bhxVIjTwxA3N95bOZTcqi/EZgIgSRLHMTqvyl0SOde2HH0g4k1pR4h5iHPSKLyubfkLqx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127180}}, "0.2.0": {"name": "@aws-amplify/ai-constructs", "version": "0.2.0", "dependencies": {"@smithy/types": "^3.3.0", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "91db9586d8e656a4ad7f2b0b539a2221a38124b2", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.2.0.tgz", "fileCount": 38, "integrity": "sha512-aqmUrUvbWpebJcNCvoFywHLTQXNIlli8VE2i9+sSMlQXAG2zRiqcpDdRha+0NQnPNj09K2/DMLTe79ldwDaGkQ==", "signatures": [{"sig": "MEUCIQCU4x4OXL74RgEvYwkfdsWFNECVLWYmPfVmk7MqgACiaAIgSlipLljFmuTbebzbralejSH0MoNWdxRM4IJpZEdsOb0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127160}}, "0.0.0-test-20241001205419": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241001205419", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "devDependencies": {"typescript": "^5.0.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "f95eed44d15fd3c5ea772e9f1f241d7af2718e8f", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241001205419.tgz", "fileCount": 40, "integrity": "sha512-BXRv4zxE7JhVSbnOnYUZWhWNRlL2z+gr0rm1aEoAuCmf2x1c408sK68HFhVbk41WTzp1KVEZpOMjKDrETexRJw==", "signatures": [{"sig": "MEUCIHZG5cbKECbWthpThqNV12dakm0x0e19nhfy3kOufjlNAiEA5sh9z0URdGdH6dF9RZz1htVrryI8W7wgCRqk87AnyuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132267}}, "0.0.0-test-20241001225724": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241001225724", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0"}, "devDependencies": {"typescript": "^5.0.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "da92432d9afe975b525067c726941ea87d943f91", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241001225724.tgz", "fileCount": 40, "integrity": "sha512-R8nW1HSg3AZvhU7dkOYb19vOGRXDPQd6RmtC0mJADGI4+p3a7uWS5tsrjaJ08QXg2lJaLFvS2KWVyiq722oIqg==", "signatures": [{"sig": "MEYCIQD1ekkRXV17aullz3Fhtv5LqfmYEzGxkDTzoa6ARH0IagIhAMa+di2ANRZl0XjrzlNkqrSOm8nA0n5awPPx/EtkcTlV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 132199}}, "0.3.0": {"name": "@aws-amplify/ai-constructs", "version": "0.3.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "60a5078c9d7da337b5c2256ac0c0f51ebfa611cc", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.3.0.tgz", "fileCount": 40, "integrity": "sha512-+J/61+cmDgZT6kaGUQddxFhesJBRLxjRsb2LQ6B6xlKHFnBrwQEdVQp6sMgW8VVUTFrwhn4K3jJe0Om51R0bDA==", "signatures": [{"sig": "MEQCIBVDf/iJHn4RzlbkCTz/d/C2TIUw7p1bRjr3nac/kob4AiBh6NdrpNcb54THJYuMFuv0i+mHYYgpi6iWrWgJb1tM/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138494}}, "0.4.0": {"name": "@aws-amplify/ai-constructs", "version": "0.4.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "985ed7be1a2aaf552d85c9dd85c7077ea835dc01", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.4.0.tgz", "fileCount": 40, "integrity": "sha512-00dKuhhzEcU27BrcPS3hYH2B3gg847yrC3t1E5Cxj3Wz7VRGw/ivsEsjX9GeJFcrjWp+opYqXxNl56vam67JMQ==", "signatures": [{"sig": "MEUCIG7OvtG+h8kncVgo0EHqvTi8GTUr1UerHvNJLtgpC0MpAiEA2Hd2v6x12I44hlg0Q3g15pUGD2NlI+ch/kvyCjhyytE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 141893}}, "0.0.0-test-20241010141034": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241010141034", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "5df452ec6fd78567a9b3e6a24a8a474394c5f785", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241010141034.tgz", "fileCount": 42, "integrity": "sha512-5iBW2elF46n2nrfblVmOkvD8J3Cx6Vqh4MgZuhOBDyd4VC+UhQwyaLA1AQdamr3hk2wqW63mxnzNlqnpc41G4A==", "signatures": [{"sig": "MEQCIAePsDogpodUYfpbkpeq4xOYUZ5i8AOWrWBJSJ6LygIXAiB/6DSvPB/UwTGOBpLbv3cVMComTqG+fSJVG7eflm2Udw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 167282}}, "0.0.0-test-20241011172758": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241011172758", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "ba0ff5daf104f33e7432e0d1856e4f08b82aee63", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241011172758.tgz", "fileCount": 42, "integrity": "sha512-+kUUSawUW+L6An+XWVLPOoxZ5kyWqK86eIUFyg0vC+Ikn2XQ3cEJ2j2hzIcih+9WWuTZmkzXg5K9F4LE38bW8g==", "signatures": [{"sig": "MEUCID7eGIdeavIhDublqo/on8le6Y4hzLlLTPStPp2SQ+MyAiEA5LALVEFKpeiDO4Avz7IR+mx2iJ/KYar8ksKwws82bVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176748}}, "0.5.0": {"name": "@aws-amplify/ai-constructs", "version": "0.5.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "d98f7f7f337be17232b59870f1bac68ee51dcfcd", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.5.0.tgz", "fileCount": 40, "integrity": "sha512-voGoAK2P5IELYb/gosnnMXx+/sVzMLvIDNg3b2ZZP7hcP5+pY7RYTET5nDCwao8voMMovDUm7d9Iuu3n04zPng==", "signatures": [{"sig": "MEQCIHaBBv4kiN9Oq4U2lSVPTEAUWaBoeKsLlrNYKq4D5K9FAiBBJcUnvW8ITBb/Si53eu0B/MjKEdcO82q0NsBLbCc5Pg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143586}}, "0.6.0": {"name": "@aws-amplify/ai-constructs", "version": "0.6.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "4ca943f24a22fb79976d46ff9f5e66e255f9bad3", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.6.0.tgz", "fileCount": 42, "integrity": "sha512-A3hfwYIVRSXn25IN7+bPs0M2pCb09k0YbdplXHlcwj9h2tR4YCDWE0ZadlVdaHDG2iwywBDoTzTc0YBbdrB5bg==", "signatures": [{"sig": "MEQCIHtCjtOboiTV1ImCmHx8RoMd5XtaNwxlNd7M+vKC0Ob5AiBcpHqypEyuQKP5aHphiqkweL78MZnl9VaKszBBBjDp6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176858}}, "0.6.1": {"name": "@aws-amplify/ai-constructs", "version": "0.6.1", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.3.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "af03f0ec18d124e2a5ab2f4c895e3048d4f5216b", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.6.1.tgz", "fileCount": 42, "integrity": "sha512-LIMC2npEUunpqsOK34IsFN+q9IpEmwhIWhPnnj1mKW0148rqVXCMU83/oYOBfY4Hq0EOFu6RRKz2fcpOZqjg5Q==", "signatures": [{"sig": "MEUCIEiqrkq9apzHRlEYya1GJIePmzMlBGyhJoiT6tuUMh/bAiEArEZuCfG1KMO2NYHYJASz5DhZkZigLMu1jPSttmdaqQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179576}}, "0.0.0-test-20241023205154": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241023205154", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241023205154", "@aws-amplify/platform-core": "0.0.0-test-20241023205154", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20241023205154"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241023205154"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "f7e8a5b98697930cb46e5199c3ee229407b2147a", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241023205154.tgz", "fileCount": 42, "integrity": "sha512-zmJsNPk7FNER5wKjZdmWWSIs7mJld8SfqXHvVRKDtHEgwhhtD7ajE6FhlLS9hHN7QJzBcVMuphuZg8rxqjue8g==", "signatures": [{"sig": "MEUCIH8ir91YcMZClhRJLKbZ1McQlbctp5nGNhK7vNv2i41YAiEAp3mZMIdZjLxg7HeSraRMckPaYVcNzBowbXOyaj7fOLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 179672}}, "0.6.2": {"name": "@aws-amplify/ai-constructs", "version": "0.6.2", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "e89a69339505067a46e623a6f885a5d87612b4bf", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.6.2.tgz", "fileCount": 42, "integrity": "sha512-e<PERSON>yYawJpf3BTxRGCCaOIPfCV1ocGk+PDjM4gAjwfQ237z7rjAzkGr4QASf0ZJ/Im9nhjwFIlWc6tZwp7Cp1XjA==", "signatures": [{"sig": "MEUCIFW59g1PGUXAYNps+gofVWneU3UxLQStx+mwUmqnHDBTAiEAoV0piH5ssu5jsMItLhpfInInnBHCzoNyvCpVToFxOKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 182358}}, "0.7.0": {"name": "@aws-amplify/ai-constructs", "version": "0.7.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "477750d62e2c564b0d156ab53d8845cbb38290ab", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.7.0.tgz", "fileCount": 40, "integrity": "sha512-HfKvoafva68HulV223wBC6V6bA/ibM1UEAEjy+m//l+zhdTlfYauA4EUpHfyXg83eWxBhmT5rtAheqNmIlzNxQ==", "signatures": [{"sig": "MEUCIQDRpL6dcQxS+QSTmNx3q+SleotmS6q8AH0Q2enFE87tRwIgXO2jDhfLi2V+KwWa1WC/gAUqdDxrE+Go138wXBNv/xc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181501}}, "0.0.0-test-20241029211113": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241029211113", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.0.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.2"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.152.0"}, "dist": {"shasum": "4fde9e50bc4e5786570b14551c0190e257796af3", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241029211113.tgz", "fileCount": 40, "integrity": "sha512-oWstcuZM1e9jHGYOf0eRN6Qwf/O/FZOvk65oQ8dzJo4ajwBRZ9Ym4rKn/DliiUNaeYZNDiIkrRMbNNN/RvJU+w==", "signatures": [{"sig": "MEYCIQCD8EfCMQvgKx3nokIPrINWdOg5vLF020oaQnWw+08ztwIhAKoYEYonIZXHArT3iJn5ExzzLvRZqNmqdplO/ErqgjC8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191207}}, "0.0.0-test-20241030224754": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241030224754", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241030224754", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241030224754"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "c167f2283dd0576ecc50996a89dfdb6a17dc8592", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241030224754.tgz", "fileCount": 44, "integrity": "sha512-3dgy8P2fuULUqUBNQtvThosH7OqANwvp5y3aspA9wdBrs7aBjmSb6G3Q6BGCckRfHBproAPL1n9oLPP/1Vx0fw==", "signatures": [{"sig": "MEQCIEjMv/54tnbuA58XcJ2SXp+IvVUMoaisGQcXso0cm8zrAiBsX8tElrxrhensk3ashpW14xlm5ZVzv3u/D+wlBEFQAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 193207}}, "0.8.0": {"name": "@aws-amplify/ai-constructs", "version": "0.8.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.3.1", "@aws-amplify/platform-core": "^1.1.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.3"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "c84e6c608684649ec9fb670af4c65921385b2f25", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.8.0.tgz", "fileCount": 44, "integrity": "sha512-DP+M73Iy8Dbm/Rp8QosQlCDLuR9U2fOWg+q6uPAksuaVuCdnqNvTHsQ0XEQMmslEx32vEdvn0ttiMbzD++ukSw==", "signatures": [{"sig": "MEQCIA0RrCrMwxNhj0hX+QqD2bT4lG73l0pDAdyImrNYdySeAiAqUiHYw/1Fu0/TSRWt1qnRPUdIxeteDVsrggsI9UqCrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 203335}}, "0.8.1": {"name": "@aws-amplify/ai-constructs", "version": "0.8.1", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.3.1", "@aws-amplify/platform-core": "^1.2.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.3"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "97250e681d70eae42736c71a5877cbd1c425ab11", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.8.1.tgz", "fileCount": 46, "integrity": "sha512-JdgVJqPr7YGlP7MHmExKGv+82lJB53kPb2l6UR+ji/mozHImnLbcGPP0BW7rl13wOwA7ZqIcQWAqQsxgWW37Fg==", "signatures": [{"sig": "MEUCIQCiP8KH97B85cU6ndQ+lUFI81JqulXeAuqblIWQMOFWxwIgQ2hLr1ESmOZAEpVecYEmUyj7tz7QPm32T3zjgReH5is=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213776}}, "0.8.2": {"name": "@aws-amplify/ai-constructs", "version": "0.8.2", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.3.1", "@aws-amplify/platform-core": "^1.2.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.3"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "57545eb1ffe8468ef6179755a895fcb11862d7db", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.8.2.tgz", "fileCount": 46, "integrity": "sha512-TAdpmV0+Q1FNt8eIzxPTPUIj61fFackz6OjtzHfpfTbMhTd3d0dPylmJBCw5Q6tA8gFKxs/TCdkDOlzXdKAH2A==", "signatures": [{"sig": "MEYCIQD+DTOnkqmpZPvaZAM9uO2Y65C7utMulWXxuwevd4VfWwIhAJ015LjiE8QL5SfSfx6Bv72qd54RDvFi5cTv3Emup4mY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214063}}, "1.0.0": {"name": "@aws-amplify/ai-constructs", "version": "1.0.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.3.1", "@aws-amplify/platform-core": "^1.2.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.3"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "3cf60199bf4550e5e4ab7a43e2590d6e0179cbd7", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.0.0.tgz", "fileCount": 46, "integrity": "sha512-gLOKpt3/Qq89/7wMPVu1anpmptFNxgV2K6DJe+6uYfuL+GW8T3XYgMNq5Wyty07MGel0F95zaHrfysuwx43Q6w==", "signatures": [{"sig": "MEUCIGx73yXs0WUYajk9IrvtyCHcsN/3n+4Q19MpZ8kSZcOJAiEAtMmYiQMP3OmqFKeMNDNnBz819mxNcNZB7Az8O6V9pEM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214755}}, "0.0.0-test-20241115171053": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241115171053", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241115171053", "@aws-amplify/platform-core": "0.0.0-test-20241115171053", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20241115171053"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241115171053"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "03b0326cd3ea81d680bdcc9d87cee971ef373f6c", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241115171053.tgz", "fileCount": 46, "integrity": "sha512-c15mEnUmgIa9TEzmgKHloOKshD2qxqRzu+mwGYTn9aI/A58fE6jvyk6RYTP/M5/dFXP9hHyRQ7xPNK2DSp8xOg==", "signatures": [{"sig": "MEUCIQDMHnttziImkc9K6tThqDkTNVLYzJaZw+NnOqfEaYKJjAIgKxSA7JtregONPS4dBbiAJAMiFpHUvN+R4cz6jZuBhz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 214851}}, "0.0.0-test-20241119003939": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241119003939", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241119003939", "@aws-amplify/platform-core": "0.0.0-test-20241119003939", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20241119003939"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241119003939"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.158.0"}, "dist": {"shasum": "a5dddae0b7dab96cbc4162e984153e7a7d3f4aa2", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241119003939.tgz", "fileCount": 40, "integrity": "sha512-LjBm7vCe+MGQLmW9F2jqV8YqF+8lCqQGTcRBBtMtP9Dqgrt7BoiRMUrzvvLqaszQ943CQ34sIJ/ePeOHhvyC+Q==", "signatures": [{"sig": "MEYCIQCB0QpqYSgezgN4euo0OUTtyerbDQ1Y8DfbSj3/9hFu2QIhAJCD2uREdRKH2V0jsRJFgDmoHnd1wDCLOSzpxDocZWfL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 181597}}, "0.0.0-test-20241204204357": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241204204357", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241204204357", "@aws-amplify/platform-core": "0.0.0-test-20241204204357", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241204204357"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "b2b4917bbb10c2d13ebcd45459e9f77b656a8c83", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241204204357.tgz", "fileCount": 46, "integrity": "sha512-1Sod4DeHJVHEit1ODcpzU2f9ylarcP5EeqyZNPa+pqNJJlv/Qme/lpEi7JQMxMCQjoEkKDF0DzKPoVgPkpdQKA==", "signatures": [{"sig": "MEUCIHVhnQXyg2fTeFSZBEhiCQGMMQ58I4NnxP2Ag2oPNJxaAiEAo5hILqrWUC2qVnMvjQsqXFBDCwj9p3D8MKo0KCC3zLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215703}}, "0.0.0-test-20241206232144": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20241206232144", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20241206232144", "@aws-amplify/platform-core": "0.0.0-test-20241206232144", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20241206232144"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "9ee7afa18d5580d6219bfbaf51f70477452e40b3", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20241206232144.tgz", "fileCount": 46, "integrity": "sha512-VUMVxnZOD9H4GtqMl5BiqZjRvpJ+OaFWB/oceM1t5fZ4HTRbJ18h25g3kTvWGXRnrxZJFd4so/ObS4Vr7JhQMQ==", "signatures": [{"sig": "MEUCIBWWOCm7O+CM62KBrx6YQcfpNrffoVL7btocpnwGjb+GAiEAwljEopLAgc4oGNC9RzfOKJAmbFec0nKUL8vpboWU/VM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215703}}, "1.1.0": {"name": "@aws-amplify/ai-constructs", "version": "1.1.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "3bebf114c4601882dae5e7fa7f64ca237614cbc9", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.1.0.tgz", "fileCount": 46, "integrity": "sha512-mOBPyg65MJPh/hO1Ngk+zx+CTOhHKEvMH3vvcyOwR/AkEE3z+uws7LPmfcNU2Q7fOoasQWhs7JAWCo0lrTqkRA==", "signatures": [{"sig": "MEUCIE6F5J4eEoxgGJ+nAHjKmETSjzkpKhZi0BBtGBHXicVZAiEA6nE3kwL+PRoXa+cJbfIcBkERapSVNkgg51QIAnldDzA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 215626}}, "1.2.0": {"name": "@aws-amplify/ai-constructs", "version": "1.2.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.3.0", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "673c21138916853b84a361644c14e1530e2b0e7a", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.2.0.tgz", "fileCount": 46, "integrity": "sha512-n600Qy+vwDZHA+7cFBRBS/deIu+X1xnY5hJ1uUDIzJ97yOJjGiMMU50TNfESjvlQvaCxinBfu95eBIGg9kVhGA==", "signatures": [{"sig": "MEQCIEGM57YTKk5eWLkkEi1eZ6rRgFp4vCneaeMcCvY26PkYAiBYhHHaDwuj4xo0U09DtuPKokOkAcfmK0+pSm5sObXZgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218044}}, "1.2.1": {"name": "@aws-amplify/ai-constructs", "version": "1.2.1", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.5.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "73dd0218f3826d38a6f2f58202a8f195db1aad29", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.2.1.tgz", "fileCount": 46, "integrity": "sha512-c7ttgMmvVSuMB0WdaiB45L69dMSWQijHfbDdkCMr5eHSVRVQA199OBoVqttDA5ZZYo11lcWiWlamUuc2peW3lQ==", "signatures": [{"sig": "MEQCIF4xJVKXCbXbjDEYQWg8RaKErOv4NVwNKi7WRwpA3bWHAiAZXTr/gc/SbIt81ecsduRmDHvp7G/qhXqYWKWAQHSsCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220422}}, "1.2.2": {"name": "@aws-amplify/ai-constructs", "version": "1.2.2", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.5.1", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "86533bca037d58805e1576a0d068c4481bffc91f", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.2.2.tgz", "fileCount": 46, "integrity": "sha512-tv5/HbLfnYRKMD5TvbvFHzoWQVBbonOliwl+RBzn6Zeqiyj0cyQMFpTn7KhA+grS8/eg0TXbQi7gE8QgtiCSWw==", "signatures": [{"sig": "MEYCIQCxhKJVLRErwz0zDZj3f+K88N3M67Ae+y2C+nOYdZPg4gIhAMQt/qcbfXXZdyY3EL8Jd4C+oSzEOUlc9ple9K+hTAAo", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222033}}, "1.2.3": {"name": "@aws-amplify/ai-constructs", "version": "1.2.3", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.6.2", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "3cfa2fb0d0bd8033e7746d377d8a22f9572538a4", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.2.3.tgz", "fileCount": 46, "integrity": "sha512-JIaZgFLpJ7jSIxmWGg5CXApbvw/Sbe4tw4prAGmYruSILUnDPUVosji7A/0yhdk7BPavW+rBqPwnZ2rvqPo+zg==", "signatures": [{"sig": "MEYCIQCxHgMOwOKUZGRj6YkAE9a+dmhzQVSOVRp+AqyspfUe0AIhANW2SKO4vGyAw6R8nmGp0HuNOC+odpk7YNqN+KPAiWuu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222040}}, "0.0.0-test-20250214001309": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250214001309", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250214001309", "@aws-amplify/platform-core": "0.0.0-test-20250214001309", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250214001309"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20250214001309"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "00737f0ea6be606331ac74a45985b1bee9067e7b", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250214001309.tgz", "fileCount": 46, "integrity": "sha512-bT9OYesOu6cL5XgEDHbLOvsA2uYKAVjPGLu8ti9bhmE4gJZ7quGdpStVY5KxCpgOPNmKIhBAt0+FHN4vgfoadA==", "signatures": [{"sig": "MEQCIB9bpirzGvg2msvZEzJvpAmW9zbN+S3YfPgiJBp2MHd9AiAJkmcflS7O+xFZnv8euuL0E1EkDq0D8VgKnq0ljx6Z5Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222129}}, "0.0.0-test-20250218000414": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250218000414", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250218000414", "@aws-amplify/platform-core": "0.0.0-test-20250218000414", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250218000414"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20250218000414"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "52e2733cc43f91bcb6daf6f1774179f0e3b4dcb3", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250218000414.tgz", "fileCount": 46, "integrity": "sha512-ny+yf4/bTq90W/hymXTOfyLaKZVcL+6FhNnl6PrJ2y3IXoKIPmel3kWUT+SxSitHBgtP4vdhtjMeIRXbazPDVw==", "signatures": [{"sig": "MEUCIHxwBH7TKnW4hBa2HExO/19Z+r5RTMoLM/L0UsXVBgLtAiEA5UWtbDTjG+GL4HWCB8g17ts6jEwIeOmkLw1OKR3Ip48=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222129}}, "0.0.0-test-20250218005144": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250218005144", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250218005144", "@aws-amplify/platform-core": "0.0.0-test-20250218005144", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250218005144"}, "devDependencies": {"typescript": "^5.0.0", "@aws-amplify/backend-output-storage": "0.0.0-test-20250218005144"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "4b5005ef374f7d5e0a1fe964df388b81446ccbb6", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250218005144.tgz", "fileCount": 46, "integrity": "sha512-jkE3UeVKHtoDFzO3JpX4FeB6i9PJa2Tk8Wdu17fqXukx7TtQzdmTMwftPxutHXE1FiWV1E0o6GY4EThwddKihA==", "signatures": [{"sig": "MEUCIAHqa3WyXCJk6tW5yFNldb1MqseOewYy+hYl3F5AZrfdAiEAxH69Hi3XZOVJZz1gm2KtRFyyiuY+mGsxfby2QKqiM6c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222129}}, "1.2.4": {"name": "@aws-amplify/ai-constructs", "version": "1.2.4", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.6.0", "@aws-amplify/platform-core": "^1.6.2", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.1.4"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.168.0"}, "dist": {"shasum": "65981d76ec9b3b2d15b1879998257b78d51c9cb8", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.2.4.tgz", "fileCount": 46, "integrity": "sha512-DLjJXhvNdQrzihppXthysKc2xcGT7f6K8enG0rPG1fAGiGo+EE8TedIwv8z8h+FGhVLpOhlmhGp2uY13WeB50Q==", "signatures": [{"sig": "MEYCIQDDV+7AQbHrXFjv8IVCw8D7soz6koD0hpEte8u3uPCrjQIhAORlVIJCY0u5g7KcLBF7suSpOeofBfu/eJAu1Cotg9ED", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222115}}, "0.0.0-test-20250307211308": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250307211308", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250307211308", "@aws-amplify/platform-core": "0.0.0-test-20250307211308", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250307211308"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "f978e54a08af72fcf15f0bd28bc5b80f27ed5e3a", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250307211308.tgz", "fileCount": 46, "integrity": "sha512-ZvJ1qDuipOKdmLCE6/jRP+iZsmEPrtpW1YjYvCr/Yqx+UN9J54FCno0tPL1Wdfz7yiUnIlm5EgLu/37D/6LmxA==", "signatures": [{"sig": "MEQCIFg8mXyH6NlGOHqyKBSJG8vm8rL2oUgBV3sFhtDSm9tRAiBi1JHv8C7NmsmHn5e2TBpdTKmSYrRh9Nbcd1Ts+O3FwA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 222192}}, "1.3.0": {"name": "@aws-amplify/ai-constructs", "version": "1.3.0", "dependencies": {"@smithy/types": "^3.3.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.8.1", "@aws-amplify/platform-core": "^1.6.5", "@aws-sdk/client-bedrock-runtime": "^3.622.0", "@aws-amplify/backend-output-schemas": "^1.4.1"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.1.5"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "c434746f80d36608c9b10bb01947903114c95bcb", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.3.0.tgz", "fileCount": 46, "integrity": "sha512-iek3TWI3iy8vlRLG9nHsultdeLZaEnElKnSW2cBwBdJo9jIcJQUs2iJx3bBGedsk2bbigVE2l8o2M50Ef2vm7Q==", "signatures": [{"sig": "MEQCIDH3bPFrXoSRQ9c2pje5vnLq755qTkxinfCJSbot3TjlAiBQhSomjN1NTXVGJPCzRmPq/p+XSWp3yoTRn/4/fd+eAg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224739}}, "0.0.0-test-20250319201152": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250319201152", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250319201152", "@aws-amplify/platform-core": "0.0.0-test-20250319201152", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250319201152"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250319201152"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "343829678c0d07f0968d2bff20b4243da844d05e", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250319201152.tgz", "fileCount": 46, "integrity": "sha512-HMBY/y38oRiUqcfe+Y7VYCD3R8FonoDMnc8fTBpVEoOIySfTYyOjDkRa6bdfXCGffsMmNTs/uDxhFlhOfcCcBQ==", "signatures": [{"sig": "MEQCICW2y+W4SBj6lUdiWggwInCcGgYQ5zzwUYwYaTyZHdvRAiA7/90o5xsR/lQyiUSlRqfMw3zDnfpXUzHbchQ1oKuTeg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224834}}, "0.0.0-test-20250320182439": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250320182439", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250320182439", "@aws-amplify/platform-core": "0.0.0-test-20250320182439", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250320182439"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250320182439"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "cc864b5c8d66398c470dd5f910627866804739b6", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250320182439.tgz", "fileCount": 46, "integrity": "sha512-hpbGbS3vJsYZF/AXCXDBeio0wrVaBo0Exhz1EJJ8BoOQmYj/vexRfx5Hjqwh+JhPbT1dtnVtSeAGPTnHgA8LvA==", "signatures": [{"sig": "MEQCIEVSwn03R5XXnOESY3PP/7gWT68yBw425DnP21QPR2YGAiBrfNlUrhxUh94g3zyNp1kgrqu+YmEW9Yt5e9vwLhrDxw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224834}}, "1.4.0": {"name": "@aws-amplify/ai-constructs", "version": "1.4.0", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.9.0", "@aws-amplify/platform-core": "^1.7.0", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "^1.5.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.2.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "8a33689bc1c587ede59b170c79d432a2aa8ac4fa", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.4.0.tgz", "fileCount": 46, "integrity": "sha512-7dmRXfuWTvlB+9Pyp1kLOAaKFXZBQVbCzNMde72EUTwhWydJyA1jLuHjSqqtIO7tbdF5SogRN9pL4tCMWW0Jzw==", "signatures": [{"sig": "MEUCIFatV3MQjcn/xsG+KX31x5iMJ70FlXoMiXye44DoFmhmAiEA3Nr8yREAN9ZVVNBdV8MoIPvjiX5Gj0sgIlC3gUbmd2c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224738}, "deprecated": "backend-cli 1.6.0 does not work with Amplify Hosting service"}, "0.0.0-test-20250327200620": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250327200620", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250327200620", "@aws-amplify/platform-core": "0.0.0-test-20250327200620", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250327200620"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250327200620"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "6109a16a9ba3d21f01565b7b0bcb6310fabb0ead", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250327200620.tgz", "fileCount": 46, "integrity": "sha512-g2BvDvlNga7WOp5Fqvj6fEHAsHV6TGLVAL4yiwbbZ2BewLo4TFcV4VrESuelgCwhcllS/hPEfVNeraeUc0m1oQ==", "signatures": [{"sig": "MEYCIQC3eEwKxtsYEobIPCWPn7HwYyd5qV1yTsk/+D+Cqob8ygIhALWuh7zChZPNDecK+b9GFpSqNuc2S9cO/33rcL2x8SKn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 224834}}, "0.0.0-test-20250404000352": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250404000352", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250404000352", "@aws-amplify/platform-core": "0.0.0-test-20250404000352", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250404000352"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250404000352"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "1de7649a6c11a18487c1ff6d34b9f057931c1a86", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250404000352.tgz", "fileCount": 46, "integrity": "sha512-DVf8ygVrZaaMSMZzSgOWU3toS2YSRIXb9dkiWqY+/Ug9CMnYGL0ddSnn2L9f3JvWwbUninWACWWe23NYnjPWdQ==", "signatures": [{"sig": "MEUCIQDN4IxqOZXtll7siBnUqrxHLdLUFcxWK1jOAir8akOAowIgeIbXr+m08mJpaPvxtvn/toxubRfbURAy8dYZg6uskVY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226544}}, "0.0.0-test-20250404003334": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250404003334", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250404003334", "@aws-amplify/platform-core": "0.0.0-test-20250404003334", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250404003334"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250404003334"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "c3807c5da27289d166d463d89a16b0cb23cb6abe", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250404003334.tgz", "fileCount": 46, "integrity": "sha512-K3Y<PERSON>brtyu5k1qN68QauL4IElt1N45gpsl2x7ya/A3diCMKmzGXim4fbHx+x3QBTQDciO7N5/rYDA2bFS4H1xXg==", "signatures": [{"sig": "MEQCIFNhGtXEg0kT5QHxj6f/kw+ARWEN15EFy88CobBbm4N+AiAPADXaStZmpp/D/YgOniVWGC9fjr8eTwu045/7M8Apqw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226544}}, "0.0.0-test-20250407234958": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250407234958", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250407234958", "@aws-amplify/platform-core": "0.0.0-test-20250407234958", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250407234958"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250407234958"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "4b74d4436c9e6663b2def1b8f509ec599d69cea2", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250407234958.tgz", "fileCount": 46, "integrity": "sha512-f8HhhwcS3WtPU0Oo1jgvUYb4rIkYMJyL8nZfrY+usAGTHG61KpyI1QJNp7YHhKs8ra8fDgKE505v7M8GUlaJYA==", "signatures": [{"sig": "MEUCIQCExQ3/k3NqAQaHr60K7wxNNrAlJxxkuDIbJngnH95lsAIgJg600ptvW7oymjb2qfTh7us1+NyevpVm27zkSJ68+Lk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226544}}, "0.0.0-test-20250416182614": {"name": "@aws-amplify/ai-constructs", "version": "0.0.0-test-20250416182614", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "0.0.0-test-20250416182614", "@aws-amplify/platform-core": "0.0.0-test-20250416182614", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "0.0.0-test-20250416182614"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "0.0.0-test-20250416182614"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "9d8e764fee9a431738930826d3d87cec483f8943", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-0.0.0-test-20250416182614.tgz", "fileCount": 46, "integrity": "sha512-r4v8IQu/zbslhyN015f4vuNTCRdqfx+3CWzqSKI+g4QIJS9q6o/GHbqVswXLQO6tteY++0bZiIY8Lc79nlmc4g==", "signatures": [{"sig": "MEYCIQD3ik24Wm/nj9QdW/L4/eJdUEVg29Gv7S5EUZ5dYNR2DQIhAKgtcQ1AFctnbMrDjyHEqY4vW7RYKe6zuFM02MII3KJI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226544}}, "1.5.0": {"name": "@aws-amplify/ai-constructs", "version": "1.5.0", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.10.0", "@aws-amplify/platform-core": "^1.8.0", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "^1.6.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.3.0"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.180.0"}, "dist": {"shasum": "777b4a523f3fe09606cac007a69d949565060290", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.5.0.tgz", "fileCount": 46, "integrity": "sha512-4cIvNpuoRDKzs58uLNuWeeKhWuumIuxrYKcxQKODnrG4F9PBDrfcV+AeTd2eZQa2kkfXite+AWB/p50qiiYm5w==", "signatures": [{"sig": "MEQCIEcYHm66UhEyxCnsZGy8Xr9sEIDpSKR8zfEyjc4OzrzoAiAaxM1Igsh06rKrBOj2FrURjZlxjK5MvSPysH0Ec2CJNg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226449}}, "1.5.1": {"name": "@aws-amplify/ai-constructs", "version": "1.5.1", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.10.1", "@aws-amplify/platform-core": "^1.9.0", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "^1.6.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.3.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.189.1"}, "dist": {"shasum": "9d398ed7be98a44441fdfac05195ec10fee1ef5e", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.5.1.tgz", "fileCount": 46, "integrity": "sha512-NHc5c5PvMshJfDaVACznKbzTAxBjPtzJZrEXuX10sMeMeiZXP/UWFj4PLBxsL9oWN5ZvialLhbL5fSXkV+N5/w==", "signatures": [{"sig": "MEYCIQD+hUQ2N/7yba6G4AFZb5p09bVOKcUW5ANcMJCYIgZmNAIhAJyQPr6nTAeBjteWmN+AexggAe8q0N745oByNvOdY0xf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 226449}}, "1.5.2": {"name": "@aws-amplify/ai-constructs", "version": "1.5.2", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.10.1", "@aws-amplify/platform-core": "^1.9.0", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "^1.6.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.3.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.189.1"}, "dist": {"shasum": "96f58d97cafb50051d1ee137613f361f99290360", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.5.2.tgz", "fileCount": 47, "integrity": "sha512-/1ipbpzS/+7IWZ00pceDsIrtqPfcuhEv1yedtfvuyIxtPNDXNOqdTO1SsZvnCO3Hwec5J+c1AfmOgyTChlOSnA==", "signatures": [{"sig": "MEQCIAwK8HDMWYkx+nkiCwdUF2a6mJfwmUr9D3oR/FEqC7N6AiBcw83199X9nXt6fcCdOg2MedZIDqVQdFRoaeBu7pP1XQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1185893}}, "1.5.3": {"name": "@aws-amplify/ai-constructs", "version": "1.5.3", "dependencies": {"@smithy/types": "^4.1.0", "json-schema-to-ts": "^3.1.1", "@aws-amplify/plugin-types": "^1.10.1", "@aws-sdk/client-bedrock-runtime": "3.622.0", "@aws-amplify/backend-output-schemas": "^1.6.0"}, "devDependencies": {"typescript": "^5.0.0", "lodash.transform": "^4.6.0", "@types/lodash.transform": "^4.6.9", "@aws-amplify/backend-output-storage": "^1.3.1"}, "peerDependencies": {"constructs": "^10.0.0", "aws-cdk-lib": "^2.189.1"}, "dist": {"shasum": "24f532f32028f45ba097fb01f43a0a83e243ded6", "tarball": "https://registry.npmjs.org/@aws-amplify/ai-constructs/-/ai-constructs-1.5.3.tgz", "fileCount": 47, "integrity": "sha512-eBOsNqh4zwo7iS8Qz2215LqV8JHMk3i+iCqKTB0v9r4Yt0bp4xYNjXr5kCN8kStK0LqtAzNcCmVtVFdlZEG4bA==", "signatures": [{"sig": "MEYCIQDZn0KKOi8i9snLy0wEA1Mu5MqhrfFz7nlon+Bs1A69RgIhANqGjRo5yWg6Aj18YmW2UrXKhKJV3bcRmX20zWf/AbZN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 1185981}}}, "modified": "2025-05-22T20:10:45.471Z"}